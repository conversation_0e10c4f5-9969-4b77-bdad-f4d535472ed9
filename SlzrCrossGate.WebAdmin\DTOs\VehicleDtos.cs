using System.ComponentModel.DataAnnotations;

namespace SlzrCrossGate.WebAdmin.DTOs
{
    public class VehicleInfoDto
    {
        public int ID { get; set; }
        public string MerchantID { get; set; } = string.Empty;
        public string MerchantName { get; set; } = string.Empty;
        public string DeviceNO { get; set; } = string.Empty;
        public string LicensePlate { get; set; } = string.Empty;
        public string? VehicleType { get; set; }
        public string? Brand { get; set; }
        public string? Model { get; set; }
        public string? Color { get; set; }
        public string? VIN { get; set; }
        public string? EngineNumber { get; set; }
        public DateTime? RegistrationDate { get; set; }
        public DateTime? ExpiryDate { get; set; }
        public string? DriverName { get; set; }
        public string? DriverPhone { get; set; }
        public string? DriverLicense { get; set; }
        public string? InsuranceCompany { get; set; }
        public string? InsurancePolicyNumber { get; set; }
        public DateTime? InsuranceExpiryDate { get; set; }
        public string MaintenanceStatus { get; set; } = "Normal";
        public DateTime? LastMaintenanceDate { get; set; }
        public DateTime? NextMaintenanceDate { get; set; }
        public decimal? Mileage { get; set; }
        public string? FuelType { get; set; }
        public string? Remark { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreateTime { get; set; }
        public DateTime? UpdateTime { get; set; }
        public string? Creator { get; set; }
        public string? Updater { get; set; }
    }

    public class CreateVehicleDto
    {
        [Required(ErrorMessage = "商户ID不能为空")]
        public string MerchantID { get; set; } = string.Empty;
        
        [Required(ErrorMessage = "设备编号不能为空")]
        [MaxLength(8, ErrorMessage = "设备编号长度不能超过8个字符")]
        public string DeviceNO { get; set; } = string.Empty;
        
        [Required(ErrorMessage = "车牌号不能为空")]
        [MaxLength(20, ErrorMessage = "车牌号长度不能超过20个字符")]
        public string LicensePlate { get; set; } = string.Empty;
        
        [MaxLength(50, ErrorMessage = "车辆类型长度不能超过50个字符")]
        public string? VehicleType { get; set; }
        
        [MaxLength(50, ErrorMessage = "品牌长度不能超过50个字符")]
        public string? Brand { get; set; }
        
        [MaxLength(50, ErrorMessage = "型号长度不能超过50个字符")]
        public string? Model { get; set; }
        
        [MaxLength(30, ErrorMessage = "颜色长度不能超过30个字符")]
        public string? Color { get; set; }
        
        [MaxLength(50, ErrorMessage = "车架号长度不能超过50个字符")]
        public string? VIN { get; set; }
        
        [MaxLength(50, ErrorMessage = "发动机号长度不能超过50个字符")]
        public string? EngineNumber { get; set; }
        
        public DateTime? RegistrationDate { get; set; }
        public DateTime? ExpiryDate { get; set; }
        
        [MaxLength(50, ErrorMessage = "司机姓名长度不能超过50个字符")]
        public string? DriverName { get; set; }
        
        [MaxLength(20, ErrorMessage = "司机电话长度不能超过20个字符")]
        public string? DriverPhone { get; set; }
        
        [MaxLength(30, ErrorMessage = "驾驶证号长度不能超过30个字符")]
        public string? DriverLicense { get; set; }
        
        [MaxLength(100, ErrorMessage = "保险公司长度不能超过100个字符")]
        public string? InsuranceCompany { get; set; }
        
        [MaxLength(50, ErrorMessage = "保险单号长度不能超过50个字符")]
        public string? InsurancePolicyNumber { get; set; }
        
        public DateTime? InsuranceExpiryDate { get; set; }
        
        [MaxLength(20, ErrorMessage = "维护状态长度不能超过20个字符")]
        public string? MaintenanceStatus { get; set; }
        
        public DateTime? LastMaintenanceDate { get; set; }
        public DateTime? NextMaintenanceDate { get; set; }
        public decimal? Mileage { get; set; }
        
        [MaxLength(20, ErrorMessage = "燃料类型长度不能超过20个字符")]
        public string? FuelType { get; set; }
        
        public string? Remark { get; set; }
    }

    public class UpdateVehicleDto : CreateVehicleDto
    {
        // 继承CreateVehicleDto的所有属性
    }

    public class VehicleStatsDto
    {
        public int TotalCount { get; set; }
        public int ActiveCount { get; set; }
        public int MaintenanceCount { get; set; }
        public int WarningCount { get; set; }
        public int ExpiredCount { get; set; }
    }
}
