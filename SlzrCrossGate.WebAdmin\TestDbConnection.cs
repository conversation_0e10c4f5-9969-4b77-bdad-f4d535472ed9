using MySqlConnector;
using Microsoft.Data.SqlClient;
using System.Diagnostics;
using System.Data.Common;

namespace SlzrCrossGate.WebAdmin;

public static class TestDbConnection
{
    public static async Task<bool> TestConnectionAsync(string connectionString, ILogger logger, string databaseProvider = "MySql")
    {
        try
        {
            logger.LogInformation("开始测试数据库连接...");
            logger.LogInformation("数据库提供程序: {Provider}", databaseProvider);
            logger.LogInformation("连接字符串: {ConnectionString}", MaskPassword(connectionString));

            var stopwatch = Stopwatch.StartNew();

            DbConnection connection = databaseProvider.ToLower() switch
            {
                "mysql" => new MySqlConnection(connectionString),
                "sqlserver" => new SqlConnection(connectionString),
                _ => throw new NotSupportedException($"不支持的数据库提供程序: {databaseProvider}")
            };

            using (connection)
            {
                logger.LogInformation("正在尝试打开数据库连接...");
                await connection.OpenAsync();

                stopwatch.Stop();
                logger.LogInformation("数据库连接成功! 耗时: {ElapsedMs}ms", stopwatch.ElapsedMilliseconds);

                // 测试基本查询
                logger.LogInformation("正在测试基本查询...");

                string testQuery = databaseProvider.ToLower() switch
                {
                    "mysql" => "SELECT VERSION(), NOW(), DATABASE()",
                    "sqlserver" => "SELECT @@VERSION, GETDATE(), DB_NAME()",
                    _ => "SELECT 1"
                };

                using var command = connection.CreateCommand();
                command.CommandText = testQuery;
                using var reader = await command.ExecuteReaderAsync();

                if (await reader.ReadAsync())
                {
                    var version = reader.GetString(0);
                    var now = reader.GetDateTime(1);
                    var database = reader.GetString(2);

                    logger.LogInformation("数据库版本: {Version}", version);
                    logger.LogInformation("服务器时间: {ServerTime}", now);
                    logger.LogInformation("当前数据库: {Database}", database);
                }

                return true;
            }
        }
        catch (MySqlException ex)
        {
            logger.LogError(ex, "MySQL 连接错误 (错误代码: {ErrorCode}): {Message}", ex.Number, ex.Message);

            // 提供具体的错误诊断
            switch (ex.Number)
            {
                case 1045:
                    logger.LogError("认证失败 - 请检查用户名和密码");
                    break;
                case 1049:
                    logger.LogError("数据库不存在 - 请检查数据库名称");
                    break;
                case 2003:
                    logger.LogError("无法连接到MySQL服务器 - 请检查服务器地址和端口");
                    break;
                case 1130:
                    logger.LogError("主机不被允许连接 - 请检查MySQL用户权限");
                    break;
                default:
                    logger.LogError("其他MySQL错误，请检查连接参数");
                    break;
            }

            return false;
        }
        catch (SqlException ex)
        {
            logger.LogError(ex, "SQL Server 连接错误 (错误代码: {ErrorCode}): {Message}", ex.Number, ex.Message);

            // 提供具体的错误诊断
            switch (ex.Number)
            {
                case 2:
                    logger.LogError("网络相关或实例特定的错误 - 请检查服务器地址和端口");
                    break;
                case 18456:
                    logger.LogError("登录失败 - 请检查用户名和密码");
                    break;
                case 4060:
                    logger.LogError("数据库不存在 - 请检查数据库名称");
                    break;
                case 53:
                    logger.LogError("网络路径未找到 - 请检查服务器地址");
                    break;
                default:
                    logger.LogError("其他SQL Server错误，请检查连接参数");
                    break;
            }

            return false;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "数据库连接测试失败: {Message}", ex.Message);
            return false;
        }
    }
    
    public static async Task TestNetworkConnectivityAsync(string host, int port, ILogger logger)
    {
        try
        {
            logger.LogInformation("测试网络连接到 {Host}:{Port}...", host, port);
            
            using var tcpClient = new System.Net.Sockets.TcpClient();
            var connectTask = tcpClient.ConnectAsync(host, port);
            var timeoutTask = Task.Delay(5000); // 5秒超时
            
            var completedTask = await Task.WhenAny(connectTask, timeoutTask);
            
            if (completedTask == timeoutTask)
            {
                logger.LogError("网络连接超时 - 无法在5秒内连接到 {Host}:{Port}", host, port);
            }
            else if (connectTask.IsFaulted)
            {
                logger.LogError(connectTask.Exception, "网络连接失败到 {Host}:{Port}", host, port);
            }
            else
            {
                logger.LogInformation("网络连接成功到 {Host}:{Port}", host, port);
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "网络连接测试异常: {Message}", ex.Message);
        }
    }
    
    private static string MaskPassword(string connectionString)
    {
        // 简单的密码掩码，避免在日志中暴露密码
        return System.Text.RegularExpressions.Regex.Replace(
            connectionString, 
            @"(Password|Pwd)=([^;]+)", 
            "$1=***", 
            System.Text.RegularExpressions.RegexOptions.IgnoreCase);
    }
}
