#!/bin/bash

echo "=========================================="
echo "WebAdmin 容器启动初始化..."
echo "=========================================="

# 配置目录
CONFIG_DIR="/app/wwwroot/config"
DEFAULT_CONFIG_DIR="/app/config-defaults"

echo "配置目录: $CONFIG_DIR"
echo "默认配置目录: $DEFAULT_CONFIG_DIR"

# 确保配置目录存在
mkdir -p "$CONFIG_DIR"
echo "✅ 配置目录已创建"

# 检查并复制默认配置文件
CONFIG_FILES=(
    "terminalFields.json"
)

echo "🔍 检查默认配置目录内容:"
ls -la "$DEFAULT_CONFIG_DIR" || echo "⚠️  默认配置目录不存在或为空"

for config_file in "${CONFIG_FILES[@]}"; do
    if [ ! -f "$CONFIG_DIR/$config_file" ]; then
        if [ -f "$DEFAULT_CONFIG_DIR/$config_file" ]; then
            echo "📝 $config_file 不存在，复制默认配置..."
            cp "$DEFAULT_CONFIG_DIR/$config_file" "$CONFIG_DIR/$config_file"
            echo "✅ $config_file 默认配置已创建"
        else
            echo "⚠️  警告: 默认配置文件 $config_file 不存在于 $DEFAULT_CONFIG_DIR"
            # 如果是关键配置文件，创建一个基本的默认配置
            if [ "$config_file" = "terminalFields.json" ]; then
                echo "🔧 创建基本的terminalFields.json配置..."
                cat > "$CONFIG_DIR/$config_file" << 'EOF'
{
  "fields": {
    "merchantName": {
      "key": "merchantName",
      "displayName": "商户",
      "dataPath": "merchantName",
      "sortable": true,
      "width": 120,
      "hideOn": ["xs"],
      "type": "text",
      "enabled": true,
      "order": 1
    },
    "machineID": {
      "key": "machineID",
      "displayName": "出厂序列号",
      "dataPath": "machineID",
      "sortable": true,
      "width": 150,
      "hideOn": ["xs"],
      "type": "text",
      "enabled": true,
      "order": 2
    },
    "deviceNO": {
      "key": "deviceNO",
      "displayName": "设备编号",
      "dataPath": "deviceNO",
      "sortable": true,
      "width": 120,
      "hideOn": [],
      "type": "text",
      "enabled": true,
      "order": 3
    }
  }
}
EOF
                echo "✅ 基本配置文件已创建"
            fi
        fi
    else
        echo "✅ $config_file 已存在，跳过初始化"
    fi
done

# 设置正确的权限
echo "🔧 设置配置文件权限..."
chown -R app:app "$CONFIG_DIR" 2>/dev/null || echo "⚠️  无法设置所有者，继续执行..."
chmod -R 644 "$CONFIG_DIR"/*.json 2>/dev/null || echo "⚠️  无法设置JSON文件权限，继续执行..."

echo "✅ 权限设置完成"

# 显示配置文件状态
echo "📋 配置文件状态:"
ls -la "$CONFIG_DIR"

echo "=========================================="
echo "WebAdmin 配置初始化完成，启动应用..."
echo "=========================================="

# 启动原始命令
exec "$@"
