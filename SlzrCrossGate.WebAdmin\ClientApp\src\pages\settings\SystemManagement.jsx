import React, { useState } from 'react';
import {
  Box,
  Typography,
  Tabs,
  Tab,
  Paper,
  Alert
} from '@mui/material';
import {
  Settings as SettingsIcon,
  ViewList as FieldIcon,
  Security as SecurityIcon
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import SystemSettingsTab from './SystemSettingsTab';
import FieldManagementTab from './FieldManagementTab';

/**
 * 系统管理主页面 - 多tab形式组织各种系统管理功能
 */
const SystemManagement = () => {
  const [currentTab, setCurrentTab] = useState(0);
  const { user } = useAuth();

  // 如果用户不是系统管理员，显示无权限提示
  if (user && !user.roles?.includes('SystemAdmin')) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">
          您没有权限访问系统管理页面。只有系统管理员可以访问此页面。
        </Alert>
      </Box>
    );
  }

  const handleTabChange = (event, newValue) => {
    setCurrentTab(newValue);
  };

  // Tab配置
  const tabs = [
    {
      label: '系统设置',
      icon: <SettingsIcon />,
      component: <SystemSettingsTab />
    },
    {
      label: '字段管理',
      icon: <FieldIcon />,
      component: <FieldManagementTab />
    }
    // 未来可以在这里添加更多tab，如：
    // {
    //   label: '安全策略',
    //   icon: <SecurityIcon />,
    //   component: <SecurityPolicyTab />
    // }
  ];

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        系统管理
      </Typography>
      
      <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
        管理系统的全局设置和配置
      </Typography>

      <Paper
        elevation={3}
        sx={{
          background: 'rgba(255, 255, 255, 0.1)',
          backdropFilter: 'blur(10px)',
          borderRadius: 2,
          boxShadow: '0 4px 30px rgba(0, 0, 0, 0.1)',
          border: '1px solid rgba(255, 255, 255, 0.2)',
          overflow: 'hidden'
        }}
      >
        {/* Tab导航 */}
        <Tabs
          value={currentTab}
          onChange={handleTabChange}
          variant="fullWidth"
          sx={{
            borderBottom: 1,
            borderColor: 'divider',
            '& .MuiTab-root': {
              minHeight: 64,
              textTransform: 'none',
              fontSize: '1rem',
              fontWeight: 500
            }
          }}
        >
          {tabs.map((tab, index) => (
            <Tab
              key={index}
              label={tab.label}
              icon={tab.icon}
              iconPosition="start"
              sx={{
                '& .MuiTab-iconWrapper': {
                  marginRight: 1,
                  marginBottom: 0
                }
              }}
            />
          ))}
        </Tabs>

        {/* Tab内容 */}
        <Box sx={{ p: 0 }}>
          {tabs[currentTab]?.component}
        </Box>
      </Paper>
    </Box>
  );
};

export default SystemManagement;
