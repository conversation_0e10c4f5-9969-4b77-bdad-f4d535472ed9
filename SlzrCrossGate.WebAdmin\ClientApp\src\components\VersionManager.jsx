import React, { useEffect, useRef } from 'react';
import { useLocation } from 'react-router-dom';
import appVersionService from '../services/appVersionService';

// 全局版本检查状态管理，防止多个组件同时进行版本检查
let isVersionCheckInProgress = false;
let versionCheckPromise = null;
let lastVersionCheckTime = 0;

// 刷新保护相关常量
const REFRESH_PROTECTION_KEY = 'version_refresh_protection';
const REFRESH_PROTECTION_TIMEOUT = 10000; // 10秒内不允许重复刷新
const VERSION_CHECK_COOLDOWN = 5000; // 5秒内不允许重复版本检查
const SMART_CHECK_INTERVAL = 30 * 60 * 1000; // 30分钟智能检查间隔
const LAST_VERSION_CHECK_KEY = 'last_version_check_time';

// 检查是否需要进行智能版本检查
const shouldPerformSmartVersionCheck = () => {
  const lastCheckTime = localStorage.getItem(LAST_VERSION_CHECK_KEY);
  const now = Date.now();

  if (!lastCheckTime) {
    // 首次检查
    return true;
  }

  const timeSinceLastCheck = now - parseInt(lastCheckTime);
  return timeSinceLastCheck > SMART_CHECK_INTERVAL;
};

// 更新最后检查时间
const updateLastVersionCheckTime = () => {
  localStorage.setItem(LAST_VERSION_CHECK_KEY, Date.now().toString());
};

// 统一的版本检查函数
const performVersionCheck = async () => {
  const now = Date.now();

  // 检查冷却时间，防止频繁版本检查
  if (now - lastVersionCheckTime < VERSION_CHECK_COOLDOWN) {
    console.log('VersionManager: 版本检查冷却中，跳过检查');
    return { needsRefresh: false };
  }

  // 如果已经有版本检查在进行，返回现有的 Promise
  if (isVersionCheckInProgress && versionCheckPromise) {
    console.log('VersionManager: 版本检查已在进行中，等待现有检查完成');
    return versionCheckPromise;
  }

  // 更新最后检查时间
  lastVersionCheckTime = now;

  // 标记版本检查开始
  isVersionCheckInProgress = true;

  versionCheckPromise = (async () => {
    try {
      console.log('VersionManager: 🔍 开始版本检查...');

      // 获取服务器当前版本
      const response = await fetch('/api/Version');
      if (!response.ok) {
        console.log('VersionManager: ❌ 无法获取服务器版本，跳过检查');
        return { needsRefresh: false };
      }

      const serverVersion = await response.json();
      console.log('VersionManager: 🌐 服务器版本:', serverVersion.version, '构建时间:', serverVersion.buildTimeString);

      // 获取localStorage中的版本信息
      const storedVersionStr = localStorage.getItem('app_version_info');

      if (!storedVersionStr) {
        // 本地无版本信息，保存服务器版本
        console.log('VersionManager: 📝 无存储版本信息，保存服务器版本');
        localStorage.setItem('app_version_info', JSON.stringify({
          version: serverVersion.version,
          buildTime: serverVersion.buildTime,
          buildTimeString: serverVersion.buildTimeString,
          timestamp: Date.now()
        }));
        return {
          needsRefresh: false,
          versionInfo: {
            version: serverVersion.version,
            buildTime: serverVersion.buildTime,
            buildTimeString: serverVersion.buildTimeString
          }
        }; // 不需要刷新，返回版本信息
      }

      const storedVersion = JSON.parse(storedVersionStr);
      console.log('VersionManager: 💾 localStorage版本:', storedVersion.version, '构建时间:', storedVersion.buildTimeString);

      // 比较版本
      const versionChanged = storedVersion.version !== serverVersion.version;
      const buildTimeChanged = storedVersion.buildTime !== serverVersion.buildTime;

      if (versionChanged || buildTimeChanged) {
        // 检查刷新保护
        const lastRefreshTime = localStorage.getItem(REFRESH_PROTECTION_KEY);
        const now = Date.now();

        if (lastRefreshTime && (now - parseInt(lastRefreshTime)) < REFRESH_PROTECTION_TIMEOUT) {
          console.log('VersionManager: ⚠️ 刷新保护激活，跳过版本更新刷新');
          return { needsRefresh: false };
        }

        console.log('VersionManager: 🔄 检测到版本变化!');
        console.log('   存储版本:', storedVersion.version);
        console.log('   服务器版本:', serverVersion.version);
        console.log('   版本变化:', versionChanged);
        console.log('   构建时间变化:', buildTimeChanged);

        // 更新本地版本信息
        console.log('VersionManager: 💾 更新本地版本信息');
        const newVersionInfo = {
          version: serverVersion.version,
          buildTime: serverVersion.buildTime,
          buildTimeString: serverVersion.buildTimeString,
          timestamp: Date.now()
        };
        localStorage.setItem('app_version_info', JSON.stringify(newVersionInfo));

        // 设置刷新保护时间戳
        localStorage.setItem(REFRESH_PROTECTION_KEY, now.toString());

        // 刷新页面获取最新前端资源
        console.log('VersionManager: 🔄 刷新页面获取最新前端资源');
        setTimeout(() => {
          window.location.reload();
        }, 100);

        return { needsRefresh: true }; // 需要刷新
      } else {
        console.log('VersionManager: ✅ 版本一致，无需刷新');
        return {
          needsRefresh: false,
          versionInfo: {
            version: serverVersion.version,
            buildTime: serverVersion.buildTime,
            buildTimeString: serverVersion.buildTimeString
          }
        }; // 不需要刷新，返回版本信息
      }
    } catch (error) {
      console.error('VersionManager: ❌ 版本检查失败:', error);
      return { needsRefresh: false };
    } finally {
      // 重置状态
      isVersionCheckInProgress = false;
      versionCheckPromise = null;
    }
  })();

  return versionCheckPromise;
};

/**
 * 版本管理器组件（优化版）
 * 负责主页版本检查和版本服务初始化
 * - 登录页面：统一进行版本检查，避免重复请求
 * - 主页面：先检查版本一致性，不一致则刷新页面，一致则初始化版本服务
 */
const VersionManager = () => {
  const location = useLocation();
  const initializationRef = useRef(false);
  const lastPathRef = useRef('');

  useEffect(() => {
    const isLoginPage = location.pathname.includes('/login') || location.pathname.includes('/two-factor');
    const isDashboardPage = location.pathname === '/app/dashboard' || location.pathname === '/app';

    // 方案3：只在特定页面检查版本（登录页面、首页）
    const shouldCheckVersionOnThisPage = isLoginPage || isDashboardPage;

    // 防止重复初始化 - 更严格的检查
    if (initializationRef.current && lastPathRef.current === location.pathname) {
      console.log('VersionManager: 已在当前页面初始化，跳过重复初始化');
      return;
    }

    // 标记当前页面已初始化
    initializationRef.current = true;
    lastPathRef.current = location.pathname;

    if (isLoginPage) {
      // 登录页面：总是进行版本检查（确保用户看到最新版本）
      console.log('VersionManager: 登录页面，执行版本检查');
      performVersionCheck().then(() => {
        updateLastVersionCheckTime(); // 更新检查时间
      });
      return;
    }

    if (!shouldCheckVersionOnThisPage) {
      // 非特定页面：只初始化版本服务，不进行版本检查
      console.log('VersionManager: 非特定页面，只初始化版本服务');
      const initializeOnly = async () => {
        try {
          // 从localStorage获取版本信息，避免重复请求
          const storedVersionStr = localStorage.getItem('app_version_info');
          if (storedVersionStr) {
            const storedVersion = JSON.parse(storedVersionStr);
            await appVersionService.initializeWithVersion(storedVersion);
          } else {
            await appVersionService.initialize();
          }
          console.log('VersionManager: 版本服务初始化完成');
        } catch (error) {
          console.error('VersionManager: 版本服务初始化失败:', error);
        }
      };
      initializeOnly();
      return;
    }

    // 特定页面（首页等）：方案4 - 基于时间间隔的智能检查
    if (!shouldPerformSmartVersionCheck()) {
      console.log('VersionManager: 智能检查 - 距离上次检查时间不足30分钟，跳过版本检查');
      // 只初始化版本服务
      const initializeOnly = async () => {
        try {
          const storedVersionStr = localStorage.getItem('app_version_info');
          if (storedVersionStr) {
            const storedVersion = JSON.parse(storedVersionStr);
            await appVersionService.initializeWithVersion(storedVersion);
          } else {
            await appVersionService.initialize();
          }
          console.log('VersionManager: 版本服务初始化完成');
        } catch (error) {
          console.error('VersionManager: 版本服务初始化失败:', error);
        }
      };
      initializeOnly();
      return;
    }

    console.log('VersionManager: 智能检查 - 执行版本检查（距离上次检查超过30分钟）');

    // 特定页面的智能版本检查和初始化
    const initializeVersionService = async () => {
      try {
        console.log('VersionManager: 特定页面智能版本检查和初始化');

        // 先进行版本检查，如果需要刷新则会自动刷新页面
        const checkResult = await performVersionCheck();

        // 更新最后检查时间
        updateLastVersionCheckTime();

        if (checkResult.needsRefresh) {
          // 如果需要刷新，函数内部已经处理了刷新逻辑，这里直接返回
          return;
        }

        // 版本一致，初始化版本服务（使用已获取的版本信息，避免重复请求）
        console.log('VersionManager: 版本一致，初始化版本服务');

        if (checkResult.versionInfo) {
          // 使用版本检查时获取的版本信息，避免重复请求
          await appVersionService.initializeWithVersion(checkResult.versionInfo);
        } else {
          // 如果没有版本信息，从localStorage获取或重新请求
          const storedVersionStr = localStorage.getItem('app_version_info');
          if (storedVersionStr) {
            const storedVersion = JSON.parse(storedVersionStr);
            await appVersionService.initializeWithVersion(storedVersion);
          } else {
            await appVersionService.initialize();
          }
        }
        console.log('VersionManager: 版本服务初始化完成');
      } catch (error) {
        console.error('VersionManager: 版本检查或初始化失败:', error);
        // 出错时仍然尝试初始化服务
        try {
          await appVersionService.initialize();
        } catch (initError) {
          console.error('VersionManager: 版本服务初始化也失败:', initError);
        }
      }
    };

    initializeVersionService();

    return () => {
      // 清理服务
      appVersionService.cleanup();
    };
  }, [location.pathname]);

  // 优化后的VersionManager不再显示更新对话框
  // 版本变化检测和刷新在初始化时处理
  return null;
};

export default VersionManager;
