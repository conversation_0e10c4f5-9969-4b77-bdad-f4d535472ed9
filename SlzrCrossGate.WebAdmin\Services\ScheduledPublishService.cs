using Microsoft.EntityFrameworkCore;
using SlzrCrossGate.Core.Database;
using SlzrCrossGate.Core.Models;
using SlzrCrossGate.Core.Service.BusinessServices;

namespace SlzrCrossGate.WebAdmin.Services
{
    /// <summary>
    /// 预约发布后台服务
    /// </summary>
    public class ScheduledPublishService : BackgroundService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<ScheduledPublishService> _logger;
        private readonly TimeSpan _checkInterval = TimeSpan.FromMinutes(1); // 每分钟检查一次

        public ScheduledPublishService(IServiceProvider serviceProvider, ILogger<ScheduledPublishService> logger)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("预约发布服务已启动");

            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    await ProcessScheduledPublishes();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "处理预约发布时发生错误");
                }

                await Task.Delay(_checkInterval, stoppingToken);
            }

            _logger.LogInformation("预约发布服务已停止");
        }

        private async Task ProcessScheduledPublishes()
        {
            using var scope = _serviceProvider.CreateScope();
            var dbContext = scope.ServiceProvider.GetRequiredService<TcpDbContext>();
            var filePublishEventService = scope.ServiceProvider.GetRequiredService<FilePublishEventService>();

            // 查找需要执行的预约发布（预约时间已到且状态为待发布）
            var now = DateTime.Now;
            var pendingPublishes = await dbContext.ScheduledFilePublishs
                .Where(sfp => sfp.Status == ScheduledPublishStatus.Pending && sfp.ScheduledTime <= now)
                .ToListAsync();

            if (pendingPublishes.Count == 0)
            {
                return;
            }

            _logger.LogInformation($"找到 {pendingPublishes.Count} 个待执行的预约发布");

            foreach (var scheduledPublish in pendingPublishes)
            {
                try
                {
                    await ExecuteScheduledPublish(dbContext, filePublishEventService, scheduledPublish);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"执行预约发布失败，ID: {scheduledPublish.ID}");
                    
                    // 更新状态为失败
                    scheduledPublish.Status = ScheduledPublishStatus.Failed;
                    scheduledPublish.ExecutedTime = DateTime.Now;
                    scheduledPublish.ErrorMessage = ex.Message;
                    
                    await dbContext.SaveChangesAsync();
                }
            }
        }

        private async Task ExecuteScheduledPublish(TcpDbContext dbContext, FilePublishEventService filePublishEventService, ScheduledFilePublish scheduledPublish)
        {
            _logger.LogInformation($"开始执行预约发布，ID: {scheduledPublish.ID}");


            // 获取文件版本信息
            var fileVersion = await dbContext.FileVers
                .Where(fv => fv.ID == scheduledPublish.FileVersionID)
                .FirstOrDefaultAsync();

            if (fileVersion == null)
            {
                throw new InvalidOperationException($"文件版本不存在，ID: {scheduledPublish.FileVersionID}");
            }


            // 检查该文件类型和发布目标与发布类型是否已存在
            var existingFilePublish = await dbContext.FilePublishs
                .FirstOrDefaultAsync(p => p.FileFullType == scheduledPublish.FileFullType 
                                         && p.PublishType == scheduledPublish.PublishType 
                                         && p.PublishTarget == scheduledPublish.PublishTarget 
                                         && p.MerchantID == scheduledPublish.MerchantID);

            var now = DateTime.Now;
            FilePublish filePublish;

            if (existingFilePublish != null)
            {
                // 存在其它版本的发布，更新版本信息
                existingFilePublish.FileVerID = scheduledPublish.FileVersionID;
                existingFilePublish.Ver = fileVersion.Ver;
                existingFilePublish.FileSize = fileVersion.FileSize;
                existingFilePublish.Crc = fileVersion.Crc;
                existingFilePublish.UploadFileID = fileVersion.UploadFileID;
                existingFilePublish.Remark = "预约发布";
                existingFilePublish.PublishTime = now;
                existingFilePublish.Operator = $"系统预约发布({scheduledPublish.CreatedBy})";
                
                filePublish = existingFilePublish;
            }
            else
            {
                
                // 创建新的文件发布记录
                filePublish = new FilePublish
                {
                    MerchantID = scheduledPublish.MerchantID,
                    FileTypeID = scheduledPublish.FileTypeID,
                    FilePara = scheduledPublish.FilePara,
                    FileFullType = scheduledPublish.FileFullType,
                    Ver = scheduledPublish.Ver,
                    FileSize = fileVersion.FileSize,
                    Crc = fileVersion.Crc,
                    FileVerID = scheduledPublish.FileVersionID,
                    UploadFileID = fileVersion.UploadFileID,
                    PublishType = scheduledPublish.PublishType,
                    PublishTarget = scheduledPublish.PublishTarget,
                    PublishTime = now,
                    Operator = $"预约发布-{scheduledPublish.CreatedBy}"
                };
                
                dbContext.FilePublishs.Add(filePublish);
            }

            // 创建文件发布历史记录
            var filePublishHistory = new FilePublishHistory
            {
                MerchantID = scheduledPublish.MerchantID,
                FileTypeID = scheduledPublish.FileTypeID,
                FilePara = scheduledPublish.FilePara,
                FileFullType = scheduledPublish.FileFullType,
                Ver = scheduledPublish.Ver,
                FileSize = filePublish.FileSize,
                Crc = filePublish.Crc,
                FileVerID = scheduledPublish.FileVersionID,
                UploadFileID = filePublish.UploadFileID,
                PublishType = scheduledPublish.PublishType,
                PublishTarget = scheduledPublish.PublishTarget,
                PublishTime = now,
                Operator = $"预约发布-{scheduledPublish.CreatedBy}",
                OperationType = "Publish"
            };

            dbContext.FilePublishHistories.Add(filePublishHistory);

            // 更新预约发布状态
            scheduledPublish.Status = ScheduledPublishStatus.Published;
            scheduledPublish.ExecutedTime = now;

            await dbContext.SaveChangesAsync();

            // 发送发布事件
            var eventMessage = new FilePublishEventMessage
            {
                ActionType = FilePublishEventActionType.Publish,
                ActionTime = now,
                FilePublishID = filePublish.ID,
                MerchantID = scheduledPublish.MerchantID,
                FileTypeID = scheduledPublish.FileTypeID,
                FilePara = scheduledPublish.FilePara,
                FileFullType = scheduledPublish.FileFullType,
                Ver = scheduledPublish.Ver,
                FileCrc = filePublish.Crc,
                FileSize = filePublish.FileSize,
                Operator = $"系统预约发布({scheduledPublish.CreatedBy})",
                PublishType = scheduledPublish.PublishType,
                PublishTarget = scheduledPublish.PublishTarget
            };

            await filePublishEventService.Publish(eventMessage);

            _logger.LogInformation($"预约发布执行成功，ID: {scheduledPublish.ID}，文件发布ID: {filePublish.ID}");
        }
    }
}
