import { useState, useEffect, useRef } from 'react';
import {
  Box,
  Button,
  Card,
  Container,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TablePagination,
  TableRow,
  Typography,
  Paper,
  Grid,
  CircularProgress
} from '@mui/material';
import {
  FileDownload as ExportIcon,
  Refresh as RefreshIcon,
  Add as AddIcon
} from '@mui/icons-material';
import { useSnackbar } from 'notistack';
import { unregisteredLineAPI } from '../../services/api';
import MerchantAutocomplete from '../../components/MerchantAutocomplete';
import CreateLineDialog from '../../components/CreateLineDialog';

const UnregisteredLines = () => {
  const { enqueueSnackbar } = useSnackbar();
  const [lines, setLines] = useState([]);
  const [loading, setLoading] = useState(true);
  const [totalCount, setTotalCount] = useState(0);
  const [page, setPage] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [selectedMerchant, setSelectedMerchant] = useState(null);

  // 创建线路对话框状态
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [selectedLineData, setSelectedLineData] = useState(null);

  // 防重复请求
  const loadLinesRef = useRef();
  const hasLoadedRef = useRef(false);

  // 加载未注册线路数据
  const loadLines = async (isInitialLoad = false, forceLoad = false) => {
    try {
      setLoading(true);

      // 构建查询参数
      const params = {
        page: page + 1, // API使用1-based索引
        pageSize
      };

      // 添加商户ID参数
      if (selectedMerchant) {
        params.merchantId = selectedMerchant.merchantID;
      }

      // 移除undefined的参数
      Object.keys(params).forEach(key => {
        if (params[key] === undefined) {
          delete params[key];
        }
      });

      // 避免重复请求：检查参数是否真的发生了变化（除非强制加载）
      const paramsString = JSON.stringify(params);
      if (!isInitialLoad && !forceLoad && loadLinesRef.current === paramsString) {
        console.log('UnregisteredLines: 参数未变化，跳过重复请求');
        setLoading(false);
        return;
      }
      loadLinesRef.current = paramsString;

      console.log('UnregisteredLines: 执行数据请求', params);
      const response = await unregisteredLineAPI.getUnregisteredLines(params);
      setLines(response.items || []);
      setTotalCount(response.totalCount || 0);
    } catch (error) {
      enqueueSnackbar('加载未注册线路列表失败', { variant: 'error' });
      console.error('加载未注册线路列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 统一的数据加载逻辑
  useEffect(() => {
    // 防止重复加载
    if (hasLoadedRef.current) {
      console.log('UnregisteredLines: 已加载过，跳过重复请求');
      return;
    }

    console.log('UnregisteredLines: 执行首次加载');
    hasLoadedRef.current = true;
    loadLines(true, false); // 标记为初始加载，非强制加载
  }, []);

  // 当参数变化时重新加载
  useEffect(() => {
    if (hasLoadedRef.current) { // 只有在首次加载完成后才响应参数变化
      console.log('UnregisteredLines: 参数变化，重新加载');
      loadLines(false, false); // 非初始加载，非强制加载
    }
  }, [page, pageSize, selectedMerchant]);

  // 处理页码变化
  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  // 处理每页大小变化
  const handleChangePageSize = (event) => {
    setPageSize(parseInt(event.target.value, 10));
    setPage(0);
  };

  // 处理商户选择变化
  const handleMerchantChange = (event, newValue) => {
    setSelectedMerchant(newValue);
    setPage(0);
  };

  // 处理创建线路
  const handleCreateLine = (lineData) => {
    setSelectedLineData(lineData);
    setCreateDialogOpen(true);
  };

  // 处理创建线路对话框关闭
  const handleCreateDialogClose = () => {
    setCreateDialogOpen(false);
    setSelectedLineData(null);
  };

  // 处理创建线路成功
  const handleCreateLineSuccess = () => {
    // 重新加载数据
    loadLines(false, true);
  };

  // 刷新数据
  const handleRefresh = () => {
    loadLines(false, true); // 强制执行刷新
  };

  // 导出未注册线路
  const handleExport = async () => {
    try {
      //enqueueSnackbar('正在导出未注册线路...', { variant: 'info' });

      // 构建查询参数
      const params = {};

      // 添加商户ID参数
      if (selectedMerchant) {
        params.merchantId = selectedMerchant.MerchantID;
      }

      // 调用API导出
      const blob = await unregisteredLineAPI.exportUnregisteredLines(params);

      // 创建下载链接
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;

      // 生成文件名
      const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
      link.download = `unregistered_lines_${timestamp}.csv`;

      // 触发下载
      document.body.appendChild(link);
      link.click();

      // 清理
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      //enqueueSnackbar('未注册线路导出成功！', { variant: 'success' });
    } catch (error) {
      console.error('Error exporting lines:', error);
      let errorMsg = '导出未注册线路失败';
      if (error.response && error.response.data && error.response.data.message) {
        errorMsg = error.response.data.message;
      }
      enqueueSnackbar(errorMsg, { variant: 'error' });
    }
  };

  return (
    <Container maxWidth={false}>
      <Box sx={{ py: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
          <Typography variant="h4" gutterBottom>
            未注册线路
          </Typography>
          <Box>
            <Button
              variant="outlined"
              onClick={handleRefresh}
              startIcon={<RefreshIcon />}
              sx={{ mr: 1 }}
            >
              刷新
            </Button>
            <Button
              variant="outlined"
              onClick={handleExport}
              startIcon={<ExportIcon />}
            >
              导出数据
            </Button>
          </Box>
        </Box>

        {/* 筛选条件 */}
        <Paper sx={{ p: 2, mb: 3 }}>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} sm={6} md={4}>
              <MerchantAutocomplete
                value={selectedMerchant}
                onChange={handleMerchantChange}
                label="筛选商户"
                placeholder="选择商户进行筛选"
                size="small"
              />
            </Grid>
          </Grid>
        </Paper>

        {/* 未注册线路列表 */}
        <Card>
          <TableContainer component={Paper} sx={{ overflowX: 'auto' }}>
            <Table sx={{ minWidth: 650 }}>
              <TableHead>
                <TableRow>
                  <TableCell>商户ID</TableCell>
                  <TableCell>商户名称</TableCell>
                  <TableCell>线路编号</TableCell>
                  <TableCell align="right">终端数量</TableCell>
                  <TableCell align="center">操作</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {loading && (
                  <TableRow>
                    <TableCell colSpan={5} align="center">
                      <CircularProgress />
                    </TableCell>
                  </TableRow>
                )}
                {!loading && lines.length === 0 && (
                  <TableRow>
                    <TableCell colSpan={5} align="center">
                      <Typography variant="body2" color="text.secondary">
                        暂无未注册线路数据
                      </Typography>
                    </TableCell>
                  </TableRow>
                )}
                {!loading && lines && lines.length > 0 && lines.map((line) => (
                  <TableRow key={`${line.merchantID}-${line.lineNO}`} hover>
                    <TableCell>{line.merchantID}</TableCell>
                    <TableCell>{line.merchantName}</TableCell>
                    <TableCell>{line.lineNO}</TableCell>
                    <TableCell align="right">{line.terminalCount}</TableCell>
                    <TableCell align="center">
                      <Button
                        variant="contained"
                        size="small"
                        startIcon={<AddIcon />}
                        onClick={() => handleCreateLine(line)}
                        sx={{
                          minWidth: 'auto',
                          px: 2,
                          py: 0.5,
                          fontSize: '0.75rem'
                        }}
                      >
                        创建线路
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
          <TablePagination
            component="div"
            count={totalCount}
            page={page}
            onPageChange={handleChangePage}
            rowsPerPage={pageSize}
            onRowsPerPageChange={handleChangePageSize}
            rowsPerPageOptions={[5, 10, 25, 50]}
            labelRowsPerPage="每页行数:"
            labelDisplayedRows={({ from, to, count }) => `${from}-${to} 共 ${count !== -1 ? count : `超过 ${to}`}`}
          />
        </Card>

        {/* 创建线路对话框 */}
        <CreateLineDialog
          open={createDialogOpen}
          onClose={handleCreateDialogClose}
          lineData={selectedLineData}
          onSuccess={handleCreateLineSuccess}
        />
      </Box>
    </Container>
  );
};

export default UnregisteredLines;
