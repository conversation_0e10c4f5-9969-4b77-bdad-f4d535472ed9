using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace SlzrCrossGate.Core.Models
{
    /// <summary>
    /// 终端文件上传记录
    /// </summary>
    public class TerminalFileUpload : ITenantEntity
    {
        /// <summary>
        /// 上传文件ID，32字节十六进制字符串，对应协议中的上传文件ID
        /// </summary>
        [Key]
        [MaxLength(32)]
        public required string ID { get; set; }

        /// <summary>
        /// 商户号
        /// </summary>
        [MaxLength(8)]
        public required string MerchantID { get; set; }

        /// <summary>
        /// 终端序列号（出厂序列号）
        /// </summary>
        [MaxLength(20)]
        public required string TerminalID { get; set; }

        /// <summary>
        /// 终端类型
        /// </summary>
        [MaxLength(10)]
        public required string TerminalType { get; set; }

        /// <summary>
        /// 文件类型，如"LOG"、"DAT"等
        /// </summary>
        [MaxLength(10)]
        public required string FileType { get; set; }

        /// <summary>
        /// 文件名
        /// </summary>
        [MaxLength(255)]
        public required string FileName { get; set; }

        /// <summary>
        /// 文件大小（字节）
        /// </summary>
        public long FileSize { get; set; }

        /// <summary>
        /// 文件CRC32校验值
        /// </summary>
        [MaxLength(8)]
        public required string FileCRC { get; set; }

        /// <summary>
        /// 已接收长度（字节）
        /// </summary>
        public long ReceivedLength { get; set; } = 0;

        /// <summary>
        /// 终端请求的块大小
        /// </summary>
        public int ClientChunkSize { get; set; }

        /// <summary>
        /// 服务端指定的块大小
        /// </summary>
        public int ServerChunkSize { get; set; }

        /// <summary>
        /// 上传状态
        /// </summary>
        public TerminalUploadStatus Status { get; set; } = TerminalUploadStatus.Uploading;

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime StartTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdateTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 临时文件路径
        /// </summary>
        [MaxLength(500)]
        public string? TempFilePath { get; set; }

        /// <summary>
        /// 最终文件ID（关联UploadFile表）
        /// </summary>
        [MaxLength(32)]
        public string? FinalUploadFileID { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        [MaxLength(500)]
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 上传者（系统自动设置为终端ID）
        /// </summary>
        [MaxLength(50)]
        public string? UploadedBy { get; set; }

        /// <summary>
        /// 最后活动时间（用于清理过期记录）
        /// </summary>
        public DateTime LastActivityTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 关联的最终上传文件
        /// </summary>
        [ForeignKey("FinalUploadFileID")]
        public virtual UploadFile? FinalUploadFile { get; set; }
    }

    /// <summary>
    /// 终端上传状态枚举
    /// </summary>
    public enum TerminalUploadStatus
    {
        /// <summary>
        /// 上传中
        /// </summary>
        Uploading = 1,

        /// <summary>
        /// 上传完成
        /// </summary>
        Completed = 2,

        /// <summary>
        /// 上传失败
        /// </summary>
        Failed = 3,

        /// <summary>
        /// 上传取消
        /// </summary>
        Cancelled = 4
    }
}
