import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Paper,
  Typography,
  Grid,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Chip,
  IconButton,
  Tooltip,
  Alert
} from '@mui/material';
import {
  Edit as EditIcon,
  Cancel as CancelIcon,
  Refresh as RefreshIcon,
  Schedule as ScheduleIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Block as BlockIcon
} from '@mui/icons-material';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { zhCN } from '@mui/x-date-pickers/locales';
import { zhCN as dateFnsZhCN } from 'date-fns/locale';
import { useSnackbar } from 'notistack';
import { fileAPI } from '../../services/api';
import { formatDateTime } from '../../utils/dateUtils';
import { parseErrorMessage } from '../../utils/errorHandler';
import MerchantAutocomplete from '../../components/MerchantAutocomplete';
import { useAuth } from '../../contexts/AuthContext';
import ResponsiveTable, {
  ResponsiveTableHead,
  ResponsiveTableBody,
  ResponsiveTableRow,
  ResponsiveTableCell
} from '../../components/ResponsiveTable';
import { TablePagination } from '@mui/material';

const ScheduledFilePublishList = () => {
  const { enqueueSnackbar } = useSnackbar();
  const { user } = useAuth();

  // 状态管理
  const [scheduledPublishes, setScheduledPublishes] = useState([]);
  const [loading, setLoading] = useState(false);
  const [totalCount, setTotalCount] = useState(0);
  const [page, setPage] = useState(0);
  const [pageSize, setPageSize] = useState(10);

  // 防止重复加载的标记
  const hasLoadedRef = useRef(false);
  const loadingRequestRef = useRef(null);

  // 筛选条件
  const [filters, setFilters] = useState({
    merchantId: '',
    status: '',
    scheduledTimeFrom: null,
    scheduledTimeTo: null
  });
  const [selectedMerchant, setSelectedMerchant] = useState(null);

  // 编辑对话框
  const [editDialog, setEditDialog] = useState(false);
  const [editingItem, setEditingItem] = useState(null);
  const [editForm, setEditForm] = useState({
    publishType: 1,
    publishTarget: '',
    scheduledTime: null,
    remarks: ''
  });
  const [editLoading, setEditLoading] = useState(false);

  // 取消确认对话框
  const [cancelDialog, setCancelDialog] = useState(false);
  const [cancelingItem, setCancelingItem] = useState(null);
  const [cancelLoading, setCancelLoading] = useState(false);

  // 加载预约发布列表
  const loadScheduledPublishes = async (resetPage = false) => {
    // 生成请求标识符
    const currentPage = resetPage ? 0 : page;
    const requestId = `${currentPage}-${pageSize}-${JSON.stringify(filters)}`;

    // 如果正在进行相同的请求，直接返回
    if (loadingRequestRef.current === requestId) {
      console.log('ScheduledFilePublishList: 跳过重复请求', requestId);
      return;
    }

    loadingRequestRef.current = requestId;
    setLoading(true);

    try {
      // 处理时间参数，转换为本地时间字符串
      const formatLocalTime = (date) => {
        if (!date) return undefined;
        return new Date(date.getTime() - date.getTimezoneOffset() * 60000)
          .toISOString()
          .slice(0, 19); // 移除毫秒和时区信息，保持ISO格式
      };

      const params = {
        page: currentPage + 1,
        pageSize,
        merchantId: filters.merchantId || undefined,
        status: filters.status || undefined,
        scheduledTimeFrom: formatLocalTime(filters.scheduledTimeFrom),
        scheduledTimeTo: formatLocalTime(filters.scheduledTimeTo)
      };

      // 移除undefined值
      Object.keys(params).forEach(key => {
        if (params[key] === undefined) {
          delete params[key];
        }
      });

      console.log('ScheduledFilePublishList: 发起API请求', requestId, params);
      const response = await fileAPI.getScheduledFilePublishes(params);

      // 处理不同的响应数据结构
      const data = response.data || response;
      const items = data.items || [];
      const totalCount = data.totalCount || 0;

      setScheduledPublishes(items);
      setTotalCount(totalCount);

      if (resetPage && page !== 0) {
        setPage(0);
      }
    } catch (error) {
      console.error('Error loading scheduled publishes:', error);
      const errorMessage = parseErrorMessage(error, '加载预约发布列表失败');
      enqueueSnackbar(errorMessage, { variant: 'error' });
    } finally {
      setLoading(false);
      loadingRequestRef.current = null; // 清除请求标识
    }
  };

  // 统一的数据加载逻辑
  useEffect(() => {
    console.log('ScheduledFilePublishList: 首次加载');
    loadScheduledPublishes();
    hasLoadedRef.current = true;
  }, []);

  // 当参数变化时重新加载（仅在首次加载完成后）
  useEffect(() => {
    if (hasLoadedRef.current) { // 只有在首次加载完成后才响应参数变化
      console.log('ScheduledFilePublishList: 参数变化，重新加载');
      loadScheduledPublishes();
    }
  }, [page, pageSize]);

  // 处理筛选条件变更
  const handleFilterChange = (event) => {
    const { name, value } = event.target;
    setFilters(prev => ({ ...prev, [name]: value }));
  };

  // 处理商户选择
  const handleMerchantChange = (merchant) => {
    setSelectedMerchant(merchant);
    setFilters(prev => ({ ...prev, merchantId: merchant?.merchantID || '' }));
  };

  // 应用筛选
  const applyFilters = () => {
    if (page === 0) {
      // 如果已经在第一页，直接加载
      loadScheduledPublishes(true);
    } else {
      // 如果不在第一页，设置页码为0，useEffect会自动触发加载
      setPage(0);
    }
  };

  // 清空筛选
  const clearFilters = () => {
    setFilters({
      merchantId: '',
      status: '',
      scheduledTimeFrom: null,
      scheduledTimeTo: null
    });
    setSelectedMerchant(null);

    if (page === 0) {
      // 如果已经在第一页，延迟加载以确保状态更新完成
      setTimeout(() => loadScheduledPublishes(true), 100);
    } else {
      // 如果不在第一页，设置页码为0，useEffect会自动触发加载
      setPage(0);
    }
  };

  // 获取状态标签
  const getStatusChip = (status) => {
    const statusConfig = {
      1: { label: '待发布', color: 'warning', icon: <ScheduleIcon /> },
      2: { label: '已发布', color: 'success', icon: <CheckCircleIcon /> },
      3: { label: '已取消', color: 'default', icon: <BlockIcon /> },
      4: { label: '发布失败', color: 'error', icon: <ErrorIcon /> }
    };

    const config = statusConfig[status] || statusConfig[1];
    return (
      <Chip
        label={config.label}
        color={config.color}
        size="small"
        icon={config.icon}
      />
    );
  };

  // 获取发布类型文本
  const getPublishTypeText = (publishType) => {
    const types = {
      1: '商户级别',
      2: '线路级别',
      3: '终端级别'
    };
    return types[publishType] || '未知';
  };

  // 打开编辑对话框
  const openEditDialog = (item) => {
    setEditingItem(item);
    setEditForm({
      publishType: item.publishType,
      publishTarget: item.publishTarget,
      scheduledTime: new Date(item.scheduledTime),
      remarks: item.remarks || ''
    });
    setEditDialog(true);
  };

  // 处理编辑表单变更
  const handleEditFormChange = (event) => {
    const { name, value } = event.target;
    setEditForm(prev => ({ ...prev, [name]: value }));
  };

  // 保存编辑
  const saveEdit = async () => {
    setEditLoading(true);
    try {
      // 将时间转换为本地时间字符串（ISO格式，不带时区信息）
      const localTimeString = new Date(editForm.scheduledTime.getTime() - editForm.scheduledTime.getTimezoneOffset() * 60000)
        .toISOString()
        .slice(0, 19); // 移除毫秒和时区信息，保持ISO格式

      await fileAPI.updateScheduledFilePublish(editingItem.id, {
        PublishType: editForm.publishType,
        PublishTarget: editForm.publishTarget,
        ScheduledTime: localTimeString,
        Remarks: editForm.remarks
      });

      setEditDialog(false);
      enqueueSnackbar('预约发布更新成功', { variant: 'success' });
      loadScheduledPublishes();
    } catch (error) {
      console.error('Error updating scheduled publish:', error);
      const errorMessage = parseErrorMessage(error, '更新预约发布失败');
      enqueueSnackbar(errorMessage, { variant: 'error' });
    } finally {
      setEditLoading(false);
    }
  };

  // 打开取消确认对话框
  const openCancelDialog = (item) => {
    setCancelingItem(item);
    setCancelDialog(true);
  };

  // 取消预约发布
  const cancelScheduledPublish = async () => {
    setCancelLoading(true);
    try {
      await fileAPI.cancelScheduledFilePublish(cancelingItem.id);
      setCancelDialog(false);
      enqueueSnackbar('预约发布已取消', { variant: 'success' });
      loadScheduledPublishes();
    } catch (error) {
      console.error('Error canceling scheduled publish:', error);
      const errorMessage = parseErrorMessage(error, '取消预约发布失败');
      enqueueSnackbar(errorMessage, { variant: 'error' });
    } finally {
      setCancelLoading(false);
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        预约发布
      </Typography>

      {/* 筛选条件 */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          筛选条件
        </Typography>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={3}>
            <MerchantAutocomplete
              value={selectedMerchant}
              onChange={handleMerchantChange}
              label="选择商户"
              fullWidth
            />
          </Grid>
          <Grid item xs={12} md={2}>
            <FormControl fullWidth>
              <InputLabel>状态</InputLabel>
              <Select
                name="status"
                value={filters.status}
                onChange={handleFilterChange}
                label="状态"
              >
                <MenuItem value="">全部</MenuItem>
                <MenuItem value="1">待发布</MenuItem>
                <MenuItem value="2">已发布</MenuItem>
                <MenuItem value="3">已取消</MenuItem>
                <MenuItem value="4">发布失败</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={2.5}>
            <LocalizationProvider
              dateAdapter={AdapterDateFns}
              adapterLocale={dateFnsZhCN}
              localeText={zhCN.components.MuiLocalizationProvider.defaultProps.localeText}
            >
              <DateTimePicker
                label="预约时间从"
                value={filters.scheduledTimeFrom}
                onChange={(newValue) => {
                  setFilters(prev => ({ ...prev, scheduledTimeFrom: newValue }));
                }}
                format="yyyy-MM-dd HH:mm"
                ampm={false} // 使用24小时制
                slotProps={{
                  textField: { fullWidth: true, size: 'small' }
                }}
              />
            </LocalizationProvider>
          </Grid>
          <Grid item xs={12} md={2.5}>
            <LocalizationProvider
              dateAdapter={AdapterDateFns}
              adapterLocale={dateFnsZhCN}
              localeText={zhCN.components.MuiLocalizationProvider.defaultProps.localeText}
            >
              <DateTimePicker
                label="预约时间到"
                value={filters.scheduledTimeTo}
                onChange={(newValue) => {
                  setFilters(prev => ({ ...prev, scheduledTimeTo: newValue }));
                }}
                format="yyyy-MM-dd HH:mm"
                ampm={false} // 使用24小时制
                slotProps={{
                  textField: { fullWidth: true, size: 'small' }
                }}
              />
            </LocalizationProvider>
          </Grid>
          <Grid item xs={12} md={2}>
            <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
              <Button
                variant="contained"
                onClick={applyFilters}
                disabled={loading}
                size="small"
              >
                查询
              </Button>
              <Button
                variant="outlined"
                onClick={clearFilters}
                disabled={loading}
                size="small"
              >
                清空
              </Button>
              <Button
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={() => loadScheduledPublishes()}
                disabled={loading}
                size="small"
              >
                刷新
              </Button>
            </Box>
          </Grid>
        </Grid>
      </Paper>

      {/* 预约发布列表 */}
      <Paper sx={{ p: 3 }}>
        <Typography variant="h6" gutterBottom>
          预约发布列表
        </Typography>

        <ResponsiveTable minWidth={1200} stickyActions={true}>
          <ResponsiveTableHead>
            <ResponsiveTableRow>
              <ResponsiveTableCell hideOn={['xs']}>商户</ResponsiveTableCell>
              <ResponsiveTableCell>文件类型</ResponsiveTableCell>
              <ResponsiveTableCell hideOn={['xs', 'sm']}>版本号</ResponsiveTableCell>
              <ResponsiveTableCell hideOn={['xs', 'sm']}>发布类型</ResponsiveTableCell>
              <ResponsiveTableCell hideOn={['xs', 'sm']}>发布目标</ResponsiveTableCell>
              <ResponsiveTableCell>预约时间</ResponsiveTableCell>
              <ResponsiveTableCell>状态</ResponsiveTableCell>
              <ResponsiveTableCell hideOn={['xs', 'sm']}>备注信息</ResponsiveTableCell>
              <ResponsiveTableCell hideOn={['xs', 'sm']}>操作人</ResponsiveTableCell>
              <ResponsiveTableCell hideOn={['xs']}>创建时间</ResponsiveTableCell>
              <ResponsiveTableCell sticky={true} minWidth={120}>操作</ResponsiveTableCell>
            </ResponsiveTableRow>
          </ResponsiveTableHead>
          <ResponsiveTableBody>
            {loading ? (
              <ResponsiveTableRow>
                <ResponsiveTableCell colSpan={11} align="center">
                  <CircularProgress size={24} />
                </ResponsiveTableCell>
              </ResponsiveTableRow>
            ) : scheduledPublishes.length === 0 ? (
              <ResponsiveTableRow>
                <ResponsiveTableCell colSpan={11} align="center">
                  没有找到预约发布记录
                </ResponsiveTableCell>
              </ResponsiveTableRow>
            ) : (
              scheduledPublishes.map((item) => (
                <ResponsiveTableRow key={item.id}>
                  <ResponsiveTableCell hideOn={['xs']}>
                    <Tooltip title={item.merchantName} placement="top">
                      <Box
                        sx={{
                          maxWidth: 120,
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap'
                        }}
                      >
                        {item.merchantName}
                      </Box>
                    </Tooltip>
                  </ResponsiveTableCell>
                  <ResponsiveTableCell>
                    <Box>
                      <Typography variant="body2" fontWeight="medium">
                        {item.fileTypeName || item.fileTypeID}
                      </Typography>
                      <Typography variant="caption" color="textSecondary">
                        {item.fileFullType}
                      </Typography>
                    </Box>
                  </ResponsiveTableCell>
                  <ResponsiveTableCell hideOn={['xs', 'sm']}>{item.ver}</ResponsiveTableCell>
                  <ResponsiveTableCell hideOn={['xs', 'sm']}>
                    {getPublishTypeText(item.publishType)}
                  </ResponsiveTableCell>
                  <ResponsiveTableCell hideOn={['xs', 'sm']}>
                    <Tooltip title={item.publishTarget} placement="top">
                      <Box
                        sx={{
                          maxWidth: 100,
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap'
                        }}
                      >
                        {item.publishTarget}
                      </Box>
                    </Tooltip>
                  </ResponsiveTableCell>
                  <ResponsiveTableCell>
                    <Typography variant="body2">
                      {formatDateTime(item.scheduledTime)}
                    </Typography>
                  </ResponsiveTableCell>
                  <ResponsiveTableCell>
                    {getStatusChip(item.status)}
                  </ResponsiveTableCell>
                  <ResponsiveTableCell hideOn={['xs', 'sm']}>
                    <Tooltip title={item.remarks || ''} placement="top">
                      <Box
                        sx={{
                          maxWidth: 150,
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap'
                        }}
                      >
                        {item.remarks || '-'}
                      </Box>
                    </Tooltip>
                  </ResponsiveTableCell>
                  <ResponsiveTableCell hideOn={['xs', 'sm']}>
                    <Tooltip title={item.createdBy || 'Unknown'} placement="top">
                      <Box
                        sx={{
                          maxWidth: 120,
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap'
                        }}
                      >
                        {item.createdBy || 'Unknown'}
                      </Box>
                    </Tooltip>
                  </ResponsiveTableCell>
                  <ResponsiveTableCell hideOn={['xs']}>
                    {formatDateTime(item.createdTime)}
                  </ResponsiveTableCell>
                  <ResponsiveTableCell sticky={true}>
                    <Box sx={{ display: 'flex', gap: 0.5 }}>
                      {item.status === 1 && ( // 待发布状态才能编辑和取消
                        <>
                          <Tooltip title="编辑预约">
                            <IconButton
                              size="small"
                              onClick={() => openEditDialog(item)}
                            >
                              <EditIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="取消预约">
                            <IconButton
                              size="small"
                              color="error"
                              onClick={() => openCancelDialog(item)}
                            >
                              <CancelIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        </>
                      )}
                    </Box>
                  </ResponsiveTableCell>
                </ResponsiveTableRow>
              ))
            )}
          </ResponsiveTableBody>
        </ResponsiveTable>

        <TablePagination
          rowsPerPageOptions={[5, 10, 25, 50]}
          component="div"
          count={totalCount}
          rowsPerPage={pageSize}
          page={page}
          onPageChange={(_, newPage) => setPage(newPage)}
          onRowsPerPageChange={(event) => {
            setPageSize(parseInt(event.target.value, 10));
            setPage(0);
          }}
        />
      </Paper>

      {/* 编辑预约对话框 */}
      <Dialog
        open={editDialog}
        onClose={() => setEditDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>编辑预约发布</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>发布类型</InputLabel>
                <Select
                  name="publishType"
                  value={editForm.publishType}
                  onChange={handleEditFormChange}
                  label="发布类型"
                >
                  <MenuItem value={1}>商户级别</MenuItem>
                  <MenuItem value={2}>线路级别</MenuItem>
                  <MenuItem value={3}>终端级别</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="发布目标"
                name="publishTarget"
                value={editForm.publishTarget}
                onChange={handleEditFormChange}
                required
                helperText="线路级别请输入线路号，终端级别请输入终端ID"
              />
            </Grid>
            <Grid item xs={12}>
              <LocalizationProvider
                dateAdapter={AdapterDateFns}
                adapterLocale={dateFnsZhCN}
                localeText={zhCN.components.MuiLocalizationProvider.defaultProps.localeText}
              >
                <DateTimePicker
                  label="预约发布时间"
                  value={editForm.scheduledTime}
                  onChange={(newValue) => {
                    setEditForm(prev => ({ ...prev, scheduledTime: newValue }));
                  }}
                  minDateTime={new Date(Date.now() + 60000)} // 最早1分钟后
                  format="yyyy-MM-dd HH:mm"
                  ampm={false} // 使用24小时制
                  slotProps={{
                    textField: {
                      fullWidth: true,
                      required: true,
                      helperText: "选择预约发布的时间（格式：YYYY-MM-DD HH:mm）"
                    }
                  }}
                />
              </LocalizationProvider>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="备注信息"
                name="remarks"
                value={editForm.remarks}
                onChange={handleEditFormChange}
                multiline
                rows={3}
                inputProps={{
                  maxLength: 200
                }}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditDialog(false)} disabled={editLoading}>
            取消
          </Button>
          <Button
            onClick={saveEdit}
            variant="contained"
            disabled={editLoading || !editForm.scheduledTime}
          >
            {editLoading ? <CircularProgress size={20} /> : '保存'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* 取消预约确认对话框 */}
      <Dialog
        open={cancelDialog}
        onClose={() => setCancelDialog(false)}
        maxWidth="sm"
      >
        <DialogTitle>确认取消预约</DialogTitle>
        <DialogContent>
          {cancelingItem && (
            <Alert severity="warning" sx={{ mb: 2 }}>
              您确定要取消以下预约发布吗？
            </Alert>
          )}
          {cancelingItem && (
            <Box>
              <Typography variant="body2" gutterBottom>
                <strong>商户：</strong>{cancelingItem.merchantName}
              </Typography>
              <Typography variant="body2" gutterBottom>
                <strong>文件类型：</strong>{cancelingItem.fileTypeName || cancelingItem.fileTypeID}
              </Typography>
              <Typography variant="body2" gutterBottom>
                <strong>版本号：</strong>{cancelingItem.ver}
              </Typography>
              <Typography variant="body2" gutterBottom>
                <strong>预约时间：</strong>{formatDateTime(cancelingItem.scheduledTime)}
              </Typography>
              <Typography variant="body2" gutterBottom>
                <strong>发布类型：</strong>{getPublishTypeText(cancelingItem.publishType)}
              </Typography>
              <Typography variant="body2" gutterBottom>
                <strong>发布目标：</strong>{cancelingItem.publishTarget}
              </Typography>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCancelDialog(false)} disabled={cancelLoading}>
            取消
          </Button>
          <Button
            onClick={cancelScheduledPublish}
            variant="contained"
            color="error"
            disabled={cancelLoading}
          >
            {cancelLoading ? <CircularProgress size={20} /> : '确认取消'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ScheduledFilePublishList;
