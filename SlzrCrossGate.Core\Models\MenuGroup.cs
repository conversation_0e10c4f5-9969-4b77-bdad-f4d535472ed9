using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace SlzrCrossGate.Core.Models
{
    /// <summary>
    /// 菜单分组
    /// </summary>
    [Table("MenuGroups")]
    public class MenuGroup
    {
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// 分组标识符（用于前端识别）
        /// </summary>
        [Required]
        [StringLength(50)]
        public required string GroupKey { get; set; }

        /// <summary>
        /// 分组标题
        /// </summary>
        [Required]
        [StringLength(100)]
        public required string Title { get; set; }

        /// <summary>
        /// 图标名称（对应前端图标组件）
        /// </summary>
        [StringLength(50)]
        public string? IconName { get; set; }

        /// <summary>
        /// 排序顺序
        /// </summary>
        public int SortOrder { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 系统管理员是否可见
        /// </summary>
        public bool VisibleToSystemAdmin { get; set; } = true;

        /// <summary>
        /// 商户管理员是否可见
        /// </summary>
        public bool VisibleToMerchantAdmin { get; set; } = true;

        /// <summary>
        /// 普通用户是否可见
        /// </summary>
        public bool VisibleToUser { get; set; } = true;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 菜单项集合
        /// </summary>
        public virtual ICollection<MenuItem> MenuItems { get; set; } = new List<MenuItem>();
    }
}
