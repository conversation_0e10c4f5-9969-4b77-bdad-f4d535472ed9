/**
 * 通用剪贴板复制工具
 * 提供多种降级方案，确保在各种环境下都能正常工作
 */

/**
 * 复制文本到剪贴板
 * @param {string} text - 要复制的文本
 * @param {Object} options - 选项
 * @param {Function} options.onSuccess - 成功回调
 * @param {Function} options.onError - 失败回调
 * @param {boolean} options.showFallbackDialog - 是否显示降级对话框
 * @returns {Promise<boolean>} 是否复制成功
 */
export const copyToClipboard = async (text, options = {}) => {
  const {
    onSuccess = () => {},
    onError = () => {},
    showFallbackDialog = true
  } = options;

  try {
    // 方案1: 现代 Clipboard API (推荐)
    if (navigator.clipboard && navigator.clipboard.writeText) {
      await navigator.clipboard.writeText(text);
      onSuccess('内容已复制到剪贴板');
      return true;
    }

    // 方案2: 传统 execCommand (兼容性)
    if (document.execCommand) {
      const textArea = document.createElement('textarea');
      textArea.value = text;
      
      // 设置样式使其不可见
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      textArea.style.opacity = '0';
      textArea.style.pointerEvents = 'none';
      textArea.setAttribute('readonly', '');
      
      document.body.appendChild(textArea);
      
      // 选择文本
      textArea.focus();
      textArea.select();
      textArea.setSelectionRange(0, text.length);
      
      // 执行复制
      const successful = document.execCommand('copy');
      document.body.removeChild(textArea);
      
      if (successful) {
        onSuccess('内容已复制到剪贴板');
        return true;
      }
    }

    // 方案3: 降级对话框
    if (showFallbackDialog) {
      const message = `自动复制失败，请手动复制以下内容：\n\n${text}`;
      
      // 创建一个模态对话框显示内容
      const dialog = document.createElement('div');
      dialog.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: white;
        border: 2px solid #ccc;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        z-index: 10000;
        max-width: 80%;
        max-height: 80%;
        overflow: auto;
        font-family: Arial, sans-serif;
      `;
      
      dialog.innerHTML = `
        <div style="margin-bottom: 15px; font-weight: bold; color: #333;">
          复制内容
        </div>
        <textarea 
          readonly 
          style="width: 100%; height: 200px; border: 1px solid #ddd; border-radius: 4px; padding: 8px; font-family: monospace; font-size: 12px; resize: vertical;"
        >${text}</textarea>
        <div style="margin-top: 15px; text-align: right;">
          <button 
            onclick="this.parentElement.parentElement.remove()" 
            style="background: #7E22CE; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;"
          >
            关闭
          </button>
        </div>
      `;
      
      document.body.appendChild(dialog);
      
      // 自动选择文本
      const textarea = dialog.querySelector('textarea');
      textarea.focus();
      textarea.select();
      
      onError('请手动复制内容');
      return false;
    }

    throw new Error('所有复制方法都不可用');

  } catch (error) {
    console.error('复制失败:', error);
    onError(`复制失败: ${error.message}`);
    return false;
  }
};

/**
 * 检查剪贴板 API 是否可用
 * @returns {boolean} 是否支持剪贴板操作
 */
export const isClipboardSupported = () => {
  return !!(navigator.clipboard && navigator.clipboard.writeText) || 
         !!(document.execCommand);
};

/**
 * 获取剪贴板支持信息
 * @returns {Object} 支持信息
 */
export const getClipboardSupport = () => {
  return {
    modern: !!(navigator.clipboard && navigator.clipboard.writeText),
    legacy: !!document.execCommand,
    supported: isClipboardSupported()
  };
};
