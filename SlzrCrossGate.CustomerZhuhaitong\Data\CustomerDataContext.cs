using Microsoft.EntityFrameworkCore;
using SlzrCrossGate.CustomerZhuhaitong.Models;

namespace SlzrCrossGate.CustomerZhuhaitong.Data
{
    /// <summary>
    /// 客户数据上下文 - 不使用迁移，只映射现有表
    /// </summary>
    public class CustomerDataContext : DbContext
    {
        public CustomerDataContext(DbContextOptions<CustomerDataContext> options) : base(options)
        {
        }

        // FTP上传服务器配置表 - 映射到现有表
        public DbSet<FtpUploadServer> FtpUploadServers { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // FTP上传服务器配置表映射 - 映射到现有表
            modelBuilder.Entity<FtpUploadServer>(entity =>
            {
                entity.ToTable("zhtong_uploadftpserver");
                entity.HasKey(e => e.ID);
                entity.Property(e => e.ID).ValueGeneratedOnAdd();
            });
        }

        /// <summary>
        /// 配置数据库上下文
        /// </summary>
        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            // 客户项目不使用迁移，这里不需要特殊配置
            base.OnConfiguring(optionsBuilder);
        }
    }
}
