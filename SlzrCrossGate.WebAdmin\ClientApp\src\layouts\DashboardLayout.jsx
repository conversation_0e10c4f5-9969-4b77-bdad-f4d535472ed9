import React, { useState, useEffect } from 'react';
import { Outlet, Navigate, useLocation, useNavigate } from 'react-router-dom';
import { styled } from '@mui/material/styles';
import { Box } from '@mui/material';
import DashboardSidebar from './DashboardSidebar';
import DashboardNavbar from './DashboardNavbar';
import VersionManager from '../components/VersionManager';
import ExternalPageContainer from '../components/ExternalPageContainer';
import { useAuth } from '../contexts/AuthContext';

const DashboardLayoutRoot = styled('div')(({ theme, isSidebarCollapsed }) => ({
  display: 'flex',
  flex: '1 1 auto',
  maxWidth: '100%',
  paddingTop: 64,
  transition: theme.transitions.create(['padding-left'], {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.leavingScreen,
  }),
  [theme.breakpoints.up('lg')]: {
    paddingLeft: isSidebarCollapsed ? 80 : 280
  }
}));

const DashboardLayout = ()  => {
    const { isAuthenticated, needTwoFactor } = useAuth();
    const location = useLocation();

    // 如果用户未认证，重定向到登录页面
    if (!isAuthenticated) {
        return <Navigate to="/login" />;
    }

    // 如果用户需要完成双因素验证，重定向到验证页面
    if (needTwoFactor) {
        return <Navigate to="/two-factor-verify" />;
    }


  const [isMobileNavOpen, setMobileNavOpen] = useState(false);
  const [isSidebarCollapsed, setSidebarCollapsed] = useState(() => {
    const savedState = localStorage.getItem('sidebarCollapsed');
    return savedState ? JSON.parse(savedState) : false;
  });

  const navigate = useNavigate();



  useEffect(() => {
    localStorage.setItem('sidebarCollapsed', JSON.stringify(isSidebarCollapsed));
  }, [isSidebarCollapsed]);







  // 版本检查现在由VersionManager组件处理

  const handleToggleSidebar = () => {
    setSidebarCollapsed(!isSidebarCollapsed);
  };



  const handleMobileNavOpen = () => {
    // 使用函数式更新确保状态正确
    setMobileNavOpen(true);
  };

  const handleMobileNavClose = () => {
    // 使用函数式更新确保状态正确
    setMobileNavOpen(false);
  };

  return (
    <Box sx={{ display: 'flex', minHeight: '100%' }}>
      <DashboardNavbar
        onMobileNavOpen={handleMobileNavOpen}
        isSidebarCollapsed={isSidebarCollapsed}
        onToggleSidebar={handleToggleSidebar}
      />
      <DashboardSidebar
        onMobileClose={handleMobileNavClose}
        openMobile={isMobileNavOpen}
        isCollapsed={isSidebarCollapsed}
      />
      <DashboardLayoutRoot isSidebarCollapsed={isSidebarCollapsed}>
        <Box
          sx={{
            display: 'flex',
            flex: '1 1 auto',
            flexDirection: 'column',
            width: '100%',
            p: 3,
            transition: (theme) => theme.transitions.create(['padding'], {
              easing: theme.transitions.easing.sharp,
              duration: theme.transitions.duration.leavingScreen,
            }),
          }}
        >
          {/* 显示页面内容 */}
          <Outlet />
        </Box>
      </DashboardLayoutRoot>

      {/* 版本管理器 - 只在已登录的仪表板页面启动 */}
      <VersionManager />
    </Box>
  );
};

export default DashboardLayout;
