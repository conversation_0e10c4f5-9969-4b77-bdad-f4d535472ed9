using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using SlzrCrossGate.Core.Database;
using SlzrCrossGate.Core.Models;
using SlzrCrossGate.Core.Service.FileStorage;
using SlzrCrossGate.WebAdmin.DTOs;

namespace SlzrCrossGate.WebAdmin.Controllers
{
    /// <summary>
    /// 终端文件上传管理控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class TerminalFileUploadsController : ControllerBase
    {
        private readonly TcpDbContext _context;
        private readonly FileService _fileService;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly ILogger<TerminalFileUploadsController> _logger;

        public TerminalFileUploadsController(
            TcpDbContext context,
            FileService fileService,
            UserManager<ApplicationUser> userManager,
            ILogger<TerminalFileUploadsController> logger)
        {
            _context = context;
            _fileService = fileService;
            _userManager = userManager;
            _logger = logger;
        }

        /// <summary>
        /// 获取终端文件上传列表
        /// </summary>
        [HttpPost("list")]
        public async Task<ActionResult<PaginatedResult<TerminalFileUploadDto>>> GetTerminalFileUploads(
            [FromBody] TerminalFileUploadQueryDto query)
        {
            try
            {
                // 获取当前用户
                var currentUser = await _userManager.GetUserAsync(User);
                if (currentUser == null)
                {
                    return Unauthorized();
                }

                // 检查是否是系统管理员
                var isSystemAdmin = await _userManager.IsInRoleAsync(currentUser, "SystemAdmin");

                // 构建查询，联接商户表获取商户名称
                var queryable = from upload in _context.TerminalFileUploads
                               join merchant in _context.Merchants on upload.MerchantID equals merchant.MerchantID into merchantJoin
                               from merchant in merchantJoin.DefaultIfEmpty()
                               select new { Upload = upload, MerchantName = merchant.Name };

                // 如果不是系统管理员，只能查看自己商户的数据
                if (!isSystemAdmin && !string.IsNullOrEmpty(currentUser.MerchantID))
                {
                    queryable = queryable.Where(x => x.Upload.MerchantID == currentUser.MerchantID);
                }

                // 应用筛选条件
                if (!string.IsNullOrEmpty(query.MerchantID))
                {
                    queryable = queryable.Where(x => x.Upload.MerchantID.Contains(query.MerchantID));
                }

                if (!string.IsNullOrEmpty(query.TerminalID))
                {
                    queryable = queryable.Where(x => x.Upload.TerminalID.Contains(query.TerminalID));
                }

                if (!string.IsNullOrEmpty(query.TerminalType))
                {
                    queryable = queryable.Where(x => x.Upload.TerminalType.Contains(query.TerminalType));
                }

                if (!string.IsNullOrEmpty(query.FileType))
                {
                    queryable = queryable.Where(x => x.Upload.FileType.Contains(query.FileType));
                }

                if (!string.IsNullOrEmpty(query.FileName))
                {
                    queryable = queryable.Where(x => x.Upload.FileName.Contains(query.FileName));
                }

                if (query.Status.HasValue)
                {
                    queryable = queryable.Where(x => x.Upload.Status == query.Status.Value);
                }

                if (query.StartTimeFrom.HasValue)
                {
                    queryable = queryable.Where(x => x.Upload.StartTime >= query.StartTimeFrom.Value);
                }

                if (query.StartTimeTo.HasValue)
                {
                    queryable = queryable.Where(x => x.Upload.StartTime <= query.StartTimeTo.Value);
                }

                // 应用排序
                queryable = query.SortBy?.ToLower() switch
                {
                    "filename" => query.SortDirection?.ToLower() == "asc"
                        ? queryable.OrderBy(x => x.Upload.FileName)
                        : queryable.OrderByDescending(x => x.Upload.FileName),
                    "filesize" => query.SortDirection?.ToLower() == "asc"
                        ? queryable.OrderBy(x => x.Upload.FileSize)
                        : queryable.OrderByDescending(x => x.Upload.FileSize),
                    "status" => query.SortDirection?.ToLower() == "asc"
                        ? queryable.OrderBy(x => x.Upload.Status)
                        : queryable.OrderByDescending(x => x.Upload.Status),
                    "updatetime" => query.SortDirection?.ToLower() == "asc"
                        ? queryable.OrderBy(x => x.Upload.UpdateTime)
                        : queryable.OrderByDescending(x => x.Upload.UpdateTime),
                    _ => query.SortDirection?.ToLower() == "asc"
                        ? queryable.OrderBy(x => x.Upload.StartTime)
                        : queryable.OrderByDescending(x => x.Upload.StartTime)
                };

                // 获取总数
                var totalCount = await queryable.CountAsync();

                // 分页
                var items = await queryable
                    .Skip((query.Page - 1) * query.PageSize)
                    .Take(query.PageSize)
                    .ToListAsync();

                // 转换为DTO
                var dtos = items.Select(x => ConvertToDto(x.Upload, x.MerchantName)).ToList();

                return Ok(new PaginatedResult<TerminalFileUploadDto>
                {
                    Items = dtos,
                    TotalCount = totalCount,
                    Page = query.Page,
                    PageSize = query.PageSize
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取终端文件上传列表失败");
                return StatusCode(500, "获取终端文件上传列表失败");
            }
        }

        /// <summary>
        /// 获取终端文件上传统计信息
        /// </summary>
        [HttpGet("stats")]
        public async Task<ActionResult<TerminalFileUploadStatsDto>> GetStats()
        {
            try
            {
                // 获取当前用户
                var currentUser = await _userManager.GetUserAsync(User);
                if (currentUser == null)
                {
                    return Unauthorized();
                }

                // 检查是否是系统管理员
                var isSystemAdmin = await _userManager.IsInRoleAsync(currentUser, "SystemAdmin");

                // 构建查询
                var queryable = _context.TerminalFileUploads.AsQueryable();

                // 如果不是系统管理员，只能查看自己商户的数据
                if (!isSystemAdmin && !string.IsNullOrEmpty(currentUser.MerchantID))
                {
                    queryable = queryable.Where(x => x.MerchantID == currentUser.MerchantID);
                }

                var now = DateTime.Now;
                var today = now.Date;
                var weekStart = today.AddDays(-(int)today.DayOfWeek);
                var monthStart = new DateTime(now.Year, now.Month, 1);

                var stats = new TerminalFileUploadStatsDto
                {
                    TotalUploads = await queryable.CountAsync(),
                    UploadingCount = await queryable.CountAsync(x => x.Status == TerminalUploadStatus.Uploading),
                    CompletedCount = await queryable.CountAsync(x => x.Status == TerminalUploadStatus.Completed),
                    FailedCount = await queryable.CountAsync(x => x.Status == TerminalUploadStatus.Failed),
                    CancelledCount = await queryable.CountAsync(x => x.Status == TerminalUploadStatus.Cancelled),
                    TotalFileSize = await queryable.SumAsync(x => x.FileSize),
                    TodayUploads = await queryable.CountAsync(x => x.StartTime >= today),
                    WeekUploads = await queryable.CountAsync(x => x.StartTime >= weekStart),
                    MonthUploads = await queryable.CountAsync(x => x.StartTime >= monthStart)
                };

                stats.TotalFileSizeFormatted = FormatFileSize(stats.TotalFileSize);

                // 文件类型统计
                stats.FileTypeStats = await queryable
                    .GroupBy(x => x.FileType)
                    .Select(g => new FileTypeStatsDto
                    {
                        FileType = g.Key,
                        Count = g.Count(),
                        TotalSize = g.Sum(x => x.FileSize)
                    })
                    .ToListAsync();

                foreach (var stat in stats.FileTypeStats)
                {
                    stat.TotalSizeFormatted = FormatFileSize(stat.TotalSize);
                }

                // 终端类型统计
                stats.TerminalTypeStats = await queryable
                    .GroupBy(x => x.TerminalType)
                    .Select(g => new TerminalFileUploadTypeStatsDto
                    {
                        TerminalType = g.Key,
                        Count = g.Count(),
                        TotalSize = g.Sum(x => x.FileSize)
                    })
                    .ToListAsync();

                foreach (var stat in stats.TerminalTypeStats)
                {
                    stat.TotalSizeFormatted = FormatFileSize(stat.TotalSize);
                }

                return Ok(stats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取终端文件上传统计信息失败");
                return StatusCode(500, "获取统计信息失败");
            }
        }

        /// <summary>
        /// 获取文件类型列表
        /// </summary>
        [HttpGet("file-types")]
        public async Task<ActionResult<List<string>>> GetFileTypes()
        {
            try
            {
                var fileTypes = await _context.TerminalFileUploads
                    .Select(x => x.FileType)
                    .Distinct()
                    .OrderBy(x => x)
                    .ToListAsync();

                return Ok(fileTypes);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取文件类型列表失败");
                return StatusCode(500, "获取文件类型列表失败");
            }
        }

        /// <summary>
        /// 获取终端类型列表
        /// </summary>
        [HttpGet("terminal-types")]
        public async Task<ActionResult<List<string>>> GetTerminalTypes()
        {
            try
            {
                var terminalTypes = await _context.TerminalFileUploads
                    .Select(x => x.TerminalType)
                    .Distinct()
                    .OrderBy(x => x)
                    .ToListAsync();

                return Ok(terminalTypes);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取终端类型列表失败");
                return StatusCode(500, "获取终端类型列表失败");
            }
        }

        /// <summary>
        /// 获取商户列表（用于筛选下拉框）
        /// </summary>
        [HttpGet("merchants")]
        public async Task<ActionResult<List<object>>> GetMerchants()
        {
            try
            {
                // 获取当前用户
                var currentUser = await _userManager.GetUserAsync(User);
                if (currentUser == null)
                {
                    return Unauthorized();
                }

                // 检查是否是系统管理员
                var isSystemAdmin = await _userManager.IsInRoleAsync(currentUser, "SystemAdmin");

                // 构建查询
                var query = _context.Merchants.AsQueryable();

                // 如果不是系统管理员，只能查看自己商户
                if (!isSystemAdmin && !string.IsNullOrEmpty(currentUser.MerchantID))
                {
                    query = query.Where(m => m.MerchantID == currentUser.MerchantID);
                }

                var merchants = await query
                    .Where(m => !m.IsDelete)
                    .Select(m => new {
                        merchantID = m.MerchantID,
                        name = m.Name ?? m.MerchantID
                    })
                    .OrderBy(m => m.merchantID)
                    .ToListAsync();

                return Ok(merchants);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取商户列表失败");
                return StatusCode(500, "获取商户列表失败");
            }
        }

        /// <summary>
        /// 下载文件
        /// </summary>
        [HttpGet("download/{uploadFileId}")]
        public async Task<IActionResult> DownloadFile(string uploadFileId)
        {
            try
            {
                // 获取当前用户
                var currentUser = await _userManager.GetUserAsync(User);
                if (currentUser == null)
                {
                    return Unauthorized();
                }

                // 检查是否是系统管理员
                var isSystemAdmin = await _userManager.IsInRoleAsync(currentUser, "SystemAdmin");

                var upload = await _context.TerminalFileUploads
                    .Include(x => x.FinalUploadFile)
                    .FirstOrDefaultAsync(x => x.ID == uploadFileId);

                if (upload == null)
                {
                    return NotFound("上传记录不存在");
                }

                // 如果不是系统管理员，只能下载自己商户的文件
                if (!isSystemAdmin && upload.MerchantID != currentUser.MerchantID)
                {
                    return Forbid("无权限下载其他商户的文件");
                }

                if (upload.Status != TerminalUploadStatus.Completed || upload.FinalUploadFile == null)
                {
                    return BadRequest("文件未完成上传或不可下载");
                }

                var fileBytes = await _fileService.GetFileContentAsync(upload.FinalUploadFile.FilePath);
                if (fileBytes == null)
                {
                    return NotFound("文件内容不存在");
                }

                var contentType = GetContentType(upload.FileType);
                var fileName = $"{upload.TerminalID}_{upload.FileName}";

                return File(fileBytes, contentType, fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "下载文件失败: {UploadFileId}", uploadFileId);
                return StatusCode(500, "下载文件失败");
            }
        }

        /// <summary>
        /// 删除上传记录
        /// </summary>
        [HttpDelete("{uploadFileId}")]
        public async Task<IActionResult> DeleteUpload(string uploadFileId)
        {
            try
            {
                // 获取当前用户
                var currentUser = await _userManager.GetUserAsync(User);
                if (currentUser == null)
                {
                    return Unauthorized();
                }

                // 检查是否是系统管理员
                var isSystemAdmin = await _userManager.IsInRoleAsync(currentUser, "SystemAdmin");

                var upload = await _context.TerminalFileUploads
                    .Include(x => x.FinalUploadFile)
                    .FirstOrDefaultAsync(x => x.ID == uploadFileId);

                if (upload == null)
                {
                    return NotFound("上传记录不存在");
                }

                // 如果不是系统管理员，只能删除自己商户的数据
                if (!isSystemAdmin && upload.MerchantID != currentUser.MerchantID)
                {
                    return Forbid("无权限删除其他商户的上传记录");
                }

                // 删除关联的文件
                if (upload.FinalUploadFile != null)
                {
                    try
                    {
                        // 根据文件路径类型删除文件
                        var filePath = upload.FinalUploadFile.FilePath;
                        if (filePath.StartsWith("minio://"))
                        {
                            // MinIO文件，暂时跳过删除（需要MinIO客户端）
                            _logger.LogWarning("MinIO文件删除暂未实现: {FilePath}", filePath);
                        }
                        else if (System.IO.File.Exists(filePath))
                        {
                            // 本地文件
                            System.IO.File.Delete(filePath);
                        }

                        _context.UploadFiles.Remove(upload.FinalUploadFile);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "删除文件失败: {FilePath}", upload.FinalUploadFile.FilePath);
                    }
                }

                // 删除临时文件
                if (!string.IsNullOrEmpty(upload.TempFilePath) && System.IO.File.Exists(upload.TempFilePath))
                {
                    try
                    {
                        System.IO.File.Delete(upload.TempFilePath);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "删除临时文件失败: {TempFilePath}", upload.TempFilePath);
                    }
                }

                _context.TerminalFileUploads.Remove(upload);
                await _context.SaveChangesAsync();

                return Ok("删除成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除上传记录失败: {UploadFileId}", uploadFileId);
                return StatusCode(500, "删除失败");
            }
        }

        /// <summary>
        /// 转换为DTO
        /// </summary>
        private static TerminalFileUploadDto ConvertToDto(TerminalFileUpload upload, string? merchantName = null)
        {
            var dto = new TerminalFileUploadDto
            {
                ID = upload.ID,
                MerchantID = upload.MerchantID,
                MerchantName = merchantName ?? "",
                TerminalID = upload.TerminalID,
                TerminalType = upload.TerminalType,
                FileType = upload.FileType,
                FileName = upload.FileName,
                FileSize = upload.FileSize,
                FileSizeFormatted = FormatFileSize(upload.FileSize),
                FileCRC = upload.FileCRC,
                ReceivedLength = upload.ReceivedLength,
                ClientChunkSize = upload.ClientChunkSize,
                ServerChunkSize = upload.ServerChunkSize,
                Status = upload.Status,
                StatusDescription = GetStatusDescription(upload.Status),
                StartTime = upload.StartTime,
                UpdateTime = upload.UpdateTime,
                LastActivityTime = upload.LastActivityTime,
                UploadedBy = upload.UploadedBy,
                ErrorMessage = upload.ErrorMessage,
                FinalUploadFileID = upload.FinalUploadFileID,
                CanDownload = upload.Status == TerminalUploadStatus.Completed && !string.IsNullOrEmpty(upload.FinalUploadFileID)
            };

            // 计算进度
            if (upload.FileSize > 0)
            {
                dto.ProgressPercentage = Math.Round((decimal)upload.ReceivedLength / upload.FileSize * 100, 2);
            }

            // 计算上传耗时和速度
            if (upload.Status == TerminalUploadStatus.Completed)
            {
                var duration = upload.UpdateTime - upload.StartTime;
                dto.UploadDurationSeconds = duration.TotalSeconds;

                if (duration.TotalSeconds > 0)
                {
                    dto.UploadSpeedKBps = Math.Round(upload.FileSize / 1024.0 / duration.TotalSeconds, 2);
                }
            }

            return dto;
        }

        /// <summary>
        /// 格式化文件大小
        /// </summary>
        private static string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB", "TB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }

        /// <summary>
        /// 获取状态描述
        /// </summary>
        private static string GetStatusDescription(TerminalUploadStatus status)
        {
            return status switch
            {
                TerminalUploadStatus.Uploading => "上传中",
                TerminalUploadStatus.Completed => "已完成",
                TerminalUploadStatus.Failed => "失败",
                TerminalUploadStatus.Cancelled => "已取消",
                _ => "未知"
            };
        }

        /// <summary>
        /// 获取内容类型
        /// </summary>
        private static string GetContentType(string fileType)
        {
            return fileType.ToUpper() switch
            {
                "LOG" => "text/plain",
                "TXT" => "text/plain",
                "JSON" => "application/json",
                "XML" => "application/xml",
                "CSV" => "text/csv",
                _ => "application/octet-stream"
            };
        }
    }
}
