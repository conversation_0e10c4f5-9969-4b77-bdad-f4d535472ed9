import React, { createContext, useContext, useState, useEffect } from 'react';
import { merchantAPI } from '../services/api';
import { useAuth } from './AuthContext';

const MerchantContext = createContext();

export const useMerchants = () => {
  const context = useContext(MerchantContext);
  if (!context) {
    throw new Error('useMerchants must be used within a MerchantProvider');
  }
  return context;
};

export const MerchantProvider = ({ children }) => {
  const { isAuthenticated } = useAuth();
  const [merchants, setMerchants] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [lastFetchTime, setLastFetchTime] = useState(null);

  // 缓存时间：5分钟
  const CACHE_DURATION = 5 * 60 * 1000;

  // 清除缓存
  const clearCache = () => {
    setMerchants([]);
    setLastFetchTime(null);
    setError(null);
  };

  const loadMerchants = async (forceRefresh = false) => {
    // 检查用户是否已登录
    if (!isAuthenticated) {
      console.log('用户未登录，跳过商户数据加载');
      return [];
    }

    // 检查缓存是否有效
    if (!forceRefresh && lastFetchTime && merchants.length > 0) {
      const timeSinceLastFetch = Date.now() - lastFetchTime;
      if (timeSinceLastFetch < CACHE_DURATION) {
        console.log('使用缓存的商户数据');
        return merchants;
      }
    }

    try {
      setLoading(true);
      setError(null);
      console.log('从服务器加载商户数据');

      // 获取足够多的商户数据，避免分页限制
      const response = await merchantAPI.getMerchants({ pageSize: 200 });
      const merchantData = response.items || [];

      setMerchants(merchantData);
      setLastFetchTime(Date.now());

      return merchantData;
    } catch (err) {
      console.error('加载商户数据失败:', err);
      setError(err);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // 当用户登录状态改变时处理商户数据
  useEffect(() => {
    if (isAuthenticated) {
      // 用户已登录，加载商户数据
      loadMerchants();
    } else {
      // 用户未登录，清除商户数据
      clearCache();
    }
  }, [isAuthenticated]);

  // 刷新商户数据
  const refreshMerchants = () => {
    return loadMerchants(true);
  };

  // 根据ID获取商户
  const getMerchantById = (merchantId) => {
    return merchants.find(m => m.merchantID === merchantId);
  };

  // 根据名称搜索商户
  const searchMerchants = (searchTerm) => {
    if (!searchTerm) return merchants;
    
    const term = searchTerm.toLowerCase();
    return merchants.filter(m => 
      m.merchantID?.toLowerCase().includes(term) ||
      m.name?.toLowerCase().includes(term) ||
      m.companyName?.toLowerCase().includes(term)
    );
  };

  const value = {
    merchants,
    loading,
    error,
    loadMerchants,
    refreshMerchants,
    clearCache,
    getMerchantById,
    searchMerchants,
    lastFetchTime
  };

  return (
    <MerchantContext.Provider value={value}>
      {children}
    </MerchantContext.Provider>
  );
};

export default MerchantProvider;
