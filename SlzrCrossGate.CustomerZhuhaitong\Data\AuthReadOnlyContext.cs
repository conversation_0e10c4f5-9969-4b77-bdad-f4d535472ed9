using Microsoft.EntityFrameworkCore;
using SlzrCrossGate.CustomerZhuhaitong.Models;

namespace SlzrCrossGate.CustomerZhuhaitong.Data
{
    /// <summary>
    /// 认证只读上下文 - 不参与迁移
    /// </summary>
    public class AuthReadOnlyContext : DbContext
    {
        public AuthReadOnlyContext(DbContextOptions<AuthReadOnlyContext> options) : base(options) { }

        // 只读实体，不生成迁移
        public DbSet<UserReadOnly> Users { get; set; }
        public DbSet<MerchantReadOnly> Merchants { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            // 映射到现有表，但不参与迁移
            modelBuilder.Entity<UserReadOnly>(entity =>
            {
                entity.ToTable("AspNetUsers");
                entity.HasNoKey(); // 标记为无键实体，不会生成迁移
            });

            modelBuilder.Entity<MerchantReadOnly>(entity =>
            {
                entity.ToTable("Merchants");
                entity.HasNoKey();
            });
        }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            // 配置为只读模式
            optionsBuilder.UseQueryTrackingBehavior(QueryTrackingBehavior.NoTracking);
        }
    }
}
