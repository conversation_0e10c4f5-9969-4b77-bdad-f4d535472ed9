using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using SlzrCrossGate.Core.Models;
using SlzrCrossGate.WebAdmin.Services;
using System.Security.Claims;
using System.Reflection;

namespace SlzrCrossGate.WebAdmin.Controllers
{
    /// <summary>
    /// 会话管理控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class SessionController : ControllerBase
    {
        private readonly SessionActivityService _sessionActivityService;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly AuditLogService _auditLogService;
        private readonly ILogger<SessionController> _logger;

        public SessionController(
            SessionActivityService sessionActivityService,
            UserManager<ApplicationUser> userManager,
            AuditLogService auditLogService,
            ILogger<SessionController> logger)
        {
            _sessionActivityService = sessionActivityService;
            _userManager = userManager;
            _auditLogService = auditLogService;
            _logger = logger;
        }

        /// <summary>
        /// 检查当前用户会话状态（只读，不更新活动时间）
        /// </summary>
        [HttpGet("status")]
        public async Task<IActionResult> GetSessionStatus()
        {
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized(new { message = "用户未认证" });
                }

                var user = await _userManager.FindByIdAsync(userId);
                if (user == null)
                {
                    return Unauthorized(new { message = "用户不存在" });
                }

                // 注意：这里不更新用户活动状态，只检查当前状态
                // 活动状态的更新由AuditLogMiddleware在用户实际操作时自动处理

                // 获取会话状态
                var sessionStatus = _sessionActivityService.GetSessionStatus(userId);

                // 获取版本信息
                var assembly = Assembly.GetExecutingAssembly();
                var version = assembly.GetName().Version?.ToString() ?? "1.0.0.0";
                var buildTime = GetBuildTime(assembly);

                return Ok(new
                {
                    isActive = sessionStatus.IsActive,
                    isExpired = sessionStatus.IsExpired,
                    isNearExpiry = sessionStatus.IsNearExpiry,
                    timeRemaining = sessionStatus.TimeRemaining.TotalMinutes,
                    lastActivityTime = sessionStatus.LastActivityTime,
                    serverTime = DateTime.Now,
                    userId = userId,
                    userName = user.UserName,
                    // 版本信息
                    version = version,
                    buildTime = buildTime,
                    buildTimeString = buildTime.ToString("yyyy-MM-dd HH:mm:ss")
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取会话状态失败");
                return StatusCode(500, new { message = "获取会话状态失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 刷新用户会话（重置活动时间）
        /// </summary>
        [HttpPost("refresh")]
        public async Task<IActionResult> RefreshSession()
        {
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized(new { message = "用户未认证" });
                }

                var user = await _userManager.FindByIdAsync(userId);
                if (user == null)
                {
                    return Unauthorized(new { message = "用户不存在" });
                }

                // 刷新用户活动状态
                _sessionActivityService.UpdateUserActivity(userId, user.UserName);

                // 记录会话刷新日志
                await _auditLogService.LogOperationAsync(
                    userId: userId,
                    userName: user.UserName,
                    realName: user.RealName,
                    merchantId: user.MerchantID,
                    merchantName: null,
                    module: "会话管理",
                    operationType: "刷新",
                    operationTarget: "用户会话",
                    isSuccess: true
                );

                var sessionStatus = _sessionActivityService.GetSessionStatus(userId);

                _logger.LogInformation("用户会话已刷新: {UserId}", userId);

                return Ok(new
                {
                    message = "会话已刷新",
                    timeRemaining = sessionStatus.TimeRemaining.TotalMinutes,
                    lastActivityTime = sessionStatus.LastActivityTime,
                    serverTime = DateTime.Now
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "刷新会话失败");
                return StatusCode(500, new { message = "刷新会话失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 手动结束会话（登出）
        /// </summary>
        [HttpPost("logout")]
        public async Task<IActionResult> Logout()
        {
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Ok(new { message = "用户未认证，无需登出" });
                }

                var user = await _userManager.FindByIdAsync(userId);

                // 移除用户会话
                _sessionActivityService.RemoveUserSession(userId);

                // 记录登出日志
                if (user != null)
                {
                    await _auditLogService.LogLoginAsync(
                        userId: userId,
                        userName: user.UserName,
                        realName: user.RealName,
                        merchantId: user.MerchantID,
                        merchantName: null,
                        loginType: "手动登出",
                        loginMethod: "Web",
                        isSuccess: true
                    );
                }

                _logger.LogInformation("用户已登出: {UserId}", userId);

                return Ok(new { message = "登出成功" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "登出失败");
                return StatusCode(500, new { message = "登出失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 获取会话配置信息
        /// </summary>
        [HttpGet("config")]
        public IActionResult GetSessionConfig()
        {
            try
            {
                var configuration = HttpContext.RequestServices.GetRequiredService<IConfiguration>();
                var timeoutMinutes = configuration.GetValue<int>("SessionTimeout:Minutes", 60);
                var warningMinutes = configuration.GetValue<int>("SessionTimeout:WarningMinutes", 5);

                return Ok(new
                {
                    timeoutMinutes = timeoutMinutes,
                    warningMinutes = warningMinutes,
                    checkIntervalSeconds = 60 // 建议的检查间隔
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取会话配置失败");
                return StatusCode(500, new { message = "获取会话配置失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 心跳接口（用于保持会话活跃）
        /// </summary>
        [HttpPost("heartbeat")]
        public async Task<IActionResult> Heartbeat()
        {
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized(new { message = "用户未认证" });
                }

                var user = await _userManager.FindByIdAsync(userId);
                if (user != null)
                {
                    _sessionActivityService.UpdateUserActivity(userId, user.UserName);
                }

                return Ok(new
                {
                    timestamp = DateTime.Now,
                    status = "alive"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "心跳检查失败");
                return StatusCode(500, new { message = "心跳检查失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 管理员查看活跃用户（仅系统管理员）
        /// </summary>
        [HttpGet("active-users")]
        [Authorize(Roles = "SystemAdmin")]
        public IActionResult GetActiveUsers()
        {
            try
            {
                var activeUserCount = _sessionActivityService.GetActiveUserCount();

                return Ok(new
                {
                    activeUserCount = activeUserCount,
                    timestamp = DateTime.Now
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取活跃用户信息失败");
                return StatusCode(500, new { message = "获取活跃用户信息失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 获取程序集构建时间
        /// </summary>
        private static DateTime GetBuildTime(Assembly assembly)
        {
            try
            {
                // 尝试从程序集的自定义属性获取构建时间
                var buildTimeAttribute = assembly.GetCustomAttribute<System.Reflection.AssemblyMetadataAttribute>();
                if (buildTimeAttribute?.Key == "BuildTime" && DateTime.TryParse(buildTimeAttribute.Value, out var buildTime))
                {
                    return buildTime;
                }

                // 如果没有自定义属性，使用程序集文件的创建时间
                var location = assembly.Location;
                if (!string.IsNullOrEmpty(location) && System.IO.File.Exists(location))
                {
                    return System.IO.File.GetCreationTime(location);
                }

                // 如果都获取不到，返回当前时间
                return DateTime.Now;
            }
            catch
            {
                // 如果出现任何异常，返回当前时间
                return DateTime.Now;
            }
        }
    }
}
