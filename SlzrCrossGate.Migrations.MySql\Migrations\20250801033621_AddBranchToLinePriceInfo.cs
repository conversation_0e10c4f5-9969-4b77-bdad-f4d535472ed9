﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace SlzrCrossGate.Migrations.MySql.Migrations
{
    /// <inheritdoc />
    public partial class AddBranchToLinePriceInfo : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<DateTime>(
                name: "CreateTime",
                table: "VehicleInfos",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 8, 1, 11, 36, 19, 565, DateTimeKind.Local).AddTicks(5175),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 7, 31, 13, 39, 18, 768, DateTimeKind.Local).AddTicks(6062));

            migrationBuilder.AlterColumn<DateTime>(
                name: "OperationTime",
                table: "PasswordChangeLogs",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 8, 1, 11, 36, 19, 564, DateTimeKind.Local).AddTicks(9369),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 7, 31, 13, 39, 18, 767, DateTimeKind.Local).AddTicks(9005));

            migrationBuilder.AlterColumn<DateTime>(
                name: "OperationTime",
                table: "OperationLogs",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 8, 1, 11, 36, 19, 565, DateTimeKind.Local).AddTicks(581),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 7, 31, 13, 39, 18, 768, DateTimeKind.Local).AddTicks(433));

            migrationBuilder.AlterColumn<DateTime>(
                name: "UpdatedAt",
                table: "MenuItems",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 8, 1, 11, 36, 19, 565, DateTimeKind.Local).AddTicks(2662),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 7, 31, 13, 39, 18, 768, DateTimeKind.Local).AddTicks(2825));

            migrationBuilder.AlterColumn<DateTime>(
                name: "CreatedAt",
                table: "MenuItems",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 8, 1, 11, 36, 19, 565, DateTimeKind.Local).AddTicks(2294),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 7, 31, 13, 39, 18, 768, DateTimeKind.Local).AddTicks(2427));

            migrationBuilder.AlterColumn<DateTime>(
                name: "UpdatedAt",
                table: "MenuGroups",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 8, 1, 11, 36, 19, 565, DateTimeKind.Local).AddTicks(1469),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 7, 31, 13, 39, 18, 768, DateTimeKind.Local).AddTicks(1489));

            migrationBuilder.AlterColumn<DateTime>(
                name: "CreatedAt",
                table: "MenuGroups",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 8, 1, 11, 36, 19, 565, DateTimeKind.Local).AddTicks(1212),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 7, 31, 13, 39, 18, 768, DateTimeKind.Local).AddTicks(1222));

            migrationBuilder.AlterColumn<DateTime>(
                name: "OperationTime",
                table: "LoginLogs",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 8, 1, 11, 36, 19, 564, DateTimeKind.Local).AddTicks(8028),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 7, 31, 13, 39, 18, 767, DateTimeKind.Local).AddTicks(7206));

            migrationBuilder.AddColumn<string>(
                name: "Branch",
                table: "LinePriceInfos",
                type: "varchar(100)",
                maxLength: 100,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Branch",
                table: "LinePriceInfos");

            migrationBuilder.AlterColumn<DateTime>(
                name: "CreateTime",
                table: "VehicleInfos",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 31, 13, 39, 18, 768, DateTimeKind.Local).AddTicks(6062),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 8, 1, 11, 36, 19, 565, DateTimeKind.Local).AddTicks(5175));

            migrationBuilder.AlterColumn<DateTime>(
                name: "OperationTime",
                table: "PasswordChangeLogs",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 31, 13, 39, 18, 767, DateTimeKind.Local).AddTicks(9005),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 8, 1, 11, 36, 19, 564, DateTimeKind.Local).AddTicks(9369));

            migrationBuilder.AlterColumn<DateTime>(
                name: "OperationTime",
                table: "OperationLogs",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 31, 13, 39, 18, 768, DateTimeKind.Local).AddTicks(433),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 8, 1, 11, 36, 19, 565, DateTimeKind.Local).AddTicks(581));

            migrationBuilder.AlterColumn<DateTime>(
                name: "UpdatedAt",
                table: "MenuItems",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 31, 13, 39, 18, 768, DateTimeKind.Local).AddTicks(2825),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 8, 1, 11, 36, 19, 565, DateTimeKind.Local).AddTicks(2662));

            migrationBuilder.AlterColumn<DateTime>(
                name: "CreatedAt",
                table: "MenuItems",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 31, 13, 39, 18, 768, DateTimeKind.Local).AddTicks(2427),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 8, 1, 11, 36, 19, 565, DateTimeKind.Local).AddTicks(2294));

            migrationBuilder.AlterColumn<DateTime>(
                name: "UpdatedAt",
                table: "MenuGroups",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 31, 13, 39, 18, 768, DateTimeKind.Local).AddTicks(1489),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 8, 1, 11, 36, 19, 565, DateTimeKind.Local).AddTicks(1469));

            migrationBuilder.AlterColumn<DateTime>(
                name: "CreatedAt",
                table: "MenuGroups",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 31, 13, 39, 18, 768, DateTimeKind.Local).AddTicks(1222),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 8, 1, 11, 36, 19, 565, DateTimeKind.Local).AddTicks(1212));

            migrationBuilder.AlterColumn<DateTime>(
                name: "OperationTime",
                table: "LoginLogs",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 31, 13, 39, 18, 767, DateTimeKind.Local).AddTicks(7206),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 8, 1, 11, 36, 19, 564, DateTimeKind.Local).AddTicks(8028));
        }
    }
}

