using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using Serilog;
using SlzrCrossGate.CustomerTemplate.Data;
using SlzrCrossGate.CustomerTemplate.Models;
using SlzrCrossGate.CustomerTemplate.Services;
using SlzrCrossGate.Core.Models;
using System.Text;

var builder = WebApplication.CreateBuilder(args);

// 配置Serilog
Log.Logger = new LoggerConfiguration()
    .ReadFrom.Configuration(builder.Configuration)
    .Enrich.WithProperty("Service", "SlzrCrossGate.CustomerTemplate")
    .Enrich.WithProperty("Customer", "template")
    .CreateLogger();

builder.Host.UseSerilog();

// 添加服务
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// 配置数据库连接
// 只读认证上下文 - 不参与迁移
builder.Services.AddDbContext<AuthReadOnlyContext>(options =>
{
    options.UseMySql(
        builder.Configuration.GetConnectionString("AuthConnection"),
        ServerVersion.AutoDetect(builder.Configuration.GetConnectionString("AuthConnection"))
    );
    // 禁用变更跟踪，提高性能
    options.UseQueryTrackingBehavior(QueryTrackingBehavior.NoTracking);
});

// 客户数据上下文 - 不使用迁移，只映射现有表
builder.Services.AddDbContext<CustomerDataContext>(options =>
    options.UseMySql(
        builder.Configuration.GetConnectionString("CustomerConnection"),
        ServerVersion.AutoDetect(builder.Configuration.GetConnectionString("CustomerConnection"))
    ));

// 注册服务
builder.Services.AddScoped<ReadOnlyAuthService>();
builder.Services.AddScoped<AuthService>();
builder.Services.AddScoped<CustomerDataService>();

// 设置客户表前缀环境变量（需要根据具体客户修改）
Environment.SetEnvironmentVariable("CUSTOMER_TABLE_PREFIX", "Customer_Template");

// 设置客户路径配置（需要根据具体客户修改）
Environment.SetEnvironmentVariable("CUSTOMER_PATH_TYPE", "customer");
Environment.SetEnvironmentVariable("CUSTOMER_BASE_PATH", "/customer");

// 配置JWT认证（与主项目保持一致）
builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddJwtBearer(options =>
    {
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidateAudience = true,
            ValidateLifetime = true,
            ValidateIssuerSigningKey = true,
            ValidIssuer = builder.Configuration["Jwt:Issuer"],
            ValidAudience = builder.Configuration["Jwt:Audience"],
            IssuerSigningKey = new SymmetricSecurityKey(
                Encoding.UTF8.GetBytes(builder.Configuration["Jwt:Key"] ?? ""))
        };
    });

// 配置CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowMainApp", policy =>
    {
        policy.WithOrigins(builder.Configuration["MainApp:BaseUrl"] ?? "http://localhost:5270")
              .AllowAnyHeader()
              .AllowAnyMethod()
              .AllowCredentials();
    });
});

var app = builder.Build();

// 配置请求管道
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseCors("AllowMainApp");

// 配置静态文件服务 - 支持基础路径
app.UseStaticFiles();

app.UseRouting();
app.UseAuthentication();
app.UseAuthorization();

// 配置基础路径支持
var basePath = Environment.GetEnvironmentVariable("ASPNETCORE_BASEPATH") ?? "/customer";
if (!string.IsNullOrEmpty(basePath) && basePath != "/")
{
    app.UsePathBase(basePath);
}

app.MapControllers();

// SPA回退路由 - 支持基础路径
app.MapFallbackToFile("index.html");

app.Run();
