#!/bin/bash

echo "=========================================="
echo "珠海通客户项目容器启动初始化..."
echo "=========================================="

# 日志目录
LOG_DIR="/app/logs"

echo "日志目录: $LOG_DIR"

# 确保日志目录存在
mkdir -p "$LOG_DIR"
echo "✅ 日志目录已创建"

# 设置正确的权限
echo "🔧 设置日志目录权限..."
chown -R app:app "$LOG_DIR" 2>/dev/null || echo "⚠️  无法设置所有者，继续执行..."
chmod -R 755 "$LOG_DIR" 2>/dev/null || echo "⚠️  无法设置目录权限，继续执行..."

echo "✅ 权限设置完成"

# 显示目录状态
echo "📋 目录状态:"
ls -la "$LOG_DIR"

echo "=========================================="
echo "珠海通客户项目配置初始化完成，启动应用..."
echo "=========================================="

# 启动原始命令
exec "$@"
