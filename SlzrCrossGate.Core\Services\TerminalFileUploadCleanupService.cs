using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace SlzrCrossGate.Core.Services
{
    /// <summary>
    /// 终端文件上传清理服务
    /// </summary>
    public class TerminalFileUploadCleanupService : BackgroundService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<TerminalFileUploadCleanupService> _logger;
        private readonly SemaphoreSlim _cleanupSemaphore = new(1, 1);

        public TerminalFileUploadCleanupService(
            IServiceProvider serviceProvider,
            ILogger<TerminalFileUploadCleanupService> logger)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("Terminal file upload cleanup service started");

            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    // 每小时执行一次清理
                    await Task.Delay(TimeSpan.FromHours(1), stoppingToken);

                    if (!await _cleanupSemaphore.WaitAsync(100, stoppingToken))
                    {
                        _logger.LogWarning("Terminal file upload cleanup is already running, skipping this execution");
                        continue;
                    }

                    try
                    {
                        await PerformCleanupAsync();
                    }
                    finally
                    {
                        _cleanupSemaphore.Release();
                    }
                }
                catch (OperationCanceledException)
                {
                    // 正常停止
                    break;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error in terminal file upload cleanup service");
                }
            }

            _logger.LogInformation("Terminal file upload cleanup service stopped");
        }

        /// <summary>
        /// 执行清理操作
        /// </summary>
        private async Task PerformCleanupAsync()
        {
            try
            {
                using var scope = _serviceProvider.CreateScope();
                var uploadService = scope.ServiceProvider.GetRequiredService<TerminalFileUploadService>();

                // 清理超过24小时没有活动的上传记录
                var maxAge = TimeSpan.FromHours(24);
                var cleanedCount = await uploadService.CleanupExpiredUploadsAsync(maxAge);

                if (cleanedCount > 0)
                {
                    _logger.LogInformation("Cleaned up {Count} expired terminal file upload records", cleanedCount);
                }
                else
                {
                    _logger.LogDebug("No expired terminal file upload records to clean up");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error performing terminal file upload cleanup");
            }
        }

        public override void Dispose()
        {
            _cleanupSemaphore?.Dispose();
            base.Dispose();
        }
    }
}
