using Microsoft.EntityFrameworkCore;
using SlzrCrossGate.CustomerTemplate.Models;

namespace SlzrCrossGate.CustomerTemplate.Data
{
    /// <summary>
    /// 客户数据上下文 - 不使用迁移，只映射现有表
    /// </summary>
    public class CustomerDataContext : DbContext
    {
        public CustomerDataContext(DbContextOptions<CustomerDataContext> options) : base(options)
        {
        }

        // 客户专用表 - 映射到手动创建的表
        public DbSet<CustomerData> CustomerData { get; set; }
        public DbSet<CustomerSettings> Settings { get; set; }
        public DbSet<CustomerWorkflow> Workflows { get; set; }
        public DbSet<CustomerReport> Reports { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // 配置表映射 - 不生成迁移，只映射现有表
            var customerPrefix = GetCustomerPrefix();

            modelBuilder.Entity<CustomerData>(entity =>
            {
                entity.ToTable($"{customerPrefix}_Data");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
                entity.Property(e => e.MerchantId).IsRequired().HasMaxLength(8);
                entity.Property(e => e.DataType).IsRequired().HasMaxLength(50);
                entity.Property(e => e.DataValue).HasColumnType("LONGTEXT");
                entity.Property(e => e.CreatedBy).HasMaxLength(50);
                entity.Property(e => e.UpdatedBy).HasMaxLength(50);
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
                entity.Property(e => e.UpdatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP");
            });

            modelBuilder.Entity<CustomerSettings>(entity =>
            {
                entity.ToTable($"{customerPrefix}_Settings");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
                entity.Property(e => e.MerchantId).IsRequired().HasMaxLength(8);
                entity.Property(e => e.SettingKey).IsRequired().HasMaxLength(100);
                entity.Property(e => e.SettingValue).HasColumnType("TEXT");
                entity.Property(e => e.Description).HasMaxLength(500);
                entity.Property(e => e.DataType).HasMaxLength(20).HasDefaultValue("string");
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
                entity.Property(e => e.UpdatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP");
            });

            modelBuilder.Entity<CustomerWorkflow>(entity =>
            {
                entity.ToTable($"{customerPrefix}_Workflows");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
                entity.Property(e => e.MerchantId).IsRequired().HasMaxLength(8);
                entity.Property(e => e.WorkflowName).IsRequired().HasMaxLength(100);
                entity.Property(e => e.WorkflowConfig).HasColumnType("JSON");
                entity.Property(e => e.Status).HasMaxLength(20).HasDefaultValue("Draft");
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
                entity.Property(e => e.UpdatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP");
            });

            modelBuilder.Entity<CustomerReport>(entity =>
            {
                entity.ToTable($"{customerPrefix}_Reports");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
                entity.Property(e => e.MerchantId).IsRequired().HasMaxLength(8);
                entity.Property(e => e.ReportName).IsRequired().HasMaxLength(100);
                entity.Property(e => e.ReportConfig).HasColumnType("JSON");
                entity.Property(e => e.ReportData).HasColumnType("LONGTEXT");
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
                entity.Property(e => e.UpdatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP");
            });
        }

        /// <summary>
        /// 获取客户表前缀（可以从配置或环境变量读取）
        /// </summary>
        private string GetCustomerPrefix()
        {
            // 可以从配置文件或环境变量获取
            return Environment.GetEnvironmentVariable("CUSTOMER_TABLE_PREFIX") ?? "Customer_A";
        }

        /// <summary>
        /// 禁用EF迁移功能
        /// </summary>
        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            // 禁用迁移历史表
            optionsBuilder.ReplaceService<Microsoft.EntityFrameworkCore.Migrations.IMigrationsAssembly, 
                                        NonMigratingMigrationsAssembly>();
        }
    }

    /// <summary>
    /// 禁用迁移的程序集
    /// </summary>
    public class NonMigratingMigrationsAssembly : Microsoft.EntityFrameworkCore.Migrations.IMigrationsAssembly
    {
        public IReadOnlyDictionary<string, Microsoft.EntityFrameworkCore.Migrations.TypeInfo> Migrations => 
            new Dictionary<string, Microsoft.EntityFrameworkCore.Migrations.TypeInfo>();

        public Microsoft.EntityFrameworkCore.Migrations.ModelSnapshot? ModelSnapshot => null;

        public System.Reflection.Assembly Assembly => typeof(CustomerDataContext).Assembly;

        public string? FindMigrationId(string nameOrId) => null;

        public Microsoft.EntityFrameworkCore.Migrations.Migration CreateMigration(Microsoft.EntityFrameworkCore.Migrations.TypeInfo typeInfo, string activeProvider) => 
            throw new NotSupportedException("客户项目不支持EF迁移");
    }
}

namespace SlzrCrossGate.CustomerTemplate.Models
{
    /// <summary>
    /// 客户数据模型 - 对应手动创建的表
    /// </summary>
    public class CustomerData
    {
        public int Id { get; set; }
        public string MerchantId { get; set; } = string.Empty;
        public string DataType { get; set; } = string.Empty;
        public string? DataValue { get; set; }
        public string? CreatedBy { get; set; }
        public string? UpdatedBy { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }

    /// <summary>
    /// 客户配置模型
    /// </summary>
    public class CustomerSettings
    {
        public int Id { get; set; }
        public string MerchantId { get; set; } = string.Empty;
        public string SettingKey { get; set; } = string.Empty;
        public string? SettingValue { get; set; }
        public string? Description { get; set; }
        public string DataType { get; set; } = "string";
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }

    /// <summary>
    /// 客户工作流模型
    /// </summary>
    public class CustomerWorkflow
    {
        public int Id { get; set; }
        public string MerchantId { get; set; } = string.Empty;
        public string WorkflowName { get; set; } = string.Empty;
        public string? WorkflowConfig { get; set; }
        public string Status { get; set; } = "Draft";
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }

    /// <summary>
    /// 客户报表模型
    /// </summary>
    public class CustomerReport
    {
        public int Id { get; set; }
        public string MerchantId { get; set; } = string.Empty;
        public string ReportName { get; set; } = string.Empty;
        public string? ReportConfig { get; set; }
        public string? ReportData { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }
}
