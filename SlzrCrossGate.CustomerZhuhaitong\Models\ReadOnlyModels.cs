using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace SlzrCrossGate.CustomerZhuhaitong.Models
{
    /// <summary>
    /// 用户只读模型 - 仅用于认证验证，不参与EF迁移
    /// </summary>
    [Table("AspNetUsers")]
    public class UserReadOnly
    {
        public string Id { get; set; } = string.Empty;
        public string UserName { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string? MerchantID { get; set; } = string.Empty;
        public bool EmailConfirmed { get; set; }
        public DateTime CreateTime { get; set; }
        public bool IsDeleted { get; set; }
    }

    /// <summary>
    /// 商户只读模型 - 仅用于获取商户信息，不参与EF迁移
    /// </summary>
    [Table("Merchants")]
    public class MerchantReadOnly
    {
        public string MerchantID { get; set; } = string.Empty;
        public string MerchantName { get; set; } = string.Empty;
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
    }



    /// <summary>
    /// FTP上传服务器配置模型
    /// </summary>
    [Table("zhtong_uploadftpserver")]
    public class FtpUploadServer
    {
        [Key]
        public int ID { get; set; }

        /// <summary>
        /// 上传次序号
        /// </summary>
        public int? UpOrder { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        [Column("isEnable")]
        public int? IsEnable { get; set; }

        /// <summary>
        /// 传输协议
        /// </summary>
        [Column("protocol")]
        public string? Protocol { get; set; }

        /// <summary>
        /// 远程地址
        /// </summary>
        [Column("ftpIp")]
        public string? FtpIp { get; set; }

        /// <summary>
        /// 远程端口
        /// </summary>
        [Column("ftpPort")]
        public int? FtpPort { get; set; }

        /// <summary>
        /// 远程用户
        /// </summary>
        [Column("ftpUser")]
        public string? FtpUser { get; set; }

        /// <summary>
        /// 远程密码
        /// </summary>
        [Column("ftpPassword")]
        public string? FtpPassword { get; set; }

        /// <summary>
        /// 312文件源路径
        /// </summary>
        [Column("originPath312")]
        public string? OriginPath312 { get; set; }

        /// <summary>
        /// 312文件备份路径
        /// </summary>
        public string? LocalBakPath312 { get; set; }

        /// <summary>
        /// 313文件源路径
        /// </summary>
        [Column("originPath313")]
        public string? OriginPath313 { get; set; }

        /// <summary>
        /// 313文件备份路径
        /// </summary>
        public string? LocalBakPath313 { get; set; }

        /// <summary>
        /// 315文件源路径
        /// </summary>
        [Column("originPath315")]
        public string? OriginPath315 { get; set; }

        /// <summary>
        /// 315文件备份路径
        /// </summary>
        public string? LocalBakPath315 { get; set; }

        /// <summary>
        /// 远程临时目录
        /// </summary>
        public string? RemotePathTemp { get; set; }

        /// <summary>
        /// 312文件远程目录
        /// </summary>
        public string? RemotePath312 { get; set; }

        /// <summary>
        /// 313文件远程目录
        /// </summary>
        public string? RemotePath313 { get; set; }

        /// <summary>
        /// 315文件远程目录
        /// </summary>
        public string? RemotePath315 { get; set; }

        /// <summary>
        /// FTP服务器描述
        /// </summary>
        [Column("sServerDesc")]
        public string? ServerDesc { get; set; }

        /// <summary>
        /// FTP类型
        /// </summary>
        public string? FtpType { get; set; }
    }

    /// <summary>
    /// FTP文件上传统计结果DTO
    /// </summary>
    public class FtpUploadStatsDto
    {
        /// <summary>
        /// 上传目标
        /// </summary>
        public string UploadTarget { get; set; } = string.Empty;

        /// <summary>
        /// 上传次序
        /// </summary>
        public int UpOrder { get; set; }

        /// <summary>
        /// 312文件待上传数
        /// </summary>
        public int File312PendingCount { get; set; }

        /// <summary>
        /// 315文件待上传数
        /// </summary>
        public int File315PendingCount { get; set; }

        /// <summary>
        /// 312源目录
        /// </summary>
        public string OriginPath312 { get; set; } = string.Empty;

        /// <summary>
        /// 312备份目录
        /// </summary>
        public string LocalBakPath312 { get; set; } = string.Empty;

        /// <summary>
        /// 315源目录
        /// </summary>
        public string OriginPath315 { get; set; } = string.Empty;

        /// <summary>
        /// 315备份目录
        /// </summary>
        public string LocalBakPath315 { get; set; } = string.Empty;
    }

    /// <summary>
    /// 文件上传统计响应DTO
    /// </summary>
    public class FtpUploadStatsResponseDto
    {
        /// <summary>
        /// 查询日期
        /// </summary>
        public DateTime QueryDate { get; set; }

        /// <summary>
        /// 上传目标统计列表
        /// </summary>
        public List<FtpUploadStatsDto> UploadTargets { get; set; } = new();

        /// <summary>
        /// 312文件上传成功数量（按日期统计）
        /// </summary>
        public int File312SuccessCount { get; set; }

        /// <summary>
        /// 315文件上传成功数量（按日期统计）
        /// </summary>
        public int File315SuccessCount { get; set; }
    }
}
