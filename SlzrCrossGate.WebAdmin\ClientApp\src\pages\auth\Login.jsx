import { useState, useEffect } from 'react';
import { Link as RouterLink, useNavigate } from 'react-router-dom';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import {
  Box,
  Button,
  Checkbox,
  Container,
  Divider,
  FormControlLabel,
  Grid,
  Link,
  Paper,
  TextField,
  Typography,
  Alert,
} from '@mui/material';
import { useAuth } from '../../contexts/AuthContext';
import { useSnackbar } from 'notistack';
import { parseErrorMessage } from '../../utils/errorHandler';
import WechatIcon from '../../components/icons/WechatIcon';

const Login = () => {
  const { login } = useAuth();
  const { enqueueSnackbar } = useSnackbar();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // 版本检查现在由 VersionManager 统一处理，避免重复请求



  // 表单验证
  const formik = useFormik({
    initialValues: {
      username: '',
      password: '',
    },
    validationSchema: Yup.object({
      username: Yup.string().required('请输入用户名'),
      password: Yup.string().required('请输入密码'),
    }),
    onSubmit: async (values) => {
      setLoading(true);
      setError('');

      try {
        console.log('开始登录请求:', values.username);
        const result = await login(values.username, values.password);
        console.log('登录结果:', result);

        if (result.success) {
          // 如果需要强制更改密码
          if (result.requirePasswordChange) {
            navigate('/force-password-change');
            return;
          }

          // 如果需要双因素验证
          if (result.requireTwoFactor) {
            navigate('/two-factor-verify');
            return;
          }

          // 如果需要设置双因素验证
          if (result.setupTwoFactor) {
            navigate('/two-factor-setup');
            return;
          }

          // 正常登录成功
          enqueueSnackbar('登录成功', { variant: 'success' });
          navigate('/app/dashboard');
        } else {
          console.error('登录失败:', result.message);
          setError(result.message || '登录失败，请检查用户名和密码');
        }
      } catch (err) {
        console.error('登录异常:', err);
        const errorMessage = parseErrorMessage(err, '登录过程中发生错误，请稍后再试');
        setError(errorMessage);
      } finally {
        setLoading(false);
      }
    },
  });



  return (
      <Container maxWidth="sm">
        <Paper
          elevation={3}
          sx={{
            p: 4,
            borderRadius: 2,
          }}
        >
          <Box sx={{ mb: 3, textAlign: 'center' }}>
            <Typography variant="h4" gutterBottom>
              终端管理系统
            </Typography>
            <Typography variant="body2" color="text.secondary">
              请登录您的账号
            </Typography>
          </Box>

          {error && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {error}
            </Alert>
          )}

          <form onSubmit={formik.handleSubmit}>
            <TextField
              fullWidth
              id="username"
              name="username"
              label="用户名"
              margin="normal"
              autoComplete="username"
              autoFocus
              value={formik.values.username}
              onChange={formik.handleChange}
              error={formik.touched.username && Boolean(formik.errors.username)}
              helperText={formik.touched.username && formik.errors.username}
              sx={(theme) => ({
                // 处理自动填充的样式 - 根据主题模式调整
                '& input:-webkit-autofill': {
                  WebkitBoxShadow: theme.palette.mode === 'dark'
                    ? '0 0 0 1000px rgba(30, 41, 59, 0.9) inset'
                    : '0 0 0 1000px white inset',
                  WebkitTextFillColor: theme.palette.text.primary,
                  transition: 'background-color 5000s ease-in-out 0s',
                },
                '& input:-webkit-autofill ~ label': {
                  transform: 'translate(14px, -9px) scale(0.75)',
                  background: theme.palette.mode === 'dark'
                    ? 'rgba(30, 41, 59, 0.9)'
                    : '#fff',
                  padding: '0 5px',
                },
                '& .MuiInputBase-input:-webkit-autofill + fieldset + .MuiInputLabel-root': {
                  transform: 'translate(14px, -9px) scale(0.75)',
                  background: theme.palette.mode === 'dark'
                    ? 'rgba(30, 41, 59, 0.9)'
                    : '#fff',
                  padding: '0 5px',
                },
              })}
            />
            <TextField
              fullWidth
              id="password"
              name="password"
              label="密码"
              type="password"
              margin="normal"
              autoComplete="current-password"
              value={formik.values.password}
              onChange={formik.handleChange}
              error={formik.touched.password && Boolean(formik.errors.password)}
              helperText={formik.touched.password && formik.errors.password}
              sx={(theme) => ({
                // 处理自动填充的样式 - 根据主题模式调整
                '& input:-webkit-autofill': {
                  WebkitBoxShadow: theme.palette.mode === 'dark'
                    ? '0 0 0 1000px rgba(30, 41, 59, 0.9) inset'
                    : '0 0 0 1000px white inset',
                  WebkitTextFillColor: theme.palette.text.primary,
                  transition: 'background-color 5000s ease-in-out 0s',
                },
                '& input:-webkit-autofill ~ label': {
                  transform: 'translate(14px, -9px) scale(0.75)',
                  background: theme.palette.mode === 'dark'
                    ? 'rgba(30, 41, 59, 0.9)'
                    : '#fff',
                  padding: '0 5px',
                },
                '& .MuiInputBase-input:-webkit-autofill + fieldset + .MuiInputLabel-root': {
                  transform: 'translate(14px, -9px) scale(0.75)',
                  background: theme.palette.mode === 'dark'
                    ? 'rgba(30, 41, 59, 0.9)'
                    : '#fff',
                  padding: '0 5px',
                },
              })}
            />

            <Button
              fullWidth
              type="submit"
              variant="contained"
              size="large"
              disabled={loading}
              sx={{ mt: 3, mb: 2 }}
            >
              {loading ? '登录中...' : '登录'}
            </Button>
          </form>
        {/*
          <Divider sx={{ my: 3 }}>
            <Typography variant="body2" color="text.secondary">
              或者
            </Typography>
          </Divider>

          <Grid container spacing={2}>
            <Grid item xs={12}>
              <Button
                fullWidth
                variant="outlined"
                color="success"
                startIcon={<WechatIcon />}
                onClick={() => navigate('/wechat-login')}
              >
                微信扫码登录
              </Button>
            </Grid>
          </Grid>
          */}
        </Paper>
      </Container>

  );
};

export default Login;
