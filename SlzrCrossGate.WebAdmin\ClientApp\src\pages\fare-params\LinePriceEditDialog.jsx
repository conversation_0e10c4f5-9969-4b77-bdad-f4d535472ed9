import React, { useState, useEffect } from 'react';
import {
  <PERSON>ton,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  TextField,
  Grid,
  CircularProgress,
  FormControlLabel,
  Switch,
  Box,
  Typography,
  Chip
} from '@mui/material';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { useSnackbar } from 'notistack';
import { linePriceAPI } from '../../services/api';
import { parseErrorMessage } from '../../utils/errorHandler';

const LinePriceEditDialog = ({ open, onClose, onUpdated, linePriceId }) => {
  const { enqueueSnackbar } = useSnackbar();
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(false);
  const [linePriceData, setLinePriceData] = useState(null);
  const [fareDisplay, setFareDisplay] = useState('2.00');

  // 表单验证规则
  const validationSchema = Yup.object({
    lineName: Yup.string()
      .required('请输入线路名称')
      .max(100, '线路名称不能超过100个字符'),
    branch: Yup.string()
      .max(50, '分公司名称不能超过50个字符'),
    fare: Yup.number()
      .required('请输入票价')
      .min(1, '票价必须大于0')
      .max(99999, '票价不能超过999.99元'),
    remark: Yup.string()
      .max(500, '备注不能超过500个字符')
  });

  // 表单处理
  const formik = useFormik({
    initialValues: {
      lineName: '',
      branch: '',
      fare: 200,
      isActive: true,
      remark: ''
    },
    validationSchema,
    onSubmit: async (values) => {
      try {
        setLoading(true);

        // 准备提交数据
        const submitData = {
          lineName: values.lineName,
          branch: values.branch || '',
          fare: values.fare,
          isActive: values.isActive,
          remark: values.remark || ''
        };

        await linePriceAPI.updateLinePrice(linePriceId, submitData);
        enqueueSnackbar('更新成功', { variant: 'success' });
        
        // 调用成功回调
        if (onUpdated) {
          onUpdated();
        }
        
        // 关闭对话框
        handleClose();
      } catch (error) {
        console.error('更新失败:', error);
        const errorMessage = parseErrorMessage(error, '更新失败');
        enqueueSnackbar(errorMessage, { variant: 'error' });
      } finally {
        setLoading(false);
      }
    }
  });

  // 加载线路参数数据
  const loadLinePriceData = async () => {
    if (!linePriceId) return;

    try {
      setInitialLoading(true);
      const data = await linePriceAPI.getLinePrice(linePriceId);
      setLinePriceData(data);
      
      // 设置表单初始值
      formik.setValues({
        lineName: data.lineName || '',
        branch: data.branch || '',
        fare: data.fare || 200,
        isActive: data.isActive !== false,
        remark: data.remark || ''
      });

      // 设置票价显示值
      setFareDisplay(formatFareDisplay(data.fare || 200));
    } catch (error) {
      console.error('加载线路参数数据失败:', error);
      const errorMessage = parseErrorMessage(error, '加载数据失败');
      enqueueSnackbar(errorMessage, { variant: 'error' });
    } finally {
      setInitialLoading(false);
    }
  };

  // 当对话框打开且有ID时加载数据
  useEffect(() => {
    if (open && linePriceId) {
      loadLinePriceData();
    }
  }, [open, linePriceId]);

  // 关闭对话框
  const handleClose = () => {
    formik.resetForm();
    setLinePriceData(null);
    setFareDisplay('2.00');
    onClose();
  };

  // 票价显示转换（分转元）
  const formatFareDisplay = (fareInCents) => {
    return (fareInCents / 100).toFixed(2);
  };

  // 票价输入变化（只更新显示值）
  const handleFareChange = (event) => {
    setFareDisplay(event.target.value);
  };

  // 票价失去焦点时转换并验证
  const handleFareBlur = (event) => {
    const fareInYuan = parseFloat(event.target.value) || 0;
    const fareInCents = Math.round(fareInYuan * 100);
    formik.setFieldValue('fare', fareInCents);
    setFareDisplay(formatFareDisplay(fareInCents));
    formik.handleBlur(event);
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          boxShadow: 3
        }
      }}
    >
      <DialogTitle sx={{ pb: 1 }}>
        <Typography variant="h6" component="div">
          编辑线路票价参数
        </Typography>
        {linePriceData && (
          <Box sx={{ mt: 1, display: 'flex', gap: 1, alignItems: 'center' }}>
            <Typography variant="body2" color="textSecondary">
              线路：{linePriceData.lineNumber}-{linePriceData.groupNumber || '无组号'}
            </Typography>
            <Chip 
              label={linePriceData.merchantName} 
              size="small" 
              color="primary" 
              variant="outlined" 
            />
          </Box>
        )}
      </DialogTitle>

      {initialLoading ? (
        <DialogContent sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
          <CircularProgress />
        </DialogContent>
      ) : (
        <form onSubmit={formik.handleSubmit}>
          <DialogContent sx={{ pt: 2 }}>
            <Grid container spacing={3}>
              {/* 线路信息显示（只读） */}
              {linePriceData && (
                <Grid item xs={12}>
                  <Box sx={{ 
                    p: 2, 
                    bgcolor: 'grey.50', 
                    borderRadius: 1,
                    border: '1px solid',
                    borderColor: 'grey.200'
                  }}>
                    <Typography variant="subtitle2" gutterBottom>
                      线路基本信息（不可修改）
                    </Typography>
                    <Grid container spacing={2}>
                      <Grid item xs={6} sm={3}>
                        <Typography variant="body2" color="textSecondary">
                          商户：{linePriceData.merchantName}
                        </Typography>
                      </Grid>
                      <Grid item xs={6} sm={3}>
                        <Typography variant="body2" color="textSecondary">
                          线路编号：{linePriceData.lineNumber}
                        </Typography>
                      </Grid>
                      <Grid item xs={6} sm={3}>
                        <Typography variant="body2" color="textSecondary">
                          组号：{linePriceData.groupNumber || '无'}
                        </Typography>
                      </Grid>
                      <Grid item xs={6} sm={3}>
                        <Typography variant="body2" color="textSecondary">
                          当前版本：{linePriceData.currentVersion || '无'}
                        </Typography>
                      </Grid>
                    </Grid>
                  </Box>
                </Grid>
              )}

              {/* 线路名称 */}
              <Grid item xs={12}>
                <TextField
                  name="lineName"
                  label="线路名称"
                  value={formik.values.lineName}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.lineName && Boolean(formik.errors.lineName)}
                  helperText={formik.touched.lineName && formik.errors.lineName}
                  required
                  fullWidth
                  placeholder="如：市区环线"
                />
              </Grid>

              {/* 分公司 */}
              <Grid item xs={12} sm={6}>
                <TextField
                  name="branch"
                  label="分公司"
                  value={formik.values.branch}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.branch && Boolean(formik.errors.branch)}
                  helperText={formik.touched.branch && formik.errors.branch}
                  fullWidth
                  placeholder="如：第一分公司（可选）"
                />
              </Grid>

              {/* 票价 */}
              <Grid item xs={12} sm={6}>
                <TextField
                  name="fare"
                  label="票价（元）"
                  type="number"
                  value={fareDisplay}
                  onChange={handleFareChange}
                  onBlur={handleFareBlur}
                  error={formik.touched.fare && Boolean(formik.errors.fare)}
                  helperText={formik.touched.fare && formik.errors.fare}
                  required
                  fullWidth
                  inputProps={{
                    min: 0.5,
                    max: 999.99,
                    step: 0.5
                  }}
                  placeholder="2.00"
                />
              </Grid>

              {/* 状态 */}
              <Grid item xs={12}>
                <FormControlLabel
                  control={
                    <Switch
                      name="isActive"
                      checked={formik.values.isActive}
                      onChange={formik.handleChange}
                      color="primary"
                    />
                  }
                  label="启用状态"
                />
              </Grid>

              {/* 备注 */}
              <Grid item xs={12}>
                <TextField
                  name="remark"
                  label="备注"
                  value={formik.values.remark}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.remark && Boolean(formik.errors.remark)}
                  helperText={formik.touched.remark && formik.errors.remark}
                  fullWidth
                  multiline
                  rows={3}
                  placeholder="可选的备注信息"
                />
              </Grid>
            </Grid>
          </DialogContent>

          <DialogActions sx={{ px: 3, pb: 3 }}>
            <Button
              onClick={handleClose}
              disabled={loading}
            >
              取消
            </Button>
            <Button
              type="submit"
              variant="contained"
              disabled={loading}
              startIcon={loading && <CircularProgress size={20} />}
            >
              {loading ? '更新中...' : '更新'}
            </Button>
          </DialogActions>
        </form>
      )}
    </Dialog>
  );
};

export default LinePriceEditDialog;
