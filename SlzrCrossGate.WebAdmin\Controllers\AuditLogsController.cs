using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SlzrCrossGate.WebAdmin.DTOs;
using SlzrCrossGate.WebAdmin.Services;
using SlzrCrossGate.WebAdmin.Extensions;

namespace SlzrCrossGate.WebAdmin.Controllers
{
    /// <summary>
    /// 审计日志控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Authorize(Roles = "SystemAdmin,MerchantAdmin")]
    public class AuditLogsController : ControllerBase
    {
        private readonly AuditLogService _auditLogService;
        private readonly UserService _userService;
        private readonly ILogger<AuditLogsController> _logger;

        public AuditLogsController(
            AuditLogService auditLogService,
            UserService userService,
            ILogger<AuditLogsController> logger)
        {
            _auditLogService = auditLogService;
            _userService = userService;
            _logger = logger;
        }

        /// <summary>
        /// 获取登录日志
        /// </summary>
        [HttpGet("login-logs")]
        public async Task<ActionResult<PaginatedResult<LoginLogDto>>> GetLoginLogs([FromQuery] LoginLogQueryDto query)
        {
            try
            {
                var currentUserMerchantId = await _userService.GetUserMerchantIdAsync(User);
                var isSystemAdmin = User.IsInRole("SystemAdmin");

                // 权限检查：非系统管理员只能查看自己商户的日志
                if (!isSystemAdmin && !string.IsNullOrEmpty(query.MerchantId) && query.MerchantId != currentUserMerchantId)
                {
                    return Forbid("您只能查看自己商户的审计日志");
                }

                var result = await _auditLogService.GetLoginLogsAsync(query, currentUserMerchantId, isSystemAdmin);

                _logger.LogUserAction($"查询登录日志 - 页码:{query.Page}, 页大小:{query.PageSize}", User.Identity?.Name);

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取登录日志时发生错误");
                return StatusCode(500, new { message = "获取登录日志失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 获取密码修改日志
        /// </summary>
        [HttpGet("password-change-logs")]
        public async Task<ActionResult<PaginatedResult<PasswordChangeLogDto>>> GetPasswordChangeLogs([FromQuery] PasswordChangeLogQueryDto query)
        {
            try
            {
                var currentUserMerchantId = await _userService.GetUserMerchantIdAsync(User);
                var isSystemAdmin = User.IsInRole("SystemAdmin");

                // 权限检查：非系统管理员只能查看自己商户的日志
                if (!isSystemAdmin && !string.IsNullOrEmpty(query.MerchantId) && query.MerchantId != currentUserMerchantId)
                {
                    return Forbid("您只能查看自己商户的审计日志");
                }

                var result = await _auditLogService.GetPasswordChangeLogsAsync(query, currentUserMerchantId, isSystemAdmin);

                _logger.LogUserAction($"查询密码修改日志 - 页码:{query.Page}, 页大小:{query.PageSize}", User.Identity?.Name);

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取密码修改日志时发生错误");
                return StatusCode(500, new { message = "获取密码修改日志失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 获取操作日志
        /// </summary>
        [HttpGet("operation-logs")]
        public async Task<ActionResult<PaginatedResult<OperationLogDto>>> GetOperationLogs([FromQuery] OperationLogQueryDto query)
        {
            try
            {
                var currentUserMerchantId = await _userService.GetUserMerchantIdAsync(User);
                var isSystemAdmin = User.IsInRole("SystemAdmin");

                // 权限检查：非系统管理员只能查看自己商户的日志
                if (!isSystemAdmin && !string.IsNullOrEmpty(query.MerchantId) && query.MerchantId != currentUserMerchantId)
                {
                    return Forbid("您只能查看自己商户的审计日志");
                }

                var result = await _auditLogService.GetOperationLogsAsync(query, currentUserMerchantId, isSystemAdmin);

                _logger.LogUserAction($"查询操作日志 - 页码:{query.Page}, 页大小:{query.PageSize}", User.Identity?.Name);

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取操作日志时发生错误");
                return StatusCode(500, new { message = "获取操作日志失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 获取审计日志统计信息
        /// </summary>
        [HttpGet("stats")]
        public async Task<ActionResult<AuditLogStatsDto>> GetAuditLogStats()
        {
            try
            {
                var currentUserMerchantId = await _userService.GetUserMerchantIdAsync(User);
                var isSystemAdmin = User.IsInRole("SystemAdmin");

                var result = await _auditLogService.GetAuditLogStatsAsync(currentUserMerchantId, isSystemAdmin);

                _logger.LogUserAction("查询审计日志统计信息", User.Identity?.Name);

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取审计日志统计信息时发生错误");
                return StatusCode(500, new { message = "获取审计日志统计信息失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 导出登录日志
        /// </summary>
        [HttpGet("login-logs/export")]
        public async Task<IActionResult> ExportLoginLogs([FromQuery] LoginLogQueryDto query)
        {
            try
            {
                var currentUserMerchantId = await _userService.GetUserMerchantIdAsync(User);
                var isSystemAdmin = User.IsInRole("SystemAdmin");

                // 权限检查：非系统管理员只能导出自己商户的日志
                if (!isSystemAdmin && !string.IsNullOrEmpty(query.MerchantId) && query.MerchantId != currentUserMerchantId)
                {
                    return Forbid("您只能导出自己商户的审计日志");
                }

                // 设置导出限制，最多导出10000条记录
                query.PageSize = 10000;
                query.Page = 1;

                var result = await _auditLogService.GetLoginLogsAsync(query, currentUserMerchantId, isSystemAdmin);

                // 生成CSV内容
                var csvContent = GenerateLoginLogsCsv(result.Items);
                var fileName = $"登录日志_{DateTime.Now:yyyyMMdd_HHmmss}.csv";

                _logger.LogUserAction($"导出登录日志 - 记录数:{result.Items.Count}", User.Identity?.Name);

                return File(csvContent, "text/csv; charset=utf-8", fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导出登录日志时发生错误");
                return StatusCode(500, new { message = "导出登录日志失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 导出密码修改日志
        /// </summary>
        [HttpGet("password-change-logs/export")]
        public async Task<IActionResult> ExportPasswordChangeLogs([FromQuery] PasswordChangeLogQueryDto query)
        {
            try
            {
                var currentUserMerchantId = await _userService.GetUserMerchantIdAsync(User);
                var isSystemAdmin = User.IsInRole("SystemAdmin");

                // 权限检查：非系统管理员只能导出自己商户的日志
                if (!isSystemAdmin && !string.IsNullOrEmpty(query.MerchantId) && query.MerchantId != currentUserMerchantId)
                {
                    return Forbid("您只能导出自己商户的审计日志");
                }

                // 设置导出限制，最多导出10000条记录
                query.PageSize = 10000;
                query.Page = 1;

                var result = await _auditLogService.GetPasswordChangeLogsAsync(query, currentUserMerchantId, isSystemAdmin);

                // 生成CSV内容
                var csvContent = GeneratePasswordChangeLogsCsv(result.Items);
                var fileName = $"密码修改日志_{DateTime.Now:yyyyMMdd_HHmmss}.csv";

                _logger.LogUserAction($"导出密码修改日志 - 记录数:{result.Items.Count}", User.Identity?.Name);

                return File(csvContent, "text/csv; charset=utf-8", fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导出密码修改日志时发生错误");
                return StatusCode(500, new { message = "导出密码修改日志失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 导出操作日志
        /// </summary>
        [HttpGet("operation-logs/export")]
        public async Task<IActionResult> ExportOperationLogs([FromQuery] OperationLogQueryDto query)
        {
            try
            {
                var currentUserMerchantId = await _userService.GetUserMerchantIdAsync(User);
                var isSystemAdmin = User.IsInRole("SystemAdmin");

                // 权限检查：非系统管理员只能导出自己商户的日志
                if (!isSystemAdmin && !string.IsNullOrEmpty(query.MerchantId) && query.MerchantId != currentUserMerchantId)
                {
                    return Forbid("您只能导出自己商户的审计日志");
                }

                // 设置导出限制，最多导出10000条记录
                query.PageSize = 10000;
                query.Page = 1;

                var result = await _auditLogService.GetOperationLogsAsync(query, currentUserMerchantId, isSystemAdmin);

                // 生成CSV内容
                var csvContent = GenerateOperationLogsCsv(result.Items);
                var fileName = $"操作日志_{DateTime.Now:yyyyMMdd_HHmmss}.csv";

                _logger.LogUserAction($"导出操作日志 - 记录数:{result.Items.Count}", User.Identity?.Name);

                return File(csvContent, "text/csv; charset=utf-8", fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导出操作日志时发生错误");
                return StatusCode(500, new { message = "导出操作日志失败", error = ex.Message });
            }
        }

        #region 私有方法

        /// <summary>
        /// 生成登录日志CSV内容
        /// </summary>
        private byte[] GenerateLoginLogsCsv(IEnumerable<LoginLogDto> logs)
        {
            var csv = new System.Text.StringBuilder();
            csv.AppendLine("ID,用户ID,用户名,真实姓名,商户ID,商户名称,IP地址,登录类型,登录方式,操作时间,是否成功,失败原因,会话ID,登出时间");

            foreach (var log in logs)
            {
                csv.AppendLine($"{log.Id},{log.UserId},{log.UserName},{log.RealName},{log.MerchantId},{log.MerchantName},{log.IpAddress},{log.LoginType},{log.LoginMethod},{log.OperationTime:yyyy-MM-dd HH:mm:ss},{(log.IsSuccess ? "成功" : "失败")},{log.FailureReason},{log.SessionId},{log.LogoutTime?.ToString("yyyy-MM-dd HH:mm:ss")}");
            }

            return System.Text.Encoding.UTF8.GetBytes(csv.ToString());
        }

        /// <summary>
        /// 生成密码修改日志CSV内容
        /// </summary>
        private byte[] GeneratePasswordChangeLogsCsv(IEnumerable<PasswordChangeLogDto> logs)
        {
            var csv = new System.Text.StringBuilder();
            csv.AppendLine("ID,用户ID,用户名,真实姓名,商户ID,商户名称,IP地址,修改类型,目标用户ID,目标用户名,目标真实姓名,操作时间,是否成功,失败原因");

            foreach (var log in logs)
            {
                csv.AppendLine($"{log.Id},{log.UserId},{log.UserName},{log.RealName},{log.MerchantId},{log.MerchantName},{log.IpAddress},{log.ChangeType},{log.TargetUserId},{log.TargetUserName},{log.TargetRealName},{log.OperationTime:yyyy-MM-dd HH:mm:ss},{(log.IsSuccess ? "成功" : "失败")},{log.FailureReason}");
            }

            return System.Text.Encoding.UTF8.GetBytes(csv.ToString());
        }

        /// <summary>
        /// 生成操作日志CSV内容
        /// </summary>
        private byte[] GenerateOperationLogsCsv(IEnumerable<OperationLogDto> logs)
        {
            var csv = new System.Text.StringBuilder();
            csv.AppendLine("ID,用户ID,用户名,真实姓名,商户ID,商户名称,IP地址,操作模块,操作类型,操作对象,请求路径,HTTP方法,响应状态码,执行耗时(ms),操作时间,是否成功,失败原因");

            foreach (var log in logs)
            {
                csv.AppendLine($"{log.Id},{log.UserId},{log.UserName},{log.RealName},{log.MerchantId},{log.MerchantName},{log.IpAddress},{log.Module},{log.OperationType},{log.OperationTarget},{log.RequestPath},{log.HttpMethod},{log.ResponseStatusCode},{log.ExecutionTime},{log.OperationTime:yyyy-MM-dd HH:mm:ss},{(log.IsSuccess ? "成功" : "失败")},{log.FailureReason}");
            }

            return System.Text.Encoding.UTF8.GetBytes(csv.ToString());
        }

        #endregion
    }
}
