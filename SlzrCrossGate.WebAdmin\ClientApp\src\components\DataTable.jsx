import React from 'react';
import {
  Box,
  CircularProgress,
  TablePagination,
  Typography,
  TableSortLabel
} from '@mui/material';
import ResponsiveTable, {
  ResponsiveTableHead,
  ResponsiveTableBody,
  ResponsiveTableRow,
  ResponsiveTableCell
} from './ResponsiveTable';

/**
 * 数据表格组件
 * 支持列定义、数据渲染、分页、排序等功能
 */
const DataTable = ({
  columns = [],
  data = [],
  loading = false,
  pagination = null,
  onPageChange,
  onPageSizeChange,
  onSort,
  sortBy = '',
  sortDirection = 'asc',
  minWidth = 800,
  stickyActions = true,
  emptyMessage = '暂无数据'
}) => {
  // 处理排序
  const handleSort = (field) => {
    if (onSort) {
      onSort(field);
    }
  };

  // 处理分页变化
  const handlePageChange = (event, newPage) => {
    if (onPageChange) {
      onPageChange(newPage + 1); // Material-UI使用0基索引，我们使用1基索引
    }
  };

  // 处理每页大小变化
  const handlePageSizeChange = (event) => {
    if (onPageSizeChange) {
      onPageSizeChange(parseInt(event.target.value, 10));
    }
  };

  // 渲染表头
  const renderTableHead = () => (
    <ResponsiveTableHead>
      <ResponsiveTableRow>
        {columns.map((column) => (
          <ResponsiveTableCell
            key={column.field}
            hideOn={column.hideOn}
            sticky={column.sticky}
            minWidth={column.width}
            align={column.align || 'left'}
          >
            {column.sortable ? (
              <TableSortLabel
                active={sortBy === column.field}
                direction={sortBy === column.field ? sortDirection : 'asc'}
                onClick={() => handleSort(column.field)}
              >
                {column.headerName}
              </TableSortLabel>
            ) : (
              column.headerName
            )}
          </ResponsiveTableCell>
        ))}
      </ResponsiveTableRow>
    </ResponsiveTableHead>
  );

  // 渲染表体
  const renderTableBody = () => {
    if (loading) {
      return (
        <ResponsiveTableBody>
          <ResponsiveTableRow>
            <ResponsiveTableCell colSpan={columns.length} align="center">
              <Box sx={{ py: 3 }}>
                <CircularProgress size={24} />
                <Typography variant="body2" sx={{ mt: 1 }}>
                  加载中...
                </Typography>
              </Box>
            </ResponsiveTableCell>
          </ResponsiveTableRow>
        </ResponsiveTableBody>
      );
    }

    if (!data || data.length === 0) {
      return (
        <ResponsiveTableBody>
          <ResponsiveTableRow>
            <ResponsiveTableCell colSpan={columns.length} align="center">
              <Box sx={{ py: 3 }}>
                <Typography variant="body2" color="textSecondary">
                  {emptyMessage}
                </Typography>
              </Box>
            </ResponsiveTableCell>
          </ResponsiveTableRow>
        </ResponsiveTableBody>
      );
    }

    return (
      <ResponsiveTableBody>
        {data.map((row, rowIndex) => (
          <ResponsiveTableRow key={row.ID || row.id || rowIndex}>
            {columns.map((column) => (
              <ResponsiveTableCell
                key={column.field}
                hideOn={column.hideOn}
                sticky={column.sticky}
                align={column.align || 'left'}
              >
                {column.renderCell ?
                  column.renderCell({ value: row[column.field], row, column }) :
                  row[column.field]
                }
              </ResponsiveTableCell>
            ))}
          </ResponsiveTableRow>
        ))}
      </ResponsiveTableBody>
    );
  };

  return (
    <Box>
      <ResponsiveTable minWidth={minWidth} stickyActions={stickyActions}>
        {renderTableHead()}
        {renderTableBody()}
      </ResponsiveTable>
      
      {pagination && (
        <TablePagination
          rowsPerPageOptions={[10, 20, 50, 100]}
          component="div"
          count={pagination.totalCount || 0}
          rowsPerPage={pagination.pageSize || 20}
          page={(pagination.page || 1) - 1} // Material-UI使用0基索引
          onPageChange={handlePageChange}
          onRowsPerPageChange={handlePageSizeChange}
          labelRowsPerPage="每页行数:"
          labelDisplayedRows={({ from, to, count }) => `${from}-${to} 共 ${count !== -1 ? count : `超过 ${to}`}`}
        />
      )}
    </Box>
  );
};

export default DataTable;
