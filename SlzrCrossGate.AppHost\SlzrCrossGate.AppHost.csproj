<Project Sdk="Microsoft.NET.Sdk">

  <Sdk Name="Aspire.AppHost.Sdk" Version="9.0.0" />

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <IsAspireHost>true</IsAspireHost>
    <UserSecretsId>e0485976-2582-487d-b48c-7c286baceeb3</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\SlzrCrossGate.ApiService\SlzrCrossGate.ApiService.csproj" />
    <ProjectReference Include="..\SlzrCrossGate.WebAdmin\SlzrCrossGate.WebAdmin.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Aspire.Hosting.AppHost" Version="9.0.0" />
  </ItemGroup>

</Project>
