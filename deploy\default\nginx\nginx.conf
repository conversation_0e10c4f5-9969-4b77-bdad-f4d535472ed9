user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log notice;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
}

# HTTP 配置块
http {
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;

    sendfile on;
    tcp_nopush on;
    keepalive_timeout 65;
    types_hash_max_size 4096;

    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # 包含HTTP配置文件
    include /etc/nginx/conf.d/*.conf;
}

# TCP/UDP 代理配置块（Stream模块）
stream {
    log_format basic '$remote_addr [$time_local] '
                     '$protocol $status $bytes_sent $bytes_received '
                     '$session_time';

    access_log /var/log/nginx/stream_access.log basic;
    error_log /var/log/nginx/stream_error.log;

    # TCP代理配置 - 代理到api-service的TCP服务
    upstream tcp_backend {
        server api-service:8001;
    }

    server {
        listen 8823;
        proxy_pass tcp_backend;
        proxy_timeout 10s;
        proxy_connect_timeout 5s;
        proxy_responses 1;
        # 移除transparent选项，因为在容器环境中可能不支持
        # proxy_bind $remote_addr transparent;
    }
}
