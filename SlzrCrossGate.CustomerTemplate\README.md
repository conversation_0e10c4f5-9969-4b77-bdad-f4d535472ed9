# SlzrCrossGate 客户项目模板

这是 SlzrCrossGate 系统的客户项目模板，用于快速创建新的客户定制项目。

## 🎯 最新更新 (v2.0)

### ✅ 已同步的功能
- **完整主题系统** - 玻璃拟态+微立体感设计，支持亮色/暗色模式切换
- **正确的用户模型** - 修复表映射错误，现在正确映射到 `AspNetUsers` 表
- **认证和授权** - JWT认证，与主应用保持一致的安全配置
- **数据库配置** - 正确的DbContext配置和连接字符串
- **基础控制器** - 认证、首页、客户数据管理等完整API
- **服务层** - 完整的业务逻辑服务和数据访问层

### 🎨 UI特性
- #7E22CE 主题色配置
- 玻璃拟态效果和微立体感按钮
- 响应式设计，支持移动端
- 主题切换功能
- 统一的组件样式

## 🚀 快速开始

### 1. 复制模板项目

```bash
# 复制整个模板目录
cp -r SlzrCrossGate.CustomerTemplate SlzrCrossGate.Customer[客户名称]
```

### 2. 修改项目配置

#### 2.1 重命名文件和目录
- 将 `SlzrCrossGate.CustomerTemplate.csproj` 重命名为 `SlzrCrossGate.Customer[客户名称].csproj`
- 修改命名空间：将所有文件中的 `SlzrCrossGate.CustomerTemplate` 替换为 `SlzrCrossGate.Customer[客户名称]`

#### 2.2 修改配置文件

**appsettings.json**:
```json
{
  "Customer": {
    "Name": "[客户英文名称]",
    "DisplayName": "[客户显示名称]",
    "TablePrefix": "Customer_[客户名称]",
    "BasePath": "/[客户路径]"
  },
  "ConnectionStrings": {
    "CustomerConnection": "Server=localhost;Database=tcpserver;Uid=customer_[客户名称];Pwd=**************;"
  }
}
```

**Program.cs**:
```csharp
// 修改环境变量设置
Environment.SetEnvironmentVariable("CUSTOMER_TABLE_PREFIX", "Customer_[客户名称]");
Environment.SetEnvironmentVariable("CUSTOMER_BASE_PATH", "/[客户路径]");
```

**Vite 配置**:
```javascript
// vite.config.js 中修改默认基础路径
const base = process.env.VITE_BASE_PATH || (mode === 'production' ? '/[客户路径]/' : '/');
```

**Dockerfile**:
```dockerfile
# 修改环境变量
ENV VITE_BASE_PATH=/[客户路径]/
```

### 3. 前端配置

#### 3.1 修改 package.json
```json
{
  "name": "slzr-[客户名称]-frontend",
  "version": "1.0.0"
}
```

#### 3.2 修改 index.html
```html
<title>[客户名称]定制系统</title>
```

### 4. 数据库配置

#### 4.1 创建客户数据库用户
```sql
-- 创建客户专用数据库用户
CREATE USER 'customer_[客户名称]'@'%' IDENTIFIED BY '**************';

-- 授予客户表的权限
GRANT SELECT, INSERT, UPDATE, DELETE ON tcpserver.Customer_[客户名称]_* TO 'customer_[客户名称]'@'%';

-- 授予只读认证表的权限
GRANT SELECT ON tcpserver.AspNetUsers TO 'customer_[客户名称]'@'%';
GRANT SELECT ON tcpserver.AspNetRoles TO 'customer_[客户名称]'@'%';
GRANT SELECT ON tcpserver.AspNetUserRoles TO 'customer_[客户名称]'@'%';

FLUSH PRIVILEGES;
```

#### 4.2 创建客户数据表
```sql
-- 示例：创建客户查询记录表
CREATE TABLE Customer_[客户名称]_QueryRecords (
    Id INT AUTO_INCREMENT PRIMARY KEY,
    UserId VARCHAR(450) NOT NULL,
    QueryType VARCHAR(50) NOT NULL,
    QueryParams JSON,
    ResultCount INT DEFAULT 0,
    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (UserId),
    INDEX idx_created_at (CreatedAt)
);
```

## 🏗️ 项目结构

```
SlzrCrossGate.Customer[客户名称]/
├── ClientApp/                 # 前端 React 应用
│   ├── src/
│   │   ├── pages/            # 页面组件
│   │   ├── services/         # 服务层
│   │   └── App.jsx           # 主应用组件
│   ├── vite.config.js        # Vite 配置
│   ├── package.json          # 前端依赖
│   └── index.html            # HTML 模板
├── Data/                     # 数据访问层
├── Models/                   # 数据模型
├── Services/                 # 业务服务
├── scripts/                  # 部署脚本
├── Dockerfile               # Docker 构建文件
├── Program.cs               # 应用入口
└── appsettings.json         # 配置文件
```

## 🔧 开发指南

### 微前端架构支持

本模板已集成微前端架构支持：

1. **路径基础配置**: 支持通过环境变量配置基础路径
2. **认证集成**: 与主应用共享认证状态
3. **API 代理**: 自动配置 axios 基础路径
4. **构建优化**: 支持生产环境路径重写

### 认证机制

- 使用 `AuthService` 与主应用通信获取认证信息
- 支持 JWT Token 自动刷新
- 处理认证过期自动跳转

### 数据访问

- 使用只读认证上下文访问主应用用户数据
- 客户数据上下文管理客户专用表
- 不参与 EF 迁移，手动管理数据表

## 📦 部署

### Docker 部署

```bash
# 构建镜像
docker build -t slzr-customer-[客户名称] .

# 运行容器
docker run -d \
  --name customer-[客户名称] \
  -p 8080:80 \
  -e ASPNETCORE_BASEPATH=/[客户路径] \
  -e VITE_BASE_PATH=/[客户路径]/ \
  slzr-customer-[客户名称]
```

### Nginx 配置

在主项目的 Nginx 配置中添加客户项目路由：

```nginx
# 客户项目路由
location /[客户路径]/ {
    rewrite ^/[客户路径]/(.*)$ /$1 break;
    proxy_pass http://customer-[客户名称]:80;
    # ... 其他代理配置
}

# 客户项目 API 路由
location /api/[客户路径]/ {
    rewrite ^/api/[客户路径]/(.*)$ /api/$1 break;
    proxy_pass http://customer-[客户名称]:80;
    # ... 其他代理配置
}
```

## 📝 注意事项

1. **命名规范**: 客户名称使用英文，避免特殊字符
2. **数据库权限**: 确保客户用户只能访问自己的表
3. **路径配置**: 前后端路径配置必须保持一致
4. **版本同步**: 保持与主项目相同的依赖版本

## 🔄 更新模板

当主项目有重要更新时，需要同步更新模板项目：

1. 检查主项目的架构改进
2. 更新模板项目的配置文件
3. 同步依赖版本
4. 更新文档说明
