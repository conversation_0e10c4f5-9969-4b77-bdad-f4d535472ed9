using Microsoft.AspNetCore.Identity;
using SlzrCrossGate.Core.Models;
using SlzrCrossGate.WebAdmin.Services;
using System.Diagnostics;
using System.Security.Claims;
using System.Text.Json;

namespace SlzrCrossGate.WebAdmin.Middleware
{
    /// <summary>
    /// 审计日志中间件
    /// </summary>
    public class AuditLogMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<AuditLogMiddleware> _logger;

        public AuditLogMiddleware(RequestDelegate next, ILogger<AuditLogMiddleware> logger)
        {
            _next = next;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context, AuditLogService auditLogService, UserManager<ApplicationUser> userManager, SessionActivityService sessionActivityService)
        {
            // 只记录API请求
            if (!context.Request.Path.StartsWithSegments("/api"))
            {
                await _next(context);
                return;
            }

            // 排除审计日志自身的API
            if (context.Request.Path.StartsWithSegments("/api/AuditLogs"))
            {
                await _next(context);
                return;
            }

            // 排除登录相关的API（已在AuthController中单独处理）
            if (context.Request.Path.StartsWithSegments("/api/Auth"))
            {
                await _next(context);
                return;
            }

            // 排除会话管理的API（系统自动发起，不需要记录操作日志）
            if (context.Request.Path.StartsWithSegments("/api/Session"))
            {
                // 只有refresh和heartbeat操作才更新用户活动状态
                // status检查是系统自动发起的，不应该更新活动时间
                var path = context.Request.Path.Value?.ToLower();
                var shouldUpdateActivity = path != null &&
                    (path.EndsWith("/refresh") || path.EndsWith("/heartbeat"));

                if (shouldUpdateActivity && context.User.Identity?.IsAuthenticated == true)
                {
                    var user = await userManager.GetUserAsync(context.User);
                    if (user != null)
                    {
                        sessionActivityService.UpdateUserActivity(user.Id, user.UserName);
                    }
                }

                await _next(context);
                return;
            }

            var stopwatch = Stopwatch.StartNew();
            var originalBodyStream = context.Response.Body;

            try
            {
                using var responseBody = new MemoryStream();
                context.Response.Body = responseBody;

                await _next(context);

                stopwatch.Stop();

                // 只记录需要认证的操作
                if (context.User.Identity?.IsAuthenticated == true)
                {
                    var user = await userManager.GetUserAsync(context.User);
                    if (user != null)
                    {
                        // 更新用户活动状态
                        sessionActivityService.UpdateUserActivity(user.Id, user.UserName);
                    }

                    await LogOperationAsync(context, auditLogService, userManager, stopwatch.ElapsedMilliseconds);
                }

                // 复制响应内容到原始流
                context.Response.Body = originalBodyStream;
                responseBody.Seek(0, SeekOrigin.Begin);
                await responseBody.CopyToAsync(originalBodyStream);
            }
            catch (Exception ex)
            {
                stopwatch.Stop();

                // 记录失败的操作
                if (context.User.Identity?.IsAuthenticated == true)
                {
                    await LogOperationAsync(context, auditLogService, userManager, stopwatch.ElapsedMilliseconds, ex);
                }

                throw;
            }
            finally
            {
                context.Response.Body = originalBodyStream;
            }
        }

        private async Task LogOperationAsync(
            HttpContext context,
            AuditLogService auditLogService,
            UserManager<ApplicationUser> userManager,
            long executionTime,
            Exception? exception = null)
        {
            try
            {
                var user = await userManager.GetUserAsync(context.User);
                if (user == null) return;

                var module = GetModuleFromPath(context.Request.Path);
                var operationType = GetOperationTypeFromMethod(context.Request.Method, context.Request.Path);
                var operationTarget = GetOperationTarget(context.Request.Path);
                var isSuccess = exception == null && context.Response.StatusCode < 400;
                var failureReason = exception?.Message ?? (context.Response.StatusCode >= 400 ? $"HTTP {context.Response.StatusCode}" : null);

                await auditLogService.LogOperationAsync(
                    userId: user.Id,
                    userName: user.UserName,
                    realName: user.RealName,
                    merchantId: user.MerchantID,
                    merchantName: null,
                    module: module,
                    operationType: operationType,
                    operationTarget: operationTarget,
                    isSuccess: isSuccess,
                    failureReason: failureReason,
                    requestPath: context.Request.Path,
                    httpMethod: context.Request.Method,
                    responseStatusCode: context.Response.StatusCode,
                    executionTime: executionTime
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "记录操作日志时发生错误");
            }
        }

        private string GetModuleFromPath(PathString path)
        {
            var segments = path.Value?.Split('/', StringSplitOptions.RemoveEmptyEntries);
            if (segments == null || segments.Length < 2) return "未知";

            var controller = segments[1].ToLower();
            return controller switch
            {
                "dashboard" => "仪表盘",
                "users" => "用户管理",
                "roles" => "角色管理",
                "merchants" => "商户管理",
                "terminals" => "终端管理",
                "vehicles" => "车辆管理",
                "files" => "文件管理",
                "filetypes" => "文件管理",
                "fileversions" => "文件管理",
                "filepublish" => "文件管理",
                "scheduledfilepublish" => "预约管理",
                "messages" => "消息管理",
                "messagetypes" => "消息管理",
                "systemsettings" => "系统设置",
                "dictionary" => "字典管理",
                "merchantdictionary" => "字典管理",
                "fareprices" or "lineprice" => "票价参数",
                "farediscountscheme" => "票价折扣方案",
                "unionpayterminalkeys" => "银联密钥管理",
                "consumedata" => "终端记录",
                "terminalevents" => "终端事件",
                "terminalagent" => "终端代理",
                "terminallog" => "终端日志记录",
                "unregisteredlines" => "未注册线路",
                "menus" => "菜单管理",
                "auditlogs" => "审计日志",
                "session" => "会话管理",
                "auth" => "身份认证",
                "permissions" => "权限管理",
                _ => "其他"
            };
        }

        private string GetOperationTypeFromMethod(string method, PathString path)
        {
            // 对于特殊路径，提供更具体的操作类型
            var pathValue = path.Value?.ToLower();

            if (pathValue != null)
            {
                // 文件发布相关的特殊操作
                if (pathValue.Contains("/filepublish") && method.ToUpper() == "POST")
                {
                    return "发布";
                }

                // 文件上传
                if (pathValue.Contains("/upload") && method.ToUpper() == "POST")
                {
                    return "上传";
                }

                // 文件下载
                if (pathValue.Contains("/download") && method.ToUpper() == "GET")
                {
                    return "下载";
                }

                // 历史记录查询
                if (pathValue.Contains("/history") && method.ToUpper() == "GET")
                {
                    return "查询历史";
                }

                // 状态查询
                if (pathValue.Contains("/status") && method.ToUpper() == "GET")
                {
                    return "查询状态";
                }
            }

            // 默认的HTTP方法映射
            return method.ToUpper() switch
            {
                "GET" => "查询",
                "POST" => "创建",
                "PUT" => "更新",
                "DELETE" => "删除",
                "PATCH" => "更新",
                _ => "其他"
            };
        }

        private string? GetOperationTarget(PathString path)
        {
            var segments = path.Value?.Split('/', StringSplitOptions.RemoveEmptyEntries);
            if (segments == null || segments.Length < 2) return null;

            var controller = segments[1].ToLower();

            // 特殊路径处理
            if (segments.Length >= 3)
            {
                var action = segments[2].ToLower();

                // 文件相关的特殊操作
                if (controller == "filepublish")
                {
                    if (action == "history")
                    {
                        return "文件发布历史";
                    }
                    else if (Guid.TryParse(action, out _) || int.TryParse(action, out _))
                    {
                        return $"文件发布#{action}";
                    }
                    else
                    {
                        return "文件发布";
                    }
                }

                if (controller == "fileversions")
                {
                    if (action == "download")
                    {
                        return segments.Length >= 4 ? $"文件版本#{segments[3]}" : "文件版本下载";
                    }
                    else if (Guid.TryParse(action, out _) || int.TryParse(action, out _))
                    {
                        return $"文件版本#{action}";
                    }
                }

                if (controller == "files" && action == "upload")
                {
                    return "文件上传";
                }

                // 通用ID处理
                if (Guid.TryParse(action, out _) || int.TryParse(action, out _))
                {
                    return $"{GetResourceName(controller)}#{action}";
                }

                // 如果不是ID，可能是子资源或操作
                return $"{GetResourceName(controller)}/{action}";
            }

            return GetResourceName(controller);
        }

        private string GetResourceName(string controller)
        {
            return controller.ToLower() switch
            {
                "dashboard" => "仪表盘数据",
                "users" => "用户",
                "roles" => "角色",
                "merchants" => "商户",
                "terminals" => "终端",
                "vehicles" => "车辆",
                "files" => "文件",
                "filetypes" => "文件类型",
                "fileversions" => "文件版本",
                "filepublish" => "文件发布",
                "scheduledfilepublish" => "预约发布",
                "messages" => "消息",
                "messagetypes" => "消息类型",
                "systemsettings" => "系统设置",
                "dictionary" => "字典",
                "merchantdictionary" => "商户字典",
                "fareprices" => "票价",
                "lineprice" => "线路票价",
                "farediscountscheme" => "票价折扣方案",
                "unionpayterminalkeys" => "银联密钥",
                "consumedata" => "消费记录",
                "terminalevents" => "终端事件",
                "unregisteredlines" => "未注册线路",
                "menus" => "菜单",
                _ => controller
            };
        }
    }

    /// <summary>
    /// 审计日志中间件扩展
    /// </summary>
    public static class AuditLogMiddlewareExtensions
    {
        public static IApplicationBuilder UseAuditLog(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<AuditLogMiddleware>();
        }
    }
}
