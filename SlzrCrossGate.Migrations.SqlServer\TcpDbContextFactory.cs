using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using Microsoft.Extensions.Configuration;
using SlzrCrossGate.Core.Database;

namespace SlzrCrossGate.Migrations.SqlServer
{
    public class TcpDbContextFactory : IDesignTimeDbContextFactory<TcpDbContext>
    {
        public TcpDbContext CreateDbContext(string[] args)
        {
            // 构建配置
            var configuration = new ConfigurationBuilder()
                .SetBasePath(Path.Combine(Directory.GetCurrentDirectory(), "../SlzrCrossGate.WebAdmin"))
                .AddJsonFile("appsettings.json", optional: false)
                .Build();

            var optionsBuilder = new DbContextOptionsBuilder<TcpDbContext>();
            
            var connectionString = configuration.GetConnectionString("DefaultConnection");
            
            // 强制使用SQL Server并指定迁移程序集
            optionsBuilder.UseSqlServer(connectionString, options =>
                options.MigrationsAssembly("SlzrCrossGate.Migrations.SqlServer"));

            return new TcpDbContext(optionsBuilder.Options, configuration);
        }
    }
}
