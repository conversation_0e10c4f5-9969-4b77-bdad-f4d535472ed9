# 多数据库支持测试指南

## 测试目的
验证项目是否正确支持MySQL和SQL Server两种数据库。

## 测试步骤

### 1. MySQL支持测试（默认配置）

```
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;Port=3306;Database=tcpserver;User=root;Password=**********;SslMode=Required;AllowLoadLocalInfile=true;"
  },
  "DatabaseProvider": "MySql",
```
当前配置已经是MySQL，可以直接测试：

```bash
cd SlzrCrossGate.WebAdmin
dotnet run --launch-profile https
```

观察启动日志，应该看到：
- 数据库连接诊断成功
- MySQL版本信息
- 健康检查配置正确

### 2. SQL Server支持测试

#### 2.1 修改配置文件

将 `appsettings.json` 中的配置修改为：

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost,1433;Database=tcpserver;User Id=sa;Password=YourPassword123!;TrustServerCertificate=true;MultipleActiveResultSets=true"
  },
  "DatabaseProvider": "SqlServer"
}
```

#### 2.2 启动测试

```bash
dotnet run --launch-profile https
```

观察启动日志，应该看到：
- 数据库提供程序识别为SQL Server
- 连接字符串解析正确（主机和端口）
- 健康检查使用SQL Server配置

### 3. 验证点

#### 3.1 连接字符串解析
- MySQL: 应该正确解析Server和Port
- SQL Server: 应该正确解析DataSource，支持server,port格式

#### 3.2 健康检查配置
- MySQL: 使用AddMySql
- SQL Server: 使用AddSqlServer

#### 3.3 数据库连接测试
- MySQL: 使用MySqlConnection和MySQL特定查询
- SQL Server: 使用SqlConnection和SQL Server特定查询

#### 3.4 错误诊断
- MySQL: 显示MySQL特定错误代码和诊断信息
- SQL Server: 显示SQL Server特定错误代码和诊断信息

## 预期结果

### MySQL模式启动日志示例
```
[WebAdmin] 开始数据库连接诊断...
[WebAdmin] 数据库提供程序: MySql
测试网络连接到 localhost:3306...
网络连接成功到 localhost:3306
开始测试数据库连接...
数据库连接成功! 耗时: XXXms
MySQL 版本: 8.0.XX
```

### SQL Server模式启动日志示例
```
[WebAdmin] 开始数据库连接诊断...
[WebAdmin] 数据库提供程序: SqlServer
测试网络连接到 localhost:1433...
网络连接成功到 localhost:1433
开始测试数据库连接...
数据库连接成功! 耗时: XXXms
数据库版本: Microsoft SQL Server 2019
```

## 故障排除

### 常见问题

1. **编译错误**: 确保已添加必要的NuGet包
   - AspNetCore.HealthChecks.SqlServer
   - Microsoft.Data.SqlClient

2. **连接失败**: 检查数据库服务是否运行
   - MySQL: 确保MySQL服务运行在3306端口
   - SQL Server: 确保SQL Server服务运行在1433端口

3. **权限问题**: 确保数据库用户有足够权限
   - 创建数据库权限
   - 读写数据权限

## 配置切换

要在MySQL和SQL Server之间切换，只需要：

1. 修改 `ConnectionStrings.DefaultConnection`
2. 修改 `DatabaseProvider` 为 "MySql" 或 "SqlServer"
3. 重启应用

无需修改代码，系统会自动适配不同的数据库类型。
