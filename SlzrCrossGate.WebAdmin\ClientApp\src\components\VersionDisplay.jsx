import React, { useState, useEffect } from 'react';
import {
  Chip,
  Tooltip,
  Box,
  Typography,
  alpha
} from '@mui/material';
import {
  Info as InfoIcon,
  Update as UpdateIcon
} from '@mui/icons-material';
import { useTheme } from '../contexts/ThemeContext';

/**
 * 版本显示组件
 * 在导航栏显示当前系统版本信息
 */
const VersionDisplay = ({ versionInfo, onVersionUpdate }) => {
  const { mode, theme } = useTheme();
  const [displayVersion, setDisplayVersion] = useState(null);

  useEffect(() => {
    if (versionInfo) {
      setDisplayVersion(versionInfo);
    }
  }, [versionInfo]);

  // 如果没有版本信息，不显示组件
  if (!displayVersion) {
    return null;
  }

  const formatVersion = (version) => {
    if (!version) return 'Unknown';
    
    // 如果版本号是 x.x.x.x 格式，只显示前三位
    const parts = version.split('.');
    if (parts.length >= 3) {
      return `v${parts[0]}.${parts[1]}.${parts[2]}`;
    }
    
    return `v${version}`;
  };

  const formatBuildTime = (buildTimeString) => {
    if (!buildTimeString) return '';
    
    try {
      const date = new Date(buildTimeString);
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch {
      return buildTimeString;
    }
  };

  const tooltipContent = (
    <Box>
      <Typography variant="subtitle2" sx={{ fontWeight: 'bold', mb: 1 }}>
        系统版本信息
      </Typography>
      <Typography variant="body2" sx={{ mb: 0.5 }}>
        版本号: {displayVersion.version || 'Unknown'}
      </Typography>
      <Typography variant="body2">
        构建时间: {formatBuildTime(displayVersion.buildTimeString)}
      </Typography>
    </Box>
  );

  return (
    <Tooltip 
      title={tooltipContent}
      placement="bottom"
      arrow
      componentsProps={{
        tooltip: {
          sx: {
            backgroundColor: mode === 'dark' 
              ? alpha(theme.palette.background.paper, 0.95)
              : alpha(theme.palette.background.paper, 0.98),
            color: theme.palette.text.primary,
            border: `1px solid ${mode === 'dark' 
              ? 'rgba(255, 255, 255, 0.12)' 
              : 'rgba(0, 0, 0, 0.08)'}`,
            backdropFilter: 'blur(8px)',
            maxWidth: 280
          }
        }
      }}
    >
      <Chip
        icon={<InfoIcon fontSize="small" />}
        label={formatVersion(displayVersion.version)}
        variant="outlined"
        size="small"
        sx={{
          ml: 1,
          height: 28,
          fontSize: '0.75rem',
          fontWeight: 500,
          borderColor: mode === 'dark' 
            ? alpha(theme.palette.primary.light, 0.3)
            : alpha(theme.palette.primary.main, 0.3),
          color: mode === 'dark' 
            ? theme.palette.primary.light
            : theme.palette.primary.main,
          backgroundColor: mode === 'dark'
            ? alpha(theme.palette.primary.dark, 0.1)
            : alpha(theme.palette.primary.light, 0.1),
          transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
          '&:hover': {
            borderColor: mode === 'dark' 
              ? theme.palette.primary.light
              : theme.palette.primary.main,
            backgroundColor: mode === 'dark'
              ? alpha(theme.palette.primary.dark, 0.2)
              : alpha(theme.palette.primary.light, 0.2),
            transform: 'scale(1.02)'
          },
          '& .MuiChip-icon': {
            color: 'inherit',
            fontSize: '0.875rem'
          },
          '& .MuiChip-label': {
            paddingLeft: '6px',
            paddingRight: '8px'
          }
        }}
      />
    </Tooltip>
  );
};

export default VersionDisplay;
