import React, { useState, useEffect } from 'react';
import {
    Box,
    Paper,
    Typography,
    Button,
    TextField,
    FormControl,
    FormControlLabel,
    Switch,
    Alert,
    Snackbar,
    Divider,
    CircularProgress
} from '@mui/material';
import {
    Save as SaveIcon,
    ArrowBack as ArrowBackIcon
} from '@mui/icons-material';
import { useNavigate, useParams } from 'react-router-dom';
import { fareDiscountSchemeAPI, linePriceAPI } from '../../services/api';
import LinePriceVersionEditForm from '../fare-params/LinePriceVersionEditForm';

const FareDiscountSchemeEditView = () => {
    const navigate = useNavigate();
    const { id } = useParams();
    const [loading, setLoading] = useState(false);
    const [initialLoading, setInitialLoading] = useState(true);
    const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });

    const [formData, setFormData] = useState({
        schemeName: '',
        description: '',
        isActive: true
    });

    const [extraParams, setExtraParams] = useState({

    });

    const [cardDiscountInfo, setCardDiscountInfo] = useState([
        {

        }
    ]);

    const [originalData, setOriginalData] = useState(null);
    const [errors, setErrors] = useState({});

    // 字典配置
    const [extraParamsConfig, setExtraParamsConfig] = useState([]);
    const [cardDiscountConfig, setCardDiscountConfig] = useState([]);

    const showSnackbar = (message, severity = 'success') => {
        setSnackbar({ open: true, message, severity });
    };

    // 加载字典配置
    const fetchDictionaryConfigs = async (merchantID) => {
        try {
            console.log("加载字典配置 - merchantID:", merchantID);
            if (!merchantID) {
                console.error('商户ID为空，无法加载字典配置');
                return;
            }

            // 加载线路参数字典配置
            const extraParamsConfigResponse = await linePriceAPI.getDictionaryConfig(merchantID, 'BUS_LINE_EXTRA_FARE_CONFIG');
            setExtraParamsConfig(extraParamsConfigResponse || []);

            // 加载卡类折扣字典配置
            const cardDiscountConfigResponse = await linePriceAPI.getDictionaryConfig(merchantID, 'BUS_ICCARD_FARE_RULE_CONFIG');
            setCardDiscountConfig(cardDiscountConfigResponse || []);
        } catch (error) {
            console.error('获取字典配置失败:', error);
            showSnackbar('获取字典配置失败', 'error');
        }
    };

    // 加载方案详情
    const loadSchemeDetail = async () => {
        setInitialLoading(true);
        try {
            const response = await fareDiscountSchemeAPI.getScheme(id);
            const scheme = response;
            
            setOriginalData(scheme);
            setFormData({
                schemeName: scheme.schemeName || '',
                description: scheme.description || '',
                isActive: scheme.isActive
            });

            if (scheme.extraParams) {
                setExtraParams(scheme.extraParams);
            }

            if (scheme.cardDiscountInfo && Array.isArray(scheme.cardDiscountInfo)) {
                setCardDiscountInfo(scheme.cardDiscountInfo);
            }

            // 加载字典配置
            if (scheme.merchantID) {
                await fetchDictionaryConfigs(scheme.merchantID);
            }
        } catch (error) {
            console.error('加载方案详情失败:', error);
            showSnackbar('加载方案详情失败', 'error');
            navigate('/app/fare-discount-schemes');
        } finally {
            setInitialLoading(false);
        }
    };

    useEffect(() => {
        if (id) {
            loadSchemeDetail();
        }
    }, [id]);

    const validateForm = () => {
        const newErrors = {};

        if (!formData.schemeName.trim()) {
            newErrors.schemeName = '方案名称不能为空';
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleInputChange = (field, value) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }));
        
        // 清除对应字段的错误
        if (errors[field]) {
            setErrors(prev => ({
                ...prev,
                [field]: undefined
            }));
        }
    };

    const handleSave = async () => {
        if (!validateForm()) {
            showSnackbar('请检查表单输入', 'error');
            return;
        }

        setLoading(true);
        try {
            const data = {
                ...formData,
                extraParams: extraParams,
                cardDiscountInfo: cardDiscountInfo
            };

            await fareDiscountSchemeAPI.updateScheme(id, data);
            showSnackbar('更新票价折扣方案成功');
            
            // 延迟跳转，让用户看到成功消息
            setTimeout(() => {
                navigate('/app/fare-discount-schemes');
            }, 1500);
        } catch (error) {
            console.error('更新票价折扣方案失败:', error);
            showSnackbar(error.response?.data?.message || '更新失败', 'error');
        } finally {
            setLoading(false);
        }
    };

    const handleBack = () => {
        navigate('/app/fare-discount-schemes');
    };

    if (initialLoading) {
        return (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
                <CircularProgress />
            </Box>
        );
    }

    return (
        <Box sx={{ p: 3 }}>
            <Paper sx={{ p: 3 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                    <Typography variant="h5" component="h1">
                        编辑票价折扣方案
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 2 }}>
                        <Button
                            variant="outlined"
                            startIcon={<ArrowBackIcon />}
                            onClick={handleBack}
                        >
                            返回
                        </Button>
                        <Button
                            variant="contained"
                            startIcon={<SaveIcon />}
                            onClick={handleSave}
                            disabled={loading}
                        >
                            保存更改
                        </Button>
                    </Box>
                </Box>

                {/* 商户信息 */}
                <Typography variant="h6" sx={{ mb: 2 }}>
                    商户信息
                </Typography>

                <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(2, 1fr)', gap: 3, mb: 3 }}>
                    <TextField
                        label="商户ID"
                        value={originalData?.merchantID || ''}
                        disabled
                        helperText="商户信息创建后不可修改"
                    />
                    <TextField
                        label="商户名称"
                        value={originalData?.merchantName || originalData?.merchantID || ''}
                        disabled
                        helperText="商户信息创建后不可修改"
                    />
                </Box>

                <Divider sx={{ my: 3 }} />

                {/* 基本信息 */}
                <Typography variant="h6" sx={{ mb: 2 }}>
                    基本信息
                </Typography>

                <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(2, 1fr)', gap: 3, mb: 3 }}>
                    <TextField
                        label="方案名称"
                        required
                        value={formData.schemeName}
                        onChange={(e) => handleInputChange('schemeName', e.target.value)}
                        error={!!errors.schemeName}
                        helperText={errors.schemeName}
                        placeholder="如：标准折扣方案"
                    />
                    <TextField
                        label="方案编码"
                        value={originalData?.schemeCode || ''}
                        disabled
                        helperText="方案编码创建后不可修改"
                    />
                </Box>

                <TextField
                    label="方案描述"
                    multiline
                    rows={3}
                    fullWidth
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    placeholder="描述此方案的用途和特点..."
                    sx={{ mb: 3 }}
                />

                <FormControlLabel
                    control={
                        <Switch
                            checked={formData.isActive}
                            onChange={(e) => handleInputChange('isActive', e.target.checked)}
                        />
                    }
                    label="启用方案"
                    sx={{ mb: 3 }}
                />

                <Divider sx={{ my: 3 }} />

                {/* 票价参数配置 */}
                <Typography variant="h6" sx={{ mb: 2 }}>
                    票价参数配置
                </Typography>
                
                <LinePriceVersionEditForm
                    extraParams={extraParams}
                    cardDiscountInfo={cardDiscountInfo}
                    onExtraParamsChange={setExtraParams}
                    onCardDiscountInfoChange={setCardDiscountInfo}
                    hideSubmitButton={true}
                    extraParamsConfig={extraParamsConfig}
                    cardDiscountConfig={cardDiscountConfig}
                />
            </Paper>

            {/* 消息提示 */}
            <Snackbar
                open={snackbar.open}
                autoHideDuration={6000}
                onClose={() => setSnackbar({ ...snackbar, open: false })}
            >
                <Alert
                    onClose={() => setSnackbar({ ...snackbar, open: false })}
                    severity={snackbar.severity}
                    sx={{ width: '100%' }}
                >
                    {snackbar.message}
                </Alert>
            </Snackbar>
        </Box>
    );
};

export default FareDiscountSchemeEditView;
