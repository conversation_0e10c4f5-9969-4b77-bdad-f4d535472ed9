import { useState, useEffect, useCallback, useMemo } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { getAllPermissionKeys } from '../constants/permissions';

/**
 * 全局权限管理器
 * 确保权限只加载一次，避免重复请求
 */
class GlobalPermissionManager {
  constructor() {
    this.memoryCache = new Map(); // 内存缓存
    this.cacheExpiry = 5 * 60 * 1000; // 5分钟过期
    this.loadingPromises = new Map(); // 正在加载的Promise，避免重复请求
    this.subscribers = new Set(); // 订阅者列表
  }

  // 获取缓存的权限
  get(userId) {
    // 只检查内存缓存
    const memoryData = this.memoryCache.get(userId);
    if (memoryData && !this.isExpired(memoryData.timestamp)) {
      return memoryData.permissions;
    }

    return null;
  }

  // 设置权限缓存
  set(userId, permissions) {
    const data = {
      permissions,
      timestamp: Date.now()
    };

    // 只设置内存缓存
    this.memoryCache.set(userId, data);
  }

  // 清除缓存
  clear(userId) {
    if (userId) {
      this.memoryCache.delete(userId);
    } else {
      this.memoryCache.clear();
    }
  }

  // 检查是否过期
  isExpired(timestamp) {
    return Date.now() - timestamp > this.cacheExpiry;
  }

  /**
   * 订阅权限变更
   */
  subscribe(callback) {
    this.subscribers.add(callback);
    return () => this.subscribers.delete(callback);
  }

  /**
   * 通知所有订阅者
   */
  notifySubscribers(permissions) {
    this.subscribers.forEach(callback => {
      try {
        callback(permissions);
      } catch (error) {
        console.error('权限订阅者回调错误:', error);
      }
    });
  }

  /**
   * 加载权限（防重复请求）
   */
  async loadPermissions(userId) {
    const cacheKey = `permissions_${userId}`;

    // 如果正在加载，返回现有的Promise
    if (this.loadingPromises.has(cacheKey)) {
      return this.loadingPromises.get(cacheKey);
    }

    // 检查缓存
    const cached = this.get(userId);
    if (cached) {
      return cached;
    }

    // 创建加载Promise
    const loadingPromise = this.fetchPermissions();
    this.loadingPromises.set(cacheKey, loadingPromise);

    try {
      const permissions = await loadingPromise;
      this.set(userId, permissions);
      this.notifySubscribers(permissions);
      return permissions;
    } finally {
      this.loadingPromises.delete(cacheKey);
    }
  }

  /**
   * 实际的API请求
   */
  async fetchPermissions() {
    try {
      // 使用现有的permissionAPI
      const { permissionAPI } = await import('../services/api');
      const response = await permissionAPI.getUserPermissions();

      // 检查响应数据
      const responseData = response?.data || response || {};
      if (typeof responseData !== 'object' || responseData === null) {
        throw new Error('权限API返回的数据格式不正确');
      }

      return responseData;
    } catch (error) {
      console.error('权限加载错误:', error);
      throw error;
    }
  }
}

const permissionManager = new GlobalPermissionManager();

/**
 * 获取默认权限配置 (降级方案)
 */
const getDefaultPermissions = (roles = []) => {
  const defaultPerms = {};
  
  if (roles.includes('SystemAdmin')) {
    // 系统管理员默认拥有所有权限
    getAllPermissionKeys().forEach(key => {
      defaultPerms[key] = true;
    });
  } else if (roles.includes('MerchantAdmin')) {
    // 商户管理员默认权限 - 基础权限但不包括删除和创建商户
    const basicPermissions = [
      'merchant.view_details', 'merchant.edit', 'merchant.toggle_active',
      'user.view_details', 'user.edit', 'user.create', 'user.lock', 'user.reset_password',
      'terminal.view_details', 'terminal.view_events', 'terminal.send_message', 'terminal.publish_file',
      'file_type.view', 'file_type.create', 'file_type.edit',
      'file_version.view', 'file_version.create', 'file_version.edit', 'file_version.publish',
      'message_type.view', 'message_type.create', 'message_type.edit',
      'line_price.view', 'line_price.create', 'line_price.edit',
      'unionpay_key.view', 'unionpay_key.create', 'unionpay_key.edit',
      'fare_discount_scheme.view', 'fare_discount_scheme.create', 'fare_discount_scheme.edit', 'fare_discount_scheme.submit_version'
    ];
    basicPermissions.forEach(key => {
      defaultPerms[key] = true;
    });

    // 明确禁用的权限
    const deniedPermissions = [
      'merchant.create', 'merchant.delete', 'user.delete', 'system.settings', 'system.feature_control'
    ];
    deniedPermissions.forEach(key => {
      defaultPerms[key] = false;
    });
  } else {
    // 普通用户默认只有查看权限
    const viewPermissions = [
      'merchant.view_details', 'user.view_details', 'terminal.view_details', 'terminal.view_events',
      'file_type.view', 'file_version.view', 'message_type.view', 'line_price.view',
      'unionpay_key.view', 'fare_discount_scheme.view', 'publish_record.view'
    ];
    viewPermissions.forEach(key => {
      defaultPerms[key] = true;
    });
  }
  
  return defaultPerms;
};

/**
 * 功能权限控制 Hook (优化版)
 * 
 * 特性:
 * 1. 多层缓存策略
 * 2. 批量权限预加载
 * 3. 权限变更实时通知
 * 4. 错误降级处理
 */
/**
 * 页面级权限检查Hook
 * 用于检查用户是否有访问整个页面的权限
 */
export const usePagePermission = (requiredPermissions = []) => {
  const { checkMultiple, loading } = useFeaturePermission();

  const permissions = useMemo(() => {
    if (loading || requiredPermissions.length === 0) return {};
    return checkMultiple(requiredPermissions);
  }, [checkMultiple, loading, requiredPermissions]);

  const hasAllPermissions = useMemo(() => {
    return requiredPermissions.every(key => permissions[key] === true);
  }, [permissions, requiredPermissions]);

  const hasAnyPermission = useMemo(() => {
    return requiredPermissions.some(key => permissions[key] === true);
  }, [permissions, requiredPermissions]);

  return {
    permissions,
    hasAllPermissions,
    hasAnyPermission,
    loading
  };
};

/**
 * 批量按钮权限Hook
 * 用于表格或列表中的批量权限检查
 */
export const useBatchPermissions = (items, getPermissionKey) => {
  const { hasPermission } = useFeaturePermission();

  return useMemo(() => {
    return items.map(item => ({
      ...item,
      permissions: {
        canEdit: hasPermission(getPermissionKey(item, 'edit')),
        canDelete: hasPermission(getPermissionKey(item, 'delete')),
        canView: hasPermission(getPermissionKey(item, 'view'))
      }
    }));
  }, [items, hasPermission, getPermissionKey]);
};

/**
 * 条件权限Hook
 * 结合业务逻辑的复杂权限判断
 */
export const useConditionalPermission = (featureKey, conditions = {}) => {
  const { hasPermission } = useFeaturePermission();
  const { user } = useAuth();

  return useMemo(() => {
    // 基础权限检查
    if (!hasPermission(featureKey)) {
      return { allowed: false, reason: 'insufficient_permission' };
    }

    // 业务条件检查
    for (const [conditionName, conditionFn] of Object.entries(conditions)) {
      if (typeof conditionFn === 'function' && !conditionFn(user)) {
        return { allowed: false, reason: conditionName };
      }
    }

    return { allowed: true, reason: null };
  }, [hasPermission, featureKey, conditions, user]);
};

export const useFeaturePermission = () => {
  const { user } = useAuth();
  const [permissions, setPermissions] = useState(new Map());
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // 权限加载
  const loadPermissions = useCallback(async () => {
    if (!user?.id) {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // 使用全局权限管理器获取权限
      const responseData = await permissionManager.loadPermissions(user.id);

      // 转换为权限映射
      const permissionMap = new Map();
      if (responseData.permissions && Array.isArray(responseData.permissions)) {
        responseData.permissions.forEach(permission => {
          permissionMap.set(permission.feature, permission.hasPermission);
        });
      } else if (typeof responseData === 'object') {
        // 兼容旧格式
        Object.entries(responseData).forEach(([key, value]) => {
          permissionMap.set(key, value);
        });
      }

      // 3. 更新状态
      setPermissions(permissionMap);

    } catch (err) {
      console.error('Failed to load permissions:', err);
      setError(err);

      // 降级到默认权限
      const defaultPermissions = getDefaultPermissions(user?.roles || []);
      setPermissions(new Map(Object.entries(defaultPermissions)));

    } finally {
      setLoading(false);
    }
  }, [user?.id]);

  // 权限检查方法
  const hasPermission = useCallback((featureKey) => {
    return permissions.get(featureKey) === true;
  }, [permissions]);
  
  const canPerform = useCallback((featureKey, additionalCheck = true) => {
    return hasPermission(featureKey) && additionalCheck;
  }, [hasPermission]);
  
  const checkMultiple = useCallback((featureKeys) => {
    return featureKeys.reduce((acc, key) => {
      acc[key] = hasPermission(key);
      return acc;
    }, {});
  }, [hasPermission]);

  // 刷新权限
  const refreshPermissions = useCallback(() => {
    if (user?.id) {
      permissionManager.clear(user.id);
      loadPermissions();
    }
  }, [loadPermissions, user?.id]);

  // 订阅权限变更
  useEffect(() => {
    if (!user?.id) return;

    // 订阅权限管理器的变更通知
    const unsubscribe = permissionManager.subscribe((newPermissions) => {
      // 转换为权限映射
      const permissionMap = new Map();
      if (newPermissions.permissions && Array.isArray(newPermissions.permissions)) {
        newPermissions.permissions.forEach(permission => {
          permissionMap.set(permission.feature, permission.hasPermission);
        });
      } else if (typeof newPermissions === 'object') {
        // 兼容旧格式
        Object.entries(newPermissions).forEach(([key, value]) => {
          permissionMap.set(key, value);
        });
      }

      setPermissions(permissionMap);
      setLoading(false);
    });

    // 初始加载
    loadPermissions();

    return unsubscribe;
  }, [user?.id, loadPermissions]);

  // 监听权限变更事件
  useEffect(() => {
    const handlePermissionChange = () => {
      refreshPermissions();
    };

    // WebSocket事件监听
    if (window.socket) {
      window.socket.on('permission_changed', handlePermissionChange);
    }

    // 定时刷新 (备用方案)
    const interval = setInterval(refreshPermissions, 5 * 60 * 1000); // 5分钟

    return () => {
      if (window.socket) {
        window.socket.off('permission_changed', handlePermissionChange);
      }
      clearInterval(interval);
    };
  }, []); // 移除依赖，避免无限循环

  return {
    permissions: Object.fromEntries(permissions), // 转换为普通对象
    loading,
    error,
    hasPermission,
    canPerform,
    checkMultiple,
    refreshPermissions
  };
};
