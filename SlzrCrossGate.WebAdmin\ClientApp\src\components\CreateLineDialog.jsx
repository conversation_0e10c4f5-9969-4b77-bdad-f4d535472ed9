import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Grid,
  FormControlLabel,
  Switch,
  Typography,
  Box,
  CircularProgress
} from '@mui/material';
import { useSnackbar } from 'notistack';
import { linePriceAPI } from '../services/api';

const CreateLineDialog = ({ open, onClose, lineData, onSuccess }) => {
  const { enqueueSnackbar } = useSnackbar();
  const [loading, setLoading] = useState(false);
  const [fareDisplay, setFareDisplay] = useState('2.00');
  const [formData, setFormData] = useState({
    merchantID: '',
    lineNumber: '',
    groupNumber: '',
    lineName: '',
    branch: '', // 分公司
    fare: 200, // 默认票价200分(2元)
    isActive: true,
    remark: ''
  });

  // 当对话框打开且有线路数据时，自动填充表单
  useEffect(() => {
    if (open && lineData) {
      const lineNO = lineData.lineNO || '';
      
      // 解析线路编号：前4位作为线路编号，第5、6位作为组号
      let lineNumber = '';
      let groupNumber = '';
      
      if (lineNO.length >= 4) {
        lineNumber = lineNO.substring(0, 4);
        if (lineNO.length >= 6) {
          groupNumber = lineNO.substring(4, 6);
        }
      } else {
        lineNumber = lineNO;
      }

      setFormData({
        merchantID: lineData.merchantID || '',
        lineNumber: lineNumber,
        groupNumber: groupNumber,
        lineName: `线路${lineNumber}${groupNumber ? `-${groupNumber}` : ''}`,
        fare: 200,
        isActive: true,
        remark: ''
      });

      // 设置票价显示值
      setFareDisplay(formatFareDisplay(200));
    }
  }, [open, lineData]);

  // 处理表单字段变更
  const handleChange = (event) => {
    const { name, value, type, checked } = event.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  // 票价显示转换（分转元）
  const formatFareDisplay = (fareInCents) => {
    return (fareInCents / 100).toFixed(2);
  };

  // 票价输入变化（只更新显示值）
  const handleFareChange = (event) => {
    setFareDisplay(event.target.value);
  };

  // 票价失去焦点时转换并验证
  const handleFareBlur = (event) => {
    const fareInYuan = parseFloat(event.target.value) || 0;
    const fareInCents = Math.round(fareInYuan * 100);
    setFormData(prev => ({
      ...prev,
      fare: fareInCents
    }));
    setFareDisplay(formatFareDisplay(fareInCents));
  };

  // 提交表单
  const handleSubmit = async (event) => {
    event.preventDefault();
    
    // 表单验证
    if (!formData.merchantID || !formData.lineNumber || !formData.lineName) {
      enqueueSnackbar('请填写必填字段', { variant: 'error' });
      return;
    }

    if (formData.lineNumber.length !== 4 || !/^[A-Za-z0-9]+$/.test(formData.lineNumber)) {
      enqueueSnackbar('线路编号必须为4位数字或英文字母', { variant: 'error' });
      return;
    }

    if (formData.groupNumber && formData.groupNumber.length !== 2) {
      enqueueSnackbar('组号必须为2位或留空', { variant: 'error' });
      return;
    }

    try {
      setLoading(true);

      // 准备提交数据
      const submitData = {
        merchantID: formData.merchantID,
        lineNumber: formData.lineNumber,
        groupNumber: formData.groupNumber || '',
        lineName: formData.lineName,
        fare: formData.fare,
        isActive: formData.isActive,
        remark: formData.remark
      };

      await linePriceAPI.createLinePrice(submitData);
      enqueueSnackbar('线路创建成功', { variant: 'success' });
      
      // 调用成功回调
      if (onSuccess) {
        onSuccess();
      }
      
      // 关闭对话框
      onClose();
    } catch (error) {
      console.error('创建线路失败:', error);
      let errorMsg = '创建线路失败';
      if (error.response?.data?.message) {
        errorMsg = error.response.data.message;
      } else if (error.response?.data) {
        errorMsg = error.response.data;
      }
      enqueueSnackbar(errorMsg, { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };

  // 关闭对话框时重置表单
  const handleClose = () => {
    if (!loading) {
      setFormData({
        merchantID: '',
        lineNumber: '',
        groupNumber: '',
        lineName: '',
        fare: 200,
        isActive: true,
        remark: ''
      });
      setFareDisplay('2.00'); // 重置票价显示
      onClose();
    }
  };

  return (
    <Dialog 
      open={open} 
      onClose={handleClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          boxShadow: '0 8px 32px rgba(0,0,0,0.12)'
        }
      }}
    >
      <DialogTitle>
        <Typography variant="h6" component="div">
          创建线路
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
          基于未注册线路信息快速创建线路票价配置
        </Typography>
      </DialogTitle>

      <DialogContent dividers>
        <Box component="form" onSubmit={handleSubmit}>
          <Grid container spacing={3}>
            {/* 商户ID - 只读显示 */}
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="商户ID"
                name="merchantID"
                value={formData.merchantID}
                onChange={handleChange}
                required
                disabled
                helperText="自动从未注册线路数据填充"
              />
            </Grid>

            {/* 线路编号和组号 */}
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="线路编号"
                name="lineNumber"
                value={formData.lineNumber}
                onChange={handleChange}
                required
                inputProps={{ maxLength: 4, pattern: '[A-Za-z0-9]{4}' }}
                placeholder="如：101A、B002"
                helperText="4位数字或字母，可编辑"
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="组号"
                name="groupNumber"
                value={formData.groupNumber}
                onChange={handleChange}
                inputProps={{ maxLength: 2, pattern: '[0-9]{2}' }}
                placeholder="如：01、02（可选）"
                helperText="2位数字，可留空"
              />
            </Grid>

            {/* 线路名称 */}
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="线路名称"
                name="lineName"
                value={formData.lineName}
                onChange={handleChange}
                required
                inputProps={{ maxLength: 100 }}
                placeholder="如：市区环线"
                helperText="请输入线路名称"
              />
            </Grid>

            {/* 分公司和票价 */}
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="分公司"
                name="branch"
                value={formData.branch}
                onChange={handleChange}
                inputProps={{ maxLength: 100 }}
                placeholder="如：第一分公司（可选）"
                helperText="可选，最多100字符"
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="票价（元）"
                name="fare"
                type="number"
                value={fareDisplay}
                onChange={handleFareChange}
                onBlur={handleFareBlur}
                required
                inputProps={{
                  min: 0.5,
                  max: 999.99,
                  step: 0.5
                }}
                placeholder="2.00"
                helperText="票价单位为元"
              />
            </Grid>

            {/* 状态开关 */}
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.isActive}
                    onChange={handleChange}
                    name="isActive"
                    color="primary"
                  />
                }
                label="启用状态"
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                label="备注"
                name="remark"
                value={formData.remark}
                onChange={handleChange}
                multiline
                rows={3}
                inputProps={{ maxLength: 500 }}
                helperText="可选，最多500字符"
              />
            </Grid>
          </Grid>
        </Box>
      </DialogContent>

      <DialogActions sx={{ px: 3, py: 2 }}>
        <Button 
          onClick={handleClose} 
          disabled={loading}
          variant="outlined"
        >
          取消
        </Button>
        <Button 
          onClick={handleSubmit}
          disabled={loading}
          variant="contained"
          startIcon={loading ? <CircularProgress size={20} /> : null}
        >
          {loading ? '创建中...' : '创建线路'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default CreateLineDialog;
