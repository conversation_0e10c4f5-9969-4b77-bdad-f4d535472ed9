using Microsoft.Extensions.Caching.Memory;
using SlzrCrossGate.Core.Models;
using Microsoft.AspNetCore.Identity;

namespace SlzrCrossGate.WebAdmin.Services
{
    /// <summary>
    /// 用户会话活动服务
    /// </summary>
    public class SessionActivityService
    {
        private readonly IMemoryCache _cache;
        private readonly ILogger<SessionActivityService> _logger;
        private readonly TimeSpan _sessionTimeout;

        public SessionActivityService(
            IMemoryCache cache,
            ILogger<SessionActivityService> logger,
            IConfiguration configuration)
        {
            _cache = cache;
            _logger = logger;

            // 从配置读取会话超时时间，默认30分钟
            var timeoutMinutes = configuration.GetValue<int>("SessionTimeout:Minutes", 60);
            _sessionTimeout = TimeSpan.FromMinutes(timeoutMinutes);
        }

        /// <summary>
        /// 更新用户活动状态（只更新已存在的会话）
        /// </summary>
        public void UpdateUserActivity(string userId, string? userName = null)
        {
            if (string.IsNullOrEmpty(userId)) return;

            try
            {
                var key = GetUserActivityKey(userId);

                // 检查会话是否已存在
                if (!_cache.TryGetValue(key, out var existingActivity))
                {
                    _logger.LogWarning("尝试更新不存在的用户会话: {UserId}", userId);
                    return; // 不自动创建会话，要求用户重新登录
                }

                var activityInfo = new UserActivityInfo
                {
                    UserId = userId,
                    UserName = userName,
                    LastActivityTime = DateTime.Now,
                    UpdateCount = GetCurrentUpdateCount(userId) + 1
                };

                // 使用滑动过期时间
                _cache.Set(key, activityInfo, new MemoryCacheEntryOptions
                {
                    SlidingExpiration = _sessionTimeout,
                    Priority = CacheItemPriority.Normal
                });

                _logger.LogDebug("用户活动已更新: {UserId}, 时间: {Time}", userId, DateTime.Now);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新用户活动状态失败: {UserId}", userId);
            }
        }

        /// <summary>
        /// 创建用户会话（仅在登录时调用）
        /// </summary>
        public void CreateUserSession(string userId, string? userName = null)
        {
            if (string.IsNullOrEmpty(userId)) return;

            try
            {
                var key = GetUserActivityKey(userId);
                var activityInfo = new UserActivityInfo
                {
                    UserId = userId,
                    UserName = userName,
                    LastActivityTime = DateTime.Now,
                    UpdateCount = 1
                };

                // 使用滑动过期时间
                _cache.Set(key, activityInfo, new MemoryCacheEntryOptions
                {
                    SlidingExpiration = _sessionTimeout,
                    Priority = CacheItemPriority.Normal
                });

                _logger.LogInformation("用户会话已创建: {UserId}, 时间: {Time}", userId, DateTime.Now);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建用户会话失败: {UserId}", userId);
            }
        }

        /// <summary>
        /// 检查用户是否活跃
        /// </summary>
        public bool IsUserActive(string userId)
        {
            if (string.IsNullOrEmpty(userId)) return false;

            try
            {
                var key = GetUserActivityKey(userId);
                var isActive = _cache.TryGetValue(key, out var activityInfo);

                if (isActive && activityInfo is UserActivityInfo info)
                {
                    var timeSinceLastActivity = DateTime.Now - info.LastActivityTime;
                    var stillActive = timeSinceLastActivity < _sessionTimeout;

                    _logger.LogDebug("用户活动检查: {UserId}, 最后活动: {LastActivity}, 是否活跃: {IsActive}",
                        userId, info.LastActivityTime, stillActive);

                    return stillActive;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查用户活动状态失败: {UserId}", userId);
                return false;
            }
        }

        /// <summary>
        /// 获取用户活动信息
        /// </summary>
        public UserActivityInfo? GetUserActivity(string userId)
        {
            if (string.IsNullOrEmpty(userId)) return null;

            try
            {
                var key = GetUserActivityKey(userId);
                _cache.TryGetValue(key, out var activityInfo);
                return activityInfo as UserActivityInfo;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户活动信息失败: {UserId}", userId);
                return null;
            }
        }

        /// <summary>
        /// 手动移除用户会话（用于登出）
        /// </summary>
        public void RemoveUserSession(string userId)
        {
            if (string.IsNullOrEmpty(userId)) return;

            try
            {
                var key = GetUserActivityKey(userId);
                _cache.Remove(key);
                _logger.LogInformation("用户会话已移除: {UserId}", userId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "移除用户会话失败: {UserId}", userId);
            }
        }

        /// <summary>
        /// 获取所有活跃用户数量
        /// </summary>
        public int GetActiveUserCount()
        {
            try
            {
                // 这里简化实现，实际可能需要更复杂的逻辑
                // 因为 IMemoryCache 不提供直接遍历所有键的方法
                return 0; // 可以考虑使用其他方式实现
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取活跃用户数量失败");
                return 0;
            }
        }

        /// <summary>
        /// 检查会话是否即将过期（用于前端警告）
        /// </summary>
        public SessionStatus GetSessionStatus(string userId)
        {
            var activity = GetUserActivity(userId);
            if (activity == null)
            {
                return new SessionStatus
                {
                    IsActive = false,
                    IsExpired = true,
                    TimeRemaining = TimeSpan.Zero
                };
            }

            var timeSinceLastActivity = DateTime.Now - activity.LastActivityTime;
            var timeRemaining = _sessionTimeout - timeSinceLastActivity;
            var isActive = timeRemaining > TimeSpan.Zero;
            var isNearExpiry = timeRemaining <= TimeSpan.FromMinutes(5); // 5分钟内过期

            return new SessionStatus
            {
                IsActive = isActive,
                IsExpired = !isActive,
                IsNearExpiry = isNearExpiry,
                TimeRemaining = timeRemaining > TimeSpan.Zero ? timeRemaining : TimeSpan.Zero,
                LastActivityTime = activity.LastActivityTime
            };
        }

        private string GetUserActivityKey(string userId)
        {
            return $"user_activity_{userId}";
        }

        private int GetCurrentUpdateCount(string userId)
        {
            var activity = GetUserActivity(userId);
            return activity?.UpdateCount ?? 0;
        }
    }

    /// <summary>
    /// 用户活动信息
    /// </summary>
    public class UserActivityInfo
    {
        public string UserId { get; set; } = string.Empty;
        public string? UserName { get; set; }
        public DateTime LastActivityTime { get; set; }
        public int UpdateCount { get; set; }
    }

    /// <summary>
    /// 会话状态信息
    /// </summary>
    public class SessionStatus
    {
        public bool IsActive { get; set; }
        public bool IsExpired { get; set; }
        public bool IsNearExpiry { get; set; }
        public TimeSpan TimeRemaining { get; set; }
        public DateTime? LastActivityTime { get; set; }
    }
}
