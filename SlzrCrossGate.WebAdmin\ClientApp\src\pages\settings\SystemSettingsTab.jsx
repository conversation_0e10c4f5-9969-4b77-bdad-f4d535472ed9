import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Switch,
  FormControlLabel,
  Button,
  Divider,
  Alert,
  Skeleton,
  Grid,
  Paper,
  TextField
} from '@mui/material';
import { LoadingButton } from '@mui/lab';
import { useSnackbar } from 'notistack';
import { systemSettingsAPI } from '../../services/api';
import { parseErrorMessage } from '../../utils/errorHandler';
import SecurityIcon from '@mui/icons-material/Security';
import ChatIcon from '@mui/icons-material/Chat';
import SaveIcon from '@mui/icons-material/Save';
import RefreshIcon from '@mui/icons-material/Refresh';

/**
 * 系统设置Tab组件
 */
const SystemSettingsTab = () => {
  const [settings, setSettings] = useState(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState(null);
  const { enqueueSnackbar } = useSnackbar();

  // 防重复请求
  const hasLoadedRef = useRef(false);

  // 加载系统设置
  const loadSettings = async () => {
    setLoading(true);
    setError(null);
    try {
      const data = await systemSettingsAPI.getSettings();
      setSettings(data);
    } catch (err) {
      console.error('加载系统设置失败:', err);
      const errorMessage = parseErrorMessage(err, '加载系统设置失败');
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // 保存系统设置
  const saveSettings = async () => {
    setSaving(true);
    setError(null);
    try {
      const updatedSettings = await systemSettingsAPI.updateSettings(settings);
      setSettings(updatedSettings);
      enqueueSnackbar('系统设置已保存', { variant: 'success' });
    } catch (err) {
      console.error('保存系统设置失败:', err);
      const errorMessage = parseErrorMessage(err, '保存系统设置失败');
      setError(errorMessage);
      enqueueSnackbar(errorMessage, { variant: 'error' });
    } finally {
      setSaving(false);
    }
  };

  // 处理开关变化
  const handleSwitchChange = (name) => (event) => {
    setSettings({
      ...settings,
      [name]: event.target.checked,
    });
  };

  // 处理数字输入变化
  const handleNumberChange = (name) => (event) => {
    const value = parseInt(event.target.value) || 0;
    setSettings({
      ...settings,
      [name]: value,
    });
  };

  // 初始加载
  useEffect(() => {
    // 防止重复加载
    if (hasLoadedRef.current) {
      console.log('SystemSettingsTab: 已加载过，跳过重复请求');
      return;
    }

    console.log('SystemSettingsTab: 执行首次加载');
    hasLoadedRef.current = true;
    loadSettings();
  }, []);

  return (
    <Box sx={{ p: 3 }}>
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {loading ? (
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Skeleton variant="rectangular" height={200} />
          </Grid>
          <Grid item xs={12} md={6}>
            <Skeleton variant="rectangular" height={200} />
          </Grid>
        </Grid>
      ) : (
        <>
          <Grid container spacing={3}>
            {/* 双因素认证设置 */}
            <Grid item xs={12} md={6}>
              <Paper
                elevation={2}
                sx={{
                  p: 3,
                  height: '100%',
                  background: 'rgba(255, 255, 255, 0.05)',
                  backdropFilter: 'blur(5px)',
                  borderRadius: 2,
                  border: '1px solid rgba(255, 255, 255, 0.1)',
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <SecurityIcon color="primary" sx={{ mr: 1 }} />
                  <Typography variant="h6">双因素认证设置</Typography>
                </Box>
                <Divider sx={{ mb: 2 }} />

                <FormControlLabel
                  control={
                    <Switch
                      checked={settings?.enableTwoFactorAuth || false}
                      onChange={handleSwitchChange('enableTwoFactorAuth')}
                      color="primary"
                    />
                  }
                  label="启用双因素认证"
                />
                <Typography variant="body2" color="text.secondary" sx={{ mt: 1, mb: 2 }}>
                  启用后，用户可以使用双因素认证增强账户安全性。禁用后，所有用户将无法使用双因素认证。
                </Typography>

                <FormControlLabel
                  control={
                    <Switch
                      checked={settings?.forceTwoFactorAuth || false}
                      onChange={handleSwitchChange('forceTwoFactorAuth')}
                      disabled={!settings?.enableTwoFactorAuth}
                      color="primary"
                    />
                  }
                  label="强制所有用户使用双因素认证"
                />
                <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                  启用后，所有用户必须设置并使用双因素认证才能登录系统。
                </Typography>
              </Paper>
            </Grid>

            {/* 微信登录设置 
            <Grid item xs={12} md={6}>
              <Paper
                elevation={2}
                sx={{
                  p: 3,
                  height: '100%',
                  background: 'rgba(255, 255, 255, 0.05)',
                  backdropFilter: 'blur(5px)',
                  borderRadius: 2,
                  border: '1px solid rgba(255, 255, 255, 0.1)',
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <ChatIcon color="primary" sx={{ mr: 1 }} />
                  <Typography variant="h6">微信登录设置</Typography>
                </Box>
                <Divider sx={{ mb: 2 }} />

                <FormControlLabel
                  control={
                    <Switch
                      checked={settings?.enableWechatLogin || false}
                      onChange={handleSwitchChange('enableWechatLogin')}
                      color="primary"
                    />
                  }
                  label="启用微信扫码登录"
                />
                <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                  启用后，用户可以使用微信扫码方式登录系统。禁用后，所有用户将无法使用微信登录。
                </Typography>
              </Paper>
            </Grid>
*/}
            {/* 密码策略设置 */}
            <Grid item xs={12} md={6}>
              <Paper
                elevation={2}
                sx={{
                  p: 3,
                  height: '100%',
                  background: 'rgba(255, 255, 255, 0.05)',
                  backdropFilter: 'blur(5px)',
                  borderRadius: 2,
                  border: '1px solid rgba(255, 255, 255, 0.1)',
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <SecurityIcon color="primary" sx={{ mr: 1 }} />
                  <Typography variant="h6">密码策略设置</Typography>
                </Box>
                <Divider sx={{ mb: 2 }} />

                <Box sx={{ mb: 2 }}>
                  <Typography variant="body1" sx={{ mb: 1 }}>
                    强制密码更改天数
                  </Typography>
                  <TextField
                    type="number"
                    value={settings?.forcePasswordChangeDays || 0}
                    onChange={handleNumberChange('forcePasswordChangeDays')}
                    inputProps={{ min: 0, max: 365 }}
                    size="small"
                    fullWidth
                    helperText="设置为0表示不强制更改密码，大于0表示密码使用天数超过此值时强制用户更改密码"
                  />
                </Box>

                <Typography variant="body2" color="text.secondary">
                  当用户密码使用天数达到设定值时，系统将在用户登录后强制要求更改密码。
                  设置为0表示不启用此功能。
                </Typography>
              </Paper>
            </Grid>
          </Grid>

          {/* 操作按钮 */}
          <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={loadSettings}
              disabled={loading || saving}
            >
              刷新
            </Button>
            <LoadingButton
              variant="contained"
              startIcon={<SaveIcon />}
              onClick={saveSettings}
              loading={saving}
              disabled={loading}
            >
              保存设置
            </LoadingButton>
          </Box>
        </>
      )}
    </Box>
  );
};

export default SystemSettingsTab;
