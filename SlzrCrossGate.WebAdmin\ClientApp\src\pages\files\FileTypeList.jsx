import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Paper,
  Typography,
  Grid,
  Button,
  TextField,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  FormHelperText,
  Autocomplete
} from '@mui/material';
import {
  ChevronRight as ChevronRight,
  Refresh as RefreshIcon,
  Search as SearchIcon,
  Clear as ClearIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useSnackbar } from 'notistack';
import { fileAPI, merchantAPI } from '../../services/api';
import { useAuth } from '../../contexts/AuthContext';
import { useMerchants } from '../../contexts/MerchantContext';
import MerchantAutocomplete from '../../components/MerchantAutocomplete';
import { FeatureGuard } from '../../components/FeatureGuard';
import { useFeaturePermission } from '../../hooks/useFeaturePermission';
import { PERMISSIONS } from '../../constants/permissions';

// 文件类型操作按钮组件
const FileTypeActionButtons = ({ fileType, onEdit, onDelete }) => {
  const { user } = useAuth();

  // 业务逻辑判断
  const canEdit = user.roles.includes('SystemAdmin') ||
    fileType.merchantID === user.merchantId;

  const canDelete = user.roles.includes('SystemAdmin') ||
    (fileType.merchantID === user.merchantId && !fileType.hasVersions);

  return (
    <Box sx={{ display: 'flex', gap: 0.5 }}>
      {/* 编辑按钮 */}
      <FeatureGuard featureKey={PERMISSIONS.FILE_TYPE.EDIT}>
        {canEdit ? (
          <Tooltip title="编辑">
            <IconButton
              size="small"
              color="primary"
              onClick={onEdit}
            >
              <EditIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        ) : (
          <Tooltip title="无编辑权限">
            <span>
              <IconButton size="small" disabled>
                <EditIcon fontSize="small" />
              </IconButton>
            </span>
          </Tooltip>
        )}
      </FeatureGuard>

      {/* 删除按钮 */}
      <FeatureGuard featureKey={PERMISSIONS.FILE_TYPE.DELETE}>
        {canDelete ? (
          <Tooltip title="删除">
            <IconButton
              size="small"
              color="error"
              onClick={onDelete}
            >
              <DeleteIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        ) : (
          <Tooltip title={
            fileType.hasVersions
              ? "该文件类型已有版本，无法删除"
              : "无删除权限"
          }>
            <span>
              <IconButton size="small" disabled>
                <DeleteIcon fontSize="small" />
              </IconButton>
            </span>
          </Tooltip>
        )}
      </FeatureGuard>
    </Box>
  );
};

const FileTypeList = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { enqueueSnackbar } = useSnackbar();
  const { merchants } = useMerchants();

  // 页面访问权限通过菜单权限控制，不再进行页面级别权限检查
  const [fileTypes, setFileTypes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  // 移除手动商户管理，使用MerchantAutocomplete内置功能
  const [isSystemAdmin, setIsSystemAdmin] = useState(false);

  // 筛选条件
  const [filters, setFilters] = useState({
    merchantID: '',
    code: '',
    name: ''
  });

  // 选中的商户对象（用于MerchantAutocomplete）
  const [selectedMerchant, setSelectedMerchant] = useState(null);
  const [dialogSelectedMerchant, setDialogSelectedMerchant] = useState(null);

  // 对话框状态
  const [openDialog, setOpenDialog] = useState(false);
  const [dialogMode, setDialogMode] = useState('create'); // 'create' or 'edit'
  const [currentFileType, setCurrentFileType] = useState({
    code: '',
    merchantID: '',
    name: '',
    remark: ''
  });
  const [dialogError, setDialogError] = useState('');
  const [dialogLoading, setDialogLoading] = useState(false);

  // 删除确认对话框
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [fileTypeToDelete, setFileTypeToDelete] = useState(null);
  const [deleteLoading, setDeleteLoading] = useState(false);

  // 防重复请求
  const loadFileTypesRef = useRef();
  const hasLoadedRef = useRef(false);

  // 加载文件类型列表
  const loadFileTypes = async (isInitialLoad = false, forceLoad = false) => {
    try {
      setLoading(true);

      // 构建查询参数
      const params = {
        page: page + 1,
        pageSize: rowsPerPage,
        ...Object.fromEntries(
          Object.entries(filters).filter(([_, value]) => value !== '')
        )
      };

      // 移除undefined的参数
      Object.keys(params).forEach(key => {
        if (params[key] === undefined) {
          delete params[key];
        }
      });

      // 避免重复请求：检查参数是否真的发生了变化（除非强制加载）
      const paramsString = JSON.stringify(params);
      if (!isInitialLoad && !forceLoad && loadFileTypesRef.current === paramsString) {
        console.log('FileTypeList: 参数未变化，跳过重复请求');
        setLoading(false);
        return;
      }
      loadFileTypesRef.current = paramsString;

      console.log('FileTypeList: 执行数据请求', params);
      const response = await fileAPI.getFileTypes(params);
      setFileTypes(response.items);
      setTotalCount(response.totalCount);
    } catch (error) {
      console.error('Error loading file types:', error);
    } finally {
      setLoading(false);
    }
  };

  // 统一的数据加载逻辑
  useEffect(() => {
    // 防止重复加载
    if (hasLoadedRef.current) {
      console.log('FileTypeList: 已加载过，跳过重复请求');
      return;
    }

    console.log('FileTypeList: 执行首次加载');
    hasLoadedRef.current = true;
    loadFileTypes(true, false); // 标记为初始加载，非强制加载
  }, []);

  // 当参数变化时重新加载
  useEffect(() => {
    if (hasLoadedRef.current) { // 只有在首次加载完成后才响应参数变化
      console.log('FileTypeList: 参数变化，重新加载');
      loadFileTypes(false, false); // 非初始加载，非强制加载
    }
  }, [page, rowsPerPage]);

  // 初始化：检查用户角色
  useEffect(() => {
    // 检查当前用户是否为系统管理员
    if (user?.roles) {
      const admin = user.roles.includes('SystemAdmin');
      setIsSystemAdmin(admin);

      // 如果不是系统管理员且有商户ID，则将筛选条件和当前文件类型的商户ID设为当前用户的商户ID
      if (!admin && user.merchantId) {
        setFilters(prev => ({ ...prev, merchantID: user.merchantId }));
        setCurrentFileType(prev => ({ ...prev, merchantID: user.merchantId }));
      }
    }
  }, [user]);

  // 同步筛选条件中的商户ID和商户对象
  useEffect(() => {
    if (merchants.length > 0 && filters.merchantID) {
      const merchant = merchants.find(m => m.merchantID === filters.merchantID);
      setSelectedMerchant(merchant || null);
    } else {
      setSelectedMerchant(null);
    }
  }, [merchants, filters.merchantID]);

  // 同步对话框中的商户ID和商户对象
  useEffect(() => {
    if (merchants.length > 0 && currentFileType.merchantID) {
      const merchant = merchants.find(m => m.merchantID === currentFileType.merchantID);
      setDialogSelectedMerchant(merchant || null);
    } else {
      setDialogSelectedMerchant(null);
    }
  }, [merchants, currentFileType.merchantID]);

  // 处理筛选条件变更
  const handleFilterChange = (event) => {
    const { name, value } = event.target;
    setFilters(prev => ({ ...prev, [name]: value }));
  };

  // 应用筛选
  const applyFilters = () => {
    setPage(0);
    loadFileTypes(false, true); // 强制执行搜索
  };

  // 清除筛选
  const clearFilters = () => {
    setFilters({
      merchantID: '',
      code: '',
      name: ''
    });
    setPage(0);
    loadFileTypes(false, true); // 强制执行搜索
  };

  // 处理分页变更
  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // 打开创建对话框
  const openCreateDialog = () => {
    setDialogMode('create');
    // 如果不是系统管理员且用户有商户ID，则自动设置
    if (!isSystemAdmin && user?.merchantId) {
      setCurrentFileType({
        code: '',
        merchantID: user.merchantId,
        name: '',
        remark: ''
      });
    } else {
      setCurrentFileType({
        code: '',
        merchantID: '',
        name: '',
        remark: ''
      });
    }
    setDialogError('');
    setOpenDialog(true);
  };

  // 打开编辑对话框
  const openEditDialog = (fileType) => {
    setDialogMode('edit');
    setCurrentFileType({
      code: fileType.code,
      merchantID: fileType.merchantID,
      name: fileType.name || '',
      remark: fileType.remark || ''
    });
    setDialogError('');
    setOpenDialog(true);
  };

  // 处理对话框输入变更
  const handleDialogInputChange = (event) => {
    const { name, value } = event.target;

    // 对code字段进行特殊处理，限制为三位字母或数字
    if (name === 'code') {
      // 只允许字母和数字
      const regex = /^[a-zA-Z0-9]*$/;
      if (!regex.test(value) && value !== '') {
        return; // 如果包含非法字符，不更新状态
      }
      // 限制最多输入3个字符
      const limitedValue = value.slice(0, 3);
      setCurrentFileType(prev => ({ ...prev, [name]: limitedValue.toUpperCase() }));
    } else {
      setCurrentFileType(prev => ({ ...prev, [name]: value }));
    }
  };

  // 验证类型代码是否有效（三位字母或数字）
  const isValidCode = (code) => {
    const regex = /^[a-zA-Z0-9]{3}$/;
    return regex.test(code);
  };

  // 保存文件类型
  const saveFileType = async () => {
    setDialogLoading(true);
    setDialogError('');

    try {
      if (dialogMode === 'create') {
        // 创建新文件类型
        await fileAPI.createFileType(currentFileType);
      } else {
        // 更新文件类型
        await fileAPI.updateFileType(
          currentFileType.code,
          currentFileType.merchantID,
          {
            name: currentFileType.name,
            remark: currentFileType.remark
          }
        );
      }

      setOpenDialog(false);
      loadFileTypes(false, true);
      enqueueSnackbar('操作成功', { variant: 'success' });
    } catch (error) {
      console.error('Error saving file type:', error);
      setDialogError(error.response?.data || '保存失败');
    } finally {
      setDialogLoading(false);
    }
  };

  // 打开删除确认对话框
  const openDeleteConfirmDialog = (fileType) => {
    setFileTypeToDelete(fileType);
    setOpenDeleteDialog(true);
  };

  // 删除文件类型
  const deleteFileType = async () => {
    if (!fileTypeToDelete) return;

    setDeleteLoading(true);
    try {
      await fileAPI.deleteFileType(fileTypeToDelete.code, fileTypeToDelete.merchantID);
      setOpenDeleteDialog(false);
      loadFileTypes(false,true);
      enqueueSnackbar('文件类型删除成功', { variant: 'success' });
    } catch (error) {
      console.error('Error deleting file type:', error);
      // 使用更友好的错误提示，并处理后端返回的具体错误信息
      let errorMessage = '删除失败';
      if (error.response?.data) {
        if (typeof error.response.data === 'string') {
          errorMessage = error.response.data;
        } else if (error.response.data.message) {
          errorMessage = error.response.data.message;
        }
      }
      // 使用更友好的错误提示方式，替换原来的alert
      enqueueSnackbar(errorMessage, { variant: 'error' });
      // 关闭删除确认对话框
      setOpenDeleteDialog(false);
    } finally {
      setDeleteLoading(false);
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">
        文件类型管理
        </Typography>
        <Button
          variant="outlined"
          startIcon={<ChevronRight />}
          onClick={() => navigate('/app/files/versions')}
        >
          文件版本管理
        </Button>
      </Box>

      {/* 筛选条件 */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={6} md={3}>
            <MerchantAutocomplete
              size="small"
              value={selectedMerchant}
              onChange={(event, newValue) => {
                setSelectedMerchant(newValue);
                setFilters(prev => ({
                  ...prev,
                  merchantID: newValue ? newValue.merchantID : ''
                }));
              }}
              disabled={Boolean(!isSystemAdmin && user?.merchantId)}
              label="商户"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <TextField
              fullWidth
              label="类型代码"
              name="code"
              value={filters.code}
              onChange={handleFilterChange}
              size="small"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <TextField
              fullWidth
              label="类型名称"
              name="name"
              value={filters.name}
              onChange={handleFilterChange}
              size="small"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={1}>
            <Button
              fullWidth
              variant="contained"
              color="primary"
              startIcon={<SearchIcon />}
              onClick={applyFilters}
            >
              搜索
            </Button>
          </Grid>
          <Grid item xs={12} sm={6} md={1}>
            <Button
              fullWidth
              variant="outlined"
              startIcon={<ClearIcon />}
              onClick={clearFilters}
            >
              清除
            </Button>
          </Grid>
          <Grid item xs={12} sm={6} md={1}>
            <Button
              fullWidth
              variant="outlined"
              color="primary"
              startIcon={<RefreshIcon />}
              onClick={loadFileTypes}
            >
              刷新
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {/* 操作按钮 */}
      <Box sx={{ mb: 2 }}>
        <FeatureGuard featureKey={PERMISSIONS.FILE_TYPE.CREATE}>
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            onClick={openCreateDialog}
          >
            新建文件类型
          </Button>
        </FeatureGuard>
      </Box>

      {/* 文件类型列表 */}
      <Paper>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>商户</TableCell>
                <TableCell>类型代码</TableCell>
                <TableCell>类型名称</TableCell>
                <TableCell>备注</TableCell>
                <TableCell>操作</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={5} align="center">
                    <CircularProgress size={24} />
                  </TableCell>
                </TableRow>
              ) : fileTypes.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={5} align="center">
                    没有找到文件类型
                  </TableCell>
                </TableRow>
              ) : (
                fileTypes.map((fileType) => (
                  <TableRow key={`${fileType.code}-${fileType.merchantID}`}>
                    <TableCell>
                      <Tooltip title={fileType.merchantID || ''}>
                        <span>{fileType.merchantName}</span>
                      </Tooltip>
                    </TableCell>
                    <TableCell>{fileType.code}</TableCell>
                    <TableCell>{fileType.name || '-'}</TableCell>
                    <TableCell>{fileType.remark || '-'}</TableCell>
                    <TableCell>
                      <FileTypeActionButtons
                        fileType={fileType}
                        onEdit={() => openEditDialog(fileType)}
                        onDelete={() => openDeleteConfirmDialog(fileType)}
                      />
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </TableContainer>
        <TablePagination
          rowsPerPageOptions={[5, 10, 25, 50]}
          component="div"
          count={totalCount}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
          labelRowsPerPage="每页行数:"
          labelDisplayedRows={({ from, to, count }) => `${from}-${to} 共 ${count}`}
        />
      </Paper>

      {/* 创建/编辑对话框 */}
      <Dialog open={openDialog} onClose={() => setOpenDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          {dialogMode === 'create' ? '新建文件类型' : '编辑文件类型'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="类型代码"
                  name="code"
                  value={currentFileType.code}
                  onChange={handleDialogInputChange}
                  disabled={dialogMode === 'edit'}
                  required
                  error={Boolean(dialogMode === 'create' && currentFileType.code && !isValidCode(currentFileType.code))}
                  helperText={
                    dialogMode === 'create'
                      ? (currentFileType.code && !isValidCode(currentFileType.code))
                        ? '类型代码必须为3位字母或数字'
                        : '请输入3位字母或数字（将自动转为大写）'
                      : ''
                  }
                  inputProps={{ maxLength: 3 }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <MerchantAutocomplete
                  value={dialogSelectedMerchant}
                  onChange={(event, newValue) => {
                    setDialogSelectedMerchant(newValue);
                    setCurrentFileType(prev => ({
                      ...prev,
                      merchantID: newValue ? newValue.merchantID : ''
                    }));
                  }}
                  disabled={Boolean(dialogMode === 'edit' || (!isSystemAdmin && user?.merchantId))}
                  required={true}
                  error={Boolean(!currentFileType.merchantID)}
                  size="medium"
                  helperText={!currentFileType.merchantID ? '请选择商户' : ''}
                  label="商户"
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="类型名称"
                  name="name"
                  value={currentFileType.name}
                  onChange={handleDialogInputChange}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="备注"
                  name="remark"
                  value={currentFileType.remark}
                  onChange={handleDialogInputChange}
                  multiline
                  rows={3}
                />
              </Grid>
            </Grid>
            {dialogError && (
              <Typography color="error" sx={{ mt: 2 }}>
                {dialogError}
              </Typography>
            )}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDialog(false)}>取消</Button>
          <Button
            onClick={saveFileType}
            variant="contained"
            color="primary"
            disabled={dialogLoading || !currentFileType.code || !currentFileType.merchantID || (dialogMode === 'create' && !isValidCode(currentFileType.code))}
          >
            {dialogLoading ? <CircularProgress size={24} /> : '保存'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* 删除确认对话框 */}
      <Dialog open={openDeleteDialog} onClose={() => setOpenDeleteDialog(false)}>
        <DialogTitle>确认删除</DialogTitle>
        <DialogContent>
          <Typography>
            确定要删除文件类型 "{fileTypeToDelete?.code}" 吗？此操作不可撤销。
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDeleteDialog(false)}>取消</Button>
          <Button
            onClick={deleteFileType}
            variant="contained"
            color="error"
            disabled={deleteLoading}
          >
            {deleteLoading ? <CircularProgress size={24} /> : '删除'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default FileTypeList;

