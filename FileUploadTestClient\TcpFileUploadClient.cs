using System.Net.Sockets;
using Microsoft.Extensions.Logging;
using FileUploadTestClient.Protocol;
using FileUploadTestClient.Utils;
using System.Buffers.Binary;

namespace FileUploadTestClient
{
    /// <summary>
    /// TCP文件上传测试客户端
    /// </summary>
    public class TcpFileUploadClient : IDisposable
    {
        private readonly ILogger<TcpFileUploadClient> _logger;
        private TcpClient? _tcpClient;
        private NetworkStream? _stream;
        private uint _requestIdCounter = 1;

        public TcpFileUploadClient(ILogger<TcpFileUploadClient> logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// 连接到服务器
        /// </summary>
        public async Task<bool> ConnectAsync(string host, int port)
        {
            try
            {
                _tcpClient = new TcpClient();
                await _tcpClient.ConnectAsync(host, port);
                _stream = _tcpClient.GetStream();
                
                _logger.LogInformation("Connected to {Host}:{Port}", host, port);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to connect to {Host}:{Port}", host, port);
                return false;
            }
        }

        /// <summary>
        /// 上传文件
        /// </summary>
        public async Task<bool> UploadFileAsync(string filePath, uint merchantId, uint terminalId, string terminalType = "POS")
        {
            if (_stream == null)
            {
                _logger.LogError("Not connected to server");
                return false;
            }

            if (!File.Exists(filePath))
            {
                _logger.LogError("File not found: {FilePath}", filePath);
                return false;
            }

            try
            {
                var fileInfo = new FileInfo(filePath);
                var fileName = fileInfo.Name;
                var fileSize = (uint)fileInfo.Length;
                var fileData = await File.ReadAllBytesAsync(filePath);
                var fileCrc = CRC32.Calculate(fileData);
                var fileType = Path.GetExtension(fileName).TrimStart('.').ToUpper();
                if (string.IsNullOrEmpty(fileType)) fileType = "DAT";

                _logger.LogInformation("Starting upload: {FileName}, Size: {Size} bytes, CRC: {CRC:X8}", 
                    fileName, fileSize, fileCrc);

                // 1. 发送文件上传请求
                var uploadRequest = new FileUploadRequestMessage
                {
                    RequestId = _requestIdCounter++,
                    MerchantId = merchantId,
                    TerminalSerialNumber = terminalId,
                    TerminalType = terminalType,
                    FileType = fileType,
                    FileName = fileName,
                    FileSize = fileSize,
                    FileCRC32 = fileCrc,
                    MaxChunkSize = 4096 // 4KB块大小
                };

                await SendMessageAsync(uploadRequest);
                _logger.LogInformation("Sent file upload request");

                // 2. 接收文件上传响应
                var uploadResponse = await ReceiveMessageAsync<FileUploadResponseMessage>();
                if (uploadResponse == null)
                {
                    _logger.LogError("Failed to receive upload response");
                    return false;
                }

                if (uploadResponse.Status != 0)
                {
                    _logger.LogError("Upload request failed, status: {Status}", uploadResponse.Status);
                    return false;
                }

                _logger.LogInformation("Upload request accepted, UploadId: {UploadId}, ServerChunkSize: {ChunkSize}",
                    uploadResponse.UploadFileId, uploadResponse.ServerChunkSize);

                // 3. 分块上传文件数据
                var chunkSize = (int)uploadResponse.ServerChunkSize;
                var totalChunks = (int)Math.Ceiling((double)fileSize / chunkSize);
                var uploadedBytes = 0;

                for (int chunkIndex = 0; chunkIndex < totalChunks; chunkIndex++)
                {
                    var offset = chunkIndex * chunkSize;
                    var currentChunkSize = Math.Min(chunkSize, fileData.Length - offset);
                    var chunkData = new byte[currentChunkSize];
                    Array.Copy(fileData, offset, chunkData, 0, currentChunkSize);

                    // 发送文件块
                    var chunkRequest = new FileChunkRequestMessage
                    {
                        RequestId = _requestIdCounter++,
                        UploadFileId = uploadResponse.UploadFileId,
                        ChunkOffset = (uint)offset,
                        ChunkSize = (uint)currentChunkSize,
                        ChunkData = chunkData
                    };

                    await SendMessageAsync(chunkRequest);
                    
                    // 接收文件块响应
                    var chunkResponse = await ReceiveMessageAsync<FileChunkResponseMessage>();
                    if (chunkResponse == null)
                    {
                        _logger.LogError("Failed to receive chunk response for chunk {ChunkIndex}", chunkIndex);
                        return false;
                    }

                    if (chunkResponse.TransferResult != 0)
                    {
                        _logger.LogError("Chunk transfer failed, chunk: {ChunkIndex}, result: {Result}", 
                            chunkIndex, chunkResponse.TransferResult);
                        return false;
                    }

                    uploadedBytes += currentChunkSize;
                    var progress = (double)uploadedBytes / fileSize * 100;
                    
                    _logger.LogInformation("Uploaded chunk {ChunkIndex}/{TotalChunks} ({Progress:F1}%), " +
                        "Next offset: {NextOffset}, Next size: {NextSize}",
                        chunkIndex + 1, totalChunks, progress, 
                        chunkResponse.NextChunkOffset, chunkResponse.NextChunkSize);

                    // 如果服务器返回的下一个块大小为0，表示上传完成
                    if (chunkResponse.NextChunkSize == 0)
                    {
                        _logger.LogInformation("Upload completed successfully!");
                        return true;
                    }
                }

                _logger.LogInformation("All chunks uploaded successfully!");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading file: {FilePath}", filePath);
                return false;
            }
        }

        /// <summary>
        /// 发送消息
        /// </summary>
        private async Task SendMessageAsync(FileUploadMessage message)
        {
            if (_stream == null) throw new InvalidOperationException("Not connected");

            var data = message.Pack();
            await _stream.WriteAsync(data);
            await _stream.FlushAsync();

            _logger.LogInformation("Sent message type {MessageType}, size: {Size} bytes: {DataHex}", 
                message.MessageType, data.Length, BitConverter.ToString(data).Replace("-", ""));
        }

        /// <summary>
        /// 接收消息
        /// </summary>
        private async Task<T?> ReceiveMessageAsync<T>() where T : FileUploadMessage, new()
        {
            if (_stream == null) throw new InvalidOperationException("Not connected");

            try
            {
                // 先读取消息头（16字节）
                var headerBuffer = new byte[16];
                var headerBytesRead = 0;
                while (headerBytesRead < 16)
                {
                    var bytesRead = await _stream.ReadAsync(headerBuffer.AsMemory(headerBytesRead, 16 - headerBytesRead));
                    if (bytesRead == 0)
                    {
                        _logger.LogError("Connection closed while reading header");
                        return null;
                    }
                    headerBytesRead += bytesRead;
                }

                // 解析消息体长度
                var bodyLength = BinaryPrimitives.ReadUInt32BigEndian(headerBuffer.AsSpan(12));
                
                // 读取消息体
                var bodyBuffer = new byte[bodyLength];
                var bodyBytesRead = 0;
                while (bodyBytesRead < bodyLength)
                {
                    var bytesRead = await _stream.ReadAsync(bodyBuffer.AsMemory(bodyBytesRead, (int)bodyLength - bodyBytesRead));
                    if (bytesRead == 0)
                    {
                        _logger.LogError("Connection closed while reading body");
                        return null;
                    }
                    bodyBytesRead += bytesRead;
                }

                // 组合完整消息
                var fullMessage = new byte[16 + bodyLength];
                headerBuffer.CopyTo(fullMessage, 0);
                bodyBuffer.CopyTo(fullMessage, 16);

                // 解析消息
                var message = FileUploadMessage.Parse<T>(fullMessage);
                
                _logger.LogInformation("Received message type {MessageType}, size: {Size} bytes: {DataHex}", 
                    message.MessageType, fullMessage.Length, BitConverter.ToString(fullMessage).Replace("-", ""));

                return message;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error receiving message");
                return null;
            }
        }

        /// <summary>
        /// 断开连接
        /// </summary>
        public void Disconnect()
        {
            try
            {
                _stream?.Close();
                _tcpClient?.Close();
                _logger.LogInformation("Disconnected from server");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error disconnecting");
            }
        }

        public void Dispose()
        {
            Disconnect();
            _stream?.Dispose();
            _tcpClient?.Dispose();
        }
    }
}
