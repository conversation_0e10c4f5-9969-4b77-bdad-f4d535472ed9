import React, { useState } from 'react';
import {
  Box,
  Typography,
  Button,
  Alert,
  Paper,
  Divider
} from '@mui/material';
import {
  ViewList as FieldIcon,
  Settings as ConfigIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { useFieldConfig } from '../../hooks/useFieldConfig';
import FieldConfigManager from '../../components/FieldConfigManager';
import { FeatureGuard } from '../../components/FeatureGuard';
import { PERMISSIONS } from '../../constants/permissions';

/**
 * 字段管理Tab组件
 */
const FieldManagementTab = () => {
  const [fieldConfigOpen, setFieldConfigOpen] = useState(false);
  
  // 使用字段配置hook
  const {
    fieldConfig,
    loading: configLoading,
    error: configError,
    reloadConfig,
    getConfigStats,
    getFieldCategories
  } = useFieldConfig();

  const stats = getConfigStats;

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
        <FieldIcon sx={{ mr: 1 }} />
        终端字段配置管理
      </Typography>
      
      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        管理终端列表页面的字段显示配置，包括字段启用状态、显示顺序、类型设置等。
      </Typography>

      {configError && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {configError}
        </Alert>
      )}

      {/* 配置概览 */}
      <Paper
        elevation={2}
        sx={{
          p: 3,
          mb: 3,
          background: 'rgba(255, 255, 255, 0.05)',
          backdropFilter: 'blur(5px)',
          borderRadius: 2,
          border: '1px solid rgba(255, 255, 255, 0.1)',
        }}
      >
        <Typography variant="h6" gutterBottom>
          配置概览
        </Typography>
        <Divider sx={{ mb: 2 }} />
        
        <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: 2 }}>
          <Box>
            <Typography variant="body2" color="text.secondary">
              配置版本
            </Typography>
            <Typography variant="h6">
              {fieldConfig?.version || '未知'}
            </Typography>
          </Box>
          
          <Box>
            <Typography variant="body2" color="text.secondary">
              字段分类
            </Typography>
            <Typography variant="h6">
              {stats.categories} 个分类
            </Typography>
          </Box>
          
          <Box>
            <Typography variant="body2" color="text.secondary">
              启用字段
            </Typography>
            <Typography variant="h6" color="primary.main">
              {stats.enabled} / {stats.total}
            </Typography>
          </Box>
          
          <Box>
            <Typography variant="body2" color="text.secondary">
              最后更新
            </Typography>
            <Typography variant="body1">
              {fieldConfig?.lastUpdated 
                ? new Date(fieldConfig.lastUpdated).toLocaleString('zh-CN')
                : '未知'
              }
            </Typography>
          </Box>
        </Box>
      </Paper>

      {/* 操作按钮 */}
      <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
        <FeatureGuard featureKey={PERMISSIONS.SYSTEM_FIELD_MANAGEMENT.OPEN_FIELD_CONFIG_MANAGER}>
          <Button
            variant="contained"
            startIcon={<ConfigIcon />}
            onClick={() => setFieldConfigOpen(true)}
            disabled={configLoading}
          >
            打开字段配置管理器
          </Button>
        </FeatureGuard>
        
        <Button
          variant="outlined"
          startIcon={<RefreshIcon />}
          onClick={reloadConfig}
          disabled={configLoading}
        >
          重新加载配置
        </Button>
      </Box>

      {/* 说明信息 */}
      <Alert severity="info" sx={{ mb: 3 }}>
        <Typography variant="body2">
          <strong>使用说明：</strong>
        </Typography>
        <Typography variant="body2" component="div" sx={{ mt: 1 }}>
          • 点击"打开字段配置管理器"查看和了解当前的字段配置<br/>
          • 字段配置文件位于：<code>/public/config/terminalFields.json</code><br/>
          • 目前字段管理器仅支持查看，如需修改请直接编辑配置文件<br/>
          • 修改配置文件后，点击"重新加载配置"使更改生效
        </Typography>
      </Alert>

      {/* 配置文件结构说明 */}
      <Paper
        elevation={1}
        sx={{
          p: 3,
          background: 'rgba(255, 255, 255, 0.02)',
          borderRadius: 2,
          border: '1px solid rgba(255, 255, 255, 0.05)',
        }}
      >
        <Typography variant="h6" gutterBottom>
          配置文件结构
        </Typography>
        <Divider sx={{ mb: 2 }} />
        
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          字段配置文件包含以下主要部分：
        </Typography>
        
        <Box component="ul" sx={{ pl: 2, '& li': { mb: 1 } }}>
          <li>
            <Typography variant="body2">
              <strong>fieldCategories</strong> - 字段分类，如基础信息、状态信息、设备属性等
            </Typography>
          </li>
          <li>
            <Typography variant="body2">
              <strong>fields</strong> - 具体字段定义，包括显示名称、数据路径、类型、排序等
            </Typography>
          </li>
          <li>
            <Typography variant="body2">
              <strong>renderTypes</strong> - 支持的字段渲染类型，如文本、日期、状态等
            </Typography>
          </li>
          <li>
            <Typography variant="body2">
              <strong>defaultSettings</strong> - 默认的字段启用和列设置
            </Typography>
          </li>
        </Box>
      </Paper>

      {/* 字段配置管理器对话框 */}
      <FieldConfigManager
        open={fieldConfigOpen}
        onClose={() => setFieldConfigOpen(false)}
        fieldConfig={fieldConfig}
        loading={configLoading}
        error={configError}
        onReloadConfig={reloadConfig}
        getConfigStats={getConfigStats}
        getFieldCategories={getFieldCategories}
      />
    </Box>
  );
};

export default FieldManagementTab;
