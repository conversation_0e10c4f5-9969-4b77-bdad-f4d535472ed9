# 珠海通定制查询系统

## 项目概述

这是为珠海通客户定制开发的查询功能模块，提供专业的数据查询和分析服务。

## 功能特性

- ✅ **系统查询**：支持终端状态、交易汇总、商户信息等多种查询
- ✅ **查询历史**：记录和查看历史查询记录
- ✅ **用户认证**：与主系统共享认证状态
- ✅ **数据隔离**：按商户隔离数据，确保安全性
- ✅ **响应式设计**：支持桌面和移动设备

## 技术架构

### 后端技术栈
- .NET 8.0
- Entity Framework Core
- MySQL数据库
- JWT认证
- Serilog日志

### 前端技术栈
- React 18
- Material-UI 5
- Vite构建工具
- Axios HTTP客户端

## 快速开始

### 1. 环境要求

- .NET 8.0 SDK
- Node.js 18+
- MySQL 8.0+

### 2. 数据库设置

```sql
-- 执行数据库脚本
mysql -u root -p < create-zhuhaitong-tables.sql
```

### 3. 后端启动

```bash
# 进入项目目录
cd SlzrCrossGate.CustomerZhuhaitong

# 还原依赖
dotnet restore

# 启动开发服务器
dotnet run --urls="http://localhost:5271"
```

### 4. 前端启动

```bash
# 进入前端目录
cd ClientApp

# 安装依赖
npm install

# 启动开发服务器
npm start
```

### 5. 访问应用

- 后端API: http://localhost:5271
- 前端应用: http://localhost:3001
- API文档: http://localhost:5271/swagger

## 配置说明

### 数据库连接

在 `appsettings.json` 中配置数据库连接：

```json
{
  "ConnectionStrings": {
    "AuthConnection": "Server=localhost;Database=tcpserver;Uid=customer_readonly;Pwd=readonly_password;",
    "CustomerConnection": "Server=localhost;Database=tcpserver;Uid=root;Pwd=**********;"
  }
}
```

### JWT配置

确保JWT配置与主系统一致：

```json
{
  "Jwt": {
    "Key": "your-secret-key-here-must-be-at-least-32-characters-long",
    "Issuer": "SlzrCrossGate",
    "Audience": "SlzrCrossGate.Users"
  }
}
```

## 部署指南

### 1. 生产环境构建

```bash
# 构建后端
dotnet publish -c Release -o ./publish

# 构建前端
cd ClientApp
npm run build
```

### 2. Docker部署

```dockerfile
# Dockerfile
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 80

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src
COPY ["SlzrCrossGate.CustomerZhuhaitong.csproj", "."]
RUN dotnet restore
COPY . .
RUN dotnet build -c Release -o /app/build

FROM build AS publish
RUN dotnet publish -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "SlzrCrossGate.CustomerZhuhaitong.dll"]
```

### 3. Nginx配置

```nginx
# 添加到主应用的nginx配置
location /zhuhaitong/ {
    rewrite ^/zhuhaitong/(.*)$ /$1 break;
    proxy_pass http://customer-zhuhaitong:80;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header X-Customer-Path /zhuhaitong;
}
```

## 菜单集成

在主系统数据库中添加菜单项：

```sql
-- 添加菜单项
INSERT INTO MenuItems (
    MenuGroupId, ItemKey, Title, Href, IconName, SortOrder, 
    IsEnabled, VisibleToSystemAdmin, VisibleToMerchantAdmin, VisibleToUser, 
    IsExternal, Target
)
VALUES (
    (SELECT Id FROM MenuGroups WHERE GroupKey = 'customer-features'),
    'zhuhaitong-query',
    '珠海通查询',
    'https://yourdomain.com/zhuhaitong/',
    'SearchIcon',
    1, 1, 1, 1, 0, 1, '_iframe'
);
```

## API接口

### 查询接口

- `POST /api/query/execute` - 执行查询
- `GET /api/query/history` - 获取查询历史
- `GET /api/query/types` - 获取查询类型
- `GET /api/query/health` - 健康检查

### 请求示例

```javascript
// 执行查询
const response = await axios.post('/api/query/execute', {
  queryType: 'terminal_status',
  parameters: {}
});
```

## 开发指南

### 添加新的查询类型

1. 在 `QueryService.cs` 中添加新的查询方法
2. 在 `PerformSystemQuery` 方法中添加新的case
3. 在前端 `QueryPage.jsx` 中更新查询类型列表

### 数据库表结构

- `Customer_Zhuhaitong_QueryRecords` - 查询记录表
- `Customer_Zhuhaitong_QueryConfigs` - 查询配置表

## 故障排除

### 常见问题

1. **认证失败**
   - 检查JWT配置是否与主系统一致
   - 确认token传递机制正常

2. **数据库连接失败**
   - 检查连接字符串配置
   - 确认数据库用户权限

3. **前端无法加载**
   - 检查代理配置
   - 确认API接口可访问

## 联系支持

如有问题，请联系开发团队或查看项目文档。

## 版本历史

- v1.0.0 - 初始版本，包含基础查询功能
