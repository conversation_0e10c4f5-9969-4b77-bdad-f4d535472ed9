import React, { useState, useEffect } from 'react';
import {
  <PERSON>ton,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  TextField,
  Grid,
  CircularProgress,
  FormControlLabel,
  Switch,
  Box,
  Typography
} from '@mui/material';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { useSnackbar } from 'notistack';
import { linePriceAPI } from '../../services/api';
import { parseErrorMessage } from '../../utils/errorHandler';
import { useAuth } from '../../contexts/AuthContext';
import MerchantAutocomplete from '../../components/MerchantAutocomplete';

const LinePriceCreateDialog = ({ open, onClose, onCreated }) => {
  const { enqueueSnackbar } = useSnackbar();
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);

  // 表单验证规则
  const validationSchema = Yup.object({
    merchantID: Yup.string().required('请选择商户'),
    lineNumber: Yup.string()
      .required('请输入线路编号')
      .matches(/^[A-Za-z0-9]{4}$/, '线路编号必须是4位数字或字母'),
    groupNumber: Yup.string()
      .matches(/^(\d{2}|)$/, '组号必须为空或2位数字'),
    lineName: Yup.string()
      .required('请输入线路名称')
      .max(100, '线路名称不能超过100个字符'),
    branch: Yup.string()
      .max(50, '分公司名称不能超过50个字符'),
    fare: Yup.number()
      .required('请输入票价')
      .min(1, '票价必须大于0')
      .max(99999, '票价不能超过999.99元'),
    remark: Yup.string()
      .max(500, '备注不能超过500个字符')
  });

  // 票价显示状态（用于输入过程中的显示）
  const [fareDisplay, setFareDisplay] = useState('2.00');

  // 表单处理
  const formik = useFormik({
    initialValues: {
      merchantID: user?.roles?.includes('SystemAdmin') ? '' : (user?.merchantId || ''),
      merchantObj: null,
      lineNumber: '',
      groupNumber: '',
      lineName: '',
      branch: '',
      fare: 200, // 默认票价200分(2元)
      isActive: true,
      remark: ''
    },
    validationSchema,
    onSubmit: async (values) => {
      try {
        setLoading(true);

        // 准备提交数据
        const submitData = {
          merchantID: values.merchantID,
          lineNumber: values.lineNumber,
          groupNumber: values.groupNumber || '',
          lineName: values.lineName,
          branch: values.branch || '',
          fare: values.fare,
          isActive: values.isActive,
          remark: values.remark || ''
        };

        await linePriceAPI.createLinePrice(submitData);
        enqueueSnackbar('创建成功', { variant: 'success' });
        
        // 调用成功回调
        if (onCreated) {
          onCreated();
        }
        
        // 关闭对话框并重置表单
        handleClose();
      } catch (error) {
        console.error('创建失败:', error);
        const errorMessage = parseErrorMessage(error, '创建失败');
        enqueueSnackbar(errorMessage, { variant: 'error' });
      } finally {
        setLoading(false);
      }
    }
  });

  // 处理商户选择变化
  const handleMerchantChange = (event, newValue) => {
    formik.setFieldValue('merchantObj', newValue);
    formik.setFieldValue('merchantID', newValue ? newValue.merchantID : '');
  };

  // 关闭对话框
  const handleClose = () => {
    formik.resetForm();
    setFareDisplay('2.00'); // 重置票价显示
    onClose();
  };

  // 票价显示转换（分转元）
  const formatFareDisplay = (fareInCents) => {
    return (fareInCents / 100).toFixed(2);
  };

  // 票价输入变化（只更新显示值）
  const handleFareChange = (event) => {
    setFareDisplay(event.target.value);
  };

  // 票价失去焦点时转换并验证
  const handleFareBlur = (event) => {
    const fareInYuan = parseFloat(event.target.value) || 0;
    const fareInCents = Math.round(fareInYuan * 100);
    formik.setFieldValue('fare', fareInCents);
    setFareDisplay(formatFareDisplay(fareInCents));
    formik.handleBlur(event);
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          boxShadow: 3
        }
      }}
    >
      <DialogTitle sx={{ pb: 1 }}>
        <Typography variant="h6" component="div">
          新建线路票价参数
        </Typography>
        <Typography variant="body2" color="textSecondary">
          创建新的线路票价参数配置
        </Typography>
      </DialogTitle>

      <form onSubmit={formik.handleSubmit}>
        <DialogContent sx={{ pt: 2 }}>
          <Grid container spacing={3}>
            {/* 商户选择 */}
            {user?.roles?.includes('SystemAdmin') && (
              <Grid item xs={12}>
                <MerchantAutocomplete
                  value={formik.values.merchantObj}
                  onChange={handleMerchantChange}
                  error={formik.touched.merchantID && Boolean(formik.errors.merchantID)}
                  helperText={formik.touched.merchantID && formik.errors.merchantID}
                  required
                  fullWidth
                />
              </Grid>
            )}

            {/* 线路编号 */}
            <Grid item xs={12} sm={6}>
              <TextField
                name="lineNumber"
                label="线路编号"
                value={formik.values.lineNumber}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.lineNumber && Boolean(formik.errors.lineNumber)}
                helperText={formik.touched.lineNumber && formik.errors.lineNumber}
                required
                fullWidth
                placeholder="如：101A、B002"
                inputProps={{ maxLength: 4 }}
              />
            </Grid>

            {/* 组号 */}
            <Grid item xs={12} sm={6}>
              <TextField
                name="groupNumber"
                label="组号"
                value={formik.values.groupNumber}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.groupNumber && Boolean(formik.errors.groupNumber)}
                helperText={formik.touched.groupNumber && formik.errors.groupNumber}
                fullWidth
                placeholder="如：01、02（可选）"
                inputProps={{ maxLength: 2 }}
              />
            </Grid>

            {/* 线路名称 */}
            <Grid item xs={12}>
              <TextField
                name="lineName"
                label="线路名称"
                value={formik.values.lineName}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.lineName && Boolean(formik.errors.lineName)}
                helperText={formik.touched.lineName && formik.errors.lineName}
                required
                fullWidth
                placeholder="如：市区环线"
              />
            </Grid>

            {/* 分公司 */}
            <Grid item xs={12} sm={6}>
              <TextField
                name="branch"
                label="分公司"
                value={formik.values.branch}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.branch && Boolean(formik.errors.branch)}
                helperText={formik.touched.branch && formik.errors.branch}
                fullWidth
                placeholder="如：第一分公司（可选）"
              />
            </Grid>

            {/* 票价 */}
            <Grid item xs={12} sm={6}>
              <TextField
                name="fare"
                label="票价（元）"
                type="number"
                value={fareDisplay}
                onChange={handleFareChange}
                onBlur={handleFareBlur}
                error={formik.touched.fare && Boolean(formik.errors.fare)}
                helperText={formik.touched.fare && formik.errors.fare}
                required
                fullWidth
                inputProps={{
                  min: 0.5,
                  max: 999.99,
                  step: 0.5
                }}
                placeholder="2.00"
              />
            </Grid>

            {/* 状态 */}
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    name="isActive"
                    checked={formik.values.isActive}
                    onChange={formik.handleChange}
                    color="primary"
                  />
                }
                label="启用状态"
              />
            </Grid>

            {/* 备注 */}
            <Grid item xs={12}>
              <TextField
                name="remark"
                label="备注"
                value={formik.values.remark}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.remark && Boolean(formik.errors.remark)}
                helperText={formik.touched.remark && formik.errors.remark}
                fullWidth
                multiline
                rows={3}
                placeholder="可选的备注信息"
              />
            </Grid>
          </Grid>
        </DialogContent>

        <DialogActions sx={{ px: 3, pb: 3 }}>
          <Button
            onClick={handleClose}
            disabled={loading}
          >
            取消
          </Button>
          <Button
            type="submit"
            variant="contained"
            disabled={loading}
            startIcon={loading && <CircularProgress size={20} />}
          >
            {loading ? '创建中...' : '创建'}
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  );
};

export default LinePriceCreateDialog;
