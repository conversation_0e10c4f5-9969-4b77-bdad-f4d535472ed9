using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using SlzrCrossGate.Core.Models;
using SlzrCrossGate.Core.Services;
using SlzrCrossGate.Core.Database;
using System.Threading.Channels;
using RabbitMQ.Client.Events;
using RabbitMQ.Client;

namespace SlzrCrossGate.WebAdmin.Services
{
    /// <summary>
    /// 终端日志处理后台服务
    /// 订阅消费数据并解析为终端日志记录保存到数据库
    /// </summary>
    public class TerminalLogProcessingHostedService : BackgroundService
    {
        private readonly IRabbitMQService _rabbitMQService;
        private readonly ILogger<TerminalLogProcessingHostedService> _logger;
        private readonly IServiceScopeFactory _scopeFactory;

        private readonly IConfiguration _configuration;

        private readonly Channel<(ConsumeData Data, ulong DeliveryTag, IChannel Channel)> _consumeDataChannel;
        private readonly TimeSpan _batchInterval;
        private readonly int _batchSize;
        private readonly bool _enabled;
        private readonly bool _enableDebugLogging;
        private readonly string _exchange;
        private readonly string _queue;
        private readonly string _routingKey;
        private Timer? _timer;
        private int _currentBatchSize = 0;
        private readonly SemaphoreSlim _semaphore = new(1, 1);

        public TerminalLogProcessingHostedService(
            IRabbitMQService rabbitMQService,
            ILogger<TerminalLogProcessingHostedService> logger,
            IServiceScopeFactory scopeFactory,
            IConfiguration configuration)
        {
            _rabbitMQService = rabbitMQService;
            _logger = logger;
            _scopeFactory = scopeFactory;
            _configuration = configuration;

            // 读取配置
            _enabled = configuration.GetValue<bool>("TerminalLogProcessing:Enabled", true);
            _batchSize = configuration.GetValue<int>("TerminalLogProcessing:BatchSize", 100);
            _batchInterval = TimeSpan.FromSeconds(configuration.GetValue<int>("TerminalLogProcessing:BatchIntervalSeconds", 10));
            _enableDebugLogging = configuration.GetValue<bool>("TerminalLogProcessing:EnableDebugLogging", false);
            _exchange = configuration.GetValue<string>("TerminalLogProcessing:Exchange", "SlzrCrossGate.Data")!;
            _queue = configuration.GetValue<string>("TerminalLogProcessing:Queue", "SlzrCrossGate.Data.Queue.TerminalLog")!;
            _routingKey = configuration.GetValue<string>("TerminalLogProcessing:RoutingKey", "Tcp.city.#")!;

            _consumeDataChannel = Channel.CreateUnbounded<(ConsumeData Data, ulong DeliveryTag, IChannel Channel)>();
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            if (!_enabled)
            {
                _logger.LogInformation("终端日志处理后台服务已禁用，跳过启动");
                return;
            }

            _logger.LogInformation("终端日志处理后台服务启动");

            // 启动定时批量处理
            _timer = new Timer(async _ => await ProcessQueueAsync(), null, _batchInterval, _batchInterval);

            // 订阅终端日志数据（使用指定的队列，手动ACK）
            await _rabbitMQService.SubscribeConsumeDataAsync(_exchange, _queue, _routingKey, async (consumeData, channel, deliveryTag) =>
            {
                // 将RabbitMQ消息转换为内部ConsumeData模型
                var entity = new ConsumeData
                {
                    MachineID = consumeData.MachineID,
                    MerchantID = consumeData.MerchantID,
                    MachineNO = consumeData.MachineNO,
                    PsamNO = consumeData.PsamNO,
                    ReceiveTime = DateTime.Now,
                    Buffer = consumeData.buffer
                };

                // 将数据、DeliveryTag和Channel加入处理队列（不抛出异常，确保消息不会被自动拒绝）
                await _consumeDataChannel.Writer.WriteAsync((entity, deliveryTag, channel));
                Interlocked.Increment(ref _currentBatchSize);

                // 如果达到批量大小，立即处理
                if (_semaphore.CurrentCount == 1 && _currentBatchSize >= _batchSize)
                {
                    // 异步处理，不等待结果，避免阻塞消息接收
                    _ = Task.Run(async () => await ProcessQueueAsync());
                }
            }, false); // 手动ACK

            _logger.LogInformation("终端日志处理后台服务已订阅终端日志数据。Exchange: {Exchange}, Queue: {Queue}, RoutingKey: {RoutingKey}",
                _exchange, _queue, _routingKey);
        }

        /// <summary>
        /// 批量处理队列中的消费数据
        /// </summary>
        private async Task ProcessQueueAsync()
        {
            await _semaphore.WaitAsync();
            try
            {
                var batch = new List<(ConsumeData Data, ulong DeliveryTag, IChannel Channel)>();

                // 从队列中取出一批数据
                while (batch.Count < _batchSize && _consumeDataChannel.Reader.TryRead(out var item))
                {
                    batch.Add(item);
                }

                if (batch.Count > 0)
                {
                    if (_enableDebugLogging)
                    {
                        _logger.LogDebug("开始处理批量消费数据，数量: {Count}", batch.Count);
                    }

                    try
                    {
                        using var scope = _scopeFactory.CreateScope();
                        var dbContext = scope.ServiceProvider.GetRequiredService<TcpDbContext>();
                        var parserService = scope.ServiceProvider.GetRequiredService<TerminalLogParserService>();

                        var allTerminalLogs = new List<TerminalLog>();
                        var successfulTags = new List<(ulong DeliveryTag, IChannel Channel)>();
                        var failedTags = new List<(ulong DeliveryTag, IChannel Channel)>();

                        // 解析每条消费数据
                        foreach (var (consumeData, deliveryTag, channel) in batch)
                        {
                            try
                            {
                                var terminalLogs = parserService.ParseConsumeData(consumeData);
                                allTerminalLogs.AddRange(terminalLogs);
                                successfulTags.Add((deliveryTag, channel));
                            }
                            catch (Exception ex)
                            {
                                _logger.LogError(ex, "解析消费数据失败。MerchantID: {MerchantID}, MachineID: {MachineID}",
                                    consumeData.MerchantID, consumeData.MachineID);
                                failedTags.Add((deliveryTag, channel));
                            }
                        }

                        // 批量保存终端日志记录
                        if (allTerminalLogs.Count > 0)
                        {
                            await BatchInsertTerminalLogsAsync(dbContext, allTerminalLogs);
                        }

                        // 批量确认成功处理的消息
                        foreach (var (deliveryTag, channel) in successfulTags)
                        {
                            try
                            {
                                await _rabbitMQService.Ack(channel, deliveryTag);
                            }
                            catch (Exception ex)
                            {
                                _logger.LogError(ex, "确认消息时发生错误。DeliveryTag: {DeliveryTag}", deliveryTag);
                            }
                        }

                        // 批量拒绝失败的消息并重新入队
                        foreach (var (deliveryTag, channel) in failedTags)
                        {
                            try
                            {
                                await _rabbitMQService.NAck(channel, deliveryTag, true);
                            }
                            catch (Exception ex)
                            {
                                _logger.LogError(ex, "拒绝消息时发生错误。DeliveryTag: {DeliveryTag}", deliveryTag);
                            }
                        }
                        if(failedTags.Count > 0)
                        {
                            //增加一点延时，以避免RabbitMQ拒绝消息过快导致的死循环
                            await Task.Delay(5000);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "批量处理消费数据时发生错误");

                        // 批量处理失败时，拒绝所有消息并重新入队
                        foreach (var (_, deliveryTag, channel) in batch)
                        {
                            try
                            {
                                await _rabbitMQService.NAck(channel, deliveryTag, true);
                            }
                            catch (Exception nackEx)
                            {
                                _logger.LogError(nackEx, "拒绝消息时发生错误。DeliveryTag: {DeliveryTag}", deliveryTag);
                            }
                        }
                        //增加一点延时，以避免RabbitMQ拒绝消息过快导致的死循环
                        await Task.Delay(5000);
                    }
                    finally
                    {
                        Interlocked.Add(ref _currentBatchSize, -batch.Count);
                    }
                }
            }
            finally
            {
                _semaphore.Release();
            }
        }

        /// <summary>
        /// 批量插入终端日志记录
        /// </summary>
        /// <param name="dbContext">数据库上下文</param>
        /// <param name="terminalLogs">终端日志记录列表</param>
        private async Task BatchInsertTerminalLogsAsync(TcpDbContext dbContext, List<TerminalLog> terminalLogs)
        {
            try
            {
                // 过滤掉无效的记录
                var validLogs = terminalLogs.Where(log => 
                    !string.IsNullOrEmpty(log.MerchantID) && 
                    !string.IsNullOrEmpty(log.MachineID)).ToList();

                if (validLogs.Count == 0)
                {
                    _logger.LogWarning("没有有效的终端日志记录需要保存");
                    return;
                }

                // 批量添加到数据库
                await dbContext.TerminalLogs.AddRangeAsync(validLogs);
                await dbContext.SaveChangesAsync();

                _logger.LogDebug("成功批量插入 {Count} 条终端日志记录", validLogs.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量插入终端日志记录时发生错误");
                throw;
            }
        }

        public override async Task StopAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("终端日志处理后台服务正在停止");

            // 停止定时器
            _timer?.Change(Timeout.Infinite, 0);

            // 处理剩余数据
            await ProcessQueueAsync();

            await base.StopAsync(cancellationToken);

            _logger.LogInformation("终端日志处理后台服务已停止");
        }
    }


}
