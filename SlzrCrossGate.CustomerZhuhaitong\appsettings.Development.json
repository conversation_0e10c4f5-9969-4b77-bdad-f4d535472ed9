{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore.Database.Command": "Information"}}, "ConnectionStrings": {"AuthConnection": "Server=localhost;Database=tcpserver;Uid=customer_readonly;Pwd=readonly_password_2024!;", "CustomerConnection": "Server=localhost;Database=tcpserver;Uid=root;Pwd=**********;"}, "Jwt": {"Key": "your-secret-key-here-must-be-at-least-32-characters-long", "Issuer": "SlzrCrossGate", "Audience": "SlzrCrossGate.Users"}, "MainApp": {"BaseUrl": "http://localhost:5270"}, "Customer": {"Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DisplayName": "珠海通", "TablePrefix": "Customer_<PERSON>g", "BasePath": "/zhuhaitong"}, "Serilog": {"MinimumLevel": {"Default": "Debug", "Override": {"Microsoft": "Information", "System": "Information"}}}}