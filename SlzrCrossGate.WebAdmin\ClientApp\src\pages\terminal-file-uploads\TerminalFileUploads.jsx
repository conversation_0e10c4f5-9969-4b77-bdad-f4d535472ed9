import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  TextField,
  Button,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Chip,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  LinearProgress,
  Stack
} from '@mui/material';
import {
  Search as SearchIcon,
  Refresh as RefreshIcon,
  Download as DownloadIcon,
  Delete as DeleteIcon,
  Upload as CloudUploadIcon,
  Assessment as AssessmentIcon,
  FilterList as FilterListIcon
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { zhCN } from 'date-fns/locale';
import DataTable from '../../components/DataTable';
import MerchantAutocomplete from '../../components/MerchantAutocomplete';
import api from '../../services/api';
import { formatDateTime } from '../../utils/dateUtils';
import { copyToClipboard } from '../../utils/clipboard';

const TerminalFileUploads = () => {
  const [uploads, setUploads] = useState([]);
  const [stats, setStats] = useState(null);
  const [fileTypes, setFileTypes] = useState([]);
  const [terminalTypes, setTerminalTypes] = useState([]);
  const [selectedMerchant, setSelectedMerchant] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedUpload, setSelectedUpload] = useState(null);
  const [statsDialogOpen, setStatsDialogOpen] = useState(false);

  // 查询参数
  const [queryParams, setQueryParams] = useState({
    merchantID: '',
    terminalID: '',
    terminalType: '',
    fileType: '',
    fileName: '',
    status: '', // 使用空字符串，在发送请求时转换为null
    startTimeFrom: null,
    startTimeTo: null,
    page: 1,
    pageSize: 20,
    sortBy: 'StartTime',
    sortDirection: 'desc'
  });

  // 分页信息
  const [pagination, setPagination] = useState({
    totalCount: 0,
    totalPages: 0,
    page: 1,
    pageSize: 20
  });

  // 状态选项
  const statusOptions = [
    { value: '', label: '全部状态' },
    { value: 1, label: '上传中' },
    { value: 2, label: '已完成' },
    { value: 3, label: '失败' },
    { value: 4, label: '已取消' }
  ];

  // 防重复请求
  const fetchUploadsRef = useRef();
  const hasLoadedRef = useRef(false);

  // 获取上传列表
  const fetchUploads = async (isInitialLoad = false, forceLoad = false) => {
    try {
      setLoading(true);
      setError('');

      // 转换查询参数，处理空字符串和null值
      const requestParams = {
        ...queryParams,
        // 将空字符串转换为null，避免后端验证错误
        merchantID: queryParams.merchantID || null,
        terminalID: queryParams.terminalID || null,
        terminalType: queryParams.terminalType || null,
        fileType: queryParams.fileType || null,
        fileName: queryParams.fileName || null,
        // 将空字符串转换为null，数字保持原样
        status: queryParams.status === '' ? null : Number(queryParams.status)
      };

      // 避免重复请求：检查参数是否真的发生了变化（除非强制加载）
      const paramsString = JSON.stringify(requestParams);
      if (!isInitialLoad && !forceLoad && fetchUploadsRef.current === paramsString) {
        console.log('TerminalFileUploads: 参数未变化，跳过重复请求');
        setLoading(false);
        return;
      }
      fetchUploadsRef.current = paramsString;

      console.log('TerminalFileUploads: 执行数据请求', requestParams);
      const response = await api.post('/TerminalFileUploads/list', requestParams);

      setUploads(response.items || []);
      setPagination({
        totalCount: response.totalCount,
        totalPages: response.totalPages,
        page: response.page,
        pageSize: response.pageSize
      });
    } catch (err) {
      console.error('API请求失败:', err);
      setError('获取上传列表失败: ' + (err.response?.data?.message || err.message || err));
    } finally {
      setLoading(false);
    }
  };

  // 获取统计信息
  const fetchStats = async () => {
    try {
      const response = await api.get('/TerminalFileUploads/stats');
      setStats(response);
    } catch (err) {
      console.error('获取统计信息失败:', err);
    }
  };

  // 获取文件类型列表
  const fetchFileTypes = async () => {
    try {
      const response = await api.get('/TerminalFileUploads/file-types');
      setFileTypes(response || []);
    } catch (err) {
      console.error('获取文件类型列表失败:', err);
    }
  };

  // 获取终端类型列表
  const fetchTerminalTypes = async () => {
    try {
      const response = await api.get('/TerminalFileUploads/terminal-types');
      setTerminalTypes(response || []);
    } catch (err) {
      console.error('获取终端类型列表失败:', err);
    }
  };

  // 注意：MerchantAutocomplete 组件会自动加载商户数据，不需要手动获取

  // 统一的数据加载逻辑
  useEffect(() => {
    // 防止重复加载
    if (hasLoadedRef.current) {
      console.log('TerminalFileUploads: 已加载过，跳过重复请求');
      return;
    }

    console.log('TerminalFileUploads: 执行首次加载');
    hasLoadedRef.current = true;
    fetchUploads(true, false); // 标记为初始加载，非强制加载
    fetchStats();
    fetchFileTypes();
    fetchTerminalTypes();
  }, []);

  // 当参数变化时重新加载
  useEffect(() => {
    if (hasLoadedRef.current) { // 只有在首次加载完成后才响应参数变化
      console.log('TerminalFileUploads: 参数变化，重新加载');
      fetchUploads(false, false); // 非初始加载，非强制加载
    }
  }, [queryParams]);

  // 处理查询参数变化
  const handleQueryChange = (field, value) => {
    setQueryParams(prev => ({
      ...prev,
      [field]: value,
      page: 1 // 重置到第一页
    }));
  };

  // 处理商户变更
  const handleMerchantChange = (event, newValue) => {
    setSelectedMerchant(newValue);
    setQueryParams(prev => ({
      ...prev,
      merchantID: newValue ? newValue.merchantID : '',
      page: 1 // 重置到第一页
    }));
  };

  // 处理搜索
  const handleSearch = () => {
    fetchUploads(false, true); // 强制执行搜索
  };

  // 处理重置
  const handleReset = () => {
    setSelectedMerchant(null);
    setQueryParams({
      merchantID: '',
      terminalID: '',
      terminalType: '',
      fileType: '',
      fileName: '',
      status: '', // 使用空字符串
      startTimeFrom: null,
      startTimeTo: null,
      page: 1,
      pageSize: 20,
      sortBy: 'StartTime',
      sortDirection: 'desc'
    });
  };

  // 处理分页
  const handlePageChange = (newPage) => {
    setQueryParams(prev => ({ ...prev, page: newPage }));
  };

  // 处理每页大小变化
  const handlePageSizeChange = (newPageSize) => {
    setQueryParams(prev => ({ ...prev, pageSize: newPageSize, page: 1 }));
  };

  // 处理排序
  const handleSort = (field) => {
    setQueryParams(prev => ({
      ...prev,
      sortBy: field,
      sortDirection: prev.sortBy === field && prev.sortDirection === 'asc' ? 'desc' : 'asc'
    }));
  };

  // 下载文件
  const handleDownload = async (upload) => {
    try {
      const response = await api.get(`/TerminalFileUploads/download/${upload.id}`, {
        responseType: 'blob'
      });

      // 对于blob响应，api.js直接返回blob数据
      const url = window.URL.createObjectURL(new Blob([response]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `${upload.terminalID}_${upload.fileName}`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
    } catch (err) {
      setError('下载文件失败: ' + (err.response?.data?.message || err.message || err));
    }
  };

  // 删除上传记录
  const handleDelete = async () => {
    if (!selectedUpload) return;

    try {
      await api.delete(`/TerminalFileUploads/${selectedUpload.id}`);
      setDeleteDialogOpen(false);
      setSelectedUpload(null);
      fetchUploads();
      fetchStats();
    } catch (err) {
      setError('删除失败: ' + (err.response?.data?.message || err.message || err));
    }
  };

  // 获取状态颜色
  const getStatusColor = (status) => {
    switch (status) {
      case 1: return 'info';     // 上传中
      case 2: return 'success';  // 已完成
      case 3: return 'error';    // 失败
      case 4: return 'default';  // 已取消
      default: return 'default';
    }
  };

  // 表格列定义
  const columns = [
    {
      field: 'merchantName',
      headerName: '商户',
      width: 150,
      sortable: true,
      renderCell: (params) => (
        <Tooltip title={`商户ID: ${params.row.merchantID}`}>
          <Typography
            variant="body2"
            sx={{
              cursor: 'help',
              maxWidth: '150px',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap'
            }}
          >
            {params.value || params.row.merchantID}
          </Typography>
        </Tooltip>
      )
    },
    {
      field: 'terminalID',
      headerName: '终端ID',
      width: 120,
      sortable: true,
      renderCell: (params) => (
        <Tooltip title="点击复制">
          <Typography
            variant="body2"
            sx={{ cursor: 'pointer', '&:hover': { color: 'primary.main' } }}
            onClick={() => copyToClipboard(params.value)}
          >
            {params.value}
          </Typography>
        </Tooltip>
      )
    },
    {
      field: 'terminalType',
      headerName: '终端类型',
      width: 100,
      sortable: true
    },
    {
      field: 'fileType',
      headerName: '文件类型',
      width: 100,
      sortable: true,
      renderCell: (params) => (
        <Chip label={params.value} size="small" variant="outlined" />
      )
    },
    {
      field: 'fileName',
      headerName: '文件名',
      width: 200,
      sortable: true,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2" noWrap>
            {params.value}
          </Typography>
        </Tooltip>
      )
    },
    {
      field: 'fileSizeFormatted',
      headerName: '文件大小',
      width: 100,
      sortable: true,
      align: 'right'
    },
    {
      field: 'progressPercentage',
      headerName: '进度',
      width: 120,
      renderCell: (params) => (
        <Box sx={{ width: '100%' }}>
          <LinearProgress
            variant="determinate"
            value={params.value || 0}
            sx={{ height: 8, borderRadius: 4 }}
          />
          <Typography variant="caption" sx={{ mt: 0.5 }}>
            {(params.value || 0).toFixed(1)}%
          </Typography>
        </Box>
      )
    },
    {
      field: 'statusDescription',
      headerName: '状态',
      width: 100,
      renderCell: (params) => (
        <Chip
          label={params.value}
          size="small"
          color={getStatusColor(params.row.status)}
        />
      )
    },
    {
      field: 'startTime',
      headerName: '开始时间',
      width: 160,
      sortable: true,
      renderCell: (params) => formatDateTime(params.value)
    },
    {
      field: 'actions',
      headerName: '操作',
      width: 120,
      renderCell: (params) => (
        <Stack direction="row" spacing={1}>
          {params.row.canDownload && (
            <Tooltip title="下载文件">
              <IconButton
                size="small"
                onClick={() => handleDownload(params.row)}
              >
                <DownloadIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          )}
          <Tooltip title="删除">
            <IconButton
              size="small"
              color="error"
              onClick={() => {
                setSelectedUpload(params.row);
                setDeleteDialogOpen(true);
              }}
            >
              <DeleteIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        </Stack>
      )
    }
  ];

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={zhCN}>
      <Box sx={{ p: 3 }}>
        <Typography variant="h4" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <CloudUploadIcon />
          终端文件上传管理
        </Typography>

        {/* 统计卡片 */}
        {stats && (
          <Grid container spacing={2} sx={{ mb: 3 }}>
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Typography color="textSecondary" gutterBottom>
                    总上传数
                  </Typography>
                  <Typography variant="h5">
                    {stats.totalUploads}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Typography color="textSecondary" gutterBottom>
                    已完成
                  </Typography>
                  <Typography variant="h5" color="success.main">
                    {stats.completedCount}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Typography color="textSecondary" gutterBottom>
                    上传中
                  </Typography>
                  <Typography variant="h5" color="info.main">
                    {stats.uploadingCount}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Typography color="textSecondary" gutterBottom>
                    总文件大小
                  </Typography>
                  <Typography variant="h6">
                    {stats.totalFileSizeFormatted}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        )}

        {/* 筛选条件 */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <FilterListIcon />
              筛选条件
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6} md={3}>
                <MerchantAutocomplete
                  value={selectedMerchant}
                  onChange={handleMerchantChange}
                  label="商户"
                  size="small"
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  fullWidth
                  label="终端ID"
                  value={queryParams.terminalID}
                  onChange={(e) => handleQueryChange('terminalID', e.target.value)}
                  size="small"
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <FormControl fullWidth size="small">
                  <InputLabel>终端类型</InputLabel>
                  <Select
                    value={queryParams.terminalType}
                    label="终端类型"
                    onChange={(e) => handleQueryChange('terminalType', e.target.value)}
                  >
                    <MenuItem value="">全部类型</MenuItem>
                    {terminalTypes.map(type => (
                      <MenuItem key={type} value={type}>{type}</MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <FormControl fullWidth size="small">
                  <InputLabel>文件类型</InputLabel>
                  <Select
                    value={queryParams.fileType}
                    label="文件类型"
                    onChange={(e) => handleQueryChange('fileType', e.target.value)}
                  >
                    <MenuItem value="">全部类型</MenuItem>
                    {fileTypes.map(type => (
                      <MenuItem key={type} value={type}>{type}</MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  fullWidth
                  label="文件名"
                  value={queryParams.fileName}
                  onChange={(e) => handleQueryChange('fileName', e.target.value)}
                  size="small"
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <FormControl fullWidth size="small">
                  <InputLabel>状态</InputLabel>
                  <Select
                    value={queryParams.status}
                    label="状态"
                    onChange={(e) => handleQueryChange('status', e.target.value)}
                  >
                    {statusOptions.map(option => (
                      <MenuItem key={option.value} value={option.value}>
                        {option.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <DatePicker
                  label="开始时间（从）"
                  value={queryParams.startTimeFrom}
                  onChange={(value) => handleQueryChange('startTimeFrom', value)}
                  slotProps={{ textField: { size: 'small', fullWidth: true } }}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <DatePicker
                  label="开始时间（到）"
                  value={queryParams.startTimeTo}
                  onChange={(value) => handleQueryChange('startTimeTo', value)}
                  slotProps={{ textField: { size: 'small', fullWidth: true } }}
                />
              </Grid>
            </Grid>
            <Box sx={{ mt: 2, display: 'flex', gap: 1 }}>
              <Button
                variant="contained"
                startIcon={<SearchIcon />}
                onClick={handleSearch}
              >
                搜索
              </Button>
              <Button
                variant="outlined"
                onClick={handleReset}
              >
                重置
              </Button>
              <Button
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={() => {
                  fetchUploads(false, true); // 强制执行刷新
                  fetchStats();
                }}
              >
                刷新
              </Button>
              <Button
                variant="outlined"
                startIcon={<AssessmentIcon />}
                onClick={() => setStatsDialogOpen(true)}
              >
                详细统计
              </Button>
            </Box>
          </CardContent>
        </Card>

        {/* 错误提示 */}
        {error && (
          <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError('')}>
            {error}
          </Alert>
        )}

        {/* 数据表格 */}
        <DataTable
          columns={columns}
          data={uploads}
          loading={loading}
          pagination={pagination}
          onPageChange={handlePageChange}
          onPageSizeChange={handlePageSizeChange}
          onSort={handleSort}
          sortBy={queryParams.sortBy}
          sortDirection={queryParams.sortDirection}
          emptyMessage="暂无上传记录"
        />

        {/* 删除确认对话框 */}
        <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
          <DialogTitle>确认删除</DialogTitle>
          <DialogContent>
            <Typography>
              确定要删除终端 {selectedUpload?.TerminalID} 的文件上传记录吗？
            </Typography>
            <Typography variant="body2" color="textSecondary" sx={{ mt: 1 }}>
              文件名: {selectedUpload?.FileName}
            </Typography>
            <Typography variant="body2" color="error" sx={{ mt: 1 }}>
              此操作不可撤销，相关文件也将被删除。
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setDeleteDialogOpen(false)}>取消</Button>
            <Button onClick={handleDelete} color="error" variant="contained">
              删除
            </Button>
          </DialogActions>
        </Dialog>

        {/* 详细统计对话框 */}
        <Dialog 
          open={statsDialogOpen} 
          onClose={() => setStatsDialogOpen(false)}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>详细统计信息</DialogTitle>
          <DialogContent>
            {stats && (
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Typography variant="h6" gutterBottom>
                    文件类型统计
                  </Typography>
                  {stats.FileTypeStats?.map(stat => (
                    <Box key={stat.FileType} sx={{ mb: 1 }}>
                      <Typography variant="body2">
                        {stat.FileType}: {stat.Count} 个文件 ({stat.TotalSizeFormatted})
                      </Typography>
                    </Box>
                  ))}
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="h6" gutterBottom>
                    终端类型统计
                  </Typography>
                  {stats.TerminalTypeStats?.map(stat => (
                    <Box key={stat.TerminalType} sx={{ mb: 1 }}>
                      <Typography variant="body2">
                        {stat.TerminalType}: {stat.Count} 个文件 ({stat.TotalSizeFormatted})
                      </Typography>
                    </Box>
                  ))}
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="h6" gutterBottom>
                    时间统计
                  </Typography>
                  <Typography variant="body2">今日上传: {stats.TodayUploads} 个</Typography>
                  <Typography variant="body2">本周上传: {stats.WeekUploads} 个</Typography>
                  <Typography variant="body2">本月上传: {stats.MonthUploads} 个</Typography>
                </Grid>
              </Grid>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setStatsDialogOpen(false)}>关闭</Button>
          </DialogActions>
        </Dialog>
      </Box>
    </LocalizationProvider>
  );
};

export default TerminalFileUploads;
