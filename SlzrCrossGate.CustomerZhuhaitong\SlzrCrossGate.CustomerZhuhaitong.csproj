<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <SpaRoot>ClientApp\</SpaRoot>
    <!-- SpaProxyServerUrl 仅用于开发环境，生产环境不使用 -->
    <SpaProxyLaunchCommand>npm start</SpaProxyLaunchCommand>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="FluentFTP" Version="50.0.1" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.14" />
    <PackageReference Include="Microsoft.AspNetCore.SpaServices.Extensions" Version="8.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.4">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Pomelo.EntityFrameworkCore.MySql" Version="8.0.3" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="8.0.0" />
    <PackageReference Include="Serilog.AspNetCore" Version="8.0.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="5.0.0" />
    <PackageReference Include="Serilog.Formatting.Compact" Version="2.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="../SlzrCrossGate.Core/SlzrCrossGate.Core.csproj" />
    <ProjectReference Include="../SlzrCrossGate.Common/SlzrCrossGate.Common.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Content Remove="ClientApp\**" />
    <None Remove="ClientApp\**" />
  </ItemGroup>

</Project>
