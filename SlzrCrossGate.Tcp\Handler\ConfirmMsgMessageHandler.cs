﻿using Microsoft.Extensions.Logging;
using SlzrCrossGate.Core.DTOs;
using SlzrCrossGate.Core.Service.BusinessServices;
using SlzrCrossGate.Core.Services.BusinessServices;
using SlzrCrossGate.Tcp.Protocol;
using System.Globalization;

namespace SlzrCrossGate.Tcp.Handler
{

    [MessageType(Iso8583MessageType.MsgConfirmResquest)]
    public class ConfirmMsgMessageHandler : IIso8583MessageHandler
    {
        private readonly ILogger<ConfirmMsgMessageHandler> _logger;
        private readonly Iso8583Schema _schema;
        private readonly MsgBoxService _msgBoxService;
        private readonly MsgboxEventService _msgboxEventService;

        public ConfirmMsgMessageHandler(ILogger<ConfirmMsgMessageHandler> logger, Iso8583Schema schema, MsgBoxService msgBoxService,MsgboxEventService msgboxEventService)
        {
            _logger = logger;
            _schema = schema;
            _msgBoxService = msgBoxService;
            _msgboxEventService = msgboxEventService;
        }


        public async Task<Iso8583Message> HandleMessageAsync(TcpConnectionContext context, Iso8583Message message)
        {
            var content = message.GetString(52);

            var msgConfirmDtos = ConvertToMsgConfirmDtos(content);

            await _msgBoxService.MarkMessageAsRepliedAsync(msgConfirmDtos);

            foreach(var msgConfirmDto in msgConfirmDtos)
            {
                await _msgboxEventService.Publish(new MsgboxEventMessage
                {
                    ID = msgConfirmDto.ID,
                    ActionType = MsgboxEventActionType.Reply,
                    MerchantID = message.MerchantID,
                    TerminalID = message.TerimalID,
                    ActionTime = DateTime.Now
                });
            }

            // 创建一个确认响应
            var response = new Iso8583Message(_schema, Iso8583MessageType.MsgConfirmResponse);
            response.Ok();
            response.SetField(52, content);
            return response;
        }



        static List<MsgConfirmDto> ConvertToMsgConfirmDtos(string content)
        {
            if (string.IsNullOrEmpty(content))
                return new List<MsgConfirmDto>();

            var result = new List<MsgConfirmDto>();
            ReadOnlySpan<char> span = content.AsSpan();
            int pos = 0;
            int length = span.Length;

            while (pos <= length - 20)
            {
                // 解析 MSGLEN (4 chars, 2 bytes per char → actual length = value * 2)
                if (!int.TryParse(span.Slice(pos, 4), NumberStyles.None, CultureInfo.InvariantCulture, out int msgLen))
                    break; // 或 throw new FormatException("Invalid MSGLEN format");
                msgLen *= 2; // 转换为实际字符长度
                pos += 4;

                // 跳过 MSGTYPE (4 chars)
                pos += 4;

                // 解析 MSGID (8 chars, hexadecimal)
                if (!int.TryParse(span.Slice(pos, 8), NumberStyles.HexNumber, CultureInfo.InvariantCulture, out int msgId))
                    break; // 或 throw new FormatException("Invalid MSGID format");
                pos += 8;

                // 解析 RESULTCODE (4 chars)
                var replyCode = span.Slice(pos, 4).ToString();
                pos += 4;

                // 解析 RESULTCONTENT (remaining chars: msgLen - 16)
                int contentLength = msgLen - 16;
                if (contentLength < 0 || pos + contentLength > length)
                    break; // 或 throw new FormatException("Invalid content length");

                var replyContent = span.Slice(pos, contentLength).ToString();
                pos += contentLength;

                result.Add(new MsgConfirmDto
                {
                    ID = msgId,
                    ReplyCode = replyCode,
                    ReplyContent = replyContent
                });
            }

            return result;
        }

    }
}
