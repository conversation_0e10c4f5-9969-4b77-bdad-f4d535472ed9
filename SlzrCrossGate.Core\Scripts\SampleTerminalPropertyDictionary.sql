-- 示例终端属性字典配置
-- 这个脚本为演示目的添加一些常见的终端属性配置

-- 为商户 '00000001' 添加终端属性字典配置
INSERT INTO MerchantDictionaries (MerchantID, DictionaryType, DictionaryCode, DictionaryLabel, DictionaryValue, SortOrder, IsActive, Description, CreateTime, Creator)
VALUES 
-- 设备信息相关
('00000001', 'TerminalProperty', 'DeviceModel', '设备型号', '', 1, 1, '终端设备的型号信息', NOW(), 'System'),
('00000001', 'TerminalProperty', 'DeviceSerial', '设备序列号', '', 2, 1, '终端设备的序列号', NOW(), 'System'),
('00000001', 'TerminalProperty', 'DeviceManufacturer', '设备制造商', '', 3, 1, '终端设备的制造商信息', NOW(), 'System'),

-- 软件版本相关
('00000001', 'TerminalProperty', 'FirmwareVersion', '固件版本', '', 10, 1, '终端设备的固件版本号', NOW(), 'System'),
('00000001', 'TerminalProperty', 'AppVersion', '应用版本', '', 11, 1, '终端应用程序版本号', NOW(), 'System'),
('00000001', 'TerminalProperty', 'OSVersion', '操作系统版本', '', 12, 1, '终端操作系统版本', NOW(), 'System'),

-- 网络连接相关
('00000001', 'TerminalProperty', 'NetworkStatus', '网络状态', '', 20, 1, '终端网络连接状态', NOW(), 'System'),
('00000001', 'TerminalProperty', 'IPAddress', 'IP地址', '', 21, 1, '终端设备的IP地址', NOW(), 'System'),
('00000001', 'TerminalProperty', 'MACAddress', 'MAC地址', '', 22, 1, '终端设备的MAC地址', NOW(), 'System'),
('00000001', 'TerminalProperty', 'SignalStrength', '信号强度', '', 23, 1, '网络信号强度', NOW(), 'System'),

-- 硬件状态相关
('00000001', 'TerminalProperty', 'BatteryLevel', '电池电量', '', 30, 1, '终端设备电池电量百分比', NOW(), 'System'),
('00000001', 'TerminalProperty', 'Temperature', '设备温度', '', 31, 1, '终端设备当前温度', NOW(), 'System'),
('00000001', 'TerminalProperty', 'StorageUsage', '存储使用率', '', 32, 1, '终端设备存储空间使用率', NOW(), 'System'),
('00000001', 'TerminalProperty', 'MemoryUsage', '内存使用率', '', 33, 1, '终端设备内存使用率', NOW(), 'System'),

-- 维护相关
('00000001', 'TerminalProperty', 'LastMaintenance', '最后维护时间', '', 40, 1, '终端设备最后一次维护的时间', NOW(), 'System'),
('00000001', 'TerminalProperty', 'MaintenanceStatus', '维护状态', '', 41, 1, '终端设备当前的维护状态', NOW(), 'System'),
('00000001', 'TerminalProperty', 'ErrorCount', '错误计数', '', 42, 1, '终端设备累计错误次数', NOW(), 'System'),

-- 业务相关
('00000001', 'TerminalProperty', 'TransactionCount', '交易笔数', '', 50, 1, '终端设备今日交易笔数', NOW(), 'System'),
('00000001', 'TerminalProperty', 'LastTransaction', '最后交易时间', '', 51, 1, '终端设备最后一笔交易时间', NOW(), 'System'),
('00000001', 'TerminalProperty', 'WorkMode', '工作模式', '', 52, 1, '终端设备当前工作模式', NOW(), 'System');

-- 为商户 '00000002' 添加类似的配置（如果存在的话）
INSERT INTO MerchantDictionaries (MerchantID, DictionaryType, DictionaryCode, DictionaryLabel, DictionaryValue, SortOrder, IsActive, Description, CreateTime, Creator)
SELECT '00000002', DictionaryType, DictionaryCode, DictionaryLabel, DictionaryValue, SortOrder, IsActive, Description, NOW(), Creator
FROM MerchantDictionaries 
WHERE MerchantID = '00000001' AND DictionaryType = 'TerminalProperty'
AND EXISTS (SELECT 1 FROM Merchants WHERE MerchantID = '00000002');

-- 为商户 '00000003' 添加类似的配置（如果存在的话）
INSERT INTO MerchantDictionaries (MerchantID, DictionaryType, DictionaryCode, DictionaryLabel, DictionaryValue, SortOrder, IsActive, Description, CreateTime, Creator)
SELECT '00000003', DictionaryType, DictionaryCode, DictionaryLabel, DictionaryValue, SortOrder, IsActive, Description, NOW(), Creator
FROM MerchantDictionaries 
WHERE MerchantID = '00000001' AND DictionaryType = 'TerminalProperty'
AND EXISTS (SELECT 1 FROM Merchants WHERE MerchantID = '00000003');
