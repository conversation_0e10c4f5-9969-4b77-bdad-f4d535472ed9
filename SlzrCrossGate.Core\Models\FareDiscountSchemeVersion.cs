using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace SlzrCrossGate.Core.Models
{
    /// <summary>
    /// 票价折扣方案版本表，用于存储票价折扣方案的各个版本
    /// </summary>
    public class FareDiscountSchemeVersion : ITenantEntity
    {
        /// <summary>
        /// 自增ID（主键）
        /// </summary>
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int ID { get; set; }

        /// <summary>
        /// 商户ID
        /// </summary>
        [Required]
        [MaxLength(8)]
        public required string MerchantID { get; set; }

        /// <summary>
        /// 关联的票价折扣方案ID
        /// </summary>
        public int FareDiscountSchemeID { get; set; }

        /// <summary>
        /// 方案名称（冗余存储，便于查询）
        /// </summary>
        [Required]
        [MaxLength(100)]
        public required string SchemeName { get; set; }

        /// <summary>
        /// 版本号，4位16进制数，从0001开始
        /// </summary>
        [Required]
        [MaxLength(4)]
        public required string Version { get; set; }

        /// <summary>
        /// 文件参数，用于生成文件时的para字段
        /// </summary>
        [Required]
        [MaxLength(50)]
        public required string FilePara { get; set; }

        /// <summary>
        /// 线路额外参数JSON字符串，包含如差额换乘时间、定额换乘时间、是否支持换乘等参数
        /// </summary>
        [Column(TypeName = "text")]
        public string? ExtraParamsJson { get; set; }

        /// <summary>
        /// 卡类参数信息JSON字符串，包含各卡类的折扣、播报语音等参数
        /// </summary>
        [Column(TypeName = "text")]
        public string? CardDiscountInfoJson { get; set; }

        /// <summary>
        /// 完整的票价折扣方案文件JSON内容
        /// </summary>
        [Column(TypeName = "text")]
        public string? FileContentJson { get; set; }

        /// <summary>
        /// 版本状态：草稿或已提交
        /// </summary>
        public FareDiscountVersionStatus Status { get; set; } = FareDiscountVersionStatus.Draft;

        /// <summary>
        /// 是否已发布到文件版本
        /// </summary>
        public bool IsPublished { get; set; } = false;

        /// <summary>
        /// 发布的文件版本ID，关联FileVer表
        /// </summary>
        public int? FileVerID { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdateTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 提交时间
        /// </summary>
        public DateTime? SubmitTime { get; set; }

        /// <summary>
        /// 创建者
        /// </summary>
        [MaxLength(50)]
        public string? Creator { get; set; }

        /// <summary>
        /// 更新者
        /// </summary>
        [MaxLength(50)]
        public string? Updater { get; set; }

        /// <summary>
        /// 提交者
        /// </summary>
        [MaxLength(50)]
        public string? Submitter { get; set; }

        /// <summary>
        /// 版本备注信息
        /// </summary>
        [MaxLength(500)]
        public string? Remarks { get; set; }

        /// <summary>
        /// 关联的票价折扣方案
        /// </summary>
        [ForeignKey("FareDiscountSchemeID")]
        public FareDiscountScheme? FareDiscountScheme { get; set; }

        /// <summary>
        /// 关联的商户
        /// </summary>
        [ForeignKey("MerchantID")]
        public Merchant? Merchant { get; set; }
    }
}
