using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using SlzrCrossGate.Core.Database;
using SlzrCrossGate.Core.Models;
using SlzrCrossGate.WebAdmin.DTOs;
using SlzrCrossGate.WebAdmin.Services;

namespace SlzrCrossGate.WebAdmin.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class ScheduledFilePublishController : ControllerBase
    {
        private readonly TcpDbContext _dbContext;
        private readonly UserService _userService;

        public ScheduledFilePublishController(TcpDbContext dbContext, UserService userService)
        {
            _dbContext = dbContext;
            _userService = userService;
        }

        // GET: api/ScheduledFilePublish
        [HttpGet]
        public async Task<ActionResult<PaginatedResult<ScheduledFilePublishDto>>> GetScheduledFilePublishes(
            [FromQuery] string? merchantId = null,
            [FromQuery] ScheduledPublishStatus? status = null,
            [FromQuery] DateTime? scheduledTimeFrom = null,
            [FromQuery] DateTime? scheduledTimeTo = null,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 10)
        {
            // 获取当前用户的商户ID
            var currentUserMerchantId = await _userService.GetUserMerchantIdAsync(User);
            var isSystemAdmin = User.IsInRole("SystemAdmin");

            // 如果不是系统管理员，只能查看自己商户的预约发布
            if (!isSystemAdmin && merchantId != null && merchantId != currentUserMerchantId)
            {
                return Forbid();
            }

            // 构建查询
            var query = from sfp in _dbContext.ScheduledFilePublishs
                        join m in _dbContext.Merchants on sfp.MerchantID equals m.MerchantID
                        join ft in _dbContext.FileTypes on new { TypeId = sfp.FileTypeID, MerchantId = sfp.MerchantID }
                            equals new { TypeId = ft.ID, MerchantId = ft.MerchantID } into ftGroup
                        from ft in ftGroup.DefaultIfEmpty()
                        select new { ScheduledFilePublish = sfp, MerchantName = m.Name, FileTypeName = ft.Name };

            // 应用过滤条件
            if (!isSystemAdmin)
            {
                query = query.Where(x => x.ScheduledFilePublish.MerchantID == currentUserMerchantId);
            }
            else if (!string.IsNullOrEmpty(merchantId))
            {
                query = query.Where(x => x.ScheduledFilePublish.MerchantID == merchantId);
            }

            if (status.HasValue)
            {
                query = query.Where(x => x.ScheduledFilePublish.Status == status.Value);
            }

            if (scheduledTimeFrom.HasValue)
            {
                query = query.Where(x => x.ScheduledFilePublish.ScheduledTime >= scheduledTimeFrom.Value);
            }

            if (scheduledTimeTo.HasValue)
            {
                query = query.Where(x => x.ScheduledFilePublish.ScheduledTime <= scheduledTimeTo.Value);
            }

            // 按创建时间倒序排列
            query = query.OrderByDescending(x => x.ScheduledFilePublish.CreatedTime);

            // 分页
            var totalCount = await query.CountAsync();
            var items = await query
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            // 转换为DTO
            var scheduledFilePublishDtos = items.Select(x => new ScheduledFilePublishDto
            {
                ID = x.ScheduledFilePublish.ID,
                FileVersionID = x.ScheduledFilePublish.FileVersionID,
                MerchantID = x.ScheduledFilePublish.MerchantID,
                MerchantName = x.MerchantName ?? "",
                FileTypeID = x.ScheduledFilePublish.FileTypeID,
                FileTypeName = x.FileTypeName ?? "",
                FilePara = x.ScheduledFilePublish.FilePara,
                FileFullType = x.ScheduledFilePublish.FileFullType,
                Ver = x.ScheduledFilePublish.Ver,
                PublishType = x.ScheduledFilePublish.PublishType,
                PublishTarget = x.ScheduledFilePublish.PublishTarget,
                ScheduledTime = x.ScheduledFilePublish.ScheduledTime,
                Status = x.ScheduledFilePublish.Status,
                CreatedTime = x.ScheduledFilePublish.CreatedTime,
                CreatedBy = x.ScheduledFilePublish.CreatedBy,
                ExecutedTime = x.ScheduledFilePublish.ExecutedTime,
                ErrorMessage = x.ScheduledFilePublish.ErrorMessage,
                Remarks = x.ScheduledFilePublish.Remarks
            }).ToList();

            return new PaginatedResult<ScheduledFilePublishDto>
            {
                Items = scheduledFilePublishDtos,
                TotalCount = totalCount,
                Page = page,
                PageSize = pageSize
            };
        }

        // GET: api/ScheduledFilePublish/5
        [HttpGet("{id}")]
        public async Task<ActionResult<ScheduledFilePublishDto>> GetScheduledFilePublish(int id)
        {
            // 获取当前用户的商户ID
            var currentUserMerchantId = await _userService.GetUserMerchantIdAsync(User);
            var isSystemAdmin = User.IsInRole("SystemAdmin");

            var result = await (from sfp in _dbContext.ScheduledFilePublishs
                               join m in _dbContext.Merchants on sfp.MerchantID equals m.MerchantID
                               join ft in _dbContext.FileTypes on new { TypeId = sfp.FileTypeID, MerchantId = sfp.MerchantID }
                                   equals new { TypeId = ft.ID, MerchantId = ft.MerchantID } into ftGroup
                               from ft in ftGroup.DefaultIfEmpty()
                               where sfp.ID == id
                               select new { ScheduledFilePublish = sfp, MerchantName = m.Name, FileTypeName = ft.Name })
                              .FirstOrDefaultAsync();

            if (result == null)
            {
                return NotFound();
            }

            var scheduledFilePublish = result.ScheduledFilePublish;

            // 如果不是系统管理员，只能查看自己商户的预约发布
            if (!isSystemAdmin && scheduledFilePublish.MerchantID != currentUserMerchantId)
            {
                return Forbid();
            }

            return new ScheduledFilePublishDto
            {
                ID = scheduledFilePublish.ID,
                FileVersionID = scheduledFilePublish.FileVersionID,
                MerchantID = scheduledFilePublish.MerchantID,
                MerchantName = result.MerchantName ?? "",
                FileTypeID = scheduledFilePublish.FileTypeID,
                FileTypeName = result.FileTypeName ?? "",
                FilePara = scheduledFilePublish.FilePara,
                FileFullType = scheduledFilePublish.FileFullType,
                Ver = scheduledFilePublish.Ver,
                PublishType = scheduledFilePublish.PublishType,
                PublishTarget = scheduledFilePublish.PublishTarget,
                ScheduledTime = scheduledFilePublish.ScheduledTime,
                Status = scheduledFilePublish.Status,
                CreatedTime = scheduledFilePublish.CreatedTime,
                CreatedBy = scheduledFilePublish.CreatedBy,
                ExecutedTime = scheduledFilePublish.ExecutedTime,
                ErrorMessage = scheduledFilePublish.ErrorMessage,
                Remarks = scheduledFilePublish.Remarks
            };
        }

        // POST: api/ScheduledFilePublish
        [HttpPost]
        public async Task<ActionResult<ScheduledFilePublishDto>> CreateScheduledFilePublish([FromBody] CreateScheduledFilePublishDto model)
        {
            // 获取当前用户信息
            var currentUserMerchantId = await _userService.GetUserMerchantIdAsync(User);
            var isSystemAdmin = User.IsInRole("SystemAdmin");
            var username = UserService.GetUserNameForOperator(User);

            // 如果不是系统管理员，只能为自己的商户创建预约发布
            if (!isSystemAdmin && model.MerchantID != currentUserMerchantId)
            {
                return Forbid();
            }

            // 验证预约时间不能是过去时间
            if (model.ScheduledTime <= DateTime.Now)
            {
                return BadRequest("预约时间不能是过去时间");
            }

            // 验证发布目标：商户级别不需要发布目标，线路和终端级别需要
            if (model.PublishType != PublishTypeOption.Merchant && string.IsNullOrEmpty(model.PublishTarget))
            {
                return BadRequest("线路级别和终端级别发布必须指定发布目标");
            }

            // 商户级别发布时，发布目标设置为商户ID
            if (model.PublishType == PublishTypeOption.Merchant)
            {
                model.PublishTarget = model.MerchantID;
            }

            // 验证文件版本是否存在
            var fileVersion = await _dbContext.FileVers
                .Where(fv => fv.ID == model.FileVersionID && fv.MerchantID == model.MerchantID && !fv.IsDelete)
                .FirstOrDefaultAsync();

            if (fileVersion == null)
            {
                return BadRequest("指定的文件版本不存在");
            }

            // 检查是否已存在相同的待发布预约
            var existingScheduled = await _dbContext.ScheduledFilePublishs
                .Where(sfp => sfp.FileVersionID == model.FileVersionID
                             && sfp.PublishType == model.PublishType
                             && sfp.PublishTarget == model.PublishTarget
                             && sfp.Status == ScheduledPublishStatus.Pending)
                .FirstOrDefaultAsync();

            if (existingScheduled != null)
            {
                return Conflict("已存在相同的待发布预约");
            }

            // 创建预约发布记录
            var scheduledFilePublish = new ScheduledFilePublish
            {
                FileVersionID = model.FileVersionID,
                MerchantID = model.MerchantID,
                FileTypeID = fileVersion.FileTypeID,
                FilePara = fileVersion.FilePara,
                FileFullType = fileVersion.FileFullType,
                Ver = fileVersion.Ver,
                PublishType = model.PublishType,
                PublishTarget = model.PublishTarget,
                ScheduledTime = model.ScheduledTime,
                Status = ScheduledPublishStatus.Pending,
                CreatedTime = DateTime.Now,
                CreatedBy = username,
                Remarks = model.Remarks
            };

            _dbContext.ScheduledFilePublishs.Add(scheduledFilePublish);
            await _dbContext.SaveChangesAsync();

            // 返回创建的记录
            var result = await GetScheduledFilePublish(scheduledFilePublish.ID);
            return CreatedAtAction(nameof(GetScheduledFilePublish), new { id = scheduledFilePublish.ID }, result.Value);
        }

        // PUT: api/ScheduledFilePublish/5
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateScheduledFilePublish(int id, [FromBody] UpdateScheduledFilePublishDto model)
        {
            // 获取当前用户信息
            var currentUserMerchantId = await _userService.GetUserMerchantIdAsync(User);
            var isSystemAdmin = User.IsInRole("SystemAdmin");

            var scheduledFilePublish = await _dbContext.ScheduledFilePublishs.FindAsync(id);
            if (scheduledFilePublish == null)
            {
                return NotFound();
            }

            // 如果不是系统管理员，只能修改自己商户的预约发布
            if (!isSystemAdmin && scheduledFilePublish.MerchantID != currentUserMerchantId)
            {
                return Forbid();
            }

            // 只有待发布状态的记录才能修改
            if (scheduledFilePublish.Status != ScheduledPublishStatus.Pending)
            {
                return BadRequest("只有待发布状态的预约才能修改");
            }

            // 验证预约时间不能是过去时间
            if (model.ScheduledTime <= DateTime.Now)
            {
                return BadRequest("预约时间不能是过去时间");
            }

            // 验证发布目标：商户级别不需要发布目标，线路和终端级别需要
            if (model.PublishType != PublishTypeOption.Merchant && string.IsNullOrEmpty(model.PublishTarget))
            {
                return BadRequest("线路级别和终端级别发布必须指定发布目标");
            }

            // 更新字段
            scheduledFilePublish.PublishType = model.PublishType;
            scheduledFilePublish.PublishTarget = model.PublishType == PublishTypeOption.Merchant
                ? scheduledFilePublish.MerchantID
                : model.PublishTarget;
            scheduledFilePublish.ScheduledTime = model.ScheduledTime;
            scheduledFilePublish.Remarks = model.Remarks;

            await _dbContext.SaveChangesAsync();

            return NoContent();
        }

        // DELETE: api/ScheduledFilePublish/5
        [HttpDelete("{id}")]
        public async Task<IActionResult> CancelScheduledFilePublish(int id)
        {
            // 获取当前用户信息
            var currentUserMerchantId = await _userService.GetUserMerchantIdAsync(User);
            var isSystemAdmin = User.IsInRole("SystemAdmin");

            var scheduledFilePublish = await _dbContext.ScheduledFilePublishs.FindAsync(id);
            if (scheduledFilePublish == null)
            {
                return NotFound();
            }

            // 如果不是系统管理员，只能取消自己商户的预约发布
            if (!isSystemAdmin && scheduledFilePublish.MerchantID != currentUserMerchantId)
            {
                return Forbid();
            }

            // 只有待发布状态的记录才能取消
            if (scheduledFilePublish.Status != ScheduledPublishStatus.Pending)
            {
                return BadRequest("只有待发布状态的预约才能取消");
            }

            // 更新状态为已取消
            scheduledFilePublish.Status = ScheduledPublishStatus.Cancelled;
            await _dbContext.SaveChangesAsync();

            return NoContent();
        }
    }
}
