using Microsoft.EntityFrameworkCore;
using SlzrCrossGate.Core.Database;
using SlzrCrossGate.Core.Models;
using SlzrCrossGate.WebAdmin.DTOs;

namespace SlzrCrossGate.WebAdmin.Services
{
    /// <summary>
    /// 菜单管理服务
    /// </summary>
    public class MenuService
    {
        private readonly TcpDbContext _dbContext;
        private readonly ILogger<MenuService> _logger;

        public MenuService(TcpDbContext dbContext, ILogger<MenuService> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }

        /// <summary>
        /// 获取所有菜单分组和菜单项
        /// </summary>
        /// <returns></returns>
        public async Task<List<MenuGroupDto>> GetAllMenusAsync()
        {
            var menuGroups = await _dbContext.MenuGroups
                .Include(g => g.MenuItems)
                .OrderBy(g => g.SortOrder)
                .ThenBy(g => g.Id)
                .ToListAsync();

            return menuGroups.Select(g => new MenuGroupDto
            {
                Id = g.Id,
                GroupKey = g.GroupKey,
                Title = g.Title,
                IconName = g.IconName,
                SortOrder = g.SortOrder,
                IsEnabled = g.IsEnabled,
                VisibleToSystemAdmin = g.VisibleToSystemAdmin,
                VisibleToMerchantAdmin = g.VisibleToMerchantAdmin,
                VisibleToUser = g.VisibleToUser,
                CreatedAt = g.CreatedAt,
                UpdatedAt = g.UpdatedAt,
                MenuItems = g.MenuItems
                    .OrderBy(i => i.SortOrder)
                    .ThenBy(i => i.Id)
                    .Select(i => new MenuItemDto
                    {
                        Id = i.Id,
                        MenuGroupId = i.MenuGroupId,
                        ItemKey = i.ItemKey,
                        Title = i.Title,
                        Href = i.Href,
                        IconName = i.IconName,
                        SortOrder = i.SortOrder,
                        IsEnabled = i.IsEnabled,
                        VisibleToSystemAdmin = i.VisibleToSystemAdmin,
                        VisibleToMerchantAdmin = i.VisibleToMerchantAdmin,
                        VisibleToUser = i.VisibleToUser,
                        IsExternal = i.IsExternal,
                        Target = i.Target,
                        CreatedAt = i.CreatedAt,
                        UpdatedAt = i.UpdatedAt
                    }).ToList()
            }).ToList();
        }

        /// <summary>
        /// 根据用户角色获取可见的菜单
        /// </summary>
        /// <param name="userRoles">用户角色列表</param>
        /// <returns></returns>
        public async Task<List<MenuGroupDto>> GetMenusByRoleAsync(List<string> userRoles)
        {
            var allMenus = await GetAllMenusAsync();
            var isSystemAdmin = userRoles.Contains("SystemAdmin");
            var isMerchantAdmin = userRoles.Contains("MerchantAdmin");

            return allMenus
                .Where(g => g.IsEnabled && IsGroupVisibleToUser(g, isSystemAdmin, isMerchantAdmin))
                .Select(g => new MenuGroupDto
                {
                    Id = g.Id,
                    GroupKey = g.GroupKey,
                    Title = g.Title,
                    IconName = g.IconName,
                    SortOrder = g.SortOrder,
                    IsEnabled = g.IsEnabled,
                    VisibleToSystemAdmin = g.VisibleToSystemAdmin,
                    VisibleToMerchantAdmin = g.VisibleToMerchantAdmin,
                    VisibleToUser = g.VisibleToUser,
                    CreatedAt = g.CreatedAt,
                    UpdatedAt = g.UpdatedAt,
                    MenuItems = g.MenuItems
                        .Where(i => i.IsEnabled && IsItemVisibleToUser(i, isSystemAdmin, isMerchantAdmin))
                        .ToList()
                })
                .Where(g => g.MenuItems.Any()) // 只返回有可见菜单项的分组
                .ToList();
        }

        /// <summary>
        /// 检查菜单分组是否对用户可见
        /// </summary>
        private bool IsGroupVisibleToUser(MenuGroupDto group, bool isSystemAdmin, bool isMerchantAdmin)
        {
            if (isSystemAdmin) return group.VisibleToSystemAdmin;
            if (isMerchantAdmin) return group.VisibleToMerchantAdmin;
            return group.VisibleToUser;
        }

        /// <summary>
        /// 检查菜单项是否对用户可见
        /// </summary>
        private bool IsItemVisibleToUser(MenuItemDto item, bool isSystemAdmin, bool isMerchantAdmin)
        {
            if (isSystemAdmin) return item.VisibleToSystemAdmin;
            if (isMerchantAdmin) return item.VisibleToMerchantAdmin;
            return item.VisibleToUser;
        }

        /// <summary>
        /// 初始化默认菜单数据
        /// </summary>
        public async Task InitializeDefaultMenusAsync()
        {
            // 检查是否已有菜单数据
            var existingCount = await _dbContext.MenuGroups.CountAsync();
            if (existingCount > 0)
            {
                _logger.LogInformation("菜单数据已存在，跳过初始化");
                return;
            }

            _logger.LogInformation("开始初始化默认菜单数据");

            var defaultMenus = GetDefaultMenuData();

            foreach (var groupData in defaultMenus)
            {
                var menuGroup = new MenuGroup
                {
                    GroupKey = groupData.GroupKey,
                    Title = groupData.Title,
                    IconName = groupData.IconName,
                    SortOrder = groupData.SortOrder,
                    IsEnabled = groupData.IsEnabled,
                    VisibleToSystemAdmin = groupData.VisibleToSystemAdmin,
                    VisibleToMerchantAdmin = groupData.VisibleToMerchantAdmin,
                    VisibleToUser = groupData.VisibleToUser
                };

                _dbContext.MenuGroups.Add(menuGroup);
                await _dbContext.SaveChangesAsync(); // 保存以获取ID

                foreach (var itemData in groupData.MenuItems)
                {
                    var menuItem = new MenuItem
                    {
                        MenuGroupId = menuGroup.Id,
                        ItemKey = itemData.ItemKey,
                        Title = itemData.Title,
                        Href = itemData.Href,
                        IconName = itemData.IconName,
                        SortOrder = itemData.SortOrder,
                        IsEnabled = itemData.IsEnabled,
                        VisibleToSystemAdmin = itemData.VisibleToSystemAdmin,
                        VisibleToMerchantAdmin = itemData.VisibleToMerchantAdmin,
                        VisibleToUser = itemData.VisibleToUser
                    };

                    _dbContext.MenuItems.Add(menuItem);
                }
            }

            await _dbContext.SaveChangesAsync();
            _logger.LogInformation("默认菜单数据初始化完成");
        }

        /// <summary>
        /// 获取默认菜单数据（基于当前硬编码的菜单结构）
        /// </summary>
        private List<MenuGroupDto> GetDefaultMenuData()
        {
            return new List<MenuGroupDto>
            {
                new MenuGroupDto
                {
                    GroupKey = "overview",
                    Title = "概览监控",
                    IconName = "ActivityIcon",
                    SortOrder = 1,
                    IsEnabled = true,
                    VisibleToSystemAdmin = true,
                    VisibleToMerchantAdmin = true,
                    VisibleToUser = true,
                    MenuItems = new List<MenuItemDto>
                    {
                        new MenuItemDto
                        {
                            ItemKey = "dashboard",
                            Title = "仪表盘",
                            Href = "/app/dashboard",
                            IconName = "BarChartIcon",
                            SortOrder = 1,
                            IsEnabled = true,
                            VisibleToSystemAdmin = true,
                            VisibleToMerchantAdmin = true,
                            VisibleToUser = true
                        }
                    }
                },
                new MenuGroupDto
                {
                    GroupKey = "terminal",
                    Title = "终端管理",
                    IconName = "SmartphoneIcon",
                    SortOrder = 2,
                    IsEnabled = true,
                    VisibleToSystemAdmin = true,
                    VisibleToMerchantAdmin = true,
                    VisibleToUser = true,
                    MenuItems = new List<MenuItemDto>
                    {
                        new MenuItemDto
                        {
                            ItemKey = "terminals",
                            Title = "终端管理",
                            Href = "/app/terminals",
                            IconName = "SmartphoneIcon",
                            SortOrder = 1,
                            IsEnabled = true,
                            VisibleToSystemAdmin = true,
                            VisibleToMerchantAdmin = true,
                            VisibleToUser = true
                        },
                        new MenuItemDto
                        {
                            ItemKey = "terminal-records",
                            Title = "终端记录",
                            Href = "/app/terminal-records",
                            IconName = "ListIcon",
                            SortOrder = 2,
                            IsEnabled = true,
                            VisibleToSystemAdmin = true,
                            VisibleToMerchantAdmin = true,
                            VisibleToUser = false
                        },
                        new MenuItemDto
                        {
                            ItemKey = "terminal-events",
                            Title = "终端事件",
                            Href = "/app/terminal-events",
                            IconName = "CpuIcon",
                            SortOrder = 3,
                            IsEnabled = true,
                            VisibleToSystemAdmin = true,
                            VisibleToMerchantAdmin = true,
                            VisibleToUser = false
                        },
                        new MenuItemDto
                        {
                            ItemKey = "unregistered-lines",
                            Title = "未注册线路",
                            Href = "/app/unregistered-lines",
                            IconName = "MonitorIcon",
                            SortOrder = 4,
                            IsEnabled = true,
                            VisibleToSystemAdmin = true,
                            VisibleToMerchantAdmin = true,
                            VisibleToUser = false
                        },
                        new MenuItemDto
                        {
                            ItemKey = "terminal-file-uploads",
                            Title = "文件上传管理",
                            Href = "/app/terminal-file-uploads",
                            IconName = "Upload",
                            SortOrder = 5,
                            IsEnabled = true,
                            VisibleToSystemAdmin = true,
                            VisibleToMerchantAdmin = true,
                            VisibleToUser = false
                        },
                        new MenuItemDto
                        {
                            ItemKey = "terminal-logs",
                            Title = "终端日志记录",
                            Href = "/app/terminal-logs",
                            IconName = "ActivityIcon",
                            SortOrder = 6,
                            IsEnabled = true,
                            VisibleToSystemAdmin = true,
                            VisibleToMerchantAdmin = true,
                            VisibleToUser = false
                        }
                    }
                },
                new MenuGroupDto
                {
                    GroupKey = "content",
                    Title = "内容管理",
                    IconName = "FolderIcon",
                    SortOrder = 3,
                    IsEnabled = true,
                    VisibleToSystemAdmin = true,
                    VisibleToMerchantAdmin = true,
                    VisibleToUser = false,
                    MenuItems = new List<MenuItemDto>
                    {
                        new MenuItemDto
                        {
                            ItemKey = "file-types",
                            Title = "文件类型",
                            Href = "/app/files/types",
                            IconName = "FileTextIcon",
                            SortOrder = 1,
                            IsEnabled = true,
                            VisibleToSystemAdmin = true,
                            VisibleToMerchantAdmin = true,
                            VisibleToUser = false
                        },
                        new MenuItemDto
                        {
                            ItemKey = "file-versions",
                            Title = "文件版本",
                            Href = "/app/files/versions",
                            IconName = "ArchiveIcon",
                            SortOrder = 2,
                            IsEnabled = true,
                            VisibleToSystemAdmin = true,
                            VisibleToMerchantAdmin = true,
                            VisibleToUser = false
                        },
                        new MenuItemDto
                        {
                            ItemKey = "scheduled-publish",
                            Title = "预约发布",
                            Href = "/app/files/scheduled-publish",
                            IconName = "ScheduleIcon",
                            SortOrder = 3,
                            IsEnabled = true,
                            VisibleToSystemAdmin = true,
                            VisibleToMerchantAdmin = true,
                            VisibleToUser = false
                        },
                        new MenuItemDto
                        {
                            ItemKey = "file-publish",
                            Title = "发布记录",
                            Href = "/app/files/publish-list",
                            IconName = "SendIcon",
                            SortOrder = 4,
                            IsEnabled = true,
                            VisibleToSystemAdmin = true,
                            VisibleToMerchantAdmin = true,
                            VisibleToUser = false
                        },
                        new MenuItemDto
                        {
                            ItemKey = "message-types",
                            Title = "消息类型",
                            Href = "/app/messages-types",
                            IconName = "TagIcon",
                            SortOrder = 4,
                            IsEnabled = true,
                            VisibleToSystemAdmin = true,
                            VisibleToMerchantAdmin = true,
                            VisibleToUser = false
                        },
                        new MenuItemDto
                        {
                            ItemKey = "messages",
                            Title = "消息管理",
                            Href = "/app/messages",
                            IconName = "MessageSquareIcon",
                            SortOrder = 5,
                            IsEnabled = true,
                            VisibleToSystemAdmin = true,
                            VisibleToMerchantAdmin = true,
                            VisibleToUser = false
                        }
                    }
                },
                new MenuGroupDto
                {
                    GroupKey = "business",
                    Title = "业务配置",
                    IconName = "SlidersIcon",
                    SortOrder = 4,
                    IsEnabled = true,
                    VisibleToSystemAdmin = true,
                    VisibleToMerchantAdmin = true,
                    VisibleToUser = false,
                    MenuItems = new List<MenuItemDto>
                    {
                        new MenuItemDto
                        {
                            ItemKey = "fare-params",
                            Title = "线路参数",
                            Href = "/app/fare-params",
                            IconName = "CreditCardIcon",
                            SortOrder = 1,
                            IsEnabled = true,
                            VisibleToSystemAdmin = true,
                            VisibleToMerchantAdmin = true,
                            VisibleToUser = false
                        },
                        new MenuItemDto
                        {
                            ItemKey = "union-pay-keys",
                            Title = "银联密钥",
                            Href = "/app/union-pay-terminal-keys",
                            IconName = "DatabaseIcon",
                            SortOrder = 2,
                            IsEnabled = true,
                            VisibleToSystemAdmin = true,
                            VisibleToMerchantAdmin = true,
                            VisibleToUser = false
                        },
                        new MenuItemDto
                        {
                            ItemKey = "dictionary",
                            Title = "商户字典",
                            Href = "/app/dictionary",
                            IconName = "BookIcon",
                            SortOrder = 3,
                            IsEnabled = true,
                            VisibleToSystemAdmin = true,
                            VisibleToMerchantAdmin = true,
                            VisibleToUser = false
                        },
                        new MenuItemDto
                        {
                            ItemKey = "vehicles",
                            Title = "车辆管理",
                            Href = "/app/vehicles",
                            IconName = "CarIcon",
                            SortOrder = 4,
                            IsEnabled = true,
                            VisibleToSystemAdmin = true,
                            VisibleToMerchantAdmin = true,
                            VisibleToUser = false
                        },
                        new MenuItemDto
                        {
                            ItemKey = "fare-discount-schemes",
                            Title = "票价折扣方案",
                            Href = "/app/fare-discount-schemes",
                            IconName = "PercentIcon",
                            SortOrder = 5,
                            IsEnabled = true,
                            VisibleToSystemAdmin = true,
                            VisibleToMerchantAdmin = true,
                            VisibleToUser = false
                        }
                    }
                },
                new MenuGroupDto
                {
                    GroupKey = "system",
                    Title = "系统管理",
                    IconName = "ShieldIcon",
                    SortOrder = 5,
                    IsEnabled = true,
                    VisibleToSystemAdmin = true,
                    VisibleToMerchantAdmin = true,
                    VisibleToUser = false,
                    MenuItems = new List<MenuItemDto>
                    {
                        new MenuItemDto
                        {
                            ItemKey = "users",
                            Title = "用户管理",
                            Href = "/app/users",
                            IconName = "UsersIcon",
                            SortOrder = 1,
                            IsEnabled = true,
                            VisibleToSystemAdmin = true,
                            VisibleToMerchantAdmin = true,
                            VisibleToUser = false
                        },
                        new MenuItemDto
                        {
                            ItemKey = "roles",
                            Title = "角色管理",
                            Href = "/app/roles",
                            IconName = "LockIcon",
                            SortOrder = 2,
                            IsEnabled = true,
                            VisibleToSystemAdmin = true,
                            VisibleToMerchantAdmin = false,
                            VisibleToUser = false
                        },
                        new MenuItemDto
                        {
                            ItemKey = "merchants",
                            Title = "商户管理",
                            Href = "/app/merchants",
                            IconName = "ShoppingBagIcon",
                            SortOrder = 3,
                            IsEnabled = true,
                            VisibleToSystemAdmin = true,
                            VisibleToMerchantAdmin = false,
                            VisibleToUser = false
                        },
                        new MenuItemDto
                        {
                            ItemKey = "settings",
                            Title = "系统设置",
                            Href = "/app/settings",
                            IconName = "SettingsIcon",
                            SortOrder = 4,
                            IsEnabled = true,
                            VisibleToSystemAdmin = true,
                            VisibleToMerchantAdmin = false,
                            VisibleToUser = false
                        },
                        new MenuItemDto
                        {
                            ItemKey = "feature-permissions",
                            Title = "功能权限管理",
                            Href = "/app/feature-permissions",
                            IconName = "SecurityIcon",
                            SortOrder = 5,
                            IsEnabled = true,
                            VisibleToSystemAdmin = true,
                            VisibleToMerchantAdmin = false,
                            VisibleToUser = false
                        },
                        new MenuItemDto
                        {
                            ItemKey = "menu-management",
                            Title = "菜单管理",
                            Href = "/app/menu-management",
                            IconName = "MenuIcon",
                            SortOrder = 6,
                            IsEnabled = true,
                            VisibleToSystemAdmin = true,
                            VisibleToMerchantAdmin = false,
                            VisibleToUser = false
                        }
                    }
                },
                new MenuGroupDto
                {
                    GroupKey = "audit",
                    Title = "日志审计",
                    IconName = "FileTextIcon",
                    SortOrder = 6,
                    IsEnabled = true,
                    VisibleToSystemAdmin = true,
                    VisibleToMerchantAdmin = true,
                    VisibleToUser = false,
                    MenuItems = new List<MenuItemDto>
                    {
                        new MenuItemDto
                        {
                            ItemKey = "login-logs",
                            Title = "登录日志",
                            Href = "/app/audit-logs/login-logs",
                            IconName = "LogInIcon",
                            SortOrder = 1,
                            IsEnabled = true,
                            VisibleToSystemAdmin = true,
                            VisibleToMerchantAdmin = true,
                            VisibleToUser = false
                        },
                        new MenuItemDto
                        {
                            ItemKey = "password-logs",
                            Title = "改密日志",
                            Href = "/app/audit-logs/password-logs",
                            IconName = "KeyIcon",
                            SortOrder = 2,
                            IsEnabled = true,
                            VisibleToSystemAdmin = true,
                            VisibleToMerchantAdmin = true,
                            VisibleToUser = false
                        },
                        new MenuItemDto
                        {
                            ItemKey = "operation-logs",
                            Title = "操作日志",
                            Href = "/app/audit-logs/operation-logs",
                            IconName = "ActivityIcon",
                            SortOrder = 3,
                            IsEnabled = true,
                            VisibleToSystemAdmin = true,
                            VisibleToMerchantAdmin = true,
                            VisibleToUser = false
                        }
                    }
                },
                new MenuGroupDto
                {
                    GroupKey = "customer-features",
                    Title = "客户功能",
                    IconName = "StarIcon",
                    SortOrder = 7,
                    IsEnabled = false, // 默认禁用，需要时手动启用
                    VisibleToSystemAdmin = true,
                    VisibleToMerchantAdmin = true,
                    VisibleToUser = false,
                    MenuItems = new List<MenuItemDto>
                    {
                        new MenuItemDto
                        {
                            ItemKey = "customer-demo",
                            Title = "客户演示功能",
                            Href = "http://localhost:5271", // 客户项目URL
                            IconName = "SettingsIcon",
                            SortOrder = 1,
                            IsEnabled = false, // 默认禁用
                            VisibleToSystemAdmin = true,
                            VisibleToMerchantAdmin = true,
                            VisibleToUser = false,
                            IsExternal = true,
                            Target = "_iframe"
                        }
                    }
                }
            };
        }
    }
}
