# 页面按钮权限控制实施计划

## 🎯 实施范围

需要实现按钮权限控制的页面：
1. ✅ 终端管理 (TerminalList.jsx)
2. ✅ 文件类型 (FileTypeList.jsx) 
3. ✅ 文件版本 (FileVersionList.jsx)
4. ✅ 发布记录 (FilePublishList.jsx)
5. ✅ 消息类型 (MessageTypeList.jsx)
6. ✅ 线路参数 (LinePriceListView.jsx)
7. ✅ 线路参数版本管理 (LinePriceVersionsView.jsx)
8. ✅ 银联密钥 (UnionPayTerminalKeyList.jsx)
9. ✅ 票价折扣方案 (FareDiscountSchemesView.jsx)

## 📋 实施阶段

### 阶段一：基础框架搭建 (1周)

#### 1.1 权限常量和基础组件
- [x] 创建权限常量文件 `constants/permissions.js`
- [ ] 实现 `useFeaturePermission` Hook
- [ ] 实现 `FeatureGuard` 组件
- [ ] 实现辅助Hook (`useBatchPermissions`) - 注意：不使用页面级权限检查

#### 1.2 后端权限服务
- [ ] 创建数据库表 (FeatureConfig, RoleFeaturePermission)
- [ ] 实现权限服务 `IFeaturePermissionService`
- [ ] 创建权限验证特性 `RequireFeaturePermissionAttribute`
- [ ] 实现权限管理API控制器

### 阶段二：核心页面改造 (2周)

#### 2.1 高优先级页面 (第1周)
1. **终端管理页面** - 使用频率高，权限需求复杂
   - 查看详情、发送消息、发布文件、查看事件按钮
   - 需要考虑终端在线状态的业务逻辑
   
2. **票价折扣方案页面** - 业务重要性高
   - 编辑、提交版本、版本历史、删除按钮
   - 需要考虑方案状态和版本发布状态
   
3. **文件类型管理页面** - 基础数据管理
   - 创建、编辑、删除按钮
   - 相对简单，适合作为实施模板

#### 2.2 中优先级页面 (第2周)
4. **文件版本管理页面**
   - 创建、编辑、删除、发布、下载按钮
   
5. **线路参数管理页面**
   - 创建、编辑、删除、导出按钮
   
6. **银联密钥管理页面**
   - 创建、编辑、删除、导出按钮

### 阶段三：其他页面改造 (1周)

#### 3.1 低优先级页面
7. **发布记录页面**
   - 查看、删除、重试、导出按钮
   
8. **消息类型页面**
   - 创建、编辑、删除按钮
   
9. **线路参数版本管理页面**
   - 创建、编辑、删除、发布、预览按钮

### 阶段四：测试和优化 (1周)

#### 4.1 功能测试
- [ ] 单元测试：Hook和组件功能测试
- [ ] 集成测试：权限验证流程测试
- [ ] 用户测试：不同角色权限验证

#### 4.2 性能优化
- [ ] 权限缓存优化
- [ ] 批量权限检查优化
- [ ] 页面加载性能优化

## 🔧 技术实施要点

### 1. 权限检查策略

**重要：本项目不使用页面级权限检查，只对具体按钮功能进行权限控制**

```javascript
// 不要使用页面级权限检查！
// 页面本身对所有用户开放

// 只对按钮级功能进行权限检查
<FeatureGuard
  featureKey={PERMISSIONS.MODULE.DELETE}
  additionalCheck={businessLogicCheck}
  fallback={<DisabledButton />}
>
  <ActionButton />
</FeatureGuard>
```

### 2. 性能优化策略
```javascript
// 批量权限检查
const permissions = useMemo(() => 
  checkMultiple([
    PERMISSIONS.MODULE.EDIT,
    PERMISSIONS.MODULE.DELETE,
    PERMISSIONS.MODULE.EXPORT
  ]), 
  [checkMultiple]
);
```

### 3. 业务逻辑集成
```javascript
// 结合业务条件的权限判断
const canDelete = permissions[PERMISSIONS.MODULE.DELETE] && 
  item.status !== 'active' && 
  (user.roles.includes('SystemAdmin') || item.createdBy === user.id);
```

## 📊 各页面权限矩阵

| 页面 | 查看 | 创建 | 编辑 | 删除 | 特殊操作 |
|------|------|------|------|------|----------|
| 终端管理 | ✅ | ❌ | ❌ | ❌ | 发送消息、发布文件、查看事件 |
| 文件类型 | ✅ | ✅ | ✅ | ✅ | - |
| 文件版本 | ✅ | ✅ | ✅ | ✅ | 发布、下载 |
| 发布记录 | ✅ | ❌ | ❌ | ✅ | 重试、导出 |
| 消息类型 | ✅ | ✅ | ✅ | ✅ | - |
| 线路参数 | ✅ | ✅ | ✅ | ✅ | 导出 |
| 线路参数版本 | ✅ | ✅ | ✅ | ✅ | 发布、预览 |
| 银联密钥 | ✅ | ✅ | ✅ | ✅ | 导出 |
| 票价折扣方案 | ✅ | ✅ | ✅ | ✅ | 提交版本、版本历史、复制 |

## 🎯 成功标准

### 功能完整性
- [ ] 所有页面的操作按钮都有权限控制
- [ ] 权限控制逻辑正确，无绕过风险
- [ ] 用户体验良好，权限不足时有明确提示

### 性能要求
- [ ] 页面加载时间不超过原来的20%
- [ ] 权限检查响应时间 < 100ms
- [ ] 缓存命中率 > 90%

### 安全要求
- [ ] 前后端双重权限验证
- [ ] 权限变更实时生效
- [ ] 完整的权限审计日志

## 📝 注意事项

1. **向后兼容**：实施过程中保持现有功能正常运行
2. **渐进式改造**：按页面优先级逐步实施，避免大规模变更
3. **测试覆盖**：每个页面改造完成后立即进行测试
4. **文档更新**：及时更新用户手册和开发文档
5. **用户培训**：为管理员提供权限配置培训

## 🚀 下一步行动

1. **立即开始**：实施阶段一的基础框架搭建
2. **准备数据**：设计默认权限配置数据
3. **团队协调**：分配开发任务和测试责任
4. **环境准备**：搭建开发和测试环境
