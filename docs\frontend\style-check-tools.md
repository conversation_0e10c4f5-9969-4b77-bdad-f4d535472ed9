# WebAdmin样式检查工具

## 📋 概述

本文档介绍WebAdmin项目的样式规范检查工具，帮助开发者自动检查代码是否符合UI样式规范。

## 🔧 样式检查脚本

### 安装位置
```
SlzrCrossGate.WebAdmin/ClientApp/scripts/style-check.js
```

### 使用方法
```bash
# 在ClientApp目录下运行
cd SlzrCrossGate.WebAdmin/ClientApp
node scripts/style-check.js

# 或添加到package.json的scripts中
npm run style-check
```

### 检查规则

#### 1. Container maxWidth检查
- **检查内容**: `<Container maxWidth="xl">`
- **错误提示**: 发现使用固定maxWidth，应该使用maxWidth={false}
- **修复建议**: 使用 `<Container maxWidth={false}>` 让页面适应屏幕宽度

#### 2. 手动fontSize检查
- **检查内容**: `fontSize: '14px'` 等手动设置
- **错误提示**: 发现手动设置fontSize，应该使用Material-UI的variant
- **修复建议**: 使用 `variant="body1|body2|subtitle1|subtitle2|h4|h6|caption"`

#### 3. 硬编码颜色检查
- **检查内容**: `color: '#666666'` 等硬编码颜色
- **错误提示**: 发现硬编码颜色值，应该使用主题颜色
- **修复建议**: 使用 `color="primary|secondary|text.primary|text.secondary"`

#### 4. Typography无variant检查
- **检查内容**: `<Typography>` 未指定variant
- **错误提示**: Typography组件未指定variant
- **修复建议**: 添加 `variant="body1|body2|subtitle1|subtitle2|h4|h6|caption"`

#### 5. 响应式断点检查
- **检查内容**: `<Grid item xs={12} sm={12}>` 等不合理断点
- **错误提示**: 可能不合理的响应式断点设置
- **修复建议**: 使用更合理的断点：`xs={12} sm={6} md={4} lg={3}`

### 脚本配置

```javascript
// 配置选项
const config = {
  srcDir: path.join(__dirname, '../src'),
  excludePatterns: [
    '**/node_modules/**',
    '**/build/**',
    '**/dist/**',
    '**/*.test.js',
    '**/*.spec.js'
  ],
  filePatterns: [
    '**/*.jsx',
    '**/*.js'
  ]
};
```

## 🔍 ESLint规则配置

### 配置文件位置
```
SlzrCrossGate.WebAdmin/ClientApp/.eslintrc.webadmin-style.js
```

### 自定义规则

#### 1. typography-variant-required
- **功能**: 检查Typography组件必须指定variant属性
- **级别**: error
- **自动修复**: 否

#### 2. container-maxwidth-false
- **功能**: 检查Container组件应该使用maxWidth={false}
- **级别**: error
- **自动修复**: 是

#### 3. no-manual-fontsize
- **功能**: 禁止手动设置fontSize
- **级别**: error
- **自动修复**: 否

#### 4. use-theme-colors
- **功能**: 建议使用主题颜色而不是硬编码颜色值
- **级别**: warn
- **自动修复**: 否

### 使用方法
```bash
# 运行ESLint检查
npm run lint

# 运行ESLint并自动修复
npm run lint:fix
```

## 📋 检查清单模板

### 开发时检查
```markdown
- [ ] Container使用 `maxWidth={false}`
- [ ] Typography都有variant属性
- [ ] 没有手动设置fontSize
- [ ] 使用主题颜色而非硬编码
- [ ] 响应式断点设置合理
- [ ] 卡片使用 `height: '100%'`
- [ ] 图标使用统一映射系统
- [ ] 颜色使用主题定义
- [ ] 交互状态处理完整
- [ ] 不同屏幕尺寸测试正常
```

### 代码审查检查
```markdown
- [ ] 样式代码符合规范
- [ ] 没有硬编码的颜色值
- [ ] 响应式布局合理
- [ ] 组件复用性良好
- [ ] 可访问性考虑充分
- [ ] 性能优化合理
- [ ] 代码可维护性好
```

## 🚀 集成到CI/CD

### GitHub Actions示例
```yaml
name: Style Check
on: [push, pull_request]

jobs:
  style-check:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '18'
      - name: Install dependencies
        run: |
          cd SlzrCrossGate.WebAdmin/ClientApp
          npm install
      - name: Run style check
        run: |
          cd SlzrCrossGate.WebAdmin/ClientApp
          npm run style-check
      - name: Run ESLint
        run: |
          cd SlzrCrossGate.WebAdmin/ClientApp
          npm run lint
```

### 本地Git Hook
```bash
# 在.git/hooks/pre-commit中添加
#!/bin/sh
cd SlzrCrossGate.WebAdmin/ClientApp
npm run style-check
if [ $? -ne 0 ]; then
  echo "样式检查失败，请修复后再提交"
  exit 1
fi
```

## 📊 检查报告示例

### 成功示例
```
🔍 开始WebAdmin前端UI样式规范检查...

📁 扫描 156 个文件...

✅ 恭喜！所有文件都符合UI样式规范！
```

### 错误示例
```
🔍 开始WebAdmin前端UI样式规范检查...

📁 扫描 156 个文件...

❌ 发现 3 个样式规范问题：

📄 src/pages/example/ExamplePage.jsx
   第15行: ❌ 发现使用固定maxWidth="xl"，应该使用maxWidth={false}
   代码: <Container maxWidth="xl">
   建议: 使用 <Container maxWidth={false}> 让页面适应屏幕宽度

   第28行: ❌ 发现手动设置fontSize，应该使用Material-UI的variant
   代码: fontSize: '14px'
   建议: 使用 variant="body1|body2|subtitle1|subtitle2|h4|h6|caption" 替代手动fontSize

📚 请参考《WebAdmin前端UI样式规范.md》了解详细规范要求。
```

## 🔧 工具维护

### 更新检查规则
1. 编辑 `scripts/style-check.js` 中的 `rules` 数组
2. 添加新的检查模式和提示信息
3. 测试新规则的准确性

### 更新ESLint规则
1. 编辑 `.eslintrc.webadmin-style.js`
2. 添加或修改自定义规则
3. 更新规则文档

### 版本管理
- 工具版本与样式规范版本保持同步
- 重大更新时通知所有开发者
- 保持向后兼容性

## 📚 相关文档

- [UI样式规范](./ui-style-guide.md)
- [样式规范快速参考](./ui-style-quick-reference.md)
- [前端开发规范](../frontend-development.md)

---

**提醒**：定期运行样式检查，确保代码质量和规范一致性。
