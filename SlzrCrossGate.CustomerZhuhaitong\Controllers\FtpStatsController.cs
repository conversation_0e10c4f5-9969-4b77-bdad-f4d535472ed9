using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SlzrCrossGate.CustomerZhuhaitong.Models;
using SlzrCrossGate.CustomerZhuhaitong.Services;
using System.Security.Claims;

namespace SlzrCrossGate.CustomerZhuhaitong.Controllers
{
    /// <summary>
    /// FTP文件上传统计控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class FtpStatsController : ControllerBase
    {
        private readonly FtpFileStatsService _ftpStatsService;
        private readonly ReadOnlyAuthService _authService;
        private readonly ILogger<FtpStatsController> _logger;

        public FtpStatsController(
            FtpFileStatsService ftpStatsService,
            ReadOnlyAuthService authService,
            ILogger<FtpStatsController> logger)
        {
            _ftpStatsService = ftpStatsService;
            _authService = authService;
            _logger = logger;
        }

        /// <summary>
        /// 获取FTP文件上传统计
        /// </summary>
        /// <param name="queryDate">查询日期</param>
        /// <returns>FTP文件上传统计响应</returns>
        [HttpGet]
        public async Task<ActionResult<FtpUploadStatsResponseDto>> GetFtpUploadStats([FromQuery] DateTime? queryDate = null)
        {
            try
            {
                // 验证用户身份
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized("用户身份验证失败");
                }

                var user = await _authService.ValidateUserAsync(userId);
                if (user == null)
                {
                    return Unauthorized("用户验证失败");
                }

                // 使用传入的日期或默认为今天
                var targetDate = queryDate ?? DateTime.Today;

                _logger.LogInformation("用户 {UserId} 查询FTP文件上传统计，日期: {QueryDate}", userId, targetDate);

                var stats = await _ftpStatsService.GetFtpUploadStatsAsync(targetDate);

                return Ok(stats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取FTP文件上传统计失败");
                return StatusCode(500, "服务器内部错误");
            }
        }

        /// <summary>
        /// 获取FTP服务器配置列表（仅显示基本信息）
        /// </summary>
        /// <returns>FTP服务器配置列表</returns>
        [HttpGet("servers")]
        public async Task<ActionResult<object>> GetFtpServers()
        {
            try
            {
                // 验证用户身份
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized("用户身份验证失败");
                }

                var user = await _authService.ValidateUserAsync(userId);
                if (user == null)
                {
                    return Unauthorized("用户验证失败");
                }

                _logger.LogInformation("用户 {UserId} 查询FTP服务器配置列表", userId);

                var servers = await _ftpStatsService.GetFtpServersAsync();

                // 只返回基本信息，不包含敏感的密码等信息
                var result = servers.Select(s => new
                {
                    s.ID,
                    s.UpOrder,
                    s.ServerDesc,
                    s.IsEnable,
                    s.Protocol,
                    s.FtpIp,
                    s.FtpPort
                }).ToList();

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取FTP服务器配置列表失败");
                return StatusCode(500, "服务器内部错误");
            }
        }
    }
}
