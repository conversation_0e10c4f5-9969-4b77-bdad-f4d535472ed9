import axios from 'axios';

class AuthService {
  constructor() {
    this.token = null;
    this.setupAxiosDefaults();
    this.setupAxiosInterceptors();
  }

  setupAxiosDefaults() {
    // 设置 axios 基础配置
    // API请求应该相对于当前域名，不需要设置baseURL
    // 因为我们的API路径已经通过nginx代理正确配置了

    console.log('Axios 使用默认配置，API请求将相对于当前域名');
  }

  setupAxiosInterceptors() {
    // 请求拦截器 - 添加认证头
    axios.interceptors.request.use(
      (config) => {
        if (this.token) {
          config.headers.Authorization = `Bearer ${this.token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // 响应拦截器 - 处理认证错误
    axios.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          // 认证失败，通知主应用
          window.parent.postMessage({ 
            type: 'AUTH_EXPIRED' 
          }, '*');
        }
        return Promise.reject(error);
      }
    );
  }

  // 从主应用获取认证信息
  async getAuthFromMainApp() {
    return new Promise((resolve, reject) => {
      // 向主应用请求认证信息
      window.parent.postMessage({ 
        type: 'REQUEST_AUTH_INFO' 
      }, '*');

      const timeout = setTimeout(() => {
        window.removeEventListener('message', handleResponse);
        reject(new Error('获取认证信息超时'));
      }, 5000);

      const handleResponse = (event) => {
        if (event.data.type === 'AUTH_INFO_RESPONSE') {
          clearTimeout(timeout);
          window.removeEventListener('message', handleResponse);
          
          if (event.data.success) {
            resolve({
              token: event.data.token,
              user: event.data.user
            });
          } else {
            reject(new Error(event.data.error || '获取认证信息失败'));
          }
        }
      };

      window.addEventListener('message', handleResponse);
    });
  }

  // 设置认证token
  setAuthToken(token) {
    this.token = token;
    localStorage.setItem('customer_auth_token', token);
  }

  // 清除认证token
  clearAuthToken() {
    this.token = null;
    localStorage.removeItem('customer_auth_token');
  }

  // 获取当前token
  getToken() {
    return this.token || localStorage.getItem('customer_auth_token');
  }

  // 验证token是否有效
  async validateToken(token) {
    try {
      const response = await axios.get('/api/auth/validate', {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });
      return response.data;
    } catch (error) {
      console.error('Token验证失败:', error);
      return null;
    }
  }

  // 刷新token
  async refreshToken() {
    try {
      // 向主应用请求刷新token
      window.parent.postMessage({ 
        type: 'REQUEST_TOKEN_REFRESH' 
      }, '*');
      
      return new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          window.removeEventListener('message', handleResponse);
          reject(new Error('刷新token超时'));
        }, 5000);

        const handleResponse = (event) => {
          if (event.data.type === 'TOKEN_REFRESH_RESPONSE') {
            clearTimeout(timeout);
            window.removeEventListener('message', handleResponse);
            
            if (event.data.success) {
              this.setAuthToken(event.data.token);
              resolve(event.data.token);
            } else {
              reject(new Error(event.data.error || '刷新token失败'));
            }
          }
        };

        window.addEventListener('message', handleResponse);
      });
    } catch (error) {
      console.error('刷新token失败:', error);
      throw error;
    }
  }

  // 登出
  logout() {
    this.clearAuthToken();
    // 通知主应用登出
    window.parent.postMessage({ 
      type: 'LOGOUT_REQUEST' 
    }, '*');
  }
}

export default new AuthService();
