using Microsoft.Extensions.Logging;
using SlzrCrossGate.Core.Services;
using SlzrCrossGate.Tcp.Protocol;

namespace SlzrCrossGate.Tcp.Handler
{
    /// <summary>
    /// 文件上传协议处理器
    /// </summary>
    public class FileUploadProtocolHandler
    {
        private readonly TerminalFileUploadService _uploadService;
        private readonly ILogger<FileUploadProtocolHandler> _logger;

        public FileUploadProtocolHandler(
            TerminalFileUploadService uploadService,
            ILogger<FileUploadProtocolHandler> logger)
        {
            _uploadService = uploadService;
            _logger = logger;
        }

        /// <summary>
        /// 处理文件上传协议消息
        /// </summary>
        public async Task<byte[]?> HandleMessageAsync(TcpConnectionContext context, byte[] messageData)
        {
            try
            {
                // 解析消息头获取消息类型
                if (messageData.Length < 16)
                {
                    _logger.LogWarning("File upload message too short: {Length}", messageData.Length);
                    return null;
                }

                var messageType = messageData[5]; // 消息类型在第6个字节
                var requestId = BitConverter.ToUInt32(messageData, 6); // 请求ID在第7-10字节

                _logger.LogDebug("Processing file upload message type {MessageType} from {TerminalId}, RequestId: {RequestId}",
                    messageType, context.TerminalID, requestId);

                return messageType switch
                {
                    FileUploadMessageType.FileUploadRequest => await HandleFileUploadRequestAsync(context, messageData, requestId),
                    FileUploadMessageType.FileChunkRequest => await HandleFileChunkRequestAsync(context, messageData, requestId),
                    FileUploadMessageType.ResumeQuery => await HandleResumeQueryAsync(context, messageData, requestId),
                    _ => await HandleUnsupportedMessageAsync(messageType, requestId)
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling file upload message from {TerminalId}", context.TerminalID);
                return null;
            }
        }

        /// <summary>
        /// 处理文件上传请求 (0x01)
        /// </summary>
        private async Task<byte[]> HandleFileUploadRequestAsync(TcpConnectionContext context, byte[] messageData, uint requestId)
        {
            try
            {
                var request = FileUploadMessage.Parse<FileUploadRequestMessage>(messageData);
                
                _logger.LogInformation("File upload request from {TerminalId}: {FileName}, Size: {FileSize}, Type: {FileType}",
                    context.TerminalID, request.FileName, request.FileSize, request.FileType);

                // 转换数据类型
                var merchantId = ConvertUintToMerchantId(request.MerchantId);
                var terminalId = request.TerminalType + "-" + ConvertUintToTerminalId(request.TerminalSerialNumber);
                var fileCrc = request.FileCRC32.ToString("X8");

                // // 验证终端ID是否匹配 (不验证，可能当前终端是直接创建新连接上传文件，并没有复用当前连接)
                // if (terminalId != context.TerminalID)
                // {
                //     _logger.LogWarning("Terminal ID mismatch: context={ContextTerminalId}, request={RequestTerminalId}",
                //         context.TerminalID, terminalId);

                //     return CreateFileUploadResponse(requestId, 0x01, "", 0); // 失败响应
                // }
                

                // 设置服务端块大小（可以根据网络情况调整）
                var serverChunkSize = Math.Min((int)request.MaxChunkSize, 8192); // 最大8KB

                // 创建上传记录
                var upload = await _uploadService.CreateUploadAsync(
                    merchantId,
                    terminalId,
                    request.TerminalType,
                    request.FileType,
                    request.FileName,
                    request.FileSize,
                    fileCrc,
                    (int)request.MaxChunkSize,
                    serverChunkSize);

                _logger.LogInformation("Created upload record {UploadId} for terminal {TerminalId}",
                    upload.ID, terminalId);

                // 返回成功响应
                return CreateFileUploadResponse(requestId, 0x00, upload.ID, (uint)serverChunkSize);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling file upload request from {TerminalId}", context.TerminalID);
                return CreateFileUploadResponse(requestId, 0x01, "", 0); // 失败响应
            }
        }

        /// <summary>
        /// 处理文件块传输请求 (0x03)
        /// </summary>
        private async Task<byte[]> HandleFileChunkRequestAsync(TcpConnectionContext context, byte[] messageData, uint requestId)
        {
            try
            {
                var request = FileUploadMessage.Parse<FileChunkRequestMessage>(messageData);
                
                _logger.LogDebug("File chunk request from {TerminalId}: UploadId={UploadId}, Offset={Offset}, Size={Size}",
                    context.TerminalID, request.UploadFileId, request.ChunkOffset, request.ChunkSize);

                // 验证块大小
                if (request.ChunkData.Length != request.ChunkSize)
                {
                    _logger.LogWarning("Chunk size mismatch: declared={Declared}, actual={Actual}",
                        request.ChunkSize, request.ChunkData.Length);
                    
                    return CreateFileChunkResponse(requestId, request.UploadFileId, 0x01, 0, 0); // 失败响应
                }

                // 写入文件块
                var success = await _uploadService.WriteChunkAsync(request.UploadFileId, request.ChunkOffset, request.ChunkData);
                if (!success)
                {
                    _logger.LogWarning("Failed to write chunk for upload {UploadId}", request.UploadFileId);
                    return CreateFileChunkResponse(requestId, request.UploadFileId, 0x02, 0, 0); // 文件ID不存在
                }

                // 获取上传记录以计算下一个块
                var upload = await _uploadService.GetUploadAsync(request.UploadFileId);
                if (upload == null)
                {
                    return CreateFileChunkResponse(requestId, request.UploadFileId, 0x02, 0, 0); // 文件ID不存在
                }

                // 计算下一个块的偏移量和大小
                var nextOffset = (uint)upload.ReceivedLength;
                var remainingSize = upload.FileSize - upload.ReceivedLength;
                var nextChunkSize = (uint)Math.Min(remainingSize, upload.ServerChunkSize);

                // 如果文件已完成，尝试完成上传
                if (upload.ReceivedLength >= upload.FileSize)
                {
                    var completed = await _uploadService.CompleteUploadAsync(request.UploadFileId);
                    if (completed)
                    {
                        _logger.LogInformation("File upload completed: {UploadId}", request.UploadFileId);
                    }
                    nextChunkSize = 0; // 表示上传完成
                }

                return CreateFileChunkResponse(requestId, request.UploadFileId, 0x00, nextOffset, nextChunkSize);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling file chunk request from {TerminalId}", context.TerminalID);
                return CreateFileChunkResponse(requestId, "", 0x01, 0, 0); // 失败响应
            }
        }

        /// <summary>
        /// 处理断点续传查询 (0x05)
        /// </summary>
        private async Task<byte[]> HandleResumeQueryAsync(TcpConnectionContext context, byte[] messageData, uint requestId)
        {
            try
            {
                var request = FileUploadMessage.Parse<ResumeQueryMessage>(messageData);
                
                _logger.LogDebug("Resume query from {TerminalId}: UploadId={UploadId}",
                    context.TerminalID, request.UploadFileId);

                // 获取上传记录
                var upload = await _uploadService.GetUploadAsync(request.UploadFileId);
                if (upload == null)
                {
                    _logger.LogWarning("Upload not found for resume query: {UploadId}", request.UploadFileId);
                    return CreateResumeResponse(requestId, request.UploadFileId, 0x02, 0, 0); // 文件ID不存在
                }

                // 检查上传状态
                if (upload.Status != Core.Models.TerminalUploadStatus.Uploading)
                {
                    _logger.LogWarning("Upload not in uploading status: {UploadId}, Status: {Status}",
                        request.UploadFileId, upload.Status);
                    return CreateResumeResponse(requestId, request.UploadFileId, 0x01, 0, 0); // 查询失败
                }

                // 计算下一个块的偏移量和大小
                var nextOffset = (uint)upload.ReceivedLength;
                var remainingSize = upload.FileSize - upload.ReceivedLength;
                var nextChunkSize = (uint)Math.Min(remainingSize, upload.ServerChunkSize);

                // 更新最后活动时间
                await _uploadService.UpdateLastActivityTimeAsync(request.UploadFileId);

                _logger.LogDebug("Resume query response: UploadId={UploadId}, NextOffset={NextOffset}, NextSize={NextSize}",
                    request.UploadFileId, nextOffset, nextChunkSize);

                return CreateResumeResponse(requestId, request.UploadFileId, 0x00, nextOffset, nextChunkSize);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling resume query from {TerminalId}", context.TerminalID);
                return CreateResumeResponse(requestId, "", 0x01, 0, 0); // 失败响应
            }
        }

        /// <summary>
        /// 处理不支持的消息类型
        /// </summary>
        private async Task<byte[]?> HandleUnsupportedMessageAsync(byte messageType, uint requestId)
        {
            _logger.LogWarning("Unsupported file upload message type: {MessageType}", messageType);
            await Task.CompletedTask;
            return null;
        }

        /// <summary>
        /// 创建文件上传响应消息
        /// </summary>
        private static byte[] CreateFileUploadResponse(uint requestId, byte status, string uploadFileId, uint serverChunkSize)
        {
            var response = new FileUploadResponseMessage
            {
                RequestId = requestId,
                Status = status,
                UploadFileId = uploadFileId,
                ServerChunkSize = serverChunkSize
            };
            return response.Pack();
        }

        /// <summary>
        /// 创建文件块传输响应消息
        /// </summary>
        private static byte[] CreateFileChunkResponse(uint requestId, string uploadFileId, byte transferResult, uint nextOffset, uint nextSize)
        {
            var response = new FileChunkResponseMessage
            {
                RequestId = requestId,
                UploadFileId = uploadFileId,
                TransferResult = transferResult,
                NextChunkOffset = nextOffset,
                NextChunkSize = nextSize
            };
            return response.Pack();
        }

        /// <summary>
        /// 创建断点续传响应消息
        /// </summary>
        private static byte[] CreateResumeResponse(uint requestId, string uploadFileId, byte queryResult, uint nextOffset, uint nextSize)
        {
            var response = new ResumeResponseMessage
            {
                RequestId = requestId,
                UploadFileId = uploadFileId,
                QueryResult = queryResult,
                NextChunkOffset = nextOffset,
                NextChunkSize = nextSize
            };
            return response.Pack();
        }

        /// <summary>
        /// 将uint类型的商户ID转换为字符串
        /// </summary>
        private static string ConvertUintToMerchantId(uint merchantId)
        {
            return merchantId.ToString("X8"); // 8位数字，不足补0
        }

        /// <summary>
        /// 将uint类型的终端序列号转换为字符串
        /// </summary>
        private static string ConvertUintToTerminalId(uint terminalSerialNumber)
        {
            return terminalSerialNumber.ToString("X8"); // 8位十六进制
        }
    }
}
