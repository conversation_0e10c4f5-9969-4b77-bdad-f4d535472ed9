import React, { useState, useEffect, useMemo, useRef } from 'react';
import {
    Box,
    Paper,
    Typography,
    Button,
    TextField,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    TablePagination,
    Chip,
    IconButton,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    Tooltip,
    Alert,
    Snackbar,
    Grid,
    CircularProgress
} from '@mui/material';
import {
    Add as AddIcon,
    Edit as EditIcon,
    ContentCopy as CopyIcon,
    Delete as DeleteIcon,
    Search as SearchIcon,
    Refresh as RefreshIcon,
    Publish as PublishIcon,
    History as HistoryIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { fareDiscountSchemeAPI, merchantAPI } from '../../services/api';
import MerchantAutocomplete from '../../components/MerchantAutocomplete';
import { FeatureGuard } from '../../components/FeatureGuard';
import { useFeaturePermission } from '../../hooks/useFeaturePermission';
import { PERMISSIONS } from '../../constants/permissions';
import { useAuth } from '../../contexts/AuthContext';

// 票价折扣方案操作按钮组件
const FareDiscountSchemeActionButtons = ({
  scheme,
  onEdit,
  onSubmitVersion,
  onViewVersions,
  onCopy,
  onDelete
}) => {
  const { user } = useAuth();
  const { checkMultiple } = useFeaturePermission();

  // 批量检查权限
  const permissions = useMemo(() =>
    checkMultiple([
      PERMISSIONS.FARE_DISCOUNT_SCHEME.EDIT,
      PERMISSIONS.FARE_DISCOUNT_SCHEME.SUBMIT_VERSION,
      PERMISSIONS.FARE_DISCOUNT_SCHEME.DELETE
    ]),
    [checkMultiple]
  );

  // 业务逻辑判断
  const canEdit = permissions[PERMISSIONS.FARE_DISCOUNT_SCHEME.EDIT] &&
    (user.roles.includes('SystemAdmin') || scheme.merchantId === user.merchantId);

  const canSubmitVersion = permissions[PERMISSIONS.FARE_DISCOUNT_SCHEME.SUBMIT_VERSION] &&
    canEdit;

  const canDelete = permissions[PERMISSIONS.FARE_DISCOUNT_SCHEME.DELETE] &&
    canEdit && !scheme.hasPublishedVersions;

  return (
    <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
      {/* 编辑按钮 */}
      <FeatureGuard
        featureKey={PERMISSIONS.FARE_DISCOUNT_SCHEME.EDIT}
        additionalCheck={canEdit}
      >
        <Tooltip title="编辑">
          <IconButton
            size="small"
            color="primary"
            onClick={onEdit}
          >
            <EditIcon fontSize="small" />
          </IconButton>
        </Tooltip>
      </FeatureGuard>

      {/* 提交版本按钮 */}
      <FeatureGuard
        featureKey={PERMISSIONS.FARE_DISCOUNT_SCHEME.SUBMIT_VERSION}
        additionalCheck={canSubmitVersion}
      >
        <Tooltip title="提交版本">
          <IconButton
            size="small"
            color="success"
            onClick={onSubmitVersion}
          >
            <PublishIcon fontSize="small" />
          </IconButton>
        </Tooltip>
      </FeatureGuard>

      {/* 版本历史按钮 - 不需要权限控制 */}
      <Tooltip title="版本历史">
          <IconButton
            size="small"
            color="info"
            onClick={onViewVersions}
          >
            <HistoryIcon fontSize="small" />
          </IconButton>
        </Tooltip>

      {/* 删除按钮 */}
      <FeatureGuard
        featureKey={PERMISSIONS.FARE_DISCOUNT_SCHEME.DELETE}
        additionalCheck={canDelete}
      >
        <Tooltip title="删除">
          <IconButton
            size="small"
            color="error"
            onClick={onDelete}
          >
            <DeleteIcon fontSize="small" />
          </IconButton>
        </Tooltip>
      </FeatureGuard>
    </Box>
  );
};

const FareDiscountSchemesView = () => {
    const navigate = useNavigate();
    const { user } = useAuth();

    // 所有状态必须在条件检查之前声明
    const [schemes, setSchemes] = useState([]);
    const [loading, setLoading] = useState(false);
    const [page, setPage] = useState(0);
    const [rowsPerPage, setRowsPerPage] = useState(10);
    const [totalCount, setTotalCount] = useState(0);
    const [searchText, setSearchText] = useState('');
    const [statusFilter, setStatusFilter] = useState('');
    const [selectedMerchant, setSelectedMerchant] = useState(null);
    const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
    const [copyDialogOpen, setCopyDialogOpen] = useState(false);
    const [selectedScheme, setSelectedScheme] = useState(null);
    const [newSchemeName, setNewSchemeName] = useState('');
    const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });

    // 提交版本相关状态
    const [submitDialogOpen, setSubmitDialogOpen] = useState(false);
    const [submitData, setSubmitData] = useState({
        version: '',
        filePara: '',
        remarks: '',
        schemeId: null,
        schemeName: ''
    });
    const [submitting, setSubmitting] = useState(false);

    // 防重复请求
    const loadSchemesRef = useRef();
    const hasLoadedRef = useRef(false);

    // 加载数据
    const loadSchemes = async (isInitialLoad = false, forceLoad = false) => {
        try {
            setLoading(true);

            const params = {
                page: page + 1,
                pageSize: rowsPerPage,
                search: searchText || undefined,
                isActive: statusFilter === '' ? undefined : statusFilter === 'true',
                merchantId: selectedMerchant?.merchantID || undefined
            };

            // 移除undefined的参数
            Object.keys(params).forEach(key => {
                if (params[key] === undefined) {
                    delete params[key];
                }
            });

            // 避免重复请求：检查参数是否真的发生了变化（除非强制加载）
            const paramsString = JSON.stringify(params);
            if (!isInitialLoad && !forceLoad && loadSchemesRef.current === paramsString) {
                console.log('FareDiscountSchemesView: 参数未变化，跳过重复请求');
                setLoading(false);
                return;
            }
            loadSchemesRef.current = paramsString;

            console.log('FareDiscountSchemesView: 执行数据请求', params);
            const response = await fareDiscountSchemeAPI.getSchemes(params);
            setSchemes(response.items || []);
            setTotalCount(response.totalCount || 0);
        } catch (error) {
            console.error('加载票价折扣方案失败:', error);
            showSnackbar('加载数据失败', 'error');
        } finally {
            setLoading(false);
        }
    };

    // 统一的数据加载逻辑
    useEffect(() => {
        // 防止重复加载
        if (hasLoadedRef.current) {
            console.log('FareDiscountSchemesView: 已加载过，跳过重复请求');
            return;
        }

        console.log('FareDiscountSchemesView: 执行首次加载');
        hasLoadedRef.current = true;
        loadSchemes(true, false); // 标记为初始加载，非强制加载
    }, []);

    // 当参数变化时重新加载
    useEffect(() => {
        if (hasLoadedRef.current) { // 只有在首次加载完成后才响应参数变化
            console.log('FareDiscountSchemesView: 参数变化，重新加载');
            loadSchemes(false, false); // 非初始加载，非强制加载
        }
    }, [page, rowsPerPage, searchText, statusFilter, selectedMerchant]);

    const showSnackbar = (message, severity = 'success') => {
        setSnackbar({ open: true, message, severity });
    };

    const handleSearch = () => {
        setPage(0);
        loadSchemes(false, true); // 强制执行搜索
    };

    const handleRefresh = () => {
        setPage(0);
        setSearchText('');
        setStatusFilter('');
        loadSchemes(false, true); // 强制执行刷新
    };

    const handleCreate = () => {
        navigate('/app/fare-discount-schemes/create');
    };

    const handleEdit = (scheme) => {
        navigate(`/app/fare-discount-schemes/edit/${scheme.id}`);
    };

    const handleCopy = (scheme) => {
        setSelectedScheme(scheme);
        setNewSchemeName(`${scheme.schemeName}_副本`);
        setCopyDialogOpen(true);
    };

    const handleDelete = (scheme) => {
        setSelectedScheme(scheme);
        setDeleteDialogOpen(true);
    };

    const confirmCopy = async () => {
        if (!newSchemeName.trim()) {
            showSnackbar('请输入新方案名称', 'error');
            return;
        }

        try {
            await fareDiscountSchemeAPI.copyScheme(selectedScheme.id, newSchemeName);
            showSnackbar('复制方案成功');
            setCopyDialogOpen(false);
            setNewSchemeName('');
            loadSchemes(false, true); // 强制执行刷新
        } catch (error) {
            console.error('复制方案失败:', error);
            showSnackbar(error.response?.data?.message || '复制方案失败', 'error');
        }
    };

    const confirmDelete = async () => {
        try {
            await fareDiscountSchemeAPI.deleteScheme(selectedScheme.id);
            showSnackbar('删除方案成功');
            setDeleteDialogOpen(false);
            loadSchemes(false, true); // 强制执行刷新
        } catch (error) {
            console.error('删除方案失败:', error);
            showSnackbar(error.response?.data?.message || '删除方案失败', 'error');
        }
    };

    // 处理提交版本
    const handleSubmitVersion = (scheme) => {
        setSubmitData({
            version: '', // 空字符串表示自动生成
            filePara: scheme.currentFilePara !== null && scheme.currentFilePara !== undefined
                ? String(scheme.currentFilePara)
                : (scheme.schemeCode || ''),
            remarks: '', // 备注信息
            schemeId: scheme.id,
            schemeName: scheme.schemeName
        });
        setSubmitDialogOpen(true);
    };

    // 验证版本号格式（4位16进制）
    const validateVersion = (version) => {
        if (!version) return true; // 空字符串允许（自动生成）
        const hexPattern = /^[0-9A-Fa-f]{4}$/;
        return hexPattern.test(version);
    };

    // 验证文件参数格式（最多8个英文或数字）
    const validateFilePara = (filePara) => {
        if (!filePara) return true; // 空字符串允许
        const alphanumericPattern = /^[A-Za-z0-9]{1,8}$/;
        return alphanumericPattern.test(filePara);
    };

    // 处理版本号输入
    const handleVersionChange = (value) => {
        // 只允许输入16进制字符，最多4位
        const filteredValue = value.replace(/[^0-9A-Fa-f]/g, '').slice(0, 4);
        setSubmitData({ ...submitData, version: filteredValue });
    };

    // 处理文件参数输入
    const handleFileParaChange = (value) => {
        // 只允许输入英文和数字，最多8位
        const filteredValue = value.replace(/[^A-Za-z0-9]/g, '').slice(0, 8);
        setSubmitData({ ...submitData, filePara: filteredValue });
    };

    // 确认提交版本
    const handleConfirmSubmit = async () => {
        // 验证输入
        if (submitData.version && !validateVersion(submitData.version)) {
            showSnackbar('版本号格式错误，请输入4位16进制字符（如：0001、A1B2）', 'error');
            return;
        }

        if (submitData.filePara && !validateFilePara(submitData.filePara)) {
            showSnackbar('文件参数格式错误，请输入最多8个英文或数字字符', 'error');
            return;
        }

        setSubmitting(true);
        try {
            const data = {
                version: submitData.version || undefined, // 空字符串转为undefined，让后端自动生成
                filePara: submitData.filePara, // 保持原值，包括空字符串
                remarks: submitData.remarks // 备注信息
            };

            const response = await fareDiscountSchemeAPI.submitScheme(submitData.schemeId, data);
            showSnackbar(`版本提交成功！版本号：${response.version}`, 'success');
            setSubmitDialogOpen(false);

            // 重新加载方案列表以获取最新的版本信息
            await loadSchemes(false, true); // 强制执行刷新
        } catch (error) {
            console.error('提交版本失败:', error);
            showSnackbar(error.response?.data?.message || '提交版本失败', 'error');
        } finally {
            setSubmitting(false);
        }
    };

    // 查看版本历史
    const handleViewVersions = (scheme) => {
        navigate(`/app/fare-discount-schemes/${scheme.id}/versions`);
    };

    const formatDate = (dateString) => {
        if (!dateString) return '-';
        return new Date(dateString).toLocaleString('zh-CN');
    };

    return (
        <Box sx={{ p: 3 }}>
            <Paper sx={{ p: 3 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                    <Typography variant="h5" component="h1">
                        票价折扣方案管理
                    </Typography>
                    <FeatureGuard featureKey={PERMISSIONS.FARE_DISCOUNT_SCHEME.CREATE}>
                        <Button
                            variant="contained"
                            startIcon={<AddIcon />}
                            onClick={handleCreate}
                        >
                            新建方案
                        </Button>
                    </FeatureGuard>
                </Box>

                {/* 搜索和筛选 */}
                <Box sx={{ display: 'flex', gap: 2, mb: 3, alignItems: 'center' }}>
                    <TextField
                        label="搜索方案名称/编码/描述"
                        variant="outlined"
                        size="small"
                        value={searchText}
                        onChange={(e) => setSearchText(e.target.value)}
                        onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                        sx={{ minWidth: 300 }}
                    />
                    <MerchantAutocomplete
                        value={selectedMerchant}
                        onChange={(event, newValue) => setSelectedMerchant(newValue)}
                        size="small"
                        sx={{ minWidth: 200 }}
                    />
                    <FormControl size="small" sx={{ minWidth: 120 }}>
                        <InputLabel>状态</InputLabel>
                        <Select
                            value={statusFilter}
                            label="状态"
                            onChange={(e) => setStatusFilter(e.target.value)}
                        >
                            <MenuItem value="">全部</MenuItem>
                            <MenuItem value="true">启用</MenuItem>
                            <MenuItem value="false">禁用</MenuItem>
                        </Select>
                    </FormControl>
                    <Button
                        variant="outlined"
                        startIcon={<SearchIcon />}
                        onClick={handleSearch}
                    >
                        搜索
                    </Button>
                    <Button
                        variant="outlined"
                        startIcon={<RefreshIcon />}
                        onClick={handleRefresh}
                    >
                        刷新
                    </Button>
                </Box>

                {/* 数据表格 */}
                <TableContainer>
                    <Table>
                        <TableHead>
                            <TableRow>
                                <TableCell>方案名称</TableCell>
                                <TableCell>方案编码</TableCell>
                                <TableCell>商户</TableCell>
                                <TableCell>当前版本</TableCell>
                                <TableCell>文件参数</TableCell>
                                <TableCell>描述</TableCell>
                                <TableCell>状态</TableCell>
                                <TableCell>被引用次数</TableCell>
                                <TableCell>更新时间</TableCell>
                                <TableCell>创建时间</TableCell>
                                <TableCell>操作</TableCell>
                            </TableRow>
                        </TableHead>
                        <TableBody>
                            {schemes.map((scheme) => (
                                <TableRow key={scheme.id} hover>
                                    <TableCell>
                                        <Typography variant="body2" fontWeight="medium">
                                            {scheme.schemeName}
                                        </Typography>
                                    </TableCell>
                                    <TableCell>
                                        <Typography variant="body2" color="text.secondary">
                                            {scheme.schemeCode}
                                        </Typography>
                                    </TableCell>
                                    <TableCell>
                                        <Tooltip title={`商户ID: ${scheme.merchantID}`}>
                                            <Typography variant="body2">
                                                {scheme.merchantName || scheme.merchantID}
                                            </Typography>
                                        </Tooltip>
                                    </TableCell>
                                    <TableCell>
                                        <Typography variant="body2" color="text.secondary">
                                            {scheme.currentVersion || '-'}
                                        </Typography>
                                    </TableCell>
                                    <TableCell>
                                        <Typography variant="body2" color="text.secondary">
                                            {scheme.currentFilePara || '-'}
                                        </Typography>
                                    </TableCell>
                                    <TableCell>
                                        <Tooltip title={scheme.description || ''}>
                                            <Typography
                                                variant="body2"
                                                sx={{
                                                    maxWidth: 200,
                                                    overflow: 'hidden',
                                                    textOverflow: 'ellipsis',
                                                    whiteSpace: 'nowrap'
                                                }}
                                            >
                                                {scheme.description || '-'}
                                            </Typography>
                                        </Tooltip>
                                    </TableCell>
                                    <TableCell>
                                        <Chip
                                            label={scheme.isActive ? '启用' : '禁用'}
                                            color={scheme.isActive ? 'success' : 'default'}
                                            size="small"
                                        />
                                    </TableCell>
                                    <TableCell>
                                        <Typography variant="body2">
                                            {scheme.usageCount || 0}
                                        </Typography>
                                    </TableCell>
                                    <TableCell>
                                        <Typography variant="body2" color="text.secondary">
                                            {formatDate(scheme.updateTime)}
                                        </Typography>
                                    </TableCell>
                                    <TableCell>
                                        <Typography variant="body2" color="text.secondary">
                                            {formatDate(scheme.createTime)}
                                        </Typography>
                                    </TableCell>
                                    <TableCell>
                                        <FareDiscountSchemeActionButtons
                                            scheme={scheme}
                                            onEdit={() => handleEdit(scheme)}
                                            onSubmitVersion={() => handleSubmitVersion(scheme)}
                                            onViewVersions={() => handleViewVersions(scheme)}
                                            onCopy={() => handleCopy(scheme)}
                                            onDelete={() => handleDelete(scheme)}
                                        />
                                    </TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                </TableContainer>

                {/* 分页 */}
                <TablePagination
                    component="div"
                    count={totalCount}
                    page={page}
                    onPageChange={(event, newPage) => setPage(newPage)}
                    rowsPerPage={rowsPerPage}
                    onRowsPerPageChange={(event) => {
                        setRowsPerPage(parseInt(event.target.value, 10));
                        setPage(0);
                    }}
                    labelRowsPerPage="每页行数:"
                    labelDisplayedRows={({ from, to, count }) => `${from}-${to} 共 ${count} 条`}
                />
            </Paper>

            {/* 复制对话框 */}
            <Dialog open={copyDialogOpen} onClose={() => setCopyDialogOpen(false)}>
                <DialogTitle>复制票价折扣方案</DialogTitle>
                <DialogContent>
                    <TextField
                        autoFocus
                        margin="dense"
                        label="新方案名称"
                        fullWidth
                        variant="outlined"
                        value={newSchemeName}
                        onChange={(e) => setNewSchemeName(e.target.value)}
                    />
                </DialogContent>
                <DialogActions>
                    <Button onClick={() => setCopyDialogOpen(false)}>取消</Button>
                    <Button onClick={confirmCopy} variant="contained">确认复制</Button>
                </DialogActions>
            </Dialog>

            {/* 删除确认对话框 */}
            <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
                <DialogTitle>确认删除</DialogTitle>
                <DialogContent>
                    <Typography>
                        确定要删除方案 "{selectedScheme?.schemeName}" 吗？此操作不可撤销。
                    </Typography>
                </DialogContent>
                <DialogActions>
                    <Button onClick={() => setDeleteDialogOpen(false)}>取消</Button>
                    <Button onClick={confirmDelete} color="error" variant="contained">确认删除</Button>
                </DialogActions>
            </Dialog>

            {/* 提交版本对话框 */}
            <Dialog open={submitDialogOpen} onClose={() => setSubmitDialogOpen(false)} maxWidth="sm" fullWidth>
                <DialogTitle>提交票价折扣方案版本</DialogTitle>
                <DialogContent>
                    <Box sx={{ pt: 2 }}>
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                            方案：{submitData.schemeName}
                        </Typography>
                        <Grid container spacing={3}>
                            <Grid item xs={12}>
                                <TextField
                                    label="版本号"
                                    placeholder="留空自动生成"
                                    fullWidth
                                    value={submitData.version}
                                    onChange={(e) => handleVersionChange(e.target.value)}
                                    helperText="4位16进制数，如：0001、A1B2。留空将自动递增生成"
                                    inputProps={{
                                        maxLength: 4,
                                        style: { textTransform: 'uppercase' }
                                    }}
                                    error={Boolean(submitData.version && !validateVersion(String(submitData.version)))}
                                />
                            </Grid>
                            <Grid item xs={12}>
                                <TextField
                                    label="文件参数"
                                    fullWidth
                                    value={submitData.filePara}
                                    onChange={(e) => handleFileParaChange(e.target.value)}
                                    helperText="最多8个英文或数字字符，用于生成文件时的参数标识"
                                    inputProps={{
                                        maxLength: 8
                                    }}
                                    error={Boolean(submitData.filePara && !validateFilePara(String(submitData.filePara)))}
                                />
                            </Grid>
                            <Grid item xs={12}>
                                <TextField
                                    label="备注信息"
                                    fullWidth
                                    multiline
                                    rows={3}
                                    value={submitData.remarks}
                                    onChange={(e) => setSubmitData({ ...submitData, remarks: e.target.value })}
                                    helperText="可选，记录本次版本提交的说明信息"
                                    placeholder="请输入版本备注信息..."
                                />
                            </Grid>
                        </Grid>
                    </Box>
                </DialogContent>
                <DialogActions>
                    <Button onClick={() => setSubmitDialogOpen(false)}>取消</Button>
                    <Button
                        onClick={handleConfirmSubmit}
                        variant="contained"
                        disabled={submitting}
                        startIcon={submitting ? <CircularProgress size={20} /> : <PublishIcon />}
                    >
                        {submitting ? '提交中...' : '确认提交'}
                    </Button>
                </DialogActions>
            </Dialog>

            {/* 消息提示 */}
            <Snackbar
                open={snackbar.open}
                autoHideDuration={6000}
                onClose={() => setSnackbar({ ...snackbar, open: false })}
            >
                <Alert
                    onClose={() => setSnackbar({ ...snackbar, open: false })}
                    severity={snackbar.severity}
                    sx={{ width: '100%' }}
                >
                    {snackbar.message}
                </Alert>
            </Snackbar>
        </Box>
    );
};

export default FareDiscountSchemesView;
