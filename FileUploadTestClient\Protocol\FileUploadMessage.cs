using System.Text;
using System.Buffers.Binary;

namespace FileUploadTestClient.Protocol
{
    /// <summary>
    /// 文件上传协议消息类型
    /// </summary>
    public static class FileUploadMessageType
    {
        public const byte FileUploadRequest = 0x01;      // 文件上传请求
        public const byte FileUploadResponse = 0x02;     // 文件上传响应
        public const byte FileChunkRequest = 0x03;       // 文件块传输请求
        public const byte FileChunkResponse = 0x04;      // 文件块传输响应
        public const byte ResumeQuery = 0x05;            // 断点续传查询
        public const byte ResumeResponse = 0x06;         // 断点续传响应
    }

    /// <summary>
    /// 文件上传协议消息基类
    /// </summary>
    public abstract class FileUploadMessage
    {
        /// <summary>
        /// 固定头：0xCCCCCCCC
        /// </summary>
        public const uint FixedHeader = 0xCCCCCCCC;

        /// <summary>
        /// 协议版本：0x01
        /// </summary>
        public const byte ProtocolVersion = 0x01;

        /// <summary>
        /// 消息类型
        /// </summary>
        public byte MessageType { get; set; }

        /// <summary>
        /// 请求ID
        /// </summary>
        public uint RequestId { get; set; }

        /// <summary>
        /// 预留字段
        /// </summary>
        public ushort Reserved { get; set; } = 0x0000;

        /// <summary>
        /// 消息体
        /// </summary>
        public abstract byte[] GetMessageBody();

        /// <summary>
        /// 解析消息体
        /// </summary>
        public abstract void ParseMessageBody(byte[] body);

        /// <summary>
        /// 打包完整消息
        /// </summary>
        public byte[] Pack()
        {
            var body = GetMessageBody();
            var bodyLength = (uint)body.Length;

            var message = new byte[16 + body.Length];
            var offset = 0;

            // 固定头 (4字节) - 大端模式
            BinaryPrimitives.WriteUInt32BigEndian(message.AsSpan(offset), FixedHeader);
            offset += 4;

            // 协议版本 (1字节)
            message[offset++] = ProtocolVersion;

            // 消息类型 (1字节)
            message[offset++] = MessageType;

            // 请求ID (4字节) - 大端模式
            BinaryPrimitives.WriteUInt32BigEndian(message.AsSpan(offset), RequestId);
            offset += 4;

            // 预留 (2字节) - 大端模式
            BinaryPrimitives.WriteUInt16BigEndian(message.AsSpan(offset), Reserved);
            offset += 2;

            // 消息体长度 (4字节) - 大端模式
            BinaryPrimitives.WriteUInt32BigEndian(message.AsSpan(offset), bodyLength);
            offset += 4;

            // 消息体
            body.CopyTo(message, offset);

            return message;
        }

        /// <summary>
        /// 解析消息
        /// </summary>
        public static T Parse<T>(byte[] data) where T : FileUploadMessage, new()
        {
            if (data.Length < 16)
                throw new ArgumentException("Message too short");

            var offset = 0;

            // 验证固定头 - 大端模式
            var header = BinaryPrimitives.ReadUInt32BigEndian(data.AsSpan(offset));
            if (header != FixedHeader)
                throw new ArgumentException("Invalid fixed header");
            offset += 4;

            // 验证协议版本
            var version = data[offset++];
            if (version != ProtocolVersion)
                throw new ArgumentException("Invalid protocol version");

            var message = new T();

            // 消息类型
            message.MessageType = data[offset++];

            // 请求ID - 大端模式
            message.RequestId = BinaryPrimitives.ReadUInt32BigEndian(data.AsSpan(offset));
            offset += 4;

            // 预留 - 大端模式
            message.Reserved = BinaryPrimitives.ReadUInt16BigEndian(data.AsSpan(offset));
            offset += 2;

            // 消息体长度 - 大端模式
            var bodyLength = BinaryPrimitives.ReadUInt32BigEndian(data.AsSpan(offset));
            offset += 4;

            // 消息体
            if (data.Length < offset + bodyLength)
                throw new ArgumentException("Message body incomplete");

            var body = new byte[bodyLength];
            Array.Copy(data, offset, body, 0, (int)bodyLength);
            message.ParseMessageBody(body);

            return message;
        }
    }

    /// <summary>
    /// 文件上传请求消息 (0x01)
    /// </summary>
    public class FileUploadRequestMessage : FileUploadMessage
    {
        public uint MerchantId { get; set; }
        public uint TerminalSerialNumber { get; set; }
        public string FileType { get; set; } = "";
        public string TerminalType { get; set; } = "";
        public uint FileSize { get; set; }
        public uint FileCRC32 { get; set; }
        public uint MaxChunkSize { get; set; }
        public string FileName { get; set; } = "";

        public FileUploadRequestMessage()
        {
            MessageType = FileUploadMessageType.FileUploadRequest;
        }

        public override byte[] GetMessageBody()
        {
            var fileNameBytes = Encoding.ASCII.GetBytes(FileName);
            var fileTypeBytes = Encoding.ASCII.GetBytes(FileType.PadRight(3).Substring(0, 3));
            var terminalType = Encoding.ASCII.GetBytes(TerminalType.PadRight(3).Substring(0, 3));

            var body = new byte[4 + 4 + 3 + 3 + 4 + 4 + 4 + 1 + fileNameBytes.Length];
            var offset = 0;

            // 商户号 (4字节) - 大端模式
            BinaryPrimitives.WriteUInt32BigEndian(body.AsSpan(offset), MerchantId);
            offset += 4;

            // 终端序列号 (4字节) - 大端模式
            BinaryPrimitives.WriteUInt32BigEndian(body.AsSpan(offset), TerminalSerialNumber);
            offset += 4;

            // 终端类型 (3字节ASCII)
            terminalType.CopyTo(body, offset);
            offset += 3;

            // 文件类型 (3字节ASCII)
            fileTypeBytes.CopyTo(body, offset);
            offset += 3;


            // 文件大小 (4字节) - 大端模式
            BinaryPrimitives.WriteUInt32BigEndian(body.AsSpan(offset), FileSize);
            offset += 4;

            // 文件CRC32 (4字节) - 大端模式
            BinaryPrimitives.WriteUInt32BigEndian(body.AsSpan(offset), FileCRC32);
            offset += 4;

            // 终端传输最大块大小 (4字节) - 大端模式
            BinaryPrimitives.WriteUInt32BigEndian(body.AsSpan(offset), MaxChunkSize);
            offset += 4;

            // 文件名长度 (1字节)
            body[offset++] = (byte)fileNameBytes.Length;

            // 文件名 (变长ASCII)
            fileNameBytes.CopyTo(body, offset);

            return body;
        }

        public override void ParseMessageBody(byte[] body)
        {
            if (body.Length < 27) // 最小长度：4+4+3+3+4+4+4+1 = 27
                throw new ArgumentException("Message body too short for FileUploadRequest");

            var offset = 0;

            // 商户号 - 大端模式
            MerchantId = BinaryPrimitives.ReadUInt32BigEndian(body.AsSpan(offset));
            offset += 4;

            // 终端序列号 - 大端模式
            TerminalSerialNumber = BinaryPrimitives.ReadUInt32BigEndian(body.AsSpan(offset));
            offset += 4;

            // 终端类型
            TerminalType = Encoding.ASCII.GetString(body, offset, 3).TrimEnd('\0');
            offset += 3;

            // 文件类型
            FileType = Encoding.ASCII.GetString(body, offset, 3).TrimEnd('\0');
            offset += 3;


            // 文件大小 - 大端模式
            FileSize = BinaryPrimitives.ReadUInt32BigEndian(body.AsSpan(offset));
            offset += 4;

            // 文件CRC32 - 大端模式
            FileCRC32 = BinaryPrimitives.ReadUInt32BigEndian(body.AsSpan(offset));
            offset += 4;

            // 终端传输最大块大小 - 大端模式
            MaxChunkSize = BinaryPrimitives.ReadUInt32BigEndian(body.AsSpan(offset));
            offset += 4;

            // 文件名长度
            var fileNameLength = body[offset++];

            // 文件名
            if (body.Length < offset + fileNameLength)
                throw new ArgumentException("Message body incomplete for FileName");

            FileName = Encoding.ASCII.GetString(body, offset, fileNameLength);
        }
    }

    /// <summary>
    /// 文件上传响应消息 (0x02)
    /// </summary>
    public class FileUploadResponseMessage : FileUploadMessage
    {
        public byte Status { get; set; } // 0x00: 成功, 0x01: 失败
        public string UploadFileId { get; set; } = ""; // 16字节
        public uint ServerChunkSize { get; set; }

        public FileUploadResponseMessage()
        {
            MessageType = FileUploadMessageType.FileUploadResponse;
        }

        public override byte[] GetMessageBody()
        {
            var uploadFileIdBytes = new byte[16];
            //uploadFileIdBytes转成16进制字符串
            var idBytes = DataConvert.HexToBytes(UploadFileId);
            idBytes.CopyTo(uploadFileIdBytes, 0);

            var body = new byte[1 + 32 + 4];
            var offset = 0;

            // 状态 (1字节)
            body[offset++] = Status;

            // 上传文件ID (32字节)
            uploadFileIdBytes.CopyTo(body, offset);
            offset += 32;

            // 指定上传块大小 (4字节) - 大端模式
            BinaryPrimitives.WriteUInt32BigEndian(body.AsSpan(offset), ServerChunkSize);

            return body;
        }

        public override void ParseMessageBody(byte[] body)
        {
            if (body.Length < 21) // 1 + 16 + 4 = 21
                throw new ArgumentException("Message body too short for FileUploadResponse");

            var offset = 0;

            // 状态
            Status = body[offset++];

            // 上传文件ID
            UploadFileId = DataConvert.BytesToHex(body, offset, 16);
            offset += 16;

            // 指定上传块大小 - 大端模式
            ServerChunkSize = BinaryPrimitives.ReadUInt32BigEndian(body.AsSpan(offset));
        }
    }

    /// <summary>
    /// 文件块传输请求消息 (0x03)
    /// </summary>
    public class FileChunkRequestMessage : FileUploadMessage
    {
        public string UploadFileId { get; set; } = ""; // 16字节
        public uint ChunkOffset { get; set; }
        public uint ChunkSize { get; set; }
        public byte[] ChunkData { get; set; } = Array.Empty<byte>();

        public FileChunkRequestMessage()
        {
            MessageType = FileUploadMessageType.FileChunkRequest;
        }

        public override byte[] GetMessageBody()
        {
            var uploadFileIdBytes = new byte[16];
            var idBytes = DataConvert.HexToBytes(UploadFileId);
            idBytes.CopyTo(uploadFileIdBytes, 0);

            var body = new byte[16 + 4 + 4 + ChunkData.Length];
            var offset = 0;

            // 上传文件ID (16字节)
            uploadFileIdBytes.CopyTo(body, offset);
            offset += 16;

            // 文件块偏移量 (4字节) - 大端模式
            BinaryPrimitives.WriteUInt32BigEndian(body.AsSpan(offset), ChunkOffset);
            offset += 4;

            // 文件块大小 (4字节) - 大端模式
            BinaryPrimitives.WriteUInt32BigEndian(body.AsSpan(offset), ChunkSize);
            offset += 4;

            // 文件块内容 (变长)
            ChunkData.CopyTo(body, offset);

            return body;
        }

        public override void ParseMessageBody(byte[] body)
        {
            if (body.Length < 24) // 16 + 4 + 4 = 40
                throw new ArgumentException("Message body too short for FileChunkRequest");

            var offset = 0;

            // 上传文件ID
            UploadFileId = DataConvert.BytesToHex(body, offset, 16);
            offset += 16;

            // 文件块偏移量 - 大端模式
            ChunkOffset = BinaryPrimitives.ReadUInt32BigEndian(body.AsSpan(offset));
            offset += 4;

            // 文件块大小 - 大端模式
            ChunkSize = BinaryPrimitives.ReadUInt32BigEndian(body.AsSpan(offset));
            offset += 4;

            // 文件块内容
            var chunkDataLength = body.Length - offset;
            ChunkData = new byte[chunkDataLength];
            Array.Copy(body, offset, ChunkData, 0, chunkDataLength);
        }
    }

    /// <summary>
    /// 文件块传输响应消息 (0x04)
    /// </summary>
    public class FileChunkResponseMessage : FileUploadMessage
    {
        public string UploadFileId { get; set; } = ""; // 16字节
        public byte TransferResult { get; set; } // 0x00: 成功, 0x01: 失败, 0x02: 文件ID不存在
        public uint NextChunkOffset { get; set; }
        public uint NextChunkSize { get; set; }

        public FileChunkResponseMessage()
        {
            MessageType = FileUploadMessageType.FileChunkResponse;
        }

        public override byte[] GetMessageBody()
        {
            var uploadFileIdBytes = new byte[16];
            var idBytes = DataConvert.HexToBytes(UploadFileId);
            idBytes.CopyTo(uploadFileIdBytes, 0);

            var body = new byte[16 + 1 + 4 + 4];
            var offset = 0;

            // 上传文件ID (16字节)
            uploadFileIdBytes.CopyTo(body, offset);
            offset += 16;

            // 传输结果 (1字节)
            body[offset++] = TransferResult;

            // 下一个块偏移量 (4字节) - 大端模式
            BinaryPrimitives.WriteUInt32BigEndian(body.AsSpan(offset), NextChunkOffset);
            offset += 4;

            // 下一个块大小 (4字节) - 大端模式
            BinaryPrimitives.WriteUInt32BigEndian(body.AsSpan(offset), NextChunkSize);

            return body;
        }

        public override void ParseMessageBody(byte[] body)
        {
            if (body.Length < 25) // 16 + 1 + 4 + 4 = 41
                throw new ArgumentException("Message body too short for FileChunkResponse");

            var offset = 0;

            // 上传文件ID
            UploadFileId =  DataConvert.BytesToHex(body, offset, 16);
            offset += 16;

            // 传输结果
            TransferResult = body[offset++];

            // 下一个块偏移量 - 大端模式
            NextChunkOffset = BinaryPrimitives.ReadUInt32BigEndian(body.AsSpan(offset));
            offset += 4;

            // 下一个块大小 - 大端模式
            NextChunkSize = BinaryPrimitives.ReadUInt32BigEndian(body.AsSpan(offset));
        }
    }


    public class DataConvert
    {
        public static string BytesToHex(byte[] bByte)
        {
            StringBuilder sb = new StringBuilder();
            for (int j = 0; j < bByte.Length; j++)
            {
                sb.Append(bByte[j].ToString("X2"));
            }
            return sb.ToString();
        }

        public static string BytesToHex(byte[] bByte, int offset, int length)
        {
            StringBuilder sb = new StringBuilder();
            for (int j = offset; j < bByte.Length && j < offset + length; j++)
            {
                sb.Append(bByte[j].ToString("X2"));
            }
            return sb.ToString();
        }
                public static byte[] HexToBytes(string hex)
        {
            byte[] b = new byte[hex.Length / 2];
            for (int i = 0; i < b.Length; i++)
            {
                b[i] = Convert.ToByte(hex.Substring(2 * i, 2), 16);
            }
            return b;
        }

    }
}
