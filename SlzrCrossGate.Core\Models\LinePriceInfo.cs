using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SlzrCrossGate.Core.Models
{
    /// <summary>
    /// 线路票价信息表，用于存储线路票价的基本信息
    /// </summary>
    public class LinePriceInfo : ITenantEntity
    {
        /// <summary>
        /// 自增ID（主键）
        /// </summary>
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int ID { get; set; }

        /// <summary>
        /// 商户ID
        /// </summary>
        [Required]
        [MaxLength(8)]
        public required string MerchantID { get; set; }

        /// <summary>
        /// 线路编号，4位数字
        /// </summary>
        [Required]
        [MaxLength(4)]
        public required string LineNumber { get; set; }

        /// <summary>
        /// 子线路号（组号），2位数字或者为空字符串
        /// </summary>
        [Required]
        [MaxLength(2)]
        public required string GroupNumber { get; set; }

        /// <summary>
        /// 线路名称
        /// </summary>
        [MaxLength(100)]
        public required string LineName { get; set; }

        /// <summary>
        /// 分公司
        /// </summary>
        [MaxLength(100)]
        public string? Branch { get; set; }

        /// <summary>
        /// 票价，单位分
        /// </summary>
        public int Fare { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdateTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 创建者
        /// </summary>
        [MaxLength(50)]
        public string? Creator { get; set; }

        /// <summary>
        /// 更新者
        /// </summary>
        [MaxLength(50)]
        public string? Updater { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [MaxLength(500)]
        public string? Remark { get; set; }

        /// <summary>
        /// 当前最新版本号
        /// </summary>
        [MaxLength(4)]
        public string CurrentVersion { get; set; } = "";

        /// <summary>
        /// 当前使用的票价折扣方案ID
        /// </summary>
        public int? CurrentFareDiscountSchemeID { get; set; }

        /// <summary>
        /// 当前使用的票价折扣方案名称（冗余存储，防止方案被删除后无法追溯）
        /// </summary>
        [MaxLength(100)]
        public string? CurrentSchemeName { get; set; }

        /// <summary>
        /// 商户关系引用
        /// </summary>
        [ForeignKey("MerchantID")]
        public Merchant? Merchant { get; set; }

        /// <summary>
        /// 当前使用的票价折扣方案引用
        /// </summary>
        [ForeignKey("CurrentFareDiscountSchemeID")]
        public FareDiscountScheme? CurrentFareDiscountScheme { get; set; }
    }
}
