import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Grid,
  Button,
  Chip,
  Divider,
  List,
  ListItem,
  ListItemText,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  CircularProgress
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { useParams, useNavigate } from 'react-router-dom';
import { terminalAPI } from '../../services/api'; // 替换axios导入为terminalAPI
import { formatDateTime, isWithinMinutes, formatOfflineDuration } from '../../utils/dateUtils';
import { parseErrorMessage } from '../../utils/errorHandler';

const TerminalDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [terminal, setTerminal] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // 加载终端详情
  const loadTerminal = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await terminalAPI.getTerminal(id); // 使用terminalAPI代替axios
      setTerminal(response);
    } catch (error) {
      console.error('Error loading terminal:', error);
      const errorMessage = parseErrorMessage(error, '加载终端信息失败');
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadTerminal();
  }, [id]);

  // 获取终端状态的显示样式
  const getStatusChip = (status) => {
    if (!status) return <Chip label="未知" color="default" />;

    // 使用统一的时间处理工具判断是否在线（5分钟内活跃且状态为激活）
    if (status.activeStatus === 1 && isWithinMinutes(status.lastActiveTime, 5)) {
      return <Chip label="在线" color="success" size="small" />;
    } else {
      // 使用友好的离线时长显示
      const offlineDuration = formatOfflineDuration(status.lastActiveTime);
      return <Chip label={offlineDuration} color="error" size="small" />;
    }
  };

  // 获取发布类型显示
  const getPublishTypeChip = (type) => {
    switch (type) {
      case 1: // Merchant
        return <Chip label="商户级别" color="primary" size="small" />;
      case 2: // Line
        return <Chip label="线路级别" color="secondary" size="small" />;
      case 3: // Terminal
        return <Chip label="终端级别" color="success" size="small" />;
      default:
        return <Chip label="无" color="default" size="small" />;
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography variant="h6" color="error" gutterBottom>
          {error}
        </Typography>
        <Button
          variant="outlined"
          startIcon={<ArrowBackIcon />}
          onClick={() => navigate('/terminals')}
        >
          返回终端列表
        </Button>
      </Box>
    );
  }

  if (!terminal) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography variant="h6" gutterBottom>
          未找到终端信息
        </Typography>
        <Button
          variant="outlined"
          startIcon={<ArrowBackIcon />}
          onClick={() => navigate('/app/terminals')}
        >
          返回终端列表
        </Button>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">
          终端详情
        </Typography>
        <Box>
          <Button
            variant="outlined"
            startIcon={<ArrowBackIcon />}
            onClick={() => navigate('/app/terminals')}
            sx={{ mr: 1 }}
          >
            返回
          </Button>
          <Button
            variant="outlined"
            color="primary"
            startIcon={<RefreshIcon />}
            onClick={loadTerminal}
          >
            刷新
          </Button>
        </Box>
      </Box>

      {/* 基本信息卡片 */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          基本信息
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={12} sm={6} md={4}>
            <Typography variant="subtitle2" color="textSecondary">终端ID</Typography>
            <Typography variant="body1">{terminal.id}</Typography>
          </Grid>
          <Grid item xs={12} sm={6} md={4}>
            <Typography variant="subtitle2" color="textSecondary">商户ID</Typography>
            <Typography variant="body1">{terminal.merchantID}</Typography>
          </Grid>
          <Grid item xs={12} sm={6} md={4}>
            <Typography variant="subtitle2" color="textSecondary">商户名称</Typography>
            <Typography variant="body1">{terminal.merchantName}</Typography>
          </Grid>
          <Grid item xs={12} sm={6} md={4}>
            <Typography variant="subtitle2" color="textSecondary">机器ID</Typography>
            <Typography variant="body1">{terminal.machineID}</Typography>
          </Grid>
          <Grid item xs={12} sm={6} md={4}>
            <Typography variant="subtitle2" color="textSecondary">设备编号</Typography>
            <Typography variant="body1">{terminal.deviceNO}</Typography>
          </Grid>
          <Grid item xs={12} sm={6} md={4}>
            <Typography variant="subtitle2" color="textSecondary">车牌号</Typography>
            <Typography variant="body1">{terminal.licensePlate}</Typography>
          </Grid>
          <Grid item xs={12} sm={6} md={4}>
            <Typography variant="subtitle2" color="textSecondary">线路编号</Typography>
            <Typography variant="body1">{terminal.lineNO}</Typography>
          </Grid>
          <Grid item xs={12} sm={6} md={4}>
            <Typography variant="subtitle2" color="textSecondary">终端类型</Typography>
            <Typography variant="body1">{terminal.terminalType}</Typography>
          </Grid>
          <Grid item xs={12} sm={6} md={4}>
            <Typography variant="subtitle2" color="textSecondary">创建时间</Typography>
            <Typography variant="body1">
              {formatDateTime(terminal.createTime)}
            </Typography>
          </Grid>
        </Grid>
      </Paper>

      {/* 状态信息卡片 */}
      {terminal.status && (
        <Paper sx={{ p: 3, mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            状态信息
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6} md={4}>
              <Typography variant="subtitle2" color="textSecondary">状态</Typography>
              <Box sx={{ mt: 1 }}>{getStatusChip(terminal.status)}</Box>
            </Grid>
            <Grid item xs={12} sm={6} md={4}>
              <Typography variant="subtitle2" color="textSecondary">最后活跃时间</Typography>
              <Typography variant="body1">
                {formatDateTime(terminal.status.lastActiveTime)}
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6} md={4}>
              <Typography variant="subtitle2" color="textSecondary">连接协议</Typography>
              <Typography variant="body1">{terminal.status.connectionProtocol || '-'}</Typography>
            </Grid>
            <Grid item xs={12} sm={6} md={4}>
              <Typography variant="subtitle2" color="textSecondary">终端地址</Typography>
              <Typography variant="body1">{terminal.status.endPoint || '-'}</Typography>
            </Grid>
          </Grid>
        </Paper>
      )}

      {/* 文件版本和属性信息 - 响应式布局 */}
      {((terminal.status && terminal.status.fileVersionMetadata && Object.keys(terminal.status.fileVersionMetadata).length > 0) ||
        (terminal.status && terminal.status.enhancedPropertyMetadata && Object.keys(terminal.status.enhancedPropertyMetadata).length > 0)) && (
        <Grid container spacing={3} sx={{ mb: 3 }}>
          {/* 文件版本信息 */}
          {terminal.status && terminal.status.fileVersionMetadata && Object.keys(terminal.status.fileVersionMetadata).length > 0 && (
            <Grid item xs={12} lg={6}>
              <Paper sx={{ p: 3, height: 'fit-content' }}>
                <Typography variant="h6" gutterBottom>
                  文件版本信息
                </Typography>
                <TableContainer>
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell>文件类型</TableCell>
                        <TableCell>当前版本</TableCell>
                        <TableCell>期望版本</TableCell>
                        <TableCell>发布级别</TableCell>
                        <TableCell>状态</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {Object.entries(terminal.status.fileVersionMetadata).sort(([fileTypeA], [fileTypeB]) => {
                        return fileTypeA.localeCompare(fileTypeB);
                      }).map(([fileType, version]) => (
                        <TableRow key={fileType}>
                          <TableCell>{fileType}</TableCell>
                          <TableCell>{version.current || '-'}</TableCell>
                          <TableCell>{version.expected || '-'}</TableCell>
                          <TableCell>{getPublishTypeChip(version.publishType)}</TableCell>
                          <TableCell>
                            {version.current === version.expected ? (
                              <Chip label="最新" color="success" size="small" />
                            ) : version.expected ? (
                              <Chip label="需更新" color="warning" size="small" />
                            ) : (
                              <Chip label="无发布" color="default" size="small" />
                            )}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Paper>
            </Grid>
          )}

          {/* 属性信息 */}
          {terminal.status && terminal.status.enhancedPropertyMetadata && Object.keys(terminal.status.enhancedPropertyMetadata).length > 0 && (
            <Grid item xs={12} lg={terminal.status.fileVersionMetadata && Object.keys(terminal.status.fileVersionMetadata).length > 0 ? 6 : 12}>
              <Paper sx={{ p: 3, height: 'fit-content' }}>
                <Typography variant="h6" gutterBottom>
                  属性信息
                </Typography>
                <List dense>
                  {Object.entries(terminal.status.enhancedPropertyMetadata).map(([key, propertyInfo]) => (
                    <React.Fragment key={key}>
                      <ListItem sx={{ px: 0 }}>
                        <ListItemText
                          primary={
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <Typography variant="subtitle2" component="span" sx={{ fontWeight: 500 }}>
                                {propertyInfo.displayName}
                              </Typography>
                              {propertyInfo.displayName !== key && (
                                <Typography variant="caption" color="text.secondary" component="span">
                                  ({key})
                                </Typography>
                              )}
                            </Box>
                          }
                          secondary={
                            <Typography variant="body2" sx={{ mt: 0.5 }}>
                              {propertyInfo.value}
                            </Typography>
                          }
                        />
                      </ListItem>
                      <Divider />
                    </React.Fragment>
                  ))}
                </List>
              </Paper>
            </Grid>
          )}
        </Grid>
      )}

      {/* 操作按钮
      <Paper sx={{ p: 3 }}>
        <Typography variant="h6" gutterBottom>
          操作
        </Typography>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button
            variant="contained"
            color="secondary"
            startIcon={<MessageIcon />}
            onClick={() => navigate('/app/terminals', { state: { openMessageDialog: true, terminal } })}
          >
            发送消息
          </Button>
          <Button
            variant="contained"
            color="success"
            startIcon={<PublishIcon />}
            onClick={() => navigate('/app/terminals', { state: { openFileDialog: true, terminal } })}
          >
            发布文件
          </Button>
          <Button
            variant="contained"
            color="info"
            startIcon={<HistoryIcon />}
            onClick={() => navigate(`/app/terminals/${terminal.id}/events`)}
          >
            查看事件
          </Button>
        </Box>
      </Paper>
      */}
    </Box>
  );
};

export default TerminalDetail;
