# 外部链接菜单配置指南

## 概述

现在菜单管理系统已经支持外部链接功能，可以在菜单中添加指向外部应用或网站的链接。

⚠️ **重要更新**: 系统已升级为新的外部系统路由架构，支持专业的URL结构和正确的菜单选中状态。

## 功能特性

### 1. 外部链接类型
- **内部链接**：传统的应用内路由（如 `/app/dashboard`）
- **外部链接**：指向外部URL的链接（如 `http://localhost:5271`）

### 2. 打开方式
- **当前窗口** (`_self`)：在当前窗口打开链接
- **新窗口** (`_blank`)：在新窗口/标签页打开链接
- **内嵌框架** (`_iframe`)：在主应用内的iframe中打开（推荐用于客户项目）

### 3. 新路由系统 🆕
- **专业URL结构**: `/app/external/{itemKey}` 替代查询参数模式
- **正确菜单选中**: 外部系统菜单项自动高亮
- **ItemKey字段**: 用于路由匹配的关键标识符

#### URL对比
```
旧版本: /app/dashboard?iframe=客户功能
新版本: /app/external/customer-a-dashboard
```

## 使用方法

### 1. 通过菜单管理界面添加

1. 登录系统，进入 **系统管理 > 菜单管理**
2. 点击 **新增菜单项** 或编辑现有菜单项
3. 在菜单项编辑对话框中：
   - 填写基本信息（菜单名称、路由路径等）
   - 勾选 **外部链接** 复选框
   - 选择合适的 **打开方式**：
     - 对于客户项目，推荐选择 **内嵌框架**
     - 对于外部网站，可选择 **新窗口**
4. 保存菜单项

### 2. 通过数据库直接配置

```sql
-- 添加客户功能菜单分组
INSERT INTO MenuGroups (GroupKey, Title, IconName, SortOrder, IsEnabled, VisibleToSystemAdmin, VisibleToMerchantAdmin, VisibleToUser)
VALUES ('customer-features', '客户功能', 'StarIcon', 10, 1, 1, 1, 0);

-- 添加外部链接菜单项
INSERT INTO MenuItems (
    MenuGroupId, ItemKey, Title, Href, IconName, SortOrder,
    IsEnabled, VisibleToSystemAdmin, VisibleToMerchantAdmin, VisibleToUser,
    IsExternal, Target
)
VALUES (
    (SELECT Id FROM MenuGroups WHERE GroupKey = 'customer-features'),
    'customer-a-dashboard',  -- ⚠️ 重要：ItemKey用于新路由系统匹配
    '客户A功能',
    'http://localhost:5271',  -- 客户项目的URL
    'DashboardIcon',
    1,
    1, 1, 1, 0,  -- 权限设置
    1,           -- IsExternal = true
    '_iframe'    -- 在iframe中打开
);
```

## 配置示例

### 客户项目集成
```sql
-- 客户A的定制功能
INSERT INTO MenuItems (MenuGroupId, ItemKey, Title, Href, IconName, SortOrder, IsEnabled, VisibleToSystemAdmin, VisibleToMerchantAdmin, VisibleToUser, IsExternal, Target)
VALUES (
    (SELECT Id FROM MenuGroups WHERE GroupKey = 'customer-features'),
    'customer-a-dashboard',
    '客户A仪表盘',
    'http://localhost:5271',
    'BarChartIcon',
    1, 1, 1, 1, 0, 1, '_iframe'
);

-- 客户B的定制功能  
INSERT INTO MenuItems (MenuGroupId, ItemKey, Title, Href, IconName, SortOrder, IsEnabled, VisibleToSystemAdmin, VisibleToMerchantAdmin, VisibleToUser, IsExternal, Target)
VALUES (
    (SELECT Id FROM MenuGroups WHERE GroupKey = 'customer-features'),
    'customer-b-reports',
    '客户B报表',
    'http://localhost:5272/reports',
    'FileTextIcon',
    2, 1, 1, 1, 0, 1, '_iframe'
);
```

### 外部工具集成
```sql
-- 监控系统
INSERT INTO MenuItems (MenuGroupId, ItemKey, Title, Href, IconName, SortOrder, IsEnabled, VisibleToSystemAdmin, VisibleToMerchantAdmin, VisibleToUser, IsExternal, Target)
VALUES (
    (SELECT Id FROM MenuGroups WHERE GroupKey = 'system'),
    'grafana-monitoring',
    '系统监控',
    'http://grafana.company.com:3000',
    'ActivityIcon',
    10, 1, 1, 0, 0, 1, '_blank'
);

-- 文档中心
INSERT INTO MenuItems (MenuGroupId, ItemKey, Title, Href, IconName, SortOrder, IsEnabled, VisibleToSystemAdmin, VisibleToMerchantAdmin, VisibleToUser, IsExternal, Target)
VALUES (
    (SELECT Id FROM MenuGroups WHERE GroupKey = 'system'),
    'documentation',
    '帮助文档',
    'https://docs.company.com',
    'BookIcon',
    11, 1, 1, 1, 1, 1, '_blank'
);
```

## 技术实现

### 前端处理
- `NavItem` 组件自动检测 `isExternal` 属性
- 外部链接通过 `postMessage` 与主应用通信
- iframe模式提供无缝的用户体验

### 后端支持
- `MenuItem` 模型新增 `IsExternal` 和 `Target` 字段
- 菜单API自动返回外部链接信息
- 支持完整的CRUD操作

### 数据库结构
```sql
ALTER TABLE MenuItems 
ADD COLUMN IsExternal TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否为外部链接',
ADD COLUMN Target VARCHAR(20) NOT NULL DEFAULT '_self' COMMENT '链接打开方式';
```

## 最佳实践

### 1. 客户项目集成
- 使用 `_iframe` 模式提供统一体验
- 确保客户项目支持iframe嵌入
- 配置正确的CORS和X-Frame-Options

### 2. 外部工具集成
- 使用 `_blank` 模式避免iframe限制
- 考虑单点登录(SSO)集成
- 注意安全性和权限控制

### 3. 权限管理
- 根据用户角色设置菜单可见性
- 外部链接也要遵循权限控制
- 定期审查外部链接的有效性

### 4. 用户体验
- 为外部链接提供清晰的视觉标识
- 考虑加载状态和错误处理
- 提供返回主应用的便捷方式

## 故障排除

### 常见问题

1. **iframe无法加载**
   - 检查目标网站的X-Frame-Options设置
   - 确认HTTPS/HTTP协议匹配
   - 验证URL的可访问性

2. **菜单不显示**
   - 检查用户权限设置
   - 确认菜单项已启用
   - 验证菜单分组配置

3. **认证问题**
   - 确保客户项目支持JWT认证
   - 检查token传递机制
   - 验证CORS配置

### 调试技巧

```javascript
// 在浏览器控制台检查菜单数据
console.log('Menu data:', menuGroups);

// 检查外部链接配置
menuGroups.forEach(group => {
  group.items.forEach(item => {
    if (item.isExternal) {
      console.log('External link:', item.title, item.href, item.target);
    }
  });
});
```

## 总结

外部链接菜单功能为系统提供了强大的扩展能力：

- ✅ 支持客户项目无缝集成
- ✅ 兼容外部工具和服务
- ✅ 保持统一的用户体验
- ✅ 灵活的权限控制
- ✅ 简单的配置管理

通过这个功能，你可以轻松地将客户定制功能、监控工具、文档系统等外部资源集成到主应用的菜单中，为用户提供一站式的工作平台。
