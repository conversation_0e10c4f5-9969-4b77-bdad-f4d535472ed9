# 清理SQL Server迁移历史

$connectionString = "Server=localhost;Database=TcpserverTms;User Id=sa;Password=***********;TrustServerCertificate=true"

try {
    $connection = New-Object System.Data.SqlClient.SqlConnection($connectionString)
    $connection.Open()
    
    Write-Host "Dropping migration history table..."
    $command = New-Object System.Data.SqlClient.SqlCommand("DROP TABLE IF EXISTS __EFMigrationsHistory", $connection)
    $command.ExecuteNonQuery()
    
    Write-Host "Migration history table dropped successfully!"
    
    $connection.Close()
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}
