# 临时文档目录

## 🎯 用途

此目录专门用于存放临时文档，包括：
- 临时修复记录
- 问题分析草稿
- 实验性文档
- 一次性工作记录

## 📁 子目录结构

```
temp/
├── fixes/          # 临时修复记录
├── analysis/       # 临时分析文档
├── drafts/         # 草稿文档
└── experiments/    # 实验性文档
```

## 📝 使用规范

### 文件命名
使用日期前缀 + 描述性名称：
```
2025-07-17-iframe-navigation-fix.md
2025-07-17-menu-api-analysis.md
2025-07-17-routing-experiment.md
```

### 内容要求
- 可以是非正式的记录
- 允许包含调试信息
- 可以是未完成的草稿

## ⚠️ 重要提醒

### 🧹 及时清理
- 问题解决后立即删除相关临时文档
- 每周检查并清理过期文档
- 有价值的内容转为正式文档后删除原临时文档

### 🚫 禁止行为
- 不要在此目录放置正式文档
- 不要长期保留临时文档
- 不要在根目录创建临时文档

## 📋 清理检查清单

定期检查以下内容：
- [ ] 是否有超过1周的临时文档
- [ ] 是否有已解决问题的修复记录
- [ ] 是否有可以转为正式文档的内容
- [ ] 是否有重复或无用的文档

---

**记住**: 这个目录的存在是为了避免根目录混乱，请严格按规范使用！
