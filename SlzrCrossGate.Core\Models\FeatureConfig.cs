using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace SlzrCrossGate.Core.Models
{
    /// <summary>
    /// 功能配置表 - 定义系统中所有可控制的功能
    /// </summary>
    [Table("FeatureConfigs")]
    [Index(nameof(FeatureKey), IsUnique = true)]
    [Index(nameof(Category))]
    public class FeatureConfig
    {
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// 功能标识符 (如: merchant.delete)
        /// </summary>
        [Required]
        [StringLength(100)]
        public required string FeatureKey { get; set; }

        /// <summary>
        /// 功能名称
        /// </summary>
        [Required]
        [StringLength(100)]
        public required string FeatureName { get; set; }

        /// <summary>
        /// 功能描述
        /// </summary>
        [StringLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// 功能分组 (如: 商户管理、用户管理)
        /// </summary>
        [Required]
        [StringLength(50)]
        public required string Category { get; set; }

        /// <summary>
        /// 风险等级 (Low/Medium/High)
        /// </summary>
        [Required]
        [StringLength(20)]
        public required string RiskLevel { get; set; } = "Low";

        /// <summary>
        /// 全局是否启用
        /// </summary>
        public bool IsGloballyEnabled { get; set; } = true;

        /// <summary>
        /// 是否为系统内置功能（不可删除）
        /// </summary>
        public bool IsSystemBuiltIn { get; set; } = false;

        /// <summary>
        /// 排序顺序
        /// </summary>
        public int SortOrder { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 创建者
        /// </summary>
        [StringLength(50)]
        public string? CreatedBy { get; set; }

        /// <summary>
        /// 更新者
        /// </summary>
        [StringLength(50)]
        public string? UpdatedBy { get; set; }

        /// <summary>
        /// 角色权限配置
        /// </summary>
        public virtual ICollection<RoleFeaturePermission> RolePermissions { get; set; } = new List<RoleFeaturePermission>();
    }

    /// <summary>
    /// 角色功能权限表 - 定义特定角色对特定功能的权限覆盖
    /// </summary>
    [Table("RoleFeaturePermissions")]
    [Index(nameof(RoleName), nameof(FeatureKey), IsUnique = true)]
    public class RoleFeaturePermission
    {
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// 角色名称
        /// </summary>
        [Required]
        [StringLength(50)]
        public required string RoleName { get; set; }

        /// <summary>
        /// 功能标识符
        /// </summary>
        [Required]
        [StringLength(100)]
        public required string FeatureKey { get; set; }

        /// <summary>
        /// 是否启用 (null表示使用全局设置)
        /// </summary>
        public bool? IsEnabled { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 创建者
        /// </summary>
        [StringLength(50)]
        public string? CreatedBy { get; set; }

        /// <summary>
        /// 更新者
        /// </summary>
        [StringLength(50)]
        public string? UpdatedBy { get; set; }

        /// <summary>
        /// 关联的功能配置
        /// </summary>
        [ForeignKey("FeatureKey")]
        public virtual FeatureConfig? FeatureConfig { get; set; }
    }
}
