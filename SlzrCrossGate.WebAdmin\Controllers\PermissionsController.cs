using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SlzrCrossGate.Core.Services;
using SlzrCrossGate.Core.Attributes;
using SlzrCrossGate.Core.Database;

namespace SlzrCrossGate.WebAdmin.Controllers
{
    /// <summary>
    /// 权限管理API控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class PermissionsController : ControllerBase
    {
        private readonly IFeaturePermissionService _permissionService;
        private readonly TcpDbContext _context;
        private readonly ILogger<PermissionsController> _logger;

        public PermissionsController(
            IFeaturePermissionService permissionService,
            TcpDbContext context,
            ILogger<PermissionsController> logger)
        {
            _permissionService = permissionService;
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// 获取当前用户的所有权限
        /// </summary>
        [HttpGet("user-permissions")]
        public async Task<ActionResult<Dictionary<string, bool>>> GetUserPermissions()
        {
            try
            {
                var permissions = await _permissionService.GetUserPermissionsAsync(User);
                return Ok(permissions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get user permissions");
                return StatusCode(500, new { message = "获取权限失败" });
            }
        }

        /// <summary>
        /// 检查特定功能权限
        /// </summary>
        [HttpGet("check/{featureKey}")]
        public async Task<ActionResult<bool>> CheckPermission(string featureKey)
        {
            try
            {
                var hasPermission = await _permissionService.HasPermissionAsync(User, featureKey);
                return Ok(hasPermission);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to check permission for {FeatureKey}", featureKey);
                return StatusCode(500, new { message = "权限检查失败" });
            }
        }

        /// <summary>
        /// 批量检查权限
        /// </summary>
        [HttpPost("check-multiple")]
        public async Task<ActionResult<Dictionary<string, bool>>> CheckMultiplePermissions([FromBody] List<string> featureKeys)
        {
            try
            {
                var permissions = await _permissionService.CheckMultiplePermissionsAsync(User, featureKeys);
                return Ok(permissions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to check multiple permissions");
                return StatusCode(500, new { message = "批量权限检查失败" });
            }
        }

        /// <summary>
        /// 获取功能配置列表 (所有用户都可以获取，用于权限检查)
        /// </summary>
        [HttpGet("feature-configs")]
        public async Task<ActionResult<List<FeatureConfigDto>>> GetFeatureConfigs()
        {
            try
            {
                var configs = await _permissionService.GetFeatureConfigsAsync();
                return Ok(configs);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get feature configs");
                return StatusCode(500, new { message = "获取功能配置失败" });
            }
        }

        /// <summary>
        /// 批量更新功能配置 (仅系统管理员)
        /// </summary>
        [HttpPost("batch-update")]
        [RequireFeaturePermission("feature_permission.save_changes")]
        public async Task<IActionResult> BatchUpdateConfigs([FromBody] BatchUpdateRequest request)
        {
            try
            {
                await _permissionService.BatchUpdateConfigsAsync(request.Updates);
                return Ok(new { message = "配置更新成功" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to update feature configs");
                return StatusCode(500, new { message = "配置更新失败" });
            }
        }

        /// <summary>
        /// 更新单个功能配置 (仅系统管理员)
        /// </summary>
        [HttpPut("feature-config/{featureKey}")]
        [RequireFeaturePermission("feature_permission.save_changes")]
        public async Task<IActionResult> UpdateFeatureConfig(string featureKey, [FromBody] bool isEnabled)
        {
            try
            {
                await _permissionService.UpdateFeatureConfigAsync(featureKey, isEnabled);
                return Ok(new { message = "功能配置更新成功" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to update feature config for {FeatureKey}", featureKey);
                return StatusCode(500, new { message = "功能配置更新失败" });
            }
        }

        /// <summary>
        /// 更新角色权限 (仅系统管理员)
        /// </summary>
        [HttpPut("role-permission")]
        [RequireFeaturePermission("feature_permission.save_changes")]
        public async Task<IActionResult> UpdateRolePermission([FromBody] UpdateRolePermissionRequest request)
        {
            try
            {
                await _permissionService.UpdateRolePermissionAsync(request.RoleName, request.FeatureKey, request.IsEnabled);
                return Ok(new { message = "角色权限更新成功" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to update role permission");
                return StatusCode(500, new { message = "角色权限更新失败" });
            }
        }

        /// <summary>
        /// 刷新权限缓存 (仅系统管理员)
        /// </summary>
        [HttpPost("refresh-cache")]
        [RequireFeaturePermission("feature_permission.save_changes")]
        public async Task<IActionResult> RefreshCache()
        {
            try
            {
                await _permissionService.RefreshPermissionCacheAsync();
                return Ok(new { message = "权限缓存已刷新" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to refresh permission cache");
                return StatusCode(500, new { message = "刷新缓存失败" });
            }
        }

        /// <summary>
        /// 初始化默认配置 (仅系统管理员)
        /// </summary>
        [HttpPost("initialize-defaults")]
        [RequireFeaturePermission("feature_permission.save_changes")]
        public async Task<IActionResult> InitializeDefaults()
        {
            try
            {
                await _permissionService.InitializeDefaultConfigsAsync();
                return Ok(new { message = "默认配置初始化成功" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to initialize default configs");
                return StatusCode(500, new { message = "默认配置初始化失败" });
            }
        }
    }

    /// <summary>
    /// 更新角色权限请求
    /// </summary>
    public class UpdateRolePermissionRequest
    {
        public string RoleName { get; set; } = string.Empty;
        public string FeatureKey { get; set; } = string.Empty;
        public bool? IsEnabled { get; set; }
    }
}
