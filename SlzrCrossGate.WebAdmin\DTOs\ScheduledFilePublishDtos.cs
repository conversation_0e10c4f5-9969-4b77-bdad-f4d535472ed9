using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using SlzrCrossGate.Core.Models;

namespace SlzrCrossGate.WebAdmin.DTOs
{
    /// <summary>
    /// 预约文件发布DTO
    /// </summary>
    public class ScheduledFilePublishDto
    {
        public int ID { get; set; }
        public int FileVersionID { get; set; }
        public string MerchantID { get; set; } = string.Empty;
        public string MerchantName { get; set; } = string.Empty;
        public string FileTypeID { get; set; } = string.Empty;
        public string FileTypeName { get; set; } = string.Empty;
        public string FilePara { get; set; } = string.Empty;
        public string FileFullType { get; set; } = string.Empty;
        public string Ver { get; set; } = string.Empty;
        public PublishTypeOption PublishType { get; set; }
        public string PublishTarget { get; set; } = string.Empty;
        public DateTime ScheduledTime { get; set; }
        public ScheduledPublishStatus Status { get; set; }
        public DateTime CreatedTime { get; set; }
        public string CreatedBy { get; set; } = string.Empty;
        public DateTime? ExecutedTime { get; set; }
        public string? ErrorMessage { get; set; }
        public string? Remarks { get; set; }
    }

    /// <summary>
    /// 创建预约文件发布DTO
    /// </summary>
    public class CreateScheduledFilePublishDto
    {
        [Required(ErrorMessage = "商户ID不能为空")]
        public string MerchantID { get; set; } = string.Empty;

        [Required(ErrorMessage = "文件版本ID不能为空")]
        public int FileVersionID { get; set; }

        [Required(ErrorMessage = "发布类型不能为空")]
        public PublishTypeOption PublishType { get; set; }

        public string PublishTarget { get; set; } = string.Empty;

        [Required(ErrorMessage = "预约时间不能为空")]
        public DateTime ScheduledTime { get; set; }

        [MaxLength(200, ErrorMessage = "备注信息不能超过200个字符")]
        public string? Remarks { get; set; }
    }

    /// <summary>
    /// 更新预约文件发布DTO
    /// </summary>
    public class UpdateScheduledFilePublishDto
    {
        [Required(ErrorMessage = "发布类型不能为空")]
        public PublishTypeOption PublishType { get; set; }

        public string PublishTarget { get; set; } = string.Empty;

        [Required(ErrorMessage = "预约时间不能为空")]
        public DateTime ScheduledTime { get; set; }

        [MaxLength(200, ErrorMessage = "备注信息不能超过200个字符")]
        public string? Remarks { get; set; }
    }
}
