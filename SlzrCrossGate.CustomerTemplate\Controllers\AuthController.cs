using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SlzrCrossGate.CustomerTemplate.Services;
using System.Security.Claims;

namespace SlzrCrossGate.CustomerTemplate.Controllers
{
    /// <summary>
    /// 认证控制器 - 处理用户认证相关操作
    /// </summary>
    [ApiController]
    [Route("api/customa/[controller]")]
    public class AuthController : ControllerBase
    {
        private readonly AuthService _authService;
        private readonly ILogger<AuthController> _logger;

        public AuthController(AuthService authService, ILogger<AuthController> logger)
        {
            _authService = authService;
            _logger = logger;
        }

        /// <summary>
        /// 获取当前用户信息
        /// </summary>
        /// <returns>用户信息</returns>
        [HttpGet("userinfo")]
        [Authorize]
        public async Task<ActionResult<UserInfoResponse>> GetUserInfo()
        {
            try
            {
                // 从JWT Token中提取用户ID
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized("无效的用户身份");
                }

                // 验证用户是否存在
                var user = await _authService.ValidateUserAsync(userId);
                if (user == null)
                {
                    return Unauthorized("用户不存在或已被禁用");
                }

                // 获取商户信息
                string? merchantName = null;
                if (!string.IsNullOrEmpty(user.MerchantID))
                {
                    var merchant = await _authService.GetMerchantAsync(user.MerchantID);
                    merchantName = merchant?.MerchantName;
                }

                // 从Claims中提取角色信息
                var userInfo = _authService.ExtractUserInfo(User.Claims);
                
                var response = new UserInfoResponse
                {
                    UserId = user.Id,
                    UserName = user.UserName,
                    Email = user.Email,
                    RealName = user.RealName,
                    MerchantId = user.MerchantID,
                    MerchantName = merchantName,
                    Roles = userInfo.Roles,
                    EmailConfirmed = user.EmailConfirmed,
                    CreateTime = user.CreateTime,
                    IsTwoFactorRequired = user.IsTwoFactorRequired
                };

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户信息时发生错误");
                return StatusCode(500, "服务器内部错误");
            }
        }

        /// <summary>
        /// 验证用户权限
        /// </summary>
        /// <param name="permission">权限名称</param>
        /// <returns>是否有权限</returns>
        [HttpGet("check-permission")]
        [Authorize]
        public async Task<ActionResult<PermissionCheckResponse>> CheckPermission([FromQuery] string permission)
        {
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized();
                }

                // 简单的权限检查逻辑
                var hasPermission = await _authService.IsInRoleAsync(userId, permission);

                return Ok(new PermissionCheckResponse
                {
                    HasPermission = hasPermission,
                    Permission = permission
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查权限时发生错误: {Permission}", permission);
                return StatusCode(500, "服务器内部错误");
            }
        }

        /// <summary>
        /// 健康检查
        /// </summary>
        /// <returns>服务状态</returns>
        [HttpGet("health")]
        public ActionResult<HealthCheckResponse> HealthCheck()
        {
            return Ok(new HealthCheckResponse
            {
                Status = "Healthy",
                Timestamp = DateTime.Now,
                Service = "SlzrCrossGate.CustomerTemplate"
            });
        }
    }

    #region Response DTOs

    /// <summary>
    /// 用户信息响应
    /// </summary>
    public class UserInfoResponse
    {
        public string UserId { get; set; } = string.Empty;
        public string UserName { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string? RealName { get; set; }
        public string? MerchantId { get; set; }
        public string? MerchantName { get; set; }
        public List<string> Roles { get; set; } = new();
        public bool EmailConfirmed { get; set; }
        public DateTime CreateTime { get; set; }
        public bool IsTwoFactorRequired { get; set; }
    }

    /// <summary>
    /// 权限检查响应
    /// </summary>
    public class PermissionCheckResponse
    {
        public bool HasPermission { get; set; }
        public string Permission { get; set; } = string.Empty;
    }

    /// <summary>
    /// 健康检查响应
    /// </summary>
    public class HealthCheckResponse
    {
        public string Status { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
        public string Service { get; set; } = string.Empty;
    }

    #endregion
}
