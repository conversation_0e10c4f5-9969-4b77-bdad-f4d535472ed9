using Microsoft.EntityFrameworkCore;
using SlzrCrossGate.CustomerTemplate.Data;
using SlzrCrossGate.CustomerTemplate.Models;

namespace SlzrCrossGate.CustomerTemplate.Services
{
    /// <summary>
    /// 客户数据服务 - 使用手动维护的表
    /// </summary>
    public class CustomerDataService
    {
        private readonly CustomerDataContext _context;
        private readonly ReadOnlyAuthService _authService;
        private readonly ILogger<CustomerDataService> _logger;

        public CustomerDataService(
            CustomerDataContext context,
            ReadOnlyAuthService authService,
            ILogger<CustomerDataService> logger)
        {
            _context = context;
            _authService = authService;
            _logger = logger;
        }

        #region 客户数据管理

        /// <summary>
        /// 获取客户数据列表
        /// </summary>
        public async Task<List<CustomerData>> GetDataListAsync(string userId, string? dataType = null)
        {
            try
            {
                var user = await _authService.ValidateUserAsync(userId);
                if (user == null)
                {
                    _logger.LogWarning("用户验证失败: {UserId}", userId);
                    return new List<CustomerData>();
                }

                var query = _context.CustomerData.Where(d => d.MerchantId == user.MerchantID);

                if (!string.IsNullOrEmpty(dataType))
                {
                    query = query.Where(d => d.DataType == dataType);
                }

                return await query
                    .OrderByDescending(d => d.CreatedAt)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取客户数据失败: {UserId}", userId);
                return new List<CustomerData>();
            }
        }

        /// <summary>
        /// 保存客户数据
        /// </summary>
        public async Task<bool> SaveDataAsync(string userId, CustomerData data)
        {
            try
            {
                var user = await _authService.ValidateUserAsync(userId);
                if (user == null) return false;

                // 确保数据属于用户的商户
                data.MerchantId = user.MerchantID;
                data.UpdatedAt = DateTime.Now;
                data.UpdatedBy = user.UserName;

                if (data.Id == 0)
                {
                    data.CreatedAt = DateTime.Now;
                    data.CreatedBy = user.UserName;
                    _context.CustomerData.Add(data);
                }
                else
                {
                    // 验证数据所有权
                    var existing = await _context.CustomerData
                        .FirstOrDefaultAsync(d => d.Id == data.Id && d.MerchantId == user.MerchantID);

                    if (existing == null)
                    {
                        _logger.LogWarning("尝试修改不属于当前商户的数据: {UserId}, {DataId}", userId, data.Id);
                        return false;
                    }

                    _context.Entry(existing).CurrentValues.SetValues(data);
                }

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存客户数据失败: {UserId}", userId);
                return false;
            }
        }

        /// <summary>
        /// 删除客户数据
        /// </summary>
        public async Task<bool> DeleteDataAsync(string userId, int dataId)
        {
            try
            {
                var user = await _authService.ValidateUserAsync(userId);
                if (user == null) return false;

                var data = await _context.CustomerData
                    .FirstOrDefaultAsync(d => d.Id == dataId && d.MerchantId == user.MerchantID);

                if (data == null)
                {
                    _logger.LogWarning("尝试删除不存在或不属于当前商户的数据: {UserId}, {DataId}", userId, dataId);
                    return false;
                }

                _context.CustomerData.Remove(data);
                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除客户数据失败: {UserId}, {DataId}", userId, dataId);
                return false;
            }
        }

        /// <summary>
        /// 获取记录数量
        /// </summary>
        public async Task<int> GetRecordCountAsync(string? merchantId)
        {
            try
            {
                if (string.IsNullOrEmpty(merchantId))
                {
                    return 0;
                }

                return await _context.CustomerData
                    .Where(d => d.MerchantId == merchantId)
                    .CountAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取记录数量失败: {MerchantId}", merchantId);
                return 0;
            }
        }

        #endregion

        #region 配置管理

        /// <summary>
        /// 获取配置值
        /// </summary>
        public async Task<string?> GetSettingAsync(string userId, string settingKey)
        {
            try
            {
                var user = await _authService.ValidateUserAsync(userId);
                if (user == null) return null;

                var setting = await _context.Settings
                    .FirstOrDefaultAsync(s => s.MerchantId == user.MerchantID && s.SettingKey == settingKey);

                return setting?.SettingValue;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取配置失败: {UserId}, {SettingKey}", userId, settingKey);
                return null;
            }
        }

        /// <summary>
        /// 保存配置
        /// </summary>
        public async Task<bool> SaveSettingAsync(string userId, string settingKey, string settingValue, string? description = null)
        {
            try
            {
                var user = await _authService.ValidateUserAsync(userId);
                if (user == null) return false;

                var setting = await _context.Settings
                    .FirstOrDefaultAsync(s => s.MerchantId == user.MerchantID && s.SettingKey == settingKey);

                if (setting == null)
                {
                    setting = new CustomerSettings
                    {
                        MerchantId = user.MerchantID,
                        SettingKey = settingKey,
                        SettingValue = settingValue,
                        Description = description,
                        CreatedAt = DateTime.Now
                    };
                    _context.Settings.Add(setting);
                }
                else
                {
                    setting.SettingValue = settingValue;
                    setting.Description = description ?? setting.Description;
                    setting.UpdatedAt = DateTime.Now;
                }

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存配置失败: {UserId}, {SettingKey}", userId, settingKey);
                return false;
            }
        }

        /// <summary>
        /// 获取所有配置
        /// </summary>
        public async Task<Dictionary<string, string>> GetAllSettingsAsync(string userId)
        {
            try
            {
                var user = await _authService.ValidateUserAsync(userId);
                if (user == null) return new Dictionary<string, string>();

                var settings = await _context.Settings
                    .Where(s => s.MerchantId == user.MerchantID)
                    .ToDictionaryAsync(s => s.SettingKey, s => s.SettingValue ?? "");

                return settings;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有配置失败: {UserId}", userId);
                return new Dictionary<string, string>();
            }
        }

        #endregion

        #region 工作流管理

        /// <summary>
        /// 获取工作流列表
        /// </summary>
        public async Task<List<CustomerWorkflow>> GetWorkflowsAsync(string userId)
        {
            try
            {
                var user = await _authService.ValidateUserAsync(userId);
                if (user == null) return new List<CustomerWorkflow>();

                return await _context.Workflows
                    .Where(w => w.MerchantId == user.MerchantID)
                    .OrderByDescending(w => w.CreatedAt)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取工作流失败: {UserId}", userId);
                return new List<CustomerWorkflow>();
            }
        }

        /// <summary>
        /// 保存工作流
        /// </summary>
        public async Task<bool> SaveWorkflowAsync(string userId, CustomerWorkflow workflow)
        {
            try
            {
                var user = await _authService.ValidateUserAsync(userId);
                if (user == null) return false;

                workflow.MerchantId = user.MerchantID;
                workflow.UpdatedAt = DateTime.Now;

                if (workflow.Id == 0)
                {
                    workflow.CreatedAt = DateTime.Now;
                    _context.Workflows.Add(workflow);
                }
                else
                {
                    var existing = await _context.Workflows
                        .FirstOrDefaultAsync(w => w.Id == workflow.Id && w.MerchantId == user.MerchantID);

                    if (existing == null) return false;

                    _context.Entry(existing).CurrentValues.SetValues(workflow);
                }

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存工作流失败: {UserId}", userId);
                return false;
            }
        }

        #endregion

        #region 报表管理

        /// <summary>
        /// 获取报表列表
        /// </summary>
        public async Task<List<CustomerReport>> GetReportsAsync(string userId)
        {
            try
            {
                var user = await _authService.ValidateUserAsync(userId);
                if (user == null) return new List<CustomerReport>();

                return await _context.Reports
                    .Where(r => r.MerchantId == user.MerchantID)
                    .OrderByDescending(r => r.CreatedAt)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取报表失败: {UserId}", userId);
                return new List<CustomerReport>();
            }
        }

        /// <summary>
        /// 保存报表
        /// </summary>
        public async Task<bool> SaveReportAsync(string userId, CustomerReport report)
        {
            try
            {
                var user = await _authService.ValidateUserAsync(userId);
                if (user == null) return false;

                report.MerchantId = user.MerchantID;
                report.UpdatedAt = DateTime.Now;

                if (report.Id == 0)
                {
                    report.CreatedAt = DateTime.Now;
                    _context.Reports.Add(report);
                }
                else
                {
                    var existing = await _context.Reports
                        .FirstOrDefaultAsync(r => r.Id == report.Id && r.MerchantId == user.MerchantID);

                    if (existing == null) return false;

                    _context.Entry(existing).CurrentValues.SetValues(report);
                }

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存报表失败: {UserId}", userId);
                return false;
            }
        }

        #endregion
    }
}
