using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using SlzrCrossGate.WebAdmin.Services;
using SlzrCrossGate.Core.Models;
using SlzrCrossGate.Core.Database;
using Microsoft.Extensions.Logging;

namespace SlzrCrossGate.WebAdmin.Controllers
{
    /// <summary>
    /// 终端日志测试控制器 - 仅用于开发测试
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class TerminalLogTestController : ControllerBase
    {
        private readonly TerminalLogParserService _parserService;
        private readonly TerminalLogPublishService _publishService;
        private readonly TcpDbContext _dbContext;
        private readonly ILogger<TerminalLogTestController> _logger;

        public TerminalLogTestController(
            TerminalLogParserService parserService,
            TerminalLogPublishService publishService,
            TcpDbContext dbContext,
            ILogger<TerminalLogTestController> logger)
        {
            _parserService = parserService;
            _publishService = publishService;
            _dbContext = dbContext;
            _logger = logger;
        }

        /// <summary>
        /// 测试终端日志解析功能
        /// </summary>
        /// <returns></returns>
        [HttpPost("test-parse")]
        public async Task<IActionResult> TestParse()
        {
            try
            {
                // 创建测试消费数据
                var testConsumeData = new ConsumeData
                {
                    MerchantID = "12345678",
                    MachineID = "TEST001",
                    MachineNO = "001",
                    PsamNO = "PSAM001",
                    ReceiveTime = DateTime.Now,
                    Buffer = GenerateTestBuffer()
                };

                // 解析数据
                var terminalLogs = _parserService.ParseConsumeData(testConsumeData);

                // 保存到数据库（可选）
                if (terminalLogs.Count > 0)
                {
                    await _dbContext.TerminalLogs.AddRangeAsync(terminalLogs);
                    await _dbContext.SaveChangesAsync();
                }

                return Ok(new
                {
                    Success = true,
                    Message = $"成功解析并保存 {terminalLogs.Count} 条终端日志记录",
                    Data = terminalLogs.Select(log => new
                    {
                        log.MerchantID,
                        log.MachineID,
                        log.MachineNO,
                        log.LogType,
                        log.SetMethod,
                        log.CardNO,
                        log.Price,
                        log.DriverCardNO,
                        log.LineNO,
                        log.LogTime,
                        log.UploadTime
                    })
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "测试终端日志解析时发生错误");
                return BadRequest(new
                {
                    Success = false,
                    Message = "测试失败：" + ex.Message
                });
            }
        }

        /// <summary>
        /// 生成测试Buffer数据
        /// </summary>
        /// <returns></returns>
        private byte[] GenerateTestBuffer()
        {
            // 生成一个简单的测试Buffer
            // 这里模拟一条终端日志记录的数据格式
            var buffer = new List<byte>();

            // 记录类型 (1字节) - 例如：1表示上车刷卡
            buffer.Add(0x01);

            // 设置方式 (1字节) - 例如：1表示刷卡
            buffer.Add(0x01);

            // 卡号 (8字节，BCD编码) - 例如：1234567890123456
            buffer.AddRange(new byte[] { 0x12, 0x34, 0x56, 0x78, 0x90, 0x12, 0x34, 0x56 });

            // 票价 (4字节，小端序) - 例如：200分
            var priceBytes = BitConverter.GetBytes(200);
            buffer.AddRange(priceBytes);

            // 司机卡号 (8字节，BCD编码) - 例如：9876543210987654
            buffer.AddRange(new byte[] { 0x98, 0x76, 0x54, 0x32, 0x10, 0x98, 0x76, 0x54 });

            // 记录时间 (6字节，BCD编码的YYMMDDHHMMSS) - 例如：241231235959 (2024年12月31日23:59:59)
            buffer.AddRange(new byte[] { 0x24, 0x12, 0x31, 0x23, 0x59, 0x59 });

            // 线路号 (2字节) - 例如：线路101
            var lineBytes = BitConverter.GetBytes((ushort)101);
            buffer.AddRange(lineBytes);

            return buffer.ToArray();
        }

        /// <summary>
        /// 获取Buffer的十六进制表示
        /// </summary>
        /// <returns></returns>
        [HttpGet("test-buffer-hex")]
        public IActionResult GetTestBufferHex()
        {
            var buffer = GenerateTestBuffer();
            var hex = Convert.ToHexString(buffer);
            
            return Ok(new
            {
                BufferLength = buffer.Length,
                BufferHex = hex,
                BufferBytes = buffer
            });
        }

        /// <summary>
        /// 发布测试数据到终端日志队列
        /// </summary>
        [HttpPost("publish-test-data")]
        public async Task<IActionResult> PublishTestData()
        {
            try
            {
                // 生成测试消费数据
                var testConsumeData = new SlzrDatatransferModel.ConsumeData
                {
                    MerchantID = "TEST",
                    MachineID = "TEST001",
                    MachineNO = "TEST001",
                    PsamNO = "12345678",
                    buffer = GenerateTestBuffer()
                };

                // 发布到终端日志队列
                await _publishService.PublishConsumeDataAsync(testConsumeData);

                _logger.LogInformation("成功发布测试数据到终端日志队列");
                return Ok(new { message = "测试数据已发布到终端日志队列，请检查后台服务日志" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "发布测试数据时发生错误");
                return StatusCode(500, new { message = "发布测试数据失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 清理测试数据
        /// </summary>
        /// <returns></returns>
        [HttpDelete("cleanup-test-data")]
        public async Task<IActionResult> CleanupTestData()
        {
            try
            {
                // 删除测试商户的数据
                var testLogs = _dbContext.TerminalLogs.Where(log => log.MerchantID == "12345678");
                _dbContext.TerminalLogs.RemoveRange(testLogs);
                var deletedCount = await _dbContext.SaveChangesAsync();

                return Ok(new
                {
                    Success = true,
                    Message = $"成功清理 {deletedCount} 条测试数据"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清理测试数据时发生错误");
                return BadRequest(new
                {
                    Success = false,
                    Message = "清理失败：" + ex.Message
                });
            }
        }
    }
}
