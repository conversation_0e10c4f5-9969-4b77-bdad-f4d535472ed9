# 银联终端密钥管理需求

## 1. 需求背景
用户需要提前录入银联终端密钥信息，以便车载终端在使用时能获取未使用密钥并完成绑定，我们这里是需要一个后端管理页面，来展示银联终端密钥信息，用户可以在页面上进行增、删、改等操作，同时为了方便用户批量导入银联终端密钥信息，需要提供一个导入功能，让用户可以通过excel文件/csv文件等格式批量导入银联终端密钥信息，系统会自动解析文件内容并将数据存入数据库中。

## 2. 需求分析

银联终端密钥对应的模型类（已有，在core项目models中）：UnionPayTerminalKey.cs 
```csharp
public class UnionPayTerminalKey:ITenantEntity
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int ID { get; set; }
        /// <summary>
        /// 商户号
        /// </summary>
        [StringLength(8)]
        public required string MerchantID { get; set; }
        /// <summary>
        /// 银联商户号
        /// </summary>
        [StringLength(15)]
        public required string UP_MerchantID { get; set; }
        /// <summary>
        /// 银联终端号
        /// </summary>
        [StringLength(8)]
        public required string UP_TerminalID { get; set; }
        /// <summary>
        /// 银联终端密钥
        /// </summary>
        [StringLength(32)]
        public required string UP_Key { get; set; }
        /// <summary>
        /// 银联商户名称
        /// </summary>
        [MaxLength(50)]
        public string? UP_MerchantName { get; set; }
        /// <summary>
        /// 是否被使用（绑定）
        /// </summary>
        public bool IsInUse { get; set; }
        /// <summary>
        /// 被使用的设备ID
        /// </summary>
        [StringLength(8)]
        public string? MachineID { get; set; }
        /// <summary>
        /// 被使用的设备所在的线路ID
        /// </summary>
        [MaxLength(8)]
        public string? LineID { get; set; }
        /// <summary>
        /// 被使用的设备编号
        /// </summary>
        [MaxLength(8)]
        public string? MachineNO { get; set; }
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }
        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; }

    }
```

页面需要展示的字段：
- 商户号
- 银联商户号
- 银联终端号
- 银联终端密钥
- 银联商户名称
- 是否被使用（绑定）
- 设备序列号（被使用的设备ID）
- 线路ID（被使用的设备所在的线路ID）
- 设备编号（被使用的设备编号）
- 创建时间
- 更新时间
- 操作（增、删、改）
- 分页（每页显示10条数据）
- 搜索（支持模糊搜索，支持按商户号、银联商户号、银联终端号、银联终端密钥、银联商户名称、设备序列号、设备编号等字段进行搜索）
- 排序（支持按创建时间、更新时间等字段进行排序）
- 过滤（支持按是否被使用（绑定）进行过滤）



其它功能：
- 支持批量导入银联终端密钥信息：导入的字段包含：银联商户号、银联终端号、银联终端密钥、银联商户名称。默认被导入的都是未使用状态，导入后需要在页面上展示导入的结果，成功的条数和失败的条数，以及失败的原因。导入的文件格式可以是excel文件/csv文件等格式，系统会自动解析文件内容并将数据存入数据库中。
- 支持下载导入模板：用户可以下载一个excel文件/csv文件等格式的模板，模板中包含需要导入的字段，用户可以在模板中填写银联终端密钥信息，然后上传到系统中进行批量导入。


## 3. 需求实现
- 后端和前端都要实现
- 前端使用当前前端项目同样的技术栈，要和当前的页面风格一致,前端api请统一在api.js中定义
- 后端使用当前后端项目同样的技术栈，要和其它接口的风格一致
- 前端页面需要使用表格组件展示数据，支持分页、搜索、排序、过滤等功能
- 前端页面需要使用表单组件展示新增和编辑的表单，支持验证
- 商户号需要使用商户下拉组件哦，保持和其它页面一致

其它要求：
保证代码逻辑清晰，实现优美，页面操作流畅，风格一致且美观，如果后端有新增的class，请创建独立的文件。