using SlzrCrossGate.Core.Models;

namespace SlzrCrossGate.WebAdmin.DTOs
{
    /// <summary>
    /// 终端文件上传查询请求DTO
    /// </summary>
    public class TerminalFileUploadQueryDto
    {
        /// <summary>
        /// 商户号
        /// </summary>
        public string? MerchantID { get; set; }

        /// <summary>
        /// 终端ID
        /// </summary>
        public string? TerminalID { get; set; }

        /// <summary>
        /// 终端类型
        /// </summary>
        public string? TerminalType { get; set; }

        /// <summary>
        /// 文件类型
        /// </summary>
        public string? FileType { get; set; }

        /// <summary>
        /// 文件名（支持模糊搜索）
        /// </summary>
        public string? FileName { get; set; }

        /// <summary>
        /// 上传状态
        /// </summary>
        public TerminalUploadStatus? Status { get; set; }

        /// <summary>
        /// 开始时间（起始）
        /// </summary>
        public DateTime? StartTimeFrom { get; set; }

        /// <summary>
        /// 开始时间（结束）
        /// </summary>
        public DateTime? StartTimeTo { get; set; }

        /// <summary>
        /// 页码
        /// </summary>
        public int Page { get; set; } = 1;

        /// <summary>
        /// 每页大小
        /// </summary>
        public int PageSize { get; set; } = 20;

        /// <summary>
        /// 排序字段
        /// </summary>
        public string? SortBy { get; set; } = "StartTime";

        /// <summary>
        /// 排序方向
        /// </summary>
        public string? SortDirection { get; set; } = "desc";
    }

    /// <summary>
    /// 终端文件上传响应DTO
    /// </summary>
    public class TerminalFileUploadDto
    {
        /// <summary>
        /// 上传文件ID
        /// </summary>
        public string ID { get; set; } = "";

        /// <summary>
        /// 商户号
        /// </summary>
        public string MerchantID { get; set; } = "";

        /// <summary>
        /// 商户名称
        /// </summary>
        public string MerchantName { get; set; } = "";

        /// <summary>
        /// 终端ID
        /// </summary>
        public string TerminalID { get; set; } = "";

        /// <summary>
        /// 终端类型
        /// </summary>
        public string TerminalType { get; set; } = "";

        /// <summary>
        /// 文件类型
        /// </summary>
        public string FileType { get; set; } = "";

        /// <summary>
        /// 文件名
        /// </summary>
        public string FileName { get; set; } = "";

        /// <summary>
        /// 文件大小
        /// </summary>
        public long FileSize { get; set; }

        /// <summary>
        /// 文件大小（格式化）
        /// </summary>
        public string FileSizeFormatted { get; set; } = "";

        /// <summary>
        /// 文件CRC
        /// </summary>
        public string FileCRC { get; set; } = "";

        /// <summary>
        /// 已接收长度
        /// </summary>
        public long ReceivedLength { get; set; }

        /// <summary>
        /// 上传进度（百分比）
        /// </summary>
        public decimal ProgressPercentage { get; set; }

        /// <summary>
        /// 客户端块大小
        /// </summary>
        public int ClientChunkSize { get; set; }

        /// <summary>
        /// 服务端块大小
        /// </summary>
        public int ServerChunkSize { get; set; }

        /// <summary>
        /// 上传状态
        /// </summary>
        public TerminalUploadStatus Status { get; set; }

        /// <summary>
        /// 上传状态描述
        /// </summary>
        public string StatusDescription { get; set; } = "";

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdateTime { get; set; }

        /// <summary>
        /// 最后活动时间
        /// </summary>
        public DateTime LastActivityTime { get; set; }

        /// <summary>
        /// 上传者
        /// </summary>
        public string? UploadedBy { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 最终文件ID
        /// </summary>
        public string? FinalUploadFileID { get; set; }

        /// <summary>
        /// 是否可以下载
        /// </summary>
        public bool CanDownload { get; set; }

        /// <summary>
        /// 上传耗时（秒）
        /// </summary>
        public double? UploadDurationSeconds { get; set; }

        /// <summary>
        /// 上传速度（KB/s）
        /// </summary>
        public double? UploadSpeedKBps { get; set; }
    }

    /// <summary>
    /// 终端文件上传统计DTO
    /// </summary>
    public class TerminalFileUploadStatsDto
    {
        /// <summary>
        /// 总上传数
        /// </summary>
        public int TotalUploads { get; set; }

        /// <summary>
        /// 上传中数量
        /// </summary>
        public int UploadingCount { get; set; }

        /// <summary>
        /// 已完成数量
        /// </summary>
        public int CompletedCount { get; set; }

        /// <summary>
        /// 失败数量
        /// </summary>
        public int FailedCount { get; set; }

        /// <summary>
        /// 取消数量
        /// </summary>
        public int CancelledCount { get; set; }

        /// <summary>
        /// 总文件大小（字节）
        /// </summary>
        public long TotalFileSize { get; set; }

        /// <summary>
        /// 总文件大小（格式化）
        /// </summary>
        public string TotalFileSizeFormatted { get; set; } = "";

        /// <summary>
        /// 今日上传数
        /// </summary>
        public int TodayUploads { get; set; }

        /// <summary>
        /// 本周上传数
        /// </summary>
        public int WeekUploads { get; set; }

        /// <summary>
        /// 本月上传数
        /// </summary>
        public int MonthUploads { get; set; }

        /// <summary>
        /// 文件类型统计
        /// </summary>
        public List<FileTypeStatsDto> FileTypeStats { get; set; } = new();

        /// <summary>
        /// 终端类型统计
        /// </summary>
        public List<TerminalFileUploadTypeStatsDto> TerminalTypeStats { get; set; } = new();
    }

    /// <summary>
    /// 文件类型统计DTO
    /// </summary>
    public class FileTypeStatsDto
    {
        /// <summary>
        /// 文件类型
        /// </summary>
        public string FileType { get; set; } = "";

        /// <summary>
        /// 数量
        /// </summary>
        public int Count { get; set; }

        /// <summary>
        /// 总大小
        /// </summary>
        public long TotalSize { get; set; }

        /// <summary>
        /// 总大小（格式化）
        /// </summary>
        public string TotalSizeFormatted { get; set; } = "";
    }

    /// <summary>
    /// 终端文件上传类型统计DTO
    /// </summary>
    public class TerminalFileUploadTypeStatsDto
    {
        /// <summary>
        /// 终端类型
        /// </summary>
        public string TerminalType { get; set; } = "";

        /// <summary>
        /// 数量
        /// </summary>
        public int Count { get; set; }

        /// <summary>
        /// 总大小
        /// </summary>
        public long TotalSize { get; set; }

        /// <summary>
        /// 总大小（格式化）
        /// </summary>
        public string TotalSizeFormatted { get; set; } = "";
    }

    /// <summary>
    /// 文件下载请求DTO
    /// </summary>
    public class FileDownloadRequestDto
    {
        /// <summary>
        /// 上传文件ID
        /// </summary>
        public string UploadFileID { get; set; } = "";
    }
}
