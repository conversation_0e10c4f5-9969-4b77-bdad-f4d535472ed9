using SlzrCrossGate.Core.Models;
using Microsoft.Extensions.Logging;
using System.Text;
using SlzrCrossGate.Common;

namespace SlzrCrossGate.WebAdmin.Services
{
    /// <summary>
    /// 终端日志解析服务
    /// 负责将消费数据的Buffer解析为终端日志记录
    /// </summary>
    public class TerminalLogParserService
    {
        private readonly ILogger<TerminalLogParserService> _logger;

        public TerminalLogParserService(ILogger<TerminalLogParserService> logger)
        {
            _logger = logger;
        }


        public static byte[] BankHead = new byte[] { 0xD2, 0xF8, 0xD0, 0xD0, 0xCA, 0xFD, 0xBE, 0xDD };
        public static byte[] QrcodeHead = new byte[] { 0xb6, 0xfe, 0xce, 0xac, 0xc2, 0xeb };//B6FECEACC2EB4143
        public static byte[] IDCardHead = new byte[] { 0xC9, 0xED, 0xB7, 0xDD, 0xD6, 0xA4, 0x33, 0x30 };//身份证记录头：C9 ED B7 DD D6 A4 33 30

        /// <summary>
        /// 解析消费数据为终端日志记录
        /// </summary>
        /// <param name="consumeData">消费数据</param>
        /// <returns>解析后的终端日志记录列表</returns>
        public List<TerminalLog> ParseConsumeData(ConsumeData consumeData)
        {
            var terminalLogs = new List<TerminalLog>();

            try
            {
                if (consumeData.Buffer == null || consumeData.Buffer.Length == 0)
                {
                    _logger.LogWarning("消费数据Buffer为空，跳过解析。MerchantID: {MerchantID}, MachineID: {MachineID}",
                        consumeData.MerchantID, consumeData.MachineID);
                    return terminalLogs;
                }

                //排除不需要的记录
                if (IsStartWith(consumeData.Buffer, BankHead, 3)
                || IsStartWith(consumeData.Buffer, QrcodeHead, 3)
                || IsStartWith(consumeData.Buffer, IDCardHead, 3))
                {
                    return terminalLogs;
                }
                //最小长度128+3
                if (consumeData.Buffer.Length < 131)
                {
                    return terminalLogs;
                }

                // 解析Buffer中的终端日志记录
                var parsedLogs = ParseBufferToTerminalLogs(consumeData);
                if (parsedLogs?.Count > 0)
                {
                    terminalLogs.AddRange(parsedLogs);
                }

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "解析消费数据时发生错误。MerchantID: {MerchantID}, MachineID: {MachineID}",
                    consumeData.MerchantID, consumeData.MachineID);
            }

            return terminalLogs;
        }

        /// <summary>
        /// 解析Buffer数据为终端日志记录
        /// </summary>
        /// <param name="buffer">原始数据</param>
        /// <param name="consumeData">消费数据上下文</param>
        /// <returns>终端日志记录列表</returns>
        private List<TerminalLog> ParseBufferToTerminalLogs(ConsumeData consumeData)
        {
            var logs = new List<TerminalLog>();
            byte[] buffer = consumeData.Buffer;
            try
            {
                int offset = 0;
                while (offset < buffer.Length)
                {
                    var log = ParseSingleLogRecord(buffer, ref offset, consumeData);
                    if (log != null)
                    {
                        logs.Add(log);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "解析Buffer时发生错误");
            }

            return logs;
        }

        static List<int> ValidLogTypes = new List<int> { 10, 12, 16, 19, 20 };

        /// <summary>
        /// 解析单条终端日志记录
        /// </summary>
        /// <param name="buffer">数据缓冲区</param>
        /// <param name="offset">当前偏移量（会被更新）</param>
        /// <param name="consumeData">消费数据上下文</param>
        /// <returns>终端日志记录</returns>
        private TerminalLog? ParseSingleLogRecord(byte[] buffer, ref int offset, ConsumeData consumeData)
        {
            try
            {
                /*
                1	0－3	发行卡流水号	4	HEX（M1卡为4字节卡号；CPU卡为卡号后4字节，可不用，直接使用25.交通完整卡号）
                2	4－7	设备号（序列号）	4	BCD
                3	8	卡类	1	HEX
                4	9	交易类型	1	HEX
                5	10－11	钱包累计交易次数	2	HEX（卡交易流水号）
                6	12－15	余额(余次)	4	HEX
                7	16－18	交易金额(交易次数)	3	HEX
                8	19－25	交易时间	7	BCD（YYYYMMDD hhmmss）
                9	26	保留或M1卡司机卡号高位或CPU卡交易类型	1	HEX（高位只有岳阳使用）（测试标识：测试卡刷卡时为99。）
                10	27	保留或卡号高位或子卡类或优惠换乘标识	1	HEX（高位只有岳阳使用）（子卡类武汉一卡通使用）（宜春优惠换乘标识0xA0:优惠换乘，0x00：普通记录）
                11	28－31	司机卡号/员工编号	4	HEX（4字节卡号，低位在前/BCD码）
                12	32－35	设备交易流（PSAM卡交易流水）	4	HEX（M1卡为设备流水号，低位在前；CPU卡为PSAM卡流水号，高位在前。）
                13	36－37	主线路号	2	BCD
                14	38－41	PSAM卡号	6	BCD
                15	44	分线路号	1	BCD
                16	45	预留	1	BCD
                17	46－49	交易验证码（TAC）	4	HEX（只有CPU卡有效）
                18	50－51	商户编号	2	BCD
                19	52－55	机构代码前4字节
                交易流水，取代12字段	4	HEX（司机编号，当前不使用）
                交易的流水，按19字段卡种类分类。
                20	56	卡种类(手机2.4G卡\艺达卡\公交卡M1\CPU卡\交通卡\身份证)	1	HEX
                21	57	保留（分段收费标识:0XB4）	1	HEX（如果是分段收费 固定0xB4）
                22	58－59	固件程序版本号	2	HEX（2字节整形数，低位在前）
                23	60-63	设备自编号	4	BCD（自编设备号，如果后台通过序列号关联的可以不取）
                24	64-71	保留/分段收费
                    8	分段收费：
                64：上车站点 1字节HEX
                65：下车站点 1字节HEX
                66：当前方向/自线路（BCD）
                67-68：全程票价  HEX（低位在前）
                69-70：全程时间  HEX（低位在前）
                71：上下车标识（A0上车、B0下车、C0补扣）/班次
                BCD  64-65（华容，记录卡的商户号）
                ASC  64  （黄骅，记录调度的站点序号）

                BCD  64-67  宜春优惠换乘 上次车号
                25	72-75	发卡机构代码	4	BCD（卡内的机构代码）
                26	76-85	交通完整卡号\
                身份证卡号	10	BCD（交通部CPU卡的10字节完整卡号,住建部或其它的CPU卡则是前8位完整卡号）
                身份证卡号9字节，第18位若为F代表原卡号最后一位的X
                27	86-89	纬度	4	HEX
                28	90-93	经度	4	HEX
                29	94-97	角度\物理卡号	4	HEX  (有需要用到角度的，该字段为角度)
                30	98-98	守护卡渠道标识 	1	1B HEX 
                渠道标识：(0xAB:支付宝渠道，0:公交联盟渠道)
                31	99-99	终端类型	1	0Xa0前门 0x5e后门 7f 一票
                32	100-100	上车时的方向（卡片取）	1	0 正 1 负
                33	101-104	卡片交易总流水	4	HEX
                34	105-121	保留/分段收费	17	分段收费：
                105-111：上车时间 BCD
                112-115：上车车辆编号 BCD
                116-119：上车司机号HEX（4字节卡号，低位在前）
                120-121：上车线路号 BCD

                身份证记录(可选)：
                105-116：记录用户姓名(GBK编码)
                35	122-123	公交联盟机构代码(守护卡)	2	BCD
                36	124-125	票价	2	HEX（低位在前）
                37	126-127	CRC16校验码	2	HEX

001305925493DA690A100000000000000000002025073117271401016D0D3D0001000000104B519000130592332000000000000200000000A0002601888881220000030A000000000000000000000000000000000000000000000000000022979ACC01000000040000202507311630540013059200000000007500000A0001AA

                */

            

                var terminalLog = new TerminalLog();
                var logType = buffer[offset + 3 + 9];
                if (ValidLogTypes.Contains(logType) == false)
                {
                    offset = buffer.Length;
                    return null;
                }
                terminalLog.LogType = logType;
                terminalLog.Price = buffer[offset + 3 + 124] + buffer[offset + 3 + 125] * 256;
                terminalLog.DriverCardNO = DataConvert.bytesToInt(buffer, offset + 3 + 28).ToString();
                terminalLog.LogTime = ParseBcdDateTime(buffer, offset+3+19);
                terminalLog.LineNO = DataConvert.BytesToHex(buffer, offset + 3 + 36, 2)+buffer[offset + 3 + 66].ToString("X2");  
                terminalLog.MachineID=DataConvert.BytesToHex(buffer, offset + 3 + 4, 4);
                terminalLog.MachineNO = DataConvert.GetSlzrBusno(buffer, offset + 3 + 60);
                terminalLog.MerchantID = "0000" + DataConvert.BytesToHex(buffer, offset + 3 + 50, 2);
                terminalLog.SetMethod = buffer[offset + 3 + 56];
                terminalLog.UploadTime = DateTime.Now;
                if (terminalLog.SetMethod == 0x01 || terminalLog.SetMethod == 0x02)
                {
                    terminalLog.CardNO = DataConvert.bytesToInt(buffer, offset + 3 + 76).ToString();
                }
                else
                {
                    terminalLog.CardNO = DataConvert.BytesToHex(buffer, offset + 3 + 76, 10);
                }

                _logger.LogDebug("成功解析单条终端日志记录。LogType: {LogType}, SetMethod: {SetMethod}, CardNO: {CardNO}",
                    terminalLog.LogType, terminalLog.SetMethod, terminalLog.CardNO);

                offset += 128 + 3;

                return terminalLog;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "解析单条日志记录时发生错误。Offset: {Offset}, Buffer: {buffer}", offset, DataConvert.BytesToHex(buffer));
                return null;
            }
        }


        /// <summary>
        /// 解析BCD编码的日期时间（YYYYMMDDHHMMSS格式）
        /// </summary>
        /// <param name="buffer">数据缓冲区</param>
        /// <param name="offset">起始偏移量</param>
        /// <returns>解析后的日期时间</returns>
        private DateTime? ParseBcdDateTime(byte[] buffer, int offset)
        {
            try
            {
                if (offset + 7 > buffer.Length) return null;

                var dateTimeStr = DataConvert.BytesToHex(buffer, offset, 7);
                var year = int.Parse(dateTimeStr.Substring(0, 4));
                var month = int.Parse(dateTimeStr.Substring(4, 2));
                var day = int.Parse(dateTimeStr.Substring(6, 2));
                var hour = int.Parse(dateTimeStr.Substring(8, 2));
                var minute = int.Parse(dateTimeStr.Substring(10, 2));
                var second = int.Parse(dateTimeStr.Substring(12, 2));

                return new DateTime(year, month, day, hour, minute, second);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "解析BCD日期时间失败。Offset: {Offset}", offset);
            }

            return null;
        }



        private bool IsStartWith(byte[] buffer, byte[] head, int offset = 0)
        {
            bool result = true;
            for (int i = 0; i < head.Length; i++)
            {
                if (buffer[offset + i] != head[i])
                {
                    result = false;
                    break;
                }
            }
            return result;
        }
    }
}
