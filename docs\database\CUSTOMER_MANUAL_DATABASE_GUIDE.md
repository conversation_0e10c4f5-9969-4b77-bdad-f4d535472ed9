# 客户项目手动数据库管理指南

## 概述

本指南介绍如何为客户项目手动管理数据库表，不使用EF迁移，简化数据库维护工作。

## 架构优势

### ✅ 简化的数据库管理
- **无迁移冲突**：不使用EF迁移，避免与主项目冲突
- **手动维护**：直接使用SQL脚本管理表结构
- **灵活性高**：可以随时调整表结构
- **维护简单**：适合表数量不多的定制项目

### ✅ 数据访问模式
- **只读认证**：使用只读上下文验证用户身份
- **业务数据**：使用普通上下文操作客户表
- **数据隔离**：强制按商户隔离数据

## 快速开始

### 1. 创建客户项目

```bash
# 创建客户项目
./scripts/create-customer-project.sh customer-a

# 创建客户数据表
./scripts/manage-customer-tables.sh customer-a create
```

### 2. 验证表创建

```bash
# 列出客户表
./scripts/manage-customer-tables.sh customer-a list

# 查看表结构
./scripts/manage-customer-tables.sh customer-a describe Data
```

### 3. 开发客户功能

```csharp
// 控制器示例
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class CustomerDataController : ControllerBase
{
    private readonly CustomerDataService _dataService;
    
    public CustomerDataController(CustomerDataService dataService)
    {
        _dataService = dataService;
    }
    
    [HttpGet]
    public async Task<IActionResult> GetData()
    {
        var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
        var data = await _dataService.GetDataListAsync(userId);
        return Ok(data);
    }
    
    [HttpPost]
    public async Task<IActionResult> SaveData([FromBody] CustomerData data)
    {
        var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
        var success = await _dataService.SaveDataAsync(userId, data);
        return success ? Ok() : BadRequest();
    }
}
```

## 表结构管理

### 默认表结构

客户项目默认创建4个表：

1. **Customer_A_Data** - 通用数据表
2. **Customer_A_Settings** - 配置表
3. **Customer_A_Workflows** - 工作流表
4. **Customer_A_Reports** - 报表表

### 添加新列

```bash
# 向数据表添加新列
./scripts/manage-customer-tables.sh customer-a add-column Data \
    "Priority" "INT DEFAULT 0 COMMENT '优先级'"

# 添加索引
./scripts/manage-customer-tables.sh customer-a add-index Data \
    "idx_priority" "Priority"
```

### 创建自定义表

如果需要新表，可以手动创建：

```sql
-- 创建客户特定功能表
CREATE TABLE Customer_A_CustomFeature (
    Id INT AUTO_INCREMENT PRIMARY KEY,
    MerchantId VARCHAR(8) NOT NULL COMMENT '商户ID',
    FeatureName VARCHAR(100) NOT NULL COMMENT '功能名称',
    FeatureConfig JSON COMMENT '功能配置',
    IsEnabled TINYINT(1) DEFAULT 1 COMMENT '是否启用',
    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_customer_a_feature_merchant (MerchantId),
    INDEX idx_customer_a_feature_enabled (IsEnabled)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客户A特定功能表';
```

然后在代码中添加对应的模型和DbSet：

```csharp
// 添加模型
public class CustomerFeature
{
    public int Id { get; set; }
    public string MerchantId { get; set; } = string.Empty;
    public string FeatureName { get; set; } = string.Empty;
    public string? FeatureConfig { get; set; }
    public bool IsEnabled { get; set; } = true;
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

// 在CustomerDataContext中添加
public DbSet<CustomerFeature> Features { get; set; }

// 在OnModelCreating中配置
modelBuilder.Entity<CustomerFeature>(entity =>
{
    entity.ToTable("Customer_A_CustomFeature");
    entity.HasKey(e => e.Id);
    // 其他配置...
});
```

## 数据备份与恢复

### 备份数据

```bash
# 备份所有客户表
./scripts/manage-customer-tables.sh customer-a backup

# 备份文件保存在 backup/ 目录
ls backup/customer-a-*.sql
```

### 恢复数据

```bash
# 从备份文件恢复
./scripts/manage-customer-tables.sh customer-a restore backup/customer-a-20240101-120000.sql
```

## 开发最佳实践

### 1. 数据模型设计

```csharp
// 所有客户表都应包含这些基础字段
public abstract class BaseCustomerEntity
{
    public int Id { get; set; }
    public string MerchantId { get; set; } = string.Empty; // 必须字段
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

// 继承基础实体
public class CustomerData : BaseCustomerEntity
{
    public string DataType { get; set; } = string.Empty;
    public string? DataValue { get; set; }
    public string? CreatedBy { get; set; }
    public string? UpdatedBy { get; set; }
}
```

### 2. 数据访问安全

```csharp
// 始终验证用户身份和数据所有权
public async Task<List<CustomerData>> GetDataAsync(string userId)
{
    // 1. 验证用户
    var user = await _authService.ValidateUserAsync(userId);
    if (user == null) return new List<CustomerData>();
    
    // 2. 只返回用户商户的数据
    return await _context.CustomerData
        .Where(d => d.MerchantId == user.MerchantID)
        .ToListAsync();
}
```

### 3. 配置管理

```csharp
// 使用配置表存储客户特定设置
await _dataService.SaveSettingAsync(userId, "feature.enabled", "true");
var isEnabled = await _dataService.GetSettingAsync(userId, "feature.enabled");
```

## 部署配置

### Docker配置

```yaml
# docker-compose.customer.yml
customer-a:
  environment:
    # 只读认证连接
    - ConnectionStrings__AuthConnection=Server=mysql;Database=tcpserver;Uid=customer_readonly;Pwd=readonly_password;
    # 客户数据连接
    - ConnectionStrings__CustomerConnection=Server=mysql;Database=tcpserver;Uid=customer_a;Pwd=customer_a_password;
    # 客户表前缀
    - CUSTOMER_TABLE_PREFIX=Customer_A
```

### 数据库用户权限

```sql
-- 客户专用用户
CREATE USER 'customer_a'@'%' IDENTIFIED BY 'customer_a_password';

-- 只授予客户表的权限
GRANT ALL PRIVILEGES ON tcpserver.Customer_A_* TO 'customer_a'@'%';

-- 认证表的只读权限
GRANT SELECT ON tcpserver.Users TO 'customer_a'@'%';
GRANT SELECT ON tcpserver.Merchants TO 'customer_a'@'%';
```

## 故障排除

### 常见问题

1. **表不存在错误**
   ```bash
   # 检查表是否存在
   ./scripts/manage-customer-tables.sh customer-a list
   
   # 重新创建表
   ./scripts/manage-customer-tables.sh customer-a create
   ```

2. **权限错误**
   ```sql
   -- 检查用户权限
   SHOW GRANTS FOR 'customer_a'@'%';
   
   -- 重新授权
   GRANT ALL PRIVILEGES ON tcpserver.Customer_A_* TO 'customer_a'@'%';
   ```

3. **数据访问错误**
   ```csharp
   // 检查用户验证
   var user = await _authService.ValidateUserAsync(userId);
   if (user == null) 
   {
       _logger.LogWarning("用户验证失败: {UserId}", userId);
       return Unauthorized();
   }
   ```

## 总结

手动管理客户数据库表的优势：

- ✅ **简单直接**：不需要复杂的迁移管理
- ✅ **灵活性高**：可以随时调整表结构
- ✅ **无冲突风险**：完全避免与主项目的迁移冲突
- ✅ **维护成本低**：适合表数量不多的定制项目
- ✅ **部署简单**：只需要执行SQL脚本

这种方案特别适合客户定制项目，既满足了数据存储需求，又保持了简单性和可维护性。
