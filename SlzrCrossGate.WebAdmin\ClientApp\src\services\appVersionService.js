import api from './api';

/**
 * 应用版本服务
 * 独立于会话管理的版本检测服务，确保在任何情况下都能检测版本变化
 */
class AppVersionService {
  constructor() {
    this.currentVersion = null;
    this.currentBuildTime = null;
    this.isInitialized = false;
    this.checkTimer = null;
    this.checkInterval = 60000; // 60秒检查一次
    this.callbacks = {
      onVersionUpdate: null // 只保留版本更新回调，用于UI显示
    };

    // localStorage键名
    this.VERSION_STORAGE_KEY = 'app_version_info';
    this.LAST_CHECK_KEY = 'app_version_last_check';
  }

  /**
   * 从localStorage加载版本信息
   */
  loadVersionFromStorage() {
    try {
      const stored = localStorage.getItem(this.VERSION_STORAGE_KEY);
      if (stored) {
        const versionInfo = JSON.parse(stored);
        this.currentVersion = versionInfo.version;
        this.currentBuildTime = versionInfo.buildTime;
        console.log('从localStorage加载版本信息:', versionInfo);
        return versionInfo;
      }
    } catch (error) {
      console.warn('加载版本信息失败:', error);
    }
    return null;
  }

  /**
   * 保存版本信息到localStorage
   */
  saveVersionToStorage(version, buildTime, buildTimeString) {
    try {
      const versionInfo = {
        version,
        buildTime,
        buildTimeString,
        timestamp: Date.now()
      };
      localStorage.setItem(this.VERSION_STORAGE_KEY, JSON.stringify(versionInfo));
      localStorage.setItem(this.LAST_CHECK_KEY, Date.now().toString());
      console.log('版本信息已保存到localStorage:', versionInfo);
    } catch (error) {
      console.warn('保存版本信息失败:', error);
    }
  }

  /**
   * 初始化应用版本服务（简化版）
   */
  async initialize() {
    if (this.isInitialized) return;

    try {
      // 获取当前版本信息并保存
      await this.loadCurrentVersion();
      this.isInitialized = true;

      console.log('应用版本服务已初始化', {
        version: this.currentVersion,
        buildTime: this.currentBuildTime
      });
    } catch (error) {
      console.error('初始化应用版本服务失败:', error);
    }
  }

  /**
   * 使用已有版本信息初始化服务（避免重复请求）
   */
  async initializeWithVersion(versionInfo) {
    if (this.isInitialized) return;

    try {
      // 直接使用传入的版本信息
      this.currentVersion = versionInfo.version;
      this.currentBuildTime = versionInfo.buildTime;
      this.isInitialized = true;

      // 触发版本更新回调（用于UI显示）
      if (this.callbacks.onVersionUpdate) {
        this.callbacks.onVersionUpdate({
          version: versionInfo.version,
          buildTime: versionInfo.buildTime,
          buildTimeString: versionInfo.buildTimeString
        });
      }

      console.log('应用版本服务已初始化（使用已有版本信息）', {
        version: this.currentVersion,
        buildTime: this.currentBuildTime
      });
    } catch (error) {
      console.error('应用版本服务初始化失败:', error);
    }
  }

  /**
   * 加载当前版本信息（不检测变化）
   */
  async loadCurrentVersion() {
    try {
      const versionData = await this.getVersionDirect();
      if (versionData) {
        this.currentVersion = versionData.version;
        this.currentBuildTime = versionData.buildTime;

        // 保存到localStorage
        this.saveVersionToStorage(versionData.version, versionData.buildTime, versionData.buildTimeString);

        // 触发版本更新回调（用于UI显示）
        if (this.callbacks.onVersionUpdate) {
          this.callbacks.onVersionUpdate({
            version: versionData.version,
            buildTime: versionData.buildTime,
            buildTimeString: versionData.buildTimeString
          });
        }

        console.log('获取到应用版本:', versionData.version, '构建时间:', versionData.buildTimeString);
      }
    } catch (error) {
      console.error('加载版本信息失败:', error);
    }
  }

  /**
   * 设置回调函数
   */
  setCallbacks({ onVersionUpdate }) {
    this.callbacks.onVersionUpdate = onVersionUpdate;
  }





  /**
   * 直接获取版本信息（不需要认证）
   */
  async getVersionDirect() {
    try {
      // 调用不需要认证的版本API
      const response = await fetch('/api/Version', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        return {
          version: data.version,
          buildTime: data.buildTime,
          buildTimeString: data.buildTimeString
        };
      }
    } catch (error) {
      console.warn('直接获取版本信息失败:', error);
    }
    return null;
  }



  /**
   * 获取当前版本信息
   */
  getCurrentVersion() {
    return {
      version: this.currentVersion,
      buildTime: this.currentBuildTime
    };
  }



  /**
   * 刷新页面，强制清除所有缓存
   */
  refreshPage() {
    console.log('正在刷新页面以使用新版本...');

    // 方法1：添加时间戳参数强制刷新，确保获取最新资源
    const timestamp = new Date().getTime();
    const currentUrl = window.location.href.split('?')[0].split('#')[0];
    const newUrl = `${currentUrl}?_v=${timestamp}${window.location.hash}`;

    console.log('强制刷新URL:', newUrl);
    window.location.href = newUrl;
  }

  /**
   * 停止定期检查（兼容方法）
   */
  stopPeriodicCheck() {
    // 简化版本中不再有定期检查，保留此方法以兼容现有代码
    console.log('stopPeriodicCheck called (no-op in simplified version)');
  }

  /**
   * 清理服务
   */
  cleanup() {
    this.stopPeriodicCheck();
    this.currentVersion = null;
    this.currentBuildTime = null;
    this.isInitialized = false;
    this.callbacks = {
      onVersionUpdate: null
    };
  }
}

// 创建单例实例
const appVersionService = new AppVersionService();

export default appVersionService;
