-- 添加外部链接支持字段到MenuItems表
-- 这个脚本用于修复缺失的IsExternal和Target字段

USE tcpserver;

-- 检查字段是否已存在
SET @column_exists_isexternal = (
    SELECT COUNT(*) 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = 'tcpserver' 
    AND TABLE_NAME = 'MenuItems' 
    AND COLUMN_NAME = 'IsExternal'
);

SET @column_exists_target = (
    SELECT COUNT(*) 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = 'tcpserver' 
    AND TABLE_NAME = 'MenuItems' 
    AND COLUMN_NAME = 'Target'
);

-- 添加IsExternal字段（如果不存在）
SET @sql_isexternal = IF(@column_exists_isexternal = 0, 
    'ALTER TABLE MenuItems ADD COLUMN IsExternal TINYINT(1) NOT NULL DEFAULT 0 COMMENT ''是否为外部链接''',
    'SELECT ''IsExternal字段已存在'' as message'
);

PREPARE stmt FROM @sql_isexternal;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加Target字段（如果不存在）
SET @sql_target = IF(@column_exists_target = 0, 
    'ALTER TABLE MenuItems ADD COLUMN Target VARCHAR(20) NOT NULL DEFAULT ''_self'' COMMENT ''链接打开方式''',
    'SELECT ''Target字段已存在'' as message'
);

PREPARE stmt FROM @sql_target;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 显示表结构确认
DESCRIBE MenuItems;

-- 显示添加结果
SELECT 
    CASE 
        WHEN @column_exists_isexternal = 0 THEN '✅ IsExternal字段已添加'
        ELSE '⚠️ IsExternal字段已存在'
    END as IsExternal_Status,
    CASE 
        WHEN @column_exists_target = 0 THEN '✅ Target字段已添加'
        ELSE '⚠️ Target字段已存在'
    END as Target_Status;
