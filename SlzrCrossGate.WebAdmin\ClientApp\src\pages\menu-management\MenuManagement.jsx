import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Card,
  CardContent,
  <PERSON>pography,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Switch,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControlLabel,
  Checkbox,
  Grid,
  Alert,
  CircularProgress,
  Tooltip,
  Tabs,
  Tab,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material';
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  Add as AddIcon,
  Refresh as RefreshIcon,
  Settings as SettingsIcon,
  ExpandMore as ExpandMoreIcon,
  List as ListIcon
} from '@mui/icons-material';
import { useSnackbar } from 'notistack';
import { menuAPI } from '../../services/api';
import { FeatureGuard } from '../../components/FeatureGuard';
import { PERMISSIONS } from '../../constants/permissions';

const MenuManagement = () => {
  const { enqueueSnackbar } = useSnackbar();
  const [menuGroups, setMenuGroups] = useState([]);
  const [loading, setLoading] = useState(true);
  const [currentTab, setCurrentTab] = useState(0);
  const [editGroupDialog, setEditGroupDialog] = useState({ open: false, group: null, isEdit: false });
  const [editItemDialog, setEditItemDialog] = useState({ open: false, item: null, isEdit: false });

  // 防重复请求
  const hasLoadedRef = useRef(false);

  // 加载菜单数据
  const loadMenus = async () => {
    try {
      setLoading(true);
      const response = await menuAPI.getAllMenus();
      setMenuGroups(response || []);
    } catch (error) {
      console.error('加载菜单失败:', error);
      enqueueSnackbar('加载菜单失败', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };

  // 初始化默认菜单
  const initializeMenus = async () => {
    try {
      setLoading(true);
      await menuAPI.initializeMenus();
      enqueueSnackbar('菜单初始化成功', { variant: 'success' });
      await loadMenus();
    } catch (error) {
      console.error('初始化菜单失败:', error);
      enqueueSnackbar('初始化菜单失败', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };

  // 组件挂载时加载数据
  useEffect(() => {
    // 防止重复加载
    if (hasLoadedRef.current) {
      console.log('MenuManagement: 已加载过，跳过重复请求');
      return;
    }

    console.log('MenuManagement: 执行首次加载');
    hasLoadedRef.current = true;
    loadMenus();
  }, []);

  // 菜单分组相关处理函数
  const handleEditGroup = (group) => {
    setEditGroupDialog({ open: true, group, isEdit: true });
  };

  const handleAddGroup = () => {
    setEditGroupDialog({
      open: true,
      group: {
        groupKey: '',
        title: '',
        iconName: '',
        sortOrder: menuGroups.length + 1,
        isEnabled: true,
        visibleToSystemAdmin: true,
        visibleToMerchantAdmin: true,
        visibleToUser: false
      },
      isEdit: false
    });
  };

  const handleCloseGroupDialog = () => {
    setEditGroupDialog({ open: false, group: null, isEdit: false });
  };

  const handleSaveGroup = async (groupData) => {
    try {
      if (editGroupDialog.isEdit) {
        await menuAPI.updateMenuGroup(editGroupDialog.group.id, groupData);
        enqueueSnackbar('菜单分组更新成功', { variant: 'success' });
      } else {
        await menuAPI.createMenuGroup(groupData);
        enqueueSnackbar('菜单分组创建成功', { variant: 'success' });
      }
      handleCloseGroupDialog();
      await loadMenus();
    } catch (error) {
      console.error('保存菜单分组失败:', error);
      enqueueSnackbar('保存菜单分组失败', { variant: 'error' });
    }
  };

  const handleDeleteGroup = async (groupId) => {
    if (!window.confirm('确定要删除这个菜单分组吗？这将同时删除该分组下的所有菜单项。')) {
      return;
    }

    try {
      await menuAPI.deleteMenuGroup(groupId);
      enqueueSnackbar('菜单分组删除成功', { variant: 'success' });
      await loadMenus();
    } catch (error) {
      console.error('删除菜单分组失败:', error);
      enqueueSnackbar('删除菜单分组失败', { variant: 'error' });
    }
  };

  // 菜单项相关处理函数
  const handleEditItem = (item) => {
    setEditItemDialog({ open: true, item, isEdit: true });
  };

  const handleAddItem = (groupId) => {
    const group = menuGroups.find(g => g.id === groupId);
    const maxSortOrder = Math.max(0, ...(group?.menuItems?.map(item => item.sortOrder) || []));

    setEditItemDialog({
      open: true,
      item: {
        menuGroupId: groupId,
        itemKey: '',
        title: '',
        href: '',
        iconName: '',
        sortOrder: maxSortOrder + 1,
        isEnabled: true,
        visibleToSystemAdmin: true,
        visibleToMerchantAdmin: true,
        visibleToUser: false
      },
      isEdit: false
    });
  };

  const handleCloseItemDialog = () => {
    setEditItemDialog({ open: false, item: null, isEdit: false });
  };

  const handleSaveItem = async (itemData) => {
    try {
      if (editItemDialog.isEdit) {
        await menuAPI.updateMenuItem(editItemDialog.item.id, itemData);
        enqueueSnackbar('菜单项更新成功', { variant: 'success' });
      } else {
        await menuAPI.createMenuItem(itemData);
        enqueueSnackbar('菜单项创建成功', { variant: 'success' });
      }
      handleCloseItemDialog();
      await loadMenus();
    } catch (error) {
      console.error('保存菜单项失败:', error);
      enqueueSnackbar('保存菜单项失败', { variant: 'error' });
    }
  };

  const handleDeleteItem = async (itemId) => {
    if (!window.confirm('确定要删除这个菜单项吗？')) {
      return;
    }

    try {
      await menuAPI.deleteMenuItem(itemId);
      enqueueSnackbar('菜单项删除成功', { variant: 'success' });
      await loadMenus();
    } catch (error) {
      console.error('删除菜单项失败:', error);
      enqueueSnackbar('删除菜单项失败', { variant: 'error' });
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          菜单管理
        </Typography>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={loadMenus}
            disabled={loading}
          >
            刷新
          </Button>
          <FeatureGuard featureKey={PERMISSIONS.MENU_MANAGEMENT.INIT_DEFAULT_MENUS}>
            <Button
              variant="outlined"
              startIcon={<SettingsIcon />}
              onClick={initializeMenus}
              disabled={loading}
            >
              初始化默认菜单
            </Button>
          </FeatureGuard>
          {currentTab === 0 && (
            <FeatureGuard featureKey={PERMISSIONS.MENU_MANAGEMENT.CREATE_GROUP}>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={handleAddGroup}
                disabled={loading}
              >
                新增分组
              </Button>
            </FeatureGuard>
          )}
        </Box>
      </Box>

      <Alert severity="info" sx={{ mb: 3 }}>
        菜单管理功能允许您自定义系统菜单结构。修改后的菜单将立即生效，影响所有用户的导航体验。
        请谨慎操作，建议在修改前备份当前菜单配置。
      </Alert>

      <Card>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={currentTab} onChange={(e, newValue) => setCurrentTab(newValue)}>
            <Tab label="菜单分组管理" />
            <Tab label="菜单项管理" />
          </Tabs>
        </Box>

        <CardContent>
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
              <CircularProgress />
            </Box>
          ) : (
            <>
              {currentTab === 0 && <MenuGroupsTab
                menuGroups={menuGroups}
                onEdit={handleEditGroup}
                onDelete={handleDeleteGroup}
              />}
              {currentTab === 1 && <MenuItemsTab
                menuGroups={menuGroups}
                onEditItem={handleEditItem}
                onDeleteItem={handleDeleteItem}
                onAddItem={handleAddItem}
              />}
            </>
          )}
        </CardContent>
      </Card>

      {/* 菜单分组编辑对话框 */}
      <MenuGroupDialog
        open={editGroupDialog.open}
        group={editGroupDialog.group}
        isEdit={editGroupDialog.isEdit}
        onClose={handleCloseGroupDialog}
        onSave={handleSaveGroup}
      />

      {/* 菜单项编辑对话框 */}
      <MenuItemDialog
        open={editItemDialog.open}
        item={editItemDialog.item}
        isEdit={editItemDialog.isEdit}
        menuGroups={menuGroups}
        onClose={handleCloseItemDialog}
        onSave={handleSaveItem}
      />
    </Box>
  );
};

// 菜单分组标签页组件
const MenuGroupsTab = ({ menuGroups, onEdit, onDelete }) => {
  return (
    <TableContainer component={Paper}>
      <Table>
        <TableHead>
          <TableRow>
            <TableCell>分组标识</TableCell>
            <TableCell>分组名称</TableCell>
            <TableCell>图标</TableCell>
            <TableCell>排序</TableCell>
            <TableCell>状态</TableCell>
            <TableCell>系统管理员可见</TableCell>
            <TableCell>商户管理员可见</TableCell>
            <TableCell>普通用户可见</TableCell>
            <TableCell>菜单项数量</TableCell>
            <TableCell align="right">操作</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {menuGroups.map((group) => (
            <TableRow key={group.id} hover>
              <TableCell>
                <Typography variant="body2" fontFamily="monospace">
                  {group.groupKey}
                </Typography>
              </TableCell>
              <TableCell>{group.title}</TableCell>
              <TableCell>
                <Typography variant="body2" color="textSecondary">
                  {group.iconName || '-'}
                </Typography>
              </TableCell>
              <TableCell>{group.sortOrder}</TableCell>
              <TableCell>
                <Chip
                  label={group.isEnabled ? '启用' : '禁用'}
                  color={group.isEnabled ? 'success' : 'default'}
                  size="small"
                />
              </TableCell>
              <TableCell>
                <Chip
                  label={group.visibleToSystemAdmin ? '是' : '否'}
                  color={group.visibleToSystemAdmin ? 'primary' : 'default'}
                  size="small"
                />
              </TableCell>
              <TableCell>
                <Chip
                  label={group.visibleToMerchantAdmin ? '是' : '否'}
                  color={group.visibleToMerchantAdmin ? 'primary' : 'default'}
                  size="small"
                />
              </TableCell>
              <TableCell>
                <Chip
                  label={group.visibleToUser ? '是' : '否'}
                  color={group.visibleToUser ? 'primary' : 'default'}
                  size="small"
                />
              </TableCell>
              <TableCell>
                <Typography variant="body2">
                  {group.menuItems?.length || 0} 项
                </Typography>
              </TableCell>
              <TableCell align="right">
                <FeatureGuard featureKey={PERMISSIONS.MENU_MANAGEMENT.EDIT_GROUP}>
                  <Tooltip title="编辑">
                    <IconButton
                      size="small"
                      onClick={() => onEdit(group)}
                      color="primary"
                    >
                      <EditIcon />
                    </IconButton>
                  </Tooltip>
                </FeatureGuard>
                <FeatureGuard featureKey={PERMISSIONS.MENU_MANAGEMENT.DELETE_GROUP}>
                  <Tooltip title="删除">
                    <IconButton
                      size="small"
                      onClick={() => onDelete(group.id)}
                      color="error"
                    >
                      <DeleteIcon />
                    </IconButton>
                  </Tooltip>
                </FeatureGuard>
              </TableCell>
            </TableRow>
          ))}
          {menuGroups.length === 0 && (
            <TableRow>
              <TableCell colSpan={10} align="center">
                <Typography color="textSecondary">
                  暂无菜单数据，请点击"初始化默认菜单"创建默认菜单结构
                </Typography>
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

// 菜单项标签页组件
const MenuItemsTab = ({ menuGroups, onEditItem, onDeleteItem, onAddItem }) => {
  return (
    <Box>
      {menuGroups.map((group) => (
        <Accordion key={group.id} defaultExpanded={false}>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, width: '100%' }}>
              <Typography variant="h6">{group.title}</Typography>
              <Chip
                label={`${group.menuItems?.length || 0} 项`}
                size="small"
                color="primary"
              />
              <Box sx={{ flexGrow: 1 }} />
              <FeatureGuard featureKey={PERMISSIONS.MENU_MANAGEMENT.CREATE_MENU_ITEM}>
                <Button
                  size="small"
                  startIcon={<AddIcon />}
                  onClick={(e) => {
                    e.stopPropagation();
                    onAddItem(group.id);
                  }}
                  variant="outlined"
                >
                  添加菜单项
                </Button>
              </FeatureGuard>
            </Box>
          </AccordionSummary>
          <AccordionDetails>
            {group.menuItems && group.menuItems.length > 0 ? (
              <TableContainer component={Paper} variant="outlined">
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>菜单标识</TableCell>
                      <TableCell>菜单名称</TableCell>
                      <TableCell>路由路径</TableCell>
                      <TableCell>图标</TableCell>
                      <TableCell>排序</TableCell>
                      <TableCell>状态</TableCell>
                      <TableCell>外部链接</TableCell>
                      <TableCell>打开方式</TableCell>
                      <TableCell>系统管理员</TableCell>
                      <TableCell>商户管理员</TableCell>
                      <TableCell>普通用户</TableCell>
                      <TableCell align="right">操作</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {group.menuItems
                      .sort((a, b) => a.sortOrder - b.sortOrder)
                      .map((item) => (
                        <TableRow key={item.id} hover>
                          <TableCell>
                            <Typography variant="body2" fontFamily="monospace">
                              {item.itemKey}
                            </Typography>
                          </TableCell>
                          <TableCell>{item.title}</TableCell>
                          <TableCell>
                            <Typography variant="body2" fontFamily="monospace">
                              {item.href}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2" color="textSecondary">
                              {item.iconName || '-'}
                            </Typography>
                          </TableCell>
                          <TableCell>{item.sortOrder}</TableCell>
                          <TableCell>
                            <Chip
                              label={item.isEnabled ? '启用' : '禁用'}
                              color={item.isEnabled ? 'success' : 'default'}
                              size="small"
                            />
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={item.isExternal ? '是' : '否'}
                              color={item.isExternal ? 'warning' : 'default'}
                              size="small"
                            />
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2" color="textSecondary">
                              {item.isExternal ? (
                                item.target === '_iframe' ? '内嵌框架' :
                                item.target === '_blank' ? '新窗口' : '当前窗口'
                              ) : '-'}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={item.visibleToSystemAdmin ? '是' : '否'}
                              color={item.visibleToSystemAdmin ? 'success' : 'default'}
                              size="small"
                            />
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={item.visibleToMerchantAdmin ? '是' : '否'}
                              color={item.visibleToMerchantAdmin ? 'success' : 'default'}
                              size="small"
                            />
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={item.visibleToUser ? '是' : '否'}
                              color={item.visibleToUser ? 'success' : 'default'}
                              size="small"
                            />
                          </TableCell>
                          <TableCell align="right">
                            <FeatureGuard featureKey={PERMISSIONS.MENU_MANAGEMENT.EDIT_MENU_ITEM}>
                              <Tooltip title="编辑">
                                <IconButton
                                  size="small"
                                  onClick={() => onEditItem(item)}
                                  color="primary"
                                >
                                  <EditIcon />
                                </IconButton>
                              </Tooltip>
                            </FeatureGuard>
                            <FeatureGuard featureKey={PERMISSIONS.MENU_MANAGEMENT.DELETE_MENU_ITEM}>
                              <Tooltip title="删除">
                                <IconButton
                                  size="small"
                                  onClick={() => onDeleteItem(item.id)}
                                  color="error"
                                >
                                  <DeleteIcon />
                                </IconButton>
                              </Tooltip>
                            </FeatureGuard>
                          </TableCell>
                        </TableRow>
                      ))}
                  </TableBody>
                </Table>
              </TableContainer>
            ) : (
              <Box sx={{ textAlign: 'center', py: 3 }}>
                <Typography color="textSecondary" gutterBottom>
                  该分组下暂无菜单项
                </Typography>
                <FeatureGuard featureKey={PERMISSIONS.MENU_MANAGEMENT.CREATE_MENU_ITEM}>
                  <Button
                    startIcon={<AddIcon />}
                    onClick={() => onAddItem(group.id)}
                    variant="outlined"
                  >
                    添加第一个菜单项
                  </Button>
                </FeatureGuard>
              </Box>
            )}
          </AccordionDetails>
        </Accordion>
      ))}
      {menuGroups.length === 0 && (
        <Box sx={{ textAlign: 'center', py: 4 }}>
          <Typography color="textSecondary">
            暂无菜单分组，请先创建菜单分组
          </Typography>
        </Box>
      )}
    </Box>
  );
};

// 菜单分组编辑对话框组件
const MenuGroupDialog = ({ open, group, isEdit, onClose, onSave }) => {
  const [formData, setFormData] = useState({
    groupKey: '',
    title: '',
    iconName: '',
    sortOrder: 1,
    isEnabled: true,
    visibleToSystemAdmin: true,
    visibleToMerchantAdmin: true,
    visibleToUser: false
  });

  useEffect(() => {
    if (group) {
      setFormData({
        groupKey: group.groupKey || '',
        title: group.title || '',
        iconName: group.iconName || '',
        sortOrder: group.sortOrder || 1,
        isEnabled: group.isEnabled !== undefined ? group.isEnabled : true,
        visibleToSystemAdmin: group.visibleToSystemAdmin !== undefined ? group.visibleToSystemAdmin : true,
        visibleToMerchantAdmin: group.visibleToMerchantAdmin !== undefined ? group.visibleToMerchantAdmin : true,
        visibleToUser: group.visibleToUser !== undefined ? group.visibleToUser : false
      });
    }
  }, [group]);

  const handleChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = () => {
    if (!formData.title.trim()) {
      alert('请输入分组名称');
      return;
    }
    if (!isEdit && !formData.groupKey.trim()) {
      alert('请输入分组标识');
      return;
    }
    onSave(formData);
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        {isEdit ? '编辑菜单分组' : '新增菜单分组'}
      </DialogTitle>
      <DialogContent>
        <Grid container spacing={2} sx={{ mt: 1 }}>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="分组标识"
              value={formData.groupKey}
              onChange={(e) => handleChange('groupKey', e.target.value)}
              disabled={isEdit}
              helperText={isEdit ? '分组标识创建后不可修改' : '用于前端识别的唯一标识符'}
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="分组名称"
              value={formData.title}
              onChange={(e) => handleChange('title', e.target.value)}
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="图标名称"
              value={formData.iconName}
              onChange={(e) => handleChange('iconName', e.target.value)}
              helperText="对应React Feather图标组件名称"
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              type="number"
              label="排序顺序"
              value={formData.sortOrder}
              onChange={(e) => handleChange('sortOrder', parseInt(e.target.value) || 1)}
            />
          </Grid>
          <Grid item xs={12}>
            <FormControlLabel
              control={
                <Checkbox
                  checked={formData.isEnabled}
                  onChange={(e) => handleChange('isEnabled', e.target.checked)}
                />
              }
              label="启用此分组"
            />
          </Grid>
          <Grid item xs={12}>
            <Typography variant="subtitle2" gutterBottom>
              可见性设置
            </Typography>
            <FormControlLabel
              control={
                <Checkbox
                  checked={formData.visibleToSystemAdmin}
                  onChange={(e) => handleChange('visibleToSystemAdmin', e.target.checked)}
                />
              }
              label="系统管理员可见"
            />
            <FormControlLabel
              control={
                <Checkbox
                  checked={formData.visibleToMerchantAdmin}
                  onChange={(e) => handleChange('visibleToMerchantAdmin', e.target.checked)}
                />
              }
              label="商户管理员可见"
            />
            <FormControlLabel
              control={
                <Checkbox
                  checked={formData.visibleToUser}
                  onChange={(e) => handleChange('visibleToUser', e.target.checked)}
                />
              }
              label="普通用户可见"
            />
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>取消</Button>
        <Button onClick={handleSubmit} variant="contained">
          {isEdit ? '更新' : '创建'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

// 菜单项编辑对话框组件
const MenuItemDialog = ({ open, item, isEdit, menuGroups, onClose, onSave }) => {
  const [formData, setFormData] = useState({
    menuGroupId: 0,
    itemKey: '',
    title: '',
    href: '',
    iconName: '',
    sortOrder: 1,
    isEnabled: true,
    visibleToSystemAdmin: true,
    visibleToMerchantAdmin: true,
    visibleToUser: false,
    isExternal: false,
    target: '_self'
  });

  useEffect(() => {
    if (item) {
      setFormData({
        menuGroupId: item.menuGroupId || 0,
        itemKey: item.itemKey || '',
        title: item.title || '',
        href: item.href || '',
        iconName: item.iconName || '',
        sortOrder: item.sortOrder || 1,
        isEnabled: item.isEnabled !== undefined ? item.isEnabled : true,
        visibleToSystemAdmin: item.visibleToSystemAdmin !== undefined ? item.visibleToSystemAdmin : true,
        visibleToMerchantAdmin: item.visibleToMerchantAdmin !== undefined ? item.visibleToMerchantAdmin : true,
        visibleToUser: item.visibleToUser !== undefined ? item.visibleToUser : false,
        isExternal: item.isExternal !== undefined ? item.isExternal : false,
        target: item.target || '_self'
      });
    } else if (!open) {
      // 对话框关闭时重置表单状态
      setFormData({
        menuGroupId: 0,
        itemKey: '',
        title: '',
        href: '',
        iconName: '',
        sortOrder: 1,
        isEnabled: true,
        visibleToSystemAdmin: true,
        visibleToMerchantAdmin: true,
        visibleToUser: false,
        isExternal: false,
        target: '_self'
      });
    }
  }, [item, open]);

  const handleChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = () => {
    if (!formData.title.trim()) {
      alert('请输入菜单名称');
      return;
    }
    if (!isEdit && !formData.itemKey.trim()) {
      alert('请输入菜单标识');
      return;
    }
    if (!formData.href.trim()) {
      alert('请输入路由路径');
      return;
    }
    if (!formData.menuGroupId) {
      alert('请选择菜单分组');
      return;
    }
    onSave(formData);
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        {isEdit ? '编辑菜单项' : '新增菜单项'}
      </DialogTitle>
      <DialogContent>
        <Grid container spacing={2} sx={{ mt: 1 }}>
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <InputLabel>菜单分组</InputLabel>
              <Select
                value={formData.menuGroupId}
                onChange={(e) => handleChange('menuGroupId', e.target.value)}
                label="菜单分组"
                disabled={isEdit}
              >
                {menuGroups.map((group) => (
                  <MenuItem key={group.id} value={group.id}>
                    {group.title}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="菜单标识"
              value={formData.itemKey}
              onChange={(e) => handleChange('itemKey', e.target.value)}
              disabled={isEdit}
              helperText={isEdit ? '菜单标识创建后不可修改' : '用于前端识别的唯一标识符'}
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="菜单名称"
              value={formData.title}
              onChange={(e) => handleChange('title', e.target.value)}
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="路由路径"
              value={formData.href}
              onChange={(e) => handleChange('href', e.target.value)}
              helperText="例如：/app/dashboard"
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="图标名称"
              value={formData.iconName}
              onChange={(e) => handleChange('iconName', e.target.value)}
              helperText="对应React Feather图标组件名称"
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              type="number"
              label="排序顺序"
              value={formData.sortOrder}
              onChange={(e) => handleChange('sortOrder', parseInt(e.target.value) || 1)}
            />
          </Grid>
          <Grid item xs={12}>
            <FormControlLabel
              control={
                <Checkbox
                  checked={formData.isEnabled}
                  onChange={(e) => handleChange('isEnabled', e.target.checked)}
                />
              }
              label="启用此菜单项"
            />
          </Grid>

          {/* 外部链接配置 */}
          <Grid item xs={12}>
            <Typography variant="subtitle2" gutterBottom>
              链接类型设置
            </Typography>
            <FormControlLabel
              control={
                <Checkbox
                  checked={formData.isExternal}
                  onChange={(e) => handleChange('isExternal', e.target.checked)}
                />
              }
              label="外部链接"
            />
          </Grid>

          {formData.isExternal && (
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>打开方式</InputLabel>
                <Select
                  value={formData.target}
                  onChange={(e) => handleChange('target', e.target.value)}
                  label="打开方式"
                >
                  <MenuItem value="_self">当前窗口</MenuItem>
                  <MenuItem value="_blank">新窗口</MenuItem>
                  <MenuItem value="_iframe">内嵌框架</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          )}

          <Grid item xs={12}>
            <Typography variant="subtitle2" gutterBottom>
              可见性设置
            </Typography>
            <FormControlLabel
              control={
                <Checkbox
                  checked={formData.visibleToSystemAdmin}
                  onChange={(e) => handleChange('visibleToSystemAdmin', e.target.checked)}
                />
              }
              label="系统管理员可见"
            />
            <FormControlLabel
              control={
                <Checkbox
                  checked={formData.visibleToMerchantAdmin}
                  onChange={(e) => handleChange('visibleToMerchantAdmin', e.target.checked)}
                />
              }
              label="商户管理员可见"
            />
            <FormControlLabel
              control={
                <Checkbox
                  checked={formData.visibleToUser}
                  onChange={(e) => handleChange('visibleToUser', e.target.checked)}
                />
              }
              label="普通用户可见"
            />
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>取消</Button>
        <Button onClick={handleSubmit} variant="contained">
          {isEdit ? '更新' : '创建'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default MenuManagement;
