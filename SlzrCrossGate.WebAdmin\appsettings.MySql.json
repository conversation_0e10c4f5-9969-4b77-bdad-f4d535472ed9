{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Server=localhost;Database=TcpserverTms;User=root;Password=******;Port=3306;CharSet=utf8mb4;"}, "DatabaseProvider": "MySql", "RabbitMQ": {"HostName": "localhost", "UserName": "guest", "Password": "guest", "Port": 5672}, "FileService": {"DefaultStorageType": "Local", "LocalFilePath": "uploads", "MinIO": {"Endpoint": "localhost:9000", "AccessKey": "minioadmin", "SecretKey": "minioadmin", "BucketName": "slzr-crossgate"}}, "Jwt": {"Key": "your-secret-key-here-must-be-at-least-32-characters-long", "Issuer": "SlzrCrossGate", "Audience": "SlzrCrossGate", "ExpiryInMinutes": 60}}