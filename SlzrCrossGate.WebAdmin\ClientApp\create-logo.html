<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>创建 Logo</title>
  <style>
    body {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100vh;
      margin: 0;
      background-color: #f5f5f5;
      font-family: Arial, sans-serif;
    }
    .container {
      text-align: center;
    }
    canvas {
      border: 1px solid #ddd;
      margin: 20px 0;
      background-color: white;
    }
    button {
      padding: 10px 20px;
      background-color: #7E22CE;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
    }
    button:hover {
      background-color: #6B21A8;
    }
    p {
      margin: 20px 0;
      color: #555;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>终端管理系统 Logo 生成器</h1>
    <canvas id="logoCanvas" width="192" height="192"></canvas>
    <button id="downloadBtn">下载 logo192.png</button>
    <p>点击下载按钮后，将图片保存到 public 文件夹中</p>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const canvas = document.getElementById('logoCanvas');
      const ctx = canvas.getContext('2d');
      const downloadBtn = document.getElementById('downloadBtn');
      
      // 绘制 logo
      function drawLogo() {
        // 清除画布
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        // 创建渐变背景
        const gradient = ctx.createLinearGradient(0, 0, 192, 192);
        gradient.addColorStop(0, '#7E22CE');
        gradient.addColorStop(1, '#4338CA');
        
        // 绘制圆形背景
        ctx.beginPath();
        ctx.arc(96, 96, 88, 0, Math.PI * 2);
        ctx.fillStyle = gradient;
        ctx.fill();
        
        // 绘制显示器外框
        ctx.strokeStyle = 'white';
        ctx.lineWidth = 6;
        ctx.beginPath();
        ctx.roundRect(40, 50, 112, 80, 6);
        ctx.stroke();
        
        // 绘制显示器底座
        ctx.fillStyle = 'white';
        ctx.beginPath();
        ctx.moveTo(86, 130);
        ctx.lineTo(106, 130);
        ctx.lineTo(110, 142);
        ctx.lineTo(82, 142);
        ctx.closePath();
        ctx.fill();
        
        // 绘制终端符号
        ctx.strokeStyle = 'white';
        ctx.lineWidth = 6;
        ctx.lineCap = 'round';
        ctx.lineJoin = 'round';
        
        // 绘制 > 符号
        ctx.beginPath();
        ctx.moveTo(60, 70);
        ctx.lineTo(80, 90);
        ctx.lineTo(60, 110);
        ctx.stroke();
        
        // 绘制 _ 符号
        ctx.beginPath();
        ctx.moveTo(90, 110);
        ctx.lineTo(120, 110);
        ctx.stroke();
      }
      
      // 初始绘制
      drawLogo();
      
      // 下载功能
      downloadBtn.addEventListener('click', function() {
        const link = document.createElement('a');
        link.download = 'logo192.png';
        link.href = canvas.toDataURL('image/png');
        link.click();
      });
    });
  </script>
</body>
</html>
