{"name": "slzr-customer-template-frontend", "version": "1.0.0", "private": true, "dependencies": {"@mui/material": "^5.15.0", "@mui/icons-material": "^5.15.0", "@emotion/react": "^11.11.0", "@emotion/styled": "^11.11.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.0", "axios": "^1.6.0", "notistack": "^3.0.0", "date-fns": "^2.30.0"}, "devDependencies": {"@vitejs/plugin-react": "^4.2.0", "vite": "^5.0.0", "eslint": "^8.55.0", "eslint-plugin-react": "^7.33.0", "eslint-plugin-react-hooks": "^4.6.0"}, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "start": "vite --port 3001"}}