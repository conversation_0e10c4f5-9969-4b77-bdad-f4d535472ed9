﻿using System;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace SlzrCrossGate.Migrations.MySql.Migrations
{
    /// <inheritdoc />
    public partial class AddTerminalLogRecordingFeature : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<DateTime>(
                name: "CreateTime",
                table: "VehicleInfos",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 31, 13, 39, 18, 768, DateTimeKind.Local).AddTicks(6062),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 7, 28, 15, 44, 6, 892, DateTimeKind.Local).AddTicks(8702));

            migrationBuilder.AlterColumn<DateTime>(
                name: "OperationTime",
                table: "PasswordChangeLogs",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 31, 13, 39, 18, 767, DateTimeKind.Local).AddTicks(9005),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 7, 28, 15, 44, 6, 892, DateTimeKind.Local).AddTicks(3891));

            migrationBuilder.AlterColumn<DateTime>(
                name: "OperationTime",
                table: "OperationLogs",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 31, 13, 39, 18, 768, DateTimeKind.Local).AddTicks(433),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 7, 28, 15, 44, 6, 892, DateTimeKind.Local).AddTicks(4900));

            migrationBuilder.AlterColumn<DateTime>(
                name: "UpdatedAt",
                table: "MenuItems",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 31, 13, 39, 18, 768, DateTimeKind.Local).AddTicks(2825),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 7, 28, 15, 44, 6, 892, DateTimeKind.Local).AddTicks(6749));

            migrationBuilder.AlterColumn<DateTime>(
                name: "CreatedAt",
                table: "MenuItems",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 31, 13, 39, 18, 768, DateTimeKind.Local).AddTicks(2427),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 7, 28, 15, 44, 6, 892, DateTimeKind.Local).AddTicks(6442));

            migrationBuilder.AlterColumn<DateTime>(
                name: "UpdatedAt",
                table: "MenuGroups",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 31, 13, 39, 18, 768, DateTimeKind.Local).AddTicks(1489),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 7, 28, 15, 44, 6, 892, DateTimeKind.Local).AddTicks(5690));

            migrationBuilder.AlterColumn<DateTime>(
                name: "CreatedAt",
                table: "MenuGroups",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 31, 13, 39, 18, 768, DateTimeKind.Local).AddTicks(1222),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 7, 28, 15, 44, 6, 892, DateTimeKind.Local).AddTicks(5468));

            migrationBuilder.AlterColumn<DateTime>(
                name: "OperationTime",
                table: "LoginLogs",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 31, 13, 39, 18, 767, DateTimeKind.Local).AddTicks(7206),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 7, 28, 15, 44, 6, 892, DateTimeKind.Local).AddTicks(2876));

            migrationBuilder.CreateTable(
                name: "TerminalLogs",
                columns: table => new
                {
                    ID = table.Column<int>(type: "int", nullable: false)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.IdentityColumn),
                    MerchantID = table.Column<string>(type: "varchar(8)", maxLength: 8, nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    LogType = table.Column<int>(type: "int", nullable: true),
                    SetMethod = table.Column<int>(type: "int", nullable: true),
                    CardNO = table.Column<string>(type: "varchar(20)", maxLength: 20, nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    MachineID = table.Column<string>(type: "varchar(20)", maxLength: 20, nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    MachineNO = table.Column<string>(type: "varchar(20)", maxLength: 20, nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    LineNO = table.Column<string>(type: "varchar(20)", maxLength: 20, nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Price = table.Column<int>(type: "int", nullable: true),
                    DriverCardNO = table.Column<string>(type: "varchar(20)", maxLength: 20, nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    LogTime = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    UploadTime = table.Column<DateTime>(type: "datetime(6)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TerminalLogs", x => x.ID);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateIndex(
                name: "IX_TerminalLogs_CardNO_LogTime",
                table: "TerminalLogs",
                columns: new[] { "CardNO", "LogTime" },
                descending: new[] { false, true });

            migrationBuilder.CreateIndex(
                name: "IX_TerminalLogs_LineNO_LogTime",
                table: "TerminalLogs",
                columns: new[] { "LineNO", "LogTime" },
                descending: new[] { false, true });

            migrationBuilder.CreateIndex(
                name: "IX_TerminalLogs_LogTime",
                table: "TerminalLogs",
                column: "LogTime",
                descending: new bool[0]);

            migrationBuilder.CreateIndex(
                name: "IX_TerminalLogs_LogType_LogTime",
                table: "TerminalLogs",
                columns: new[] { "LogType", "LogTime" },
                descending: new[] { false, true });

            migrationBuilder.CreateIndex(
                name: "IX_TerminalLogs_MachineID_LogTime",
                table: "TerminalLogs",
                columns: new[] { "MachineID", "LogTime" },
                descending: new[] { false, true });

            migrationBuilder.CreateIndex(
                name: "IX_TerminalLogs_MachineNO_LogTime",
                table: "TerminalLogs",
                columns: new[] { "MachineNO", "LogTime" },
                descending: new[] { false, true });

            migrationBuilder.CreateIndex(
                name: "IX_TerminalLogs_MerchantID_LogTime",
                table: "TerminalLogs",
                columns: new[] { "MerchantID", "LogTime" },
                descending: new[] { false, true });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "TerminalLogs");

            migrationBuilder.AlterColumn<DateTime>(
                name: "CreateTime",
                table: "VehicleInfos",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 28, 15, 44, 6, 892, DateTimeKind.Local).AddTicks(8702),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 7, 31, 13, 39, 18, 768, DateTimeKind.Local).AddTicks(6062));

            migrationBuilder.AlterColumn<DateTime>(
                name: "OperationTime",
                table: "PasswordChangeLogs",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 28, 15, 44, 6, 892, DateTimeKind.Local).AddTicks(3891),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 7, 31, 13, 39, 18, 767, DateTimeKind.Local).AddTicks(9005));

            migrationBuilder.AlterColumn<DateTime>(
                name: "OperationTime",
                table: "OperationLogs",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 28, 15, 44, 6, 892, DateTimeKind.Local).AddTicks(4900),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 7, 31, 13, 39, 18, 768, DateTimeKind.Local).AddTicks(433));

            migrationBuilder.AlterColumn<DateTime>(
                name: "UpdatedAt",
                table: "MenuItems",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 28, 15, 44, 6, 892, DateTimeKind.Local).AddTicks(6749),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 7, 31, 13, 39, 18, 768, DateTimeKind.Local).AddTicks(2825));

            migrationBuilder.AlterColumn<DateTime>(
                name: "CreatedAt",
                table: "MenuItems",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 28, 15, 44, 6, 892, DateTimeKind.Local).AddTicks(6442),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 7, 31, 13, 39, 18, 768, DateTimeKind.Local).AddTicks(2427));

            migrationBuilder.AlterColumn<DateTime>(
                name: "UpdatedAt",
                table: "MenuGroups",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 28, 15, 44, 6, 892, DateTimeKind.Local).AddTicks(5690),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 7, 31, 13, 39, 18, 768, DateTimeKind.Local).AddTicks(1489));

            migrationBuilder.AlterColumn<DateTime>(
                name: "CreatedAt",
                table: "MenuGroups",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 28, 15, 44, 6, 892, DateTimeKind.Local).AddTicks(5468),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 7, 31, 13, 39, 18, 768, DateTimeKind.Local).AddTicks(1222));

            migrationBuilder.AlterColumn<DateTime>(
                name: "OperationTime",
                table: "LoginLogs",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 28, 15, 44, 6, 892, DateTimeKind.Local).AddTicks(2876),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 7, 31, 13, 39, 18, 767, DateTimeKind.Local).AddTicks(7206));
        }
    }
}

