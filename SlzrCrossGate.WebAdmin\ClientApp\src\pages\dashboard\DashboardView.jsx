import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Tabs,
  Tab,
  Paper
} from '@mui/material';
import {
  Business as BusinessIcon,
  Dashboard as DashboardIcon
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import MerchantDashboard from './MerchantDashboard';
import PlatformDashboard from './PlatformDashboard';

const DashboardView = () => {
  const { user } = useAuth();
  const [tabValue, setTabValue] = useState(0); // 0: 商户仪表盘, 1: 平台仪表盘

  // 直接从AuthContext获取用户角色信息，避免重复请求
  const isSystemAdmin = user?.roles?.includes('SystemAdmin') || false;

  useEffect(() => {
    console.log('DashboardView: 使用AuthContext中的用户信息，避免重复请求');

    // 如果是系统管理员，默认显示平台仪表盘
    if (isSystemAdmin) {
      setTabValue(1);
    }
  }, [isSystemAdmin]);

  // 处理标签页变更
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  return (
    <Container maxWidth={false}>
      <Box sx={{ pt: 3, pb: 3 }}>
        {isSystemAdmin && (
          <Paper sx={{ mb: 3 }}>
            <Tabs
              value={tabValue}
              onChange={handleTabChange}
              indicatorColor="primary"
              textColor="primary"
              centered
            >
              <Tab 
                icon={<BusinessIcon />} 
                label="商户仪表盘" 
                iconPosition="start"
              />
              <Tab 
                icon={<DashboardIcon />} 
                label="平台仪表盘" 
                iconPosition="start"
              />
            </Tabs>
          </Paper>
        )}
        
        {tabValue === 0 ? (
          <MerchantDashboard />
        ) : (
          <PlatformDashboard />
        )}
      </Box>
    </Container>
  );
};

export default DashboardView;
