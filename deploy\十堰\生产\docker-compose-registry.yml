

services:
  # MinIO 对象存储
  minio:
    image: devtest.pointlife365.net:5180/library/minio:latest
    container_name: minio
    command: server /data --console-address ":9001"
    volumes:
      - minio_data:/data
    environment:
      - MINIO_ROOT_USER=${MINIO_ROOT_USER:-minioadmin}
      - MINIO_ROOT_PASSWORD=${MINIO_ROOT_PASSWORD:-minioadmin123}
    ports:
      - "9000:9000"   # MinIO API端口
      - "9001:9001"   # MinIO Console端口
    networks:
      - slzr-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3

  api-service:
    image: devtest.pointlife365.net:5180/slzr/tcpserver-api:latest
    container_name: tcpserver-api
    extra_hosts:  # 添加宿主机IP的DNS映射
      - "host.docker.internal:host-gateway"
    ports:
    #  - "6000:8000"  # HTTP API
      - "8822:8001"  # TCP 服务
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - HTTP_PORT=8000
      - TCP_PORT=8001
      - ConnectionStrings__DefaultConnection=Server=${MYSQL_HOST:-127.0.0.1};Port=${MYSQL_PORT:-3306};Database=tcpserver;User=${MYSQL_ROOT_USER:-slzr};Password=${MYSQL_ROOT_PASSWORD:-slzr!12345};SslMode=Required;AllowLoadLocalInfile=true;
      - DatabaseProvider=MySql
      - RabbitMQ__HostName=${RABBITMQ_HOST:-127.0.0.1}
      - RabbitMQ__Port=${RABBITMQ_PORT:-5672}
      - RabbitMQ__UserName=${RABBITMQ_USER:-guest}
      - RabbitMQ__Password=${RABBITMQ_PASS:-guest}
      - FileService__DefaultStorageType=${FILE_STORAGE_TYPE:-MinIO}
      - FileService__LocalFilePath=/app/storage/files
      - FileService__MinIO__Endpoint=minio:9000
      - FileService__MinIO__AccessKey=${MINIO_ROOT_USER:-minioadmin}
      - FileService__MinIO__SecretKey=${MINIO_ROOT_PASSWORD:-minioadmin123}
      - FileService__MinIO__BucketName=${MINIO_BUCKET_NAME:-slzr-files}
      - TZ=Asia/Shanghai
    volumes:
      - api_storage:/app/storage
      - /etc/localtime:/etc/localtime:ro  # 挂载宿主机时区文件
    networks:
      - slzr-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]  # 与HTTP_PORT环境变量保持一致
      interval: 30s
      timeout: 10s
      retries: 3

  # WebAdmin 服务
  web-admin:
    image: devtest.pointlife365.net:5180/slzr/tcpserver-web:latest
    container_name: tcpserver-web
    extra_hosts:  # 添加宿主机IP的DNS映射
      - "host.docker.internal:host-gateway"
    ports:
      - "18822:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ConnectionStrings__DefaultConnection=Server=${MYSQL_HOST:-127.0.0.1};Port=${MYSQL_PORT:-3306};Database=tcpserver;User=${MYSQL_ROOT_USER:-slzr};Password=${MYSQL_ROOT_PASSWORD:-slzr!12345};SslMode=Required;AllowLoadLocalInfile=true;
      - DatabaseProvider=MySql
      - RabbitMQ__HostName=${RABBITMQ_HOST:-127.0.0.1}
      - RabbitMQ__Port=${RABBITMQ_PORT:-5672}
      - RabbitMQ__UserName=${RABBITMQ_USER:-guest}
      - RabbitMQ__Password=${RABBITMQ_PASS:-guest}
      - Jwt__Key=${JWT_KEY:-YourSecretKeyHere12345678901234567890}
      - FileService__DefaultStorageType=${FILE_STORAGE_TYPE:-MinIO}
      - FileService__LocalFilePath=/app/storage/files
      - FileService__MinIO__Endpoint=minio:9000
      - FileService__MinIO__AccessKey=${MINIO_ROOT_USER:-minioadmin}
      - FileService__MinIO__SecretKey=${MINIO_ROOT_PASSWORD:-minioadmin123}
      - FileService__MinIO__BucketName=${MINIO_BUCKET_NAME:-slzr-files}
      - TZ=Asia/Shanghai
    volumes:
      - /etc/localtime:/etc/localtime:ro  # 挂载宿主机时区文件
      - webadmin_storage:/app/storage
      - webadmin_keys:/app/Keys
    networks:
      - slzr-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  slzr-network:
    driver: bridge

volumes:
  minio_data:
  api_storage:
  webadmin_storage:
  webadmin_keys:
