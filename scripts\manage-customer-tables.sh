#!/bin/bash

# 客户项目数据表管理脚本
# 用于手动管理客户项目的数据表，不使用EF迁移

set -e

CUSTOMER_NAME=$1
ACTION=$2
DB_HOST=${3:-"localhost"}
DB_USER=${4:-"root"}
DB_PASS=${5:-"slzr!12345"}
DB_NAME=${6:-"tcpserver"}

if [ -z "$CUSTOMER_NAME" ]; then
    echo "用法: $0 <customer-name> <action> [db-host] [db-user] [db-pass] [db-name]"
    echo "示例: $0 customer-a create"
    echo "示例: $0 customer-a backup"
    echo "示例: $0 customer-a drop"
    exit 1
fi

# 转换为Pascal命名
CUSTOMER_PASCAL=$(echo "$CUSTOMER_NAME" | sed 's/-\([a-z]\)/\U\1/g' | sed 's/^./\U&/')
CUSTOMER_PREFIX="Customer_${CUSTOMER_PASCAL}"

echo "管理客户 $CUSTOMER_NAME 的数据表 (前缀: $CUSTOMER_PREFIX)..."

# 创建临时SQL文件
TMP_SQL=$(mktemp)

case "$ACTION" in
    "create")
        echo "创建客户表..."
        
        # 生成创建表的SQL
        cat > "$TMP_SQL" << EOF
-- 设置客户前缀
SET @CUSTOMER_PREFIX = '$CUSTOMER_PREFIX';

-- 启用动态SQL
SET @SQL = CONCAT('
-- 客户数据表
CREATE TABLE IF NOT EXISTS ', @CUSTOMER_PREFIX, '_Data (
    Id INT AUTO_INCREMENT PRIMARY KEY,
    MerchantId VARCHAR(8) NOT NULL COMMENT \'商户ID\',
    DataType VARCHAR(50) NOT NULL COMMENT \'数据类型\',
    DataValue LONGTEXT COMMENT \'数据内容\',
    CreatedBy VARCHAR(50) COMMENT \'创建人\',
    UpdatedBy VARCHAR(50) COMMENT \'更新人\',
    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT \'创建时间\',
    UpdatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT \'更新时间\',
    INDEX idx_', @CUSTOMER_PREFIX, '_data_merchant (MerchantId),
    INDEX idx_', @CUSTOMER_PREFIX, '_data_type (DataType),
    INDEX idx_', @CUSTOMER_PREFIX, '_data_created (CreatedAt)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT=\'客户数据表\';

-- 客户配置表
CREATE TABLE IF NOT EXISTS ', @CUSTOMER_PREFIX, '_Settings (
    Id INT AUTO_INCREMENT PRIMARY KEY,
    MerchantId VARCHAR(8) NOT NULL COMMENT \'商户ID\',
    SettingKey VARCHAR(100) NOT NULL COMMENT \'配置键\',
    SettingValue TEXT COMMENT \'配置值\',
    Description VARCHAR(500) COMMENT \'描述\',
    DataType VARCHAR(20) DEFAULT \'string\' COMMENT \'数据类型\',
    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT \'创建时间\',
    UpdatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT \'更新时间\',
    UNIQUE KEY uk_', @CUSTOMER_PREFIX, '_settings_merchant_key (MerchantId, SettingKey)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT=\'客户配置表\';

-- 客户工作流表
CREATE TABLE IF NOT EXISTS ', @CUSTOMER_PREFIX, '_Workflows (
    Id INT AUTO_INCREMENT PRIMARY KEY,
    MerchantId VARCHAR(8) NOT NULL COMMENT \'商户ID\',
    WorkflowName VARCHAR(100) NOT NULL COMMENT \'工作流名称\',
    WorkflowConfig JSON COMMENT \'工作流配置\',
    Status VARCHAR(20) DEFAULT \'Draft\' COMMENT \'状态\',
    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT \'创建时间\',
    UpdatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT \'更新时间\',
    INDEX idx_', @CUSTOMER_PREFIX, '_workflow_merchant (MerchantId),
    INDEX idx_', @CUSTOMER_PREFIX, '_workflow_status (Status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT=\'客户工作流表\';

-- 客户报表表
CREATE TABLE IF NOT EXISTS ', @CUSTOMER_PREFIX, '_Reports (
    Id INT AUTO_INCREMENT PRIMARY KEY,
    MerchantId VARCHAR(8) NOT NULL COMMENT \'商户ID\',
    ReportName VARCHAR(100) NOT NULL COMMENT \'报表名称\',
    ReportConfig JSON COMMENT \'报表配置\',
    ReportData LONGTEXT COMMENT \'报表数据\',
    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT \'创建时间\',
    UpdatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT \'更新时间\',
    INDEX idx_', @CUSTOMER_PREFIX, '_report_merchant (MerchantId),
    INDEX idx_', @CUSTOMER_PREFIX, '_report_created (CreatedAt)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT=\'客户报表表\';
');

-- 执行动态SQL
PREPARE stmt FROM @SQL;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 显示创建的表
SET @SHOW_TABLES = CONCAT('SHOW TABLES LIKE \\'', @CUSTOMER_PREFIX, '_%\\'');
PREPARE stmt FROM @SHOW_TABLES;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
EOF

        # 执行SQL
        mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" < "$TMP_SQL"
        echo "客户表创建完成"
        ;;
    
    "backup")
        echo "备份客户数据..."
        BACKUP_DIR="backup"
        mkdir -p "$BACKUP_DIR"
        BACKUP_FILE="$BACKUP_DIR/${CUSTOMER_NAME}-$(date +%Y%m%d-%H%M%S).sql"
        
        # 生成备份SQL
        mysqldump -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" \
            --tables "${CUSTOMER_PREFIX}_Data" "${CUSTOMER_PREFIX}_Settings" \
                    "${CUSTOMER_PREFIX}_Workflows" "${CUSTOMER_PREFIX}_Reports" \
            --add-drop-table \
            --create-options \
            --extended-insert \
            --routines \
            --triggers \
            --comments \
            > "$BACKUP_FILE"
        
        echo "备份完成: $BACKUP_FILE"
        ;;
    
    "restore")
        if [ -z "$3" ]; then
            echo "请指定备份文件路径"
            echo "示例: $0 customer-a restore backup/customer-a-20240101-120000.sql"
            exit 1
        fi
        
        BACKUP_FILE="$3"
        echo "从 $BACKUP_FILE 恢复客户数据..."
        
        if [ ! -f "$BACKUP_FILE" ]; then
            echo "错误: 备份文件不存在"
            exit 1
        fi
        
        mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" < "$BACKUP_FILE"
        echo "恢复完成"
        ;;
    
    "drop")
        echo "警告: 这将删除所有客户表！"
        read -p "确认继续? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            # 生成删除SQL
            cat > "$TMP_SQL" << EOF
SET FOREIGN_KEY_CHECKS = 0;
DROP TABLE IF EXISTS ${CUSTOMER_PREFIX}_Data;
DROP TABLE IF EXISTS ${CUSTOMER_PREFIX}_Settings;
DROP TABLE IF EXISTS ${CUSTOMER_PREFIX}_Workflows;
DROP TABLE IF EXISTS ${CUSTOMER_PREFIX}_Reports;
SET FOREIGN_KEY_CHECKS = 1;
EOF
            
            mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" < "$TMP_SQL"
            echo "客户表已删除"
        fi
        ;;
    
    "list")
        echo "列出客户表..."
        mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" \
            -e "SHOW TABLES LIKE '${CUSTOMER_PREFIX}_%';"
        ;;
    
    "describe")
        if [ -z "$3" ]; then
            echo "请指定表名后缀"
            echo "示例: $0 customer-a describe Data"
            exit 1
        fi
        
        TABLE_SUFFIX="$3"
        TABLE_NAME="${CUSTOMER_PREFIX}_${TABLE_SUFFIX}"
        echo "描述表 $TABLE_NAME..."
        
        mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" \
            -e "DESCRIBE $TABLE_NAME;"
        ;;
    
    "add-column")
        if [ -z "$3" ] || [ -z "$4" ] || [ -z "$5" ]; then
            echo "请指定表名后缀、列名和列定义"
            echo "示例: $0 customer-a add-column Data NewColumn 'VARCHAR(100) COMMENT \"新列\"'"
            exit 1
        fi
        
        TABLE_SUFFIX="$3"
        COLUMN_NAME="$4"
        COLUMN_DEF="$5"
        TABLE_NAME="${CUSTOMER_PREFIX}_${TABLE_SUFFIX}"
        
        echo "向表 $TABLE_NAME 添加列 $COLUMN_NAME..."
        
        # 生成添加列SQL
        cat > "$TMP_SQL" << EOF
ALTER TABLE $TABLE_NAME
ADD COLUMN $COLUMN_NAME $COLUMN_DEF;
EOF
        
        mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" < "$TMP_SQL"
        echo "列添加完成"
        ;;
    
    "add-index")
        if [ -z "$3" ] || [ -z "$4" ] || [ -z "$5" ]; then
            echo "请指定表名后缀、索引名和列名"
            echo "示例: $0 customer-a add-index Data idx_newcol NewColumn"
            exit 1
        fi
        
        TABLE_SUFFIX="$3"
        INDEX_NAME="$4"
        COLUMN_NAME="$5"
        TABLE_NAME="${CUSTOMER_PREFIX}_${TABLE_SUFFIX}"
        
        echo "向表 $TABLE_NAME 添加索引 $INDEX_NAME..."
        
        # 生成添加索引SQL
        cat > "$TMP_SQL" << EOF
ALTER TABLE $TABLE_NAME
ADD INDEX $INDEX_NAME ($COLUMN_NAME);
EOF
        
        mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" < "$TMP_SQL"
        echo "索引添加完成"
        ;;
    
    *)
        echo "未知操作: $ACTION"
        echo "支持的操作: create, backup, restore, drop, list, describe, add-column, add-index"
        exit 1
        ;;
esac

# 清理临时文件
rm -f "$TMP_SQL"

echo "操作完成!"

# 使用示例：
# ./manage-customer-tables.sh customer-a create            # 创建客户表
# ./manage-customer-tables.sh customer-a backup            # 备份客户数据
# ./manage-customer-tables.sh customer-a restore backup.sql # 恢复客户数据
# ./manage-customer-tables.sh customer-a list              # 列出客户表
# ./manage-customer-tables.sh customer-a describe Data     # 描述客户数据表
# ./manage-customer-tables.sh customer-a add-column Data NewColumn "VARCHAR(100) COMMENT 'New Column'" # 添加列
# ./manage-customer-tables.sh customer-a add-index Data idx_newcol NewColumn # 添加索引
