-- 功能权限系统数据库迁移脚本
-- 创建功能配置表和角色权限表

-- 创建功能配置表
CREATE TABLE IF NOT EXISTS FeatureConfigs (
    Id INT PRIMARY KEY AUTO_INCREMENT,
    FeatureKey VARCHAR(100) NOT NULL UNIQUE,
    FeatureName VARCHAR(100) NOT NULL,
    Description VARCHAR(500),
    Category VARCHAR(50) NOT NULL,
    RiskLevel VARCHAR(20) NOT NULL DEFAULT 'Low',
    IsGloballyEnabled BOOLEAN NOT NULL DEFAULT TRUE,
    IsSystemBuiltIn BOOLEAN NOT NULL DEFAULT FALSE,
    SortOrder INT NOT NULL DEFAULT 0,
    CreatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    CreatedBy VARCHAR(50),
    UpdatedBy VARCHAR(50),
    
    INDEX idx_category (Category),
    INDEX idx_feature_key (FeatureKey)
);

-- 创建角色功能权限表
CREATE TABLE IF NOT EXISTS RoleFeaturePermissions (
    Id INT PRIMARY KEY AUTO_INCREMENT,
    RoleName VARCHAR(50) NOT NULL,
    FeatureKey VARCHAR(100) NOT NULL,
    IsEnabled BOOLEAN,
    CreatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    CreatedBy VARCHAR(50),
    UpdatedBy VARCHAR(50),
    
    UNIQUE KEY uk_role_feature (RoleName, FeatureKey),
    FOREIGN KEY (FeatureKey) REFERENCES FeatureConfigs(FeatureKey) ON DELETE CASCADE,
    INDEX idx_role_name (RoleName),
    INDEX idx_feature_key (FeatureKey)
);

-- 插入默认功能配置
INSERT INTO FeatureConfigs (FeatureKey, FeatureName, Description, Category, RiskLevel, IsSystemBuiltIn, SortOrder) VALUES
-- 商户管理
('merchant.create', '创建商户', '允许创建新的商户账户', '商户管理', 'Low', TRUE, 1),
('merchant.edit', '编辑商户', '允许修改商户基本信息', '商户管理', 'Medium', TRUE, 2),
('merchant.delete', '删除商户', '允许删除商户账户（高风险操作）', '商户管理', 'High', TRUE, 3),
('merchant.toggle_active', '启用/停用商户', '允许激活或停用商户账户', '商户管理', 'Medium', TRUE, 4),
('merchant.view_details', '查看商户详情', '允许查看商户详细信息', '商户管理', 'Low', TRUE, 5),
('merchant.export', '导出商户数据', '允许导出商户列表数据', '商户管理', 'Medium', TRUE, 6),

-- 用户管理
('user.create', '创建用户', '允许创建新的用户账户', '用户管理', 'Low', TRUE, 10),
('user.edit', '编辑用户', '允许修改用户基本信息', '用户管理', 'Medium', TRUE, 11),
('user.delete', '删除用户', '允许删除用户账户', '用户管理', 'High', TRUE, 12),
('user.lock', '锁定/解锁用户', '允许锁定或解锁用户账户', '用户管理', 'Medium', TRUE, 13),
('user.reset_password', '重置密码', '允许重置用户密码', '用户管理', 'Medium', TRUE, 14),
('user.reset_two_factor', '重置双因素认证', '允许重置用户的双因素认证', '用户管理', 'Medium', TRUE, 15),
('user.view_details', '查看用户详情', '允许查看用户详细信息', '用户管理', 'Low', TRUE, 16),

-- 角色管理
('role.create', '创建角色', '允许创建新的系统角色', '角色管理', 'High', TRUE, 20),
('role.edit', '编辑角色', '允许修改角色信息', '角色管理', 'High', TRUE, 21),
('role.delete', '删除角色', '允许删除系统角色', '角色管理', 'High', TRUE, 22),
('role.assign', '分配角色', '允许为用户分配角色', '角色管理', 'Medium', TRUE, 23),

-- 终端管理
('terminal.create', '创建终端', '允许创建新的终端', '终端管理', 'Low', TRUE, 30),
('terminal.edit', '编辑终端', '允许修改终端信息', '终端管理', 'Medium', TRUE, 31),
('terminal.delete', '删除终端', '允许删除终端', '终端管理', 'High', TRUE, 32),
('terminal.view_details', '查看终端详情', '允许查看终端详细信息', '终端管理', 'Low', TRUE, 33),
('terminal.view_events', '查看终端事件', '允许查看终端事件日志', '终端管理', 'Low', TRUE, 34),
('terminal.view_records', '查看终端记录', '允许查看终端操作记录', '终端管理', 'Low', TRUE, 35),
('terminal.send_message', '发送消息', '允许向终端发送消息', '终端管理', 'Medium', TRUE, 36),
('terminal.publish_file', '发布文件', '允许向终端发布文件', '终端管理', 'Medium', TRUE, 37),
('terminal.export', '导出终端数据', '允许导出终端列表数据', '终端管理', 'Medium', TRUE, 38),

-- 文件管理
('file.upload', '文件上传', '允许上传文件', '文件管理', 'Low', TRUE, 40),
('file.delete', '文件删除', '允许删除文件', '文件管理', 'Medium', TRUE, 41),
('file.publish', '文件发布', '允许发布文件到终端', '文件管理', 'Medium', TRUE, 42),
('file.version_manage', '版本管理', '允许管理文件版本', '文件管理', 'Medium', TRUE, 43),
('file.export', '导出文件数据', '允许导出文件列表数据', '文件管理', 'Medium', TRUE, 44),

-- 文件类型管理
('file_type.create', '创建文件类型', '允许创建新的文件类型', '文件类型管理', 'Low', TRUE, 50),
('file_type.edit', '编辑文件类型', '允许修改文件类型信息', '文件类型管理', 'Medium', TRUE, 51),
('file_type.delete', '删除文件类型', '允许删除文件类型', '文件类型管理', 'High', TRUE, 52),
('file_type.view', '查看文件类型', '允许查看文件类型信息', '文件类型管理', 'Low', TRUE, 53),

-- 文件版本管理
('file_version.create', '创建文件版本', '允许创建新的文件版本', '文件版本管理', 'Low', TRUE, 60),
('file_version.edit', '编辑文件版本', '允许修改文件版本信息', '文件版本管理', 'Medium', TRUE, 61),
('file_version.delete', '删除文件版本', '允许删除文件版本', '文件版本管理', 'High', TRUE, 62),
('file_version.view', '查看文件版本', '允许查看文件版本信息', '文件版本管理', 'Low', TRUE, 63),
('file_version.publish', '发布文件版本', '允许发布文件版本', '文件版本管理', 'Medium', TRUE, 64),
('file_version.download', '下载文件版本', '允许下载文件版本', '文件版本管理', 'Low', TRUE, 65),

-- 发布记录管理
('publish_record.view', '查看发布记录', '允许查看文件发布记录', '发布记录管理', 'Low', TRUE, 70),
('publish_record.delete', '删除发布记录', '允许删除发布记录', '发布记录管理', 'Medium', TRUE, 71),
('publish_record.retry', '重试发布', '允许重试失败的发布操作', '发布记录管理', 'Medium', TRUE, 72),
('publish_record.export', '导出发布记录', '允许导出发布记录数据', '发布记录管理', 'Medium', TRUE, 73),

-- 消息类型管理
('message_type.create', '创建消息类型', '允许创建新的消息类型', '消息类型管理', 'Low', TRUE, 80),
('message_type.edit', '编辑消息类型', '允许修改消息类型信息', '消息类型管理', 'Medium', TRUE, 81),
('message_type.delete', '删除消息类型', '允许删除消息类型', '消息类型管理', 'High', TRUE, 82),
('message_type.view', '查看消息类型', '允许查看消息类型信息', '消息类型管理', 'Low', TRUE, 83),

-- 线路参数管理
('line_price.create', '创建线路参数', '允许创建新的线路参数', '线路参数管理', 'Low', TRUE, 90),
('line_price.edit', '编辑线路参数', '允许修改线路参数信息', '线路参数管理', 'Medium', TRUE, 91),
('line_price.delete', '删除线路参数', '允许删除线路参数', '线路参数管理', 'High', TRUE, 92),
('line_price.view', '查看线路参数', '允许查看线路参数信息', '线路参数管理', 'Low', TRUE, 93),
('line_price.export', '导出线路参数', '允许导出线路参数数据', '线路参数管理', 'Medium', TRUE, 94),

-- 线路参数版本管理
('line_price_version.create', '创建线路参数版本', '允许创建新的线路参数版本', '线路参数版本管理', 'Low', TRUE, 100),
('line_price_version.edit', '编辑线路参数版本', '允许修改线路参数版本信息', '线路参数版本管理', 'Medium', TRUE, 101),
('line_price_version.delete', '删除线路参数版本', '允许删除线路参数版本', '线路参数版本管理', 'High', TRUE, 102),
('line_price_version.view', '查看线路参数版本', '允许查看线路参数版本信息', '线路参数版本管理', 'Low', TRUE, 103),
('line_price_version.publish', '发布线路参数版本', '允许发布线路参数版本', '线路参数版本管理', 'Medium', TRUE, 104),
('line_price_version.preview', '预览线路参数版本', '允许预览线路参数版本', '线路参数版本管理', 'Low', TRUE, 105),

-- 银联密钥管理
('unionpay_key.create', '创建银联密钥', '允许创建新的银联密钥', '银联密钥管理', 'High', TRUE, 110),
('unionpay_key.edit', '编辑银联密钥', '允许修改银联密钥信息', '银联密钥管理', 'High', TRUE, 111),
('unionpay_key.delete', '删除银联密钥', '允许删除银联密钥', '银联密钥管理', 'High', TRUE, 112),
('unionpay_key.view', '查看银联密钥', '允许查看银联密钥信息', '银联密钥管理', 'Medium', TRUE, 113),
('unionpay_key.export', '导出银联密钥', '允许导出银联密钥数据', '银联密钥管理', 'High', TRUE, 114),

-- 票价折扣方案管理
('fare_discount_scheme.create', '创建票价折扣方案', '允许创建新的票价折扣方案', '票价折扣方案管理', 'Low', TRUE, 120),
('fare_discount_scheme.edit', '编辑票价折扣方案', '允许修改票价折扣方案信息', '票价折扣方案管理', 'Medium', TRUE, 121),
('fare_discount_scheme.delete', '删除票价折扣方案', '允许删除票价折扣方案', '票价折扣方案管理', 'High', TRUE, 122),
('fare_discount_scheme.view', '查看票价折扣方案', '允许查看票价折扣方案信息', '票价折扣方案管理', 'Low', TRUE, 123),
('fare_discount_scheme.submit_version', '提交版本', '允许提交票价折扣方案版本', '票价折扣方案管理', 'Medium', TRUE, 124),
('fare_discount_scheme.view_versions', '查看版本历史', '允许查看票价折扣方案版本历史', '票价折扣方案管理', 'Low', TRUE, 125),
('fare_discount_scheme.copy', '复制方案', '允许复制票价折扣方案', '票价折扣方案管理', 'Low', TRUE, 126),

-- 系统功能
('system.backup', '系统备份', '允许执行系统数据备份', '系统功能', 'Low', TRUE, 130),
('system.logs', '系统日志', '允许查看系统操作日志', '系统功能', 'Low', TRUE, 131),
('system.settings', '系统设置', '允许修改系统配置', '系统功能', 'High', TRUE, 132),
('system.feature_control', '功能控制', '允许管理功能权限配置', '系统功能', 'High', TRUE, 133);

-- 插入默认角色权限
INSERT INTO RoleFeaturePermissions (RoleName, FeatureKey, IsEnabled) VALUES
-- SystemAdmin 拥有所有权限 (明确设置高风险权限)
('SystemAdmin', 'merchant.delete', TRUE),
('SystemAdmin', 'user.delete', TRUE),
('SystemAdmin', 'role.create', TRUE),
('SystemAdmin', 'role.edit', TRUE),
('SystemAdmin', 'role.delete', TRUE),
('SystemAdmin', 'system.settings', TRUE),
('SystemAdmin', 'system.feature_control', TRUE),
('SystemAdmin', 'terminal.delete', TRUE),
('SystemAdmin', 'file_type.delete', TRUE),
('SystemAdmin', 'file_version.delete', TRUE),
('SystemAdmin', 'message_type.delete', TRUE),
('SystemAdmin', 'line_price.delete', TRUE),
('SystemAdmin', 'line_price_version.delete', TRUE),
('SystemAdmin', 'unionpay_key.create', TRUE),
('SystemAdmin', 'unionpay_key.edit', TRUE),
('SystemAdmin', 'unionpay_key.delete', TRUE),
('SystemAdmin', 'unionpay_key.export', TRUE),
('SystemAdmin', 'fare_discount_scheme.delete', TRUE),

-- MerchantAdmin 受限权限
('MerchantAdmin', 'merchant.delete', FALSE),
('MerchantAdmin', 'user.delete', FALSE),
('MerchantAdmin', 'role.create', FALSE),
('MerchantAdmin', 'role.edit', FALSE),
('MerchantAdmin', 'role.delete', FALSE),
('MerchantAdmin', 'system.settings', FALSE),
('MerchantAdmin', 'system.feature_control', FALSE),
('MerchantAdmin', 'terminal.delete', FALSE),
('MerchantAdmin', 'unionpay_key.create', FALSE),
('MerchantAdmin', 'unionpay_key.edit', FALSE),
('MerchantAdmin', 'unionpay_key.delete', FALSE),
('MerchantAdmin', 'unionpay_key.export', FALSE),

-- User 最小权限
('User', 'merchant.create', FALSE),
('User', 'merchant.edit', FALSE),
('User', 'merchant.delete', FALSE),
('User', 'user.create', FALSE),
('User', 'user.edit', FALSE),
('User', 'user.delete', FALSE),
('User', 'user.lock', FALSE),
('User', 'user.reset_password', FALSE),
('User', 'role.create', FALSE),
('User', 'role.edit', FALSE),
('User', 'role.delete', FALSE),
('User', 'role.assign', FALSE),
('User', 'system.backup', FALSE),
('User', 'system.settings', FALSE),
('User', 'system.feature_control', FALSE),
('User', 'file.delete', FALSE),
('User', 'file.publish', FALSE),
('User', 'file_type.create', FALSE),
('User', 'file_type.edit', FALSE),
('User', 'file_type.delete', FALSE),
('User', 'terminal.create', FALSE),
('User', 'terminal.edit', FALSE),
('User', 'terminal.delete', FALSE),
('User', 'terminal.send_message', FALSE),
('User', 'terminal.publish_file', FALSE),
('User', 'unionpay_key.create', FALSE),
('User', 'unionpay_key.edit', FALSE),
('User', 'unionpay_key.delete', FALSE),
('User', 'unionpay_key.view', FALSE),
('User', 'unionpay_key.export', FALSE);
