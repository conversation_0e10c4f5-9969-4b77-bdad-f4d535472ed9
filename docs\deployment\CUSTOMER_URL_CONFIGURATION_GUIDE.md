# 客户项目URL配置方案指南

## 概述

客户项目的访问地址可以根据实际需求选择不同的配置方案，每种方案都有其优缺点。

## 方案对比

### 方案一：路径前缀（推荐）

**URL格式**：
```
主应用: https://yourdomain.com/
客户A: https://yourdomain.com/customer-a/
客户B: https://yourdomain.com/customer-b/
```

**优点**：
- ✅ 统一域名，用户体验好
- ✅ 便于管理和配置
- ✅ 支持共享认证状态
- ✅ SSL证书管理简单

**缺点**：
- ❌ 需要Nginx路由配置
- ❌ 路径可能暴露客户信息

### 方案二：通用扩展路径

**URL格式**：
```
主应用: https://yourdomain.com/
客户A: https://yourdomain.com/extensions/customer-a/
客户B: https://yourdomain.com/modules/customer-b/
```

**优点**：
- ✅ 更好的语义化
- ✅ 便于分类管理
- ✅ 隐藏客户具体信息

**缺点**：
- ❌ URL稍长
- ❌ 需要额外的路由配置

### 方案三：子域名

**URL格式**：
```
主应用: https://admin.yourdomain.com/
客户A: https://customer-a.yourdomain.com/
客户B: https://customer-b.yourdomain.com/
```

**优点**：
- ✅ 完全独立的访问地址
- ✅ 更好的隔离性
- ✅ 便于独立部署

**缺点**：
- ❌ 需要配置多个子域名
- ❌ 跨域认证稍复杂
- ❌ SSL证书管理复杂

### 方案四：端口区分（仅开发环境）

**URL格式**：
```
主应用: http://localhost:5270/
客户A: http://localhost:5271/
客户B: http://localhost:5272/
```

**优点**：
- ✅ 开发简单
- ✅ 完全独立

**缺点**：
- ❌ 仅适合开发环境
- ❌ 生产环境不实用

## 配置实现

### 方案一：路径前缀配置

#### Nginx配置
```nginx
# nginx/conf.d/customer.conf
upstream webadmin {
    server webadmin:80;
}

upstream customer-a {
    server customer-a:80;
}

server {
    listen 80;
    server_name yourdomain.com;

    # 主应用路由
    location / {
        proxy_pass http://webadmin;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 客户A路由
    location /customer-a/ {
        rewrite ^/customer-a/(.*)$ /$1 break;
        proxy_pass http://customer-a;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Customer-Path /customer-a;
    }
}
```

#### 菜单配置
```sql
INSERT INTO MenuItems (MenuGroupId, ItemKey, Title, Href, IconName, SortOrder, IsEnabled, VisibleToSystemAdmin, VisibleToMerchantAdmin, VisibleToUser, IsExternal, Target)
VALUES (
    (SELECT Id FROM MenuGroups WHERE GroupKey = 'customer-features'),
    'customer-a-home',
    '客户A功能',
    'https://yourdomain.com/customer-a/',
    'SettingsIcon',
    1, 1, 1, 1, 0, 1, '_iframe'
);
```

### 方案二：通用扩展路径配置

#### Nginx配置
```nginx
    # 扩展模块路由
    location /extensions/ {
        # 根据路径分发到不同的客户服务
        location /extensions/customer-a/ {
            rewrite ^/extensions/customer-a/(.*)$ /$1 break;
            proxy_pass http://customer-a;
            # ... 其他配置
        }
        
        location /extensions/customer-b/ {
            rewrite ^/extensions/customer-b/(.*)$ /$1 break;
            proxy_pass http://customer-b;
            # ... 其他配置
        }
    }
```

#### 菜单配置
```sql
INSERT INTO MenuItems (MenuGroupId, ItemKey, Title, Href, IconName, SortOrder, IsEnabled, VisibleToSystemAdmin, VisibleToMerchantAdmin, VisibleToUser, IsExternal, Target)
VALUES (
    (SELECT Id FROM MenuGroups WHERE GroupKey = 'extensions'),
    'customer-a-extension',
    '客户A扩展',
    'https://yourdomain.com/extensions/customer-a/',
    'SettingsIcon',
    1, 1, 1, 1, 0, 1, '_iframe'
);
```

### 方案三：子域名配置

#### Nginx配置
```nginx
# 客户A子域名
server {
    listen 80;
    server_name customer-a.yourdomain.com;

    location / {
        proxy_pass http://customer-a;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}

# 主应用
server {
    listen 80;
    server_name admin.yourdomain.com;

    location / {
        proxy_pass http://webadmin;
        # ... 其他配置
    }
}
```

#### 菜单配置
```sql
INSERT INTO MenuItems (MenuGroupId, ItemKey, Title, Href, IconName, SortOrder, IsEnabled, VisibleToSystemAdmin, VisibleToMerchantAdmin, VisibleToUser, IsExternal, Target)
VALUES (
    (SELECT Id FROM MenuGroups WHERE GroupKey = 'customer-features'),
    'customer-a-subdomain',
    '客户A功能',
    'https://customer-a.yourdomain.com/',
    'SettingsIcon',
    1, 1, 1, 1, 0, 1, '_iframe'
);
```

## 动态配置支持

### 环境变量配置

在客户项目中支持动态路径配置：

```bash
# 环境变量
CUSTOMER_BASE_PATH=/customer-a
CUSTOMER_SUBDOMAIN=customer-a.yourdomain.com
CUSTOMER_ACCESS_MODE=path  # path, subdomain, port
```

### 前端路由配置

```javascript
// 客户项目的前端配置
const config = {
    basePath: process.env.REACT_APP_BASE_PATH || '/customer-a',
    apiBaseUrl: process.env.REACT_APP_API_BASE_URL || 'https://yourdomain.com/customer-a/api',
    mainAppUrl: process.env.REACT_APP_MAIN_URL || 'https://yourdomain.com'
};

// 在Router中使用
<Router basename={config.basePath}>
    <Routes>
        <Route path="/" element={<HomePage />} />
        {/* 其他路由 */}
    </Routes>
</Router>
```

## 推荐配置

### 开发环境
```
主应用: http://localhost:5270/
客户A: http://localhost:5271/
客户B: http://localhost:5272/
```

### 测试环境
```
主应用: https://test.yourdomain.com/
客户A: https://test.yourdomain.com/customer-a/
客户B: https://test.yourdomain.com/customer-b/
```

### 生产环境（方案选择）

**小型部署（推荐方案一）**：
```
主应用: https://admin.yourdomain.com/
客户A: https://admin.yourdomain.com/customer-a/
客户B: https://admin.yourdomain.com/customer-b/
```

**大型部署（推荐方案三）**：
```
主应用: https://admin.yourdomain.com/
客户A: https://customer-a.yourdomain.com/
客户B: https://customer-b.yourdomain.com/
```

## 安全考虑

### 路径前缀方案
- 使用通用前缀避免暴露客户信息
- 配置适当的访问控制
- 监控异常访问模式

### 子域名方案
- 配置独立的SSL证书
- 实施子域名访问控制
- 考虑DNS安全配置

## 总结

**推荐使用方案一（路径前缀）**，因为：
- 配置简单，维护成本低
- 用户体验好，统一域名
- 认证共享容易实现
- 适合大多数场景

如果有特殊的隔离需求或大规模部署，可以考虑方案三（子域名）。
