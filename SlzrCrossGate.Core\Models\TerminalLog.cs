using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace SlzrCrossGate.Core.Models
{
    /// <summary>
    /// 终端日志记录
    /// </summary>
    [Table("TerminalLogs")]
    public class TerminalLog
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        public int ID { get; set; }

        /// <summary>
        /// 商户ID
        /// </summary>
        [StringLength(8)]
        public string? MerchantID { get; set; }

        /// <summary>
        /// 记录类型
        /// 10-司机卡, 12-线路设置, 16-设备设置, 19-发车卡, 20-到站卡
        /// </summary>
        public int? LogType { get; set; }

        /// <summary>
        /// 设置方式
        /// 0x01-M1卡, 0x02-CPU卡, 0x0B-交通卡, 0xA0-无线, 0xA1-调度
        /// </summary>
        public int? SetMethod { get; set; }

        /// <summary>
        /// 卡号
        /// </summary>
        [StringLength(20)]
        public string? CardNO { get; set; }

        /// <summary>
        /// 设备序列号
        /// </summary>
        [StringLength(20)]
        public string? MachineID { get; set; }

        /// <summary>
        /// 设备编号
        /// </summary>
        [StringLength(20)]
        public string? MachineNO { get; set; }

        /// <summary>
        /// 线路号
        /// </summary>
        [StringLength(20)]
        public string? LineNO { get; set; }

        /// <summary>
        /// 票价（分）
        /// </summary>
        public int? Price { get; set; }

        /// <summary>
        /// 司机卡号
        /// </summary>
        [StringLength(20)]
        public string? DriverCardNO { get; set; }

        /// <summary>
        /// 记录时间
        /// </summary>
        public DateTime? LogTime { get; set; }

        /// <summary>
        /// 上传时间
        /// </summary>
        public DateTime? UploadTime { get; set; }
    }

    /// <summary>
    /// 终端日志记录类型枚举
    /// </summary>
    public enum TerminalLogType
    {
        /// <summary>
        /// 司机卡
        /// </summary>
        DriverCard = 10,

        /// <summary>
        /// 线路设置
        /// </summary>
        LineSettings = 12,

        /// <summary>
        /// 设备设置
        /// </summary>
        DeviceSettings = 16,

        /// <summary>
        /// 发车卡
        /// </summary>
        DepartureCard = 19,

        /// <summary>
        /// 到站卡
        /// </summary>
        ArrivalCard = 20
    }

    /// <summary>
    /// 终端日志设置方式枚举
    /// </summary>
    public enum TerminalLogSetMethod
    {
        /// <summary>
        /// M1卡
        /// </summary>
        M1Card = 0x01,

        /// <summary>
        /// CPU卡
        /// </summary>
        CPUCard = 0x02,

        /// <summary>
        /// 交通卡
        /// </summary>
        TransportCard = 0x0B,

        /// <summary>
        /// 无线
        /// </summary>
        Wireless = 0xA0,

        /// <summary>
        /// 调度
        /// </summary>
        Dispatch = 0xA1
    }
}
