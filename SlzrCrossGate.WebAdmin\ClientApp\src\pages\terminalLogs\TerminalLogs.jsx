import React, { useState, useEffect, useRef } from 'react';
import {
  Container,
  Box,
  Card,
  CardContent,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Paper,
  TextField,
  Button,
  Grid,
  MenuItem,
  CircularProgress,
  Chip
} from '@mui/material';
import {
  Search as SearchIcon,
  Refresh as RefreshIcon,
  FileDownload as ExportIcon
} from '@mui/icons-material';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { zhCN as dateFnsZhCN } from 'date-fns/locale';
import { useSnackbar } from 'notistack';
import { terminalLogAPI } from '../../services/api';
import MerchantAutocomplete from '../../components/MerchantAutocomplete';

const TerminalLogs = () => {
  const { enqueueSnackbar } = useSnackbar();
  
  // 状态管理
  const [logs, setLogs] = useState([]);
  const [loading, setLoading] = useState(false);
  const [totalCount, setTotalCount] = useState(0);
  const [page, setPage] = useState(0);
  const [pageSize, setPageSize] = useState(20);
  
  // 筛选条件
  const [filters, setFilters] = useState({
    merchantID: '',
    logType: '',
    cardNO: '',
    machineID: '',
    machineNO: '',
    lineNO: '',
    logTimeStart: null,
    logTimeEnd: null
  });

  // 防重复请求
  const loadLogsRef = useRef();
  const hasLoadedRef = useRef(false);

  // 记录类型选项
  const logTypeOptions = [
    { value: '', label: '全部' },
    { value: 10, label: '司机卡' },
    { value: 12, label: '线路设置' },
    { value: 16, label: '设备设置' },
    { value: 19, label: '发车卡' },
    { value: 20, label: '到站卡' }
  ];

  // 加载日志数据
  const loadLogs = async (showLoading = true, forceReload = false) => {
    if (loadLogsRef.current && !forceReload) return;
    
    try {
      if (showLoading) setLoading(true);
      loadLogsRef.current = true;

      const queryParams = {
        page,
        pageSize,
        merchantID: filters.merchantID || undefined,
        logType: filters.logType || undefined,
        cardNO: filters.cardNO || undefined,
        machineID: filters.machineID || undefined,
        machineNO: filters.machineNO || undefined,
        lineNO: filters.lineNO || undefined,
        logTimeStart: filters.logTimeStart ?
          new Date(filters.logTimeStart.getTime() - filters.logTimeStart.getTimezoneOffset() * 60000)
            .toISOString().slice(0, 19) : undefined,
        logTimeEnd: filters.logTimeEnd ?
          new Date(filters.logTimeEnd.getTime() - filters.logTimeEnd.getTimezoneOffset() * 60000)
            .toISOString().slice(0, 19) : undefined,
        sortBy: 'LogTime',
        sortDirection: 'desc'
      };

      const response = await terminalLogAPI.getTerminalLogs(queryParams);
      setLogs(response.items || []);
      setTotalCount(response.totalCount || 0);
      hasLoadedRef.current = true;
    } catch (error) {
      console.error('加载终端日志失败:', error);
      enqueueSnackbar('加载终端日志失败', { variant: 'error' });
    } finally {
      setLoading(false);
      loadLogsRef.current = false;
    }
  };

  // 初始加载
  useEffect(() => {
    if (!hasLoadedRef.current) {
      loadLogs();
    }
  }, []);

  // 分页或筛选条件变化时重新加载
  useEffect(() => {
    if (hasLoadedRef.current) {
      loadLogs();
    }
  }, [page, pageSize, filters]);

  // 处理页码变化
  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  // 处理页大小变化
  const handleChangePageSize = (event) => {
    setPageSize(parseInt(event.target.value, 10));
    setPage(0);
  };

  // 处理筛选条件变化
  const handleFilterChange = (field, value) => {
    setFilters(prev => ({
      ...prev,
      [field]: value
    }));
    setPage(0);
  };

  // 处理商户选择变化
  const handleMerchantChange = (event, newValue) => {
    handleFilterChange('merchantID', newValue?.merchantID || '');
  };

  // 重置筛选条件
  const handleResetFilters = () => {
    setFilters({
      merchantID: '',
      logType: '',
      cardNO: '',
      machineID: '',
      machineNO: '',
      lineNO: '',
      logTimeStart: null,
      logTimeEnd: null
    });
    setPage(0);
  };

  // 刷新数据
  const handleRefresh = () => {
    loadLogs(true, true);
  };

  // 格式化日期时间
  const formatDateTime = (dateTime) => {
    if (!dateTime) return '-';
    return new Date(dateTime).toLocaleString('zh-CN');
  };

  // 格式化票价
  const formatPrice = (price) => {
    if (price == null) return '-';
    return `${(price / 100).toFixed(2)}元`;
  };

  // 获取记录类型颜色
  const getLogTypeColor = (logType) => {
    switch (logType) {
      case 10: return 'primary';
      case 12: return 'secondary';
      case 16: return 'warning';
      case 19: return 'success';
      case 20: return 'error';
      default: return 'default';
    }
  };

  return (
    <LocalizationProvider
      dateAdapter={AdapterDateFns}
      adapterLocale={dateFnsZhCN}
    >
      <Container maxWidth={false}>
        <Box sx={{ pt: 3, pb: 3 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            终端日志记录
          </Typography>

          {/* 筛选条件 */}
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Grid container spacing={2} alignItems="center">
                <Grid item xs={12} md={3}>
                  <MerchantAutocomplete
                    value={filters.merchantID ? { merchantID: filters.merchantID } : null}
                    onChange={handleMerchantChange}
                    label="商户"
                    size="small"
                  />
                </Grid>
                
                <Grid item xs={12} md={2}>
                  <TextField
                    select
                    fullWidth
                    size="small"
                    label="记录类型"
                    value={filters.logType}
                    onChange={(e) => handleFilterChange('logType', e.target.value)}
                  >
                    {logTypeOptions.map((option) => (
                      <MenuItem key={option.value} value={option.value}>
                        {option.label}
                      </MenuItem>
                    ))}
                  </TextField>
                </Grid>

                <Grid item xs={12} md={2}>
                  <TextField
                    fullWidth
                    size="small"
                    label="卡号"
                    value={filters.cardNO}
                    onChange={(e) => handleFilterChange('cardNO', e.target.value)}
                  />
                </Grid>

                <Grid item xs={12} md={2}>
                  <TextField
                    fullWidth
                    size="small"
                    label="设备序列号"
                    value={filters.machineID}
                    onChange={(e) => handleFilterChange('machineID', e.target.value)}
                  />
                </Grid>

                <Grid item xs={12} md={2}>
                  <TextField
                    fullWidth
                    size="small"
                    label="设备编号"
                    value={filters.machineNO}
                    onChange={(e) => handleFilterChange('machineNO', e.target.value)}
                  />
                </Grid>

                <Grid item xs={12} md={1}>
                  <TextField
                    fullWidth
                    size="small"
                    label="线路号"
                    value={filters.lineNO}
                    onChange={(e) => handleFilterChange('lineNO', e.target.value)}
                  />
                </Grid>

                <Grid item xs={12} md={3}>
                  <DateTimePicker
                    label="记录时间开始"
                    value={filters.logTimeStart}
                    onChange={(value) => handleFilterChange('logTimeStart', value)}
                    format="yyyy-MM-dd HH:mm"
                    ampm={false}
                    slotProps={{
                      textField: {
                        size: 'small',
                        fullWidth: true
                      }
                    }}
                  />
                </Grid>

                <Grid item xs={12} md={3}>
                  <DateTimePicker
                    label="记录时间结束"
                    value={filters.logTimeEnd}
                    onChange={(value) => handleFilterChange('logTimeEnd', value)}
                    format="yyyy-MM-dd HH:mm"
                    ampm={false}
                    slotProps={{
                      textField: {
                        size: 'small',
                        fullWidth: true
                      }
                    }}
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <Button
                      variant="outlined"
                      onClick={handleResetFilters}
                      size="small"
                    >
                      重置
                    </Button>
                    <Button
                      variant="contained"
                      startIcon={<RefreshIcon />}
                      onClick={handleRefresh}
                      size="small"
                    >
                      刷新
                    </Button>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>

          {/* 数据表格 */}
          <Card>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>商户号</TableCell>
                    <TableCell>商户名称</TableCell>
                    <TableCell>记录类型</TableCell>
                    <TableCell>设置方式</TableCell>
                    <TableCell>卡号</TableCell>
                    <TableCell>设备序列号</TableCell>
                    <TableCell>设备编号</TableCell>
                    <TableCell>线路号</TableCell>
                    <TableCell>票价</TableCell>
                    <TableCell>司机卡号</TableCell>
                    <TableCell>记录时间</TableCell>
                    <TableCell>上传时间</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {loading && (
                    <TableRow>
                      <TableCell colSpan={12} align="center">
                        <CircularProgress />
                      </TableCell>
                    </TableRow>
                  )}
                  {!loading && logs.length === 0 && (
                    <TableRow>
                      <TableCell colSpan={12} align="center">
                        <Typography variant="body2" color="text.secondary">
                          暂无终端日志数据
                        </Typography>
                      </TableCell>
                    </TableRow>
                  )}
                  {!loading && logs.length > 0 && logs.map((log) => (
                    <TableRow key={log.id} hover>
                      <TableCell>{log.merchantID || '-'}</TableCell>
                      <TableCell>{log.merchantName || '-'}</TableCell>
                      <TableCell>
                        <Chip
                          label={log.logTypeName || '-'}
                          color={getLogTypeColor(log.logType)}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>{log.setMethodName || '-'}</TableCell>
                      <TableCell>{log.cardNO || '-'}</TableCell>
                      <TableCell>{log.machineID || '-'}</TableCell>
                      <TableCell>{log.machineNO || '-'}</TableCell>
                      <TableCell>{log.lineNO || '-'}</TableCell>
                      <TableCell>{formatPrice(log.price)}</TableCell>
                      <TableCell>{log.driverCardNO || '-'}</TableCell>
                      <TableCell>{formatDateTime(log.logTime)}</TableCell>
                      <TableCell>{formatDateTime(log.uploadTime)}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>

            <TablePagination
              component="div"
              count={totalCount}
              page={page}
              onPageChange={handleChangePage}
              rowsPerPage={pageSize}
              onRowsPerPageChange={handleChangePageSize}
              rowsPerPageOptions={[10, 20, 50, 100]}
              labelRowsPerPage="每页行数:"
              labelDisplayedRows={({ from, to, count }) => 
                `${from}-${to} 共 ${count !== -1 ? count : `超过 ${to}`} 条`
              }
            />
          </Card>
        </Box>
      </Container>
    </LocalizationProvider>
  );
};

export default TerminalLogs;
