using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using SlzrCrossGate.Core.Services;

namespace SlzrCrossGate.WebAdmin.Services
{
    /// <summary>
    /// 终端日志数据发布服务
    /// 用于向终端日志队列发布消费数据
    /// </summary>
    public class TerminalLogPublishService
    {
        private readonly IRabbitMQService _rabbitMQService;
        private readonly ILogger<TerminalLogPublishService> _logger;
        private readonly string _exchange;

        public TerminalLogPublishService(
            IRabbitMQService rabbitMQService,
            ILogger<TerminalLogPublishService> logger,
            IConfiguration configuration)
        {
            _rabbitMQService = rabbitMQService;
            _logger = logger;
            _exchange = configuration.GetValue<string>("TerminalLogProcessing:Exchange", "SlzrCrossGate.Data")!;
        }

        /// <summary>
        /// 发布消费数据到终端日志队列
        /// </summary>
        /// <param name="consumeData">消费数据</param>
        public async Task PublishConsumeDataAsync(SlzrDatatransferModel.ConsumeData consumeData)
        {
            try
            {
                string routingKey = $"Tcp.city.{0000}.{consumeData.MerchantID}";
                await _rabbitMQService.PublishAsync(_exchange, routingKey, consumeData);
                _logger.LogDebug("成功发布消费数据到终端日志队列。MerchantID: {MerchantID}, MachineID: {MachineID}", 
                    consumeData.MerchantID, consumeData.MachineID);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "发布消费数据到终端日志队列失败。MerchantID: {MerchantID}, MachineID: {MachineID}", 
                    consumeData.MerchantID, consumeData.MachineID);
                throw;
            }
        }

        /// <summary>
        /// 批量发布消费数据到终端日志队列
        /// </summary>
        /// <param name="consumeDataList">消费数据列表</param>
        public async Task PublishConsumeDataBatchAsync(IEnumerable<SlzrDatatransferModel.ConsumeData> consumeDataList)
        {
            var tasks = consumeDataList.Select(PublishConsumeDataAsync);
            await Task.WhenAll(tasks);
        }
    }
}
