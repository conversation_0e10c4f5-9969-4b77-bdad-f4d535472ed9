import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Box } from '@mui/material';
import ExternalPageContainer from '../../components/ExternalPageContainer';
import { menuAPI } from '../../services/api';

/**
 * 外部系统页面组件
 * 根据URL参数中的systemKey从菜单数据中查找对应的外部系统iframe
 */
const ExternalSystemPage = () => {
  const { systemKey } = useParams();
  const navigate = useNavigate();
  const [externalPage, setExternalPage] = useState(null);
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadExternalSystemConfig = async () => {
      console.log('ExternalSystemPage mounted, systemKey:', systemKey);

      if (!systemKey) {
        setError('缺少系统标识参数');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);

        // 从菜单API获取用户菜单数据
        const response = await menuAPI.getUserMenus();
        const menus = response || [];

        // 在所有菜单项中查找匹配的外部系统
        let foundMenuItem = null;
        for (const group of menus) {
          for (const item of group.menuItems) {
            if (item.itemKey === systemKey && item.isExternal) {
              foundMenuItem = item;
              break;
            }
          }
          if (foundMenuItem) break;
        }

        if (!foundMenuItem) {
          console.error('未找到外部系统配置:', systemKey);
          setError(`未找到外部系统配置: ${systemKey}`);
          setLoading(false);
          return;
        }

        // 设置外部系统配置
        const config = {
          url: foundMenuItem.href,
          title: foundMenuItem.title,
          supportAuth: true // 默认支持认证，可以根据需要调整
        };

        console.log('设置外部系统配置:', config);
        setExternalPage(config);
        setLoading(false);

      } catch (error) {
        console.error('加载外部系统配置失败:', error);
        setError('加载外部系统配置失败');
        setLoading(false);
      }
    };

    loadExternalSystemConfig();
  }, [systemKey]);

  const handleClose = () => {
    console.log('关闭外部系统页面，返回仪表盘');
    navigate('/app/dashboard');
  };

  if (error) {
    return (
      <Box sx={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh',
        flexDirection: 'column',
        gap: 2
      }}>
        <h2>加载外部系统失败</h2>
        <p>{error}</p>
        <button onClick={handleClose}>返回仪表盘</button>
      </Box>
    );
  }

  if (loading || !externalPage) {
    return (
      <Box sx={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh'
      }}>
        <p>正在加载外部系统...</p>
      </Box>
    );
  }

  return (
    <Box sx={{ height: '100vh', width: '100%' }}>
      <ExternalPageContainer
        url={externalPage.url}
        title={externalPage.title}
        onClose={handleClose}
        height="100vh"
        supportAuth={externalPage.supportAuth}
        showHeader={false}
      />
    </Box>
  );
};

export default ExternalSystemPage;
