    public class SyncData
    {
        /// <summary>
        /// 数据类型
        /// </summary>
        public DataType Type { get; set; }

        /// <summary>
        /// 文件类型（不为空且长度在3-11位之间，则使用该字段作为文件类型，否则还是使用DataType枚举）
        /// </summary>
        public string FileType { get; set; } = "";

        /// <summary>
        /// 数据版本
        /// </summary>
        public required string Version { get; set; }

        /// <summary>
        /// 数据内容
        /// </summary>
        public required byte[] Content { get; set; }

    }


    public enum DataType
{
    /// <summary>
    /// 平台公钥
    /// </summary>
    SPK = 0,
    /// <summary>
    /// TAC密钥
    /// </summary>
    TAC = 1,
    /// <summary>
    /// 交易文件
    /// </summary>
    TXN = 2,
    /// <summary>
    /// 腾讯二维码黑名单
    /// </summary>
    TBL = 3,
    /// <summary>
    /// 腾讯二维码公钥文件
    /// </summary>
    TPK = 4,
    /// <summary>
    /// 腾讯二维码对账文件
    /// </summary>
    TCL = 5,
    /// <summary>
    /// 腾讯根mac密钥文件
    /// </summary>
    TMC = 6,
    /// <summary>
    /// 银联密钥文件
    /// </summary>
    UPK = 7,
    /// <summary>
    /// 翼支付密钥文件
    /// </summary>
    BPK = 8,
    /// <summary>
    /// 预留密钥文件
    /// </summary>
    RPK = 9,
    /// <summary>
    /// 支付宝
    /// </summary>
    ALK = 10,
    /// <summary>
    /// 两码事密钥文件
    /// </summary>
    LPK = 11,
    /// <summary>
    /// 国朗E通码
    /// </summary>
    EPK= 12,
    /// <summary>
    /// 美团
    /// </summary>
    MPK = 13,
    /// <summary>
    /// 城市码
    /// </summary>
    BJK = 14,
    /// <summary>
    /// 自定义文件类型
    /// </summary>
    FFF = 100
}