# 动态表单控件类型说明

## 支持的控件类型

### 文本输入类型

#### 1. text / textbox
- **用途**：普通文本输入
- **列宽**：160px
- **特点**：适用于一般长度的文本输入

#### 2. shorttext / short_text
- **用途**：短文本输入
- **列宽**：80px
- **特点**：
  - 适用于只需要几个字符的字段（如代码、简称等）
  - 最大长度限制为10个字符
  - 文本居中显示
  - 更紧凑的内边距

### 数字输入类型

#### 3. number
- **用途**：数字输入
- **列宽**：100px
- **特点**：
  - 数字居中显示
  - 支持min、max、step配置
  - 紧凑的内边距

### 选择类型

#### 4. select / dropdown
- **用途**：下拉选择
- **列宽**：140px
- **特点**：需要在controlConfig中配置options

#### 5. radio
- **用途**：单选（在表格中显示为下拉选择）
- **列宽**：140px
- **特点**：需要在controlConfig中配置options

### 开关类型

#### 6. switch / boolean
- **用途**：开关控件
- **列宽**：80px
- **特点**：
  - 显示为开关按钮
  - 居中对齐
  - 最紧凑的列宽

## 使用示例

在字典配置中，设置controlType字段：

```json
{
  "dictionaryCode": "cardCode",
  "dictionaryLabel": "卡类代码",
  "controlType": "shorttext",
  "dictionaryValue": "",
  "controlConfig": null
}
```

```json
{
  "dictionaryCode": "discountRate",
  "dictionaryLabel": "折扣率",
  "controlType": "number",
  "dictionaryValue": "100",
  "controlConfig": "{\"min\": 0, \"max\": 100, \"step\": 1}"
}
```

## 列宽对比

| 控件类型 | 列宽 | 适用场景 |
|---------|------|----------|
| shorttext | 80px | 代码、简称、状态等短文本 |
| switch | 80px | 开关、布尔值 |
| number | 100px | 数字、百分比、金额等 |
| select/radio | 140px | 下拉选择、单选 |
| text | 160px | 一般文本输入 |

## 注意事项

1. shorttext类型会自动限制最大输入长度为10个字符
2. 在表格中，所有控件都会使用紧凑的样式以提高显示密度
3. 建议根据实际数据长度选择合适的控件类型以优化页面布局
