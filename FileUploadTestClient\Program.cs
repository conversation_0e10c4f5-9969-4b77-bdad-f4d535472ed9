using Microsoft.Extensions.Logging;
using FileUploadTestClient;

// 配置日志
using var loggerFactory = LoggerFactory.Create(builder =>
{
    builder
        .AddConsole()
        .SetMinimumLevel(LogLevel.Information);
});

var logger = loggerFactory.CreateLogger<Program>();
var clientLogger = loggerFactory.CreateLogger<TcpFileUploadClient>();

logger.LogInformation("=== TCP文件上传测试客户端 ===");

// 特殊命令：CRC测试
if (args.Length > 0 && args[0].ToLower() == "--test-crc")
{
    TestCRC.TestCRC32Algorithm();
    if (args.Length > 1)
    {
        TestCRC.CreateTestFileAndCalculateCRC(args[1]);
    }
    Console.WriteLine("\n按任意键退出...");
    Console.ReadKey();
    return;
}

// 解析命令行参数
if (args.Length < 1)
{
    logger.LogInformation("用法:");
    logger.LogInformation("  FileUploadTestClient <文件路径> [服务器地址] [端口] [商户号] [终端号] [终端类型]");
    logger.LogInformation("  FileUploadTestClient --test-crc [测试文件路径]");
    logger.LogInformation("");
    logger.LogInformation("参数说明:");
    logger.LogInformation("  文件路径    - 要上传的文件路径");
    logger.LogInformation("  服务器地址  - TCP服务器地址 (默认: localhost)");
    logger.LogInformation("  端口        - TCP服务器端口 (默认: 8001)");
    logger.LogInformation("  商户号      - 商户ID (默认: 00009998)");
    logger.LogInformation("  终端号      - 终端序列号 (默认: ABCD1111)");
    logger.LogInformation("  终端类型    - 终端类型 (默认: POS)");
    logger.LogInformation("");
    logger.LogInformation("特殊命令:");
    logger.LogInformation("  --test-crc  - 测试CRC32算法并创建测试文件");
    logger.LogInformation("");
    logger.LogInformation("示例:");
    logger.LogInformation("  FileUploadTestClient test.log");
    logger.LogInformation("  FileUploadTestClient test.log localhost 8001 00009998 ABCD1111 POS");
    logger.LogInformation("  FileUploadTestClient --test-crc test.log");
    //return;
}

var filePath = args.Length >= 1 ? args[0] : "test.log" ;
var serverHost = args.Length > 1 ? args[1] : "localhost";
var serverPort = args.Length > 2 ? int.Parse(args[2]) : 8001;
var merchantId = args.Length > 3 ? uint.Parse(args[3], System.Globalization.NumberStyles.HexNumber) : 0x00009998;
var terminalId = args.Length > 4 ? uint.Parse(args[4], System.Globalization.NumberStyles.HexNumber) : 0xABCD1111;
var terminalType = args.Length > 5 ? args[5] : "POS";

// 检查文件是否存在
if (!File.Exists(filePath))
{
    logger.LogError("文件不存在: {FilePath}", filePath);
    return;
}

// 如果文件不存在，创建一个测试文件
if (filePath == "test.log" && !File.Exists(filePath))
{
    logger.LogInformation("创建测试文件: {FilePath}", filePath);
    var testContent = $"""
        测试日志文件
        创建时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}
        商户号: {merchantId}
        终端号: {terminalId:X8}
        终端类型: {terminalType}
        
        这是一个用于测试TCP文件上传功能的示例文件。
        文件包含一些中文内容和英文内容。
        
        Test log file content
        This file is used for testing TCP file upload functionality.
        It contains both Chinese and English content.
        
        日志条目1: 系统启动
        日志条目2: 连接建立
        日志条目3: 数据处理
        日志条目4: 交易完成
        日志条目5: 系统关闭
        
        文件结束
        """;
    
    await File.WriteAllTextAsync(filePath, testContent, System.Text.Encoding.UTF8);
    logger.LogInformation("测试文件创建完成，大小: {Size} 字节", new FileInfo(filePath).Length);
}

logger.LogInformation("上传参数:");
logger.LogInformation("  文件路径: {FilePath}", filePath);
logger.LogInformation("  服务器: {Host}:{Port}", serverHost, serverPort);
logger.LogInformation("  商户号: {MerchantId}", merchantId);
logger.LogInformation("  终端号: {TerminalId:X8}", terminalId);
logger.LogInformation("  终端类型: {TerminalType}", terminalType);
logger.LogInformation("");

// 创建客户端并上传文件
using var client = new TcpFileUploadClient(clientLogger);

try
{
    // 连接到服务器
    logger.LogInformation("正在连接到服务器...");
    if (!await client.ConnectAsync(serverHost, serverPort))
    {
        logger.LogError("连接服务器失败");
        return;
    }

    // 上传文件
    logger.LogInformation("开始上传文件...");
    var success = await client.UploadFileAsync(filePath, merchantId, terminalId, terminalType);
    
    if (success)
    {
        logger.LogInformation("✅ 文件上传成功!");
    }
    else
    {
        logger.LogError("❌ 文件上传失败!");
    }
}
catch (Exception ex)
{
    logger.LogError(ex, "上传过程中发生错误");
}
finally
{
    logger.LogInformation("断开连接...");
    client.Disconnect();
}

logger.LogInformation("程序结束");

// 等待用户按键
Console.WriteLine("\n按任意键退出...");
Console.ReadKey();
