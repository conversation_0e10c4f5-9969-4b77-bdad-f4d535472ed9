using FileUploadTestClient.Utils;
using System.Text;

namespace FileUploadTestClient
{
    /// <summary>
    /// CRC32算法测试类
    /// </summary>
    public static class TestCRC
    {
        /// <summary>
        /// 测试CRC32算法是否与服务端一致
        /// </summary>
        public static void TestCRC32Algorithm()
        {
            Console.WriteLine("=== CRC32算法测试 ===");
            
            // 测试用例1：空数据
            var emptyData = Array.Empty<byte>();
            var emptyCrc = CRC32.Calculate(emptyData);
            Console.WriteLine($"空数据 CRC32: {emptyCrc:X8}");
            
            // 测试用例2：简单文本
            var simpleText = "Hello World"u8.ToArray();
            var simpleCrc = CRC32.Calculate(simpleText);
            Console.WriteLine($"'Hello World' CRC32: {simpleCrc:X8}");
            
            // 测试用例3：512字节数据（正好一个块）
            var block512 = new byte[512];
            for (int i = 0; i < 512; i++)
            {
                block512[i] = (byte)(i % 256);
            }
            var block512Crc = CRC32.Calculate(block512);
            Console.WriteLine($"512字节测试数据 CRC32: {block512Crc:X8}");
            
            // 测试用例4：1024字节数据（两个块）
            var block1024 = new byte[1024];
            for (int i = 0; i < 1024; i++)
            {
                block1024[i] = (byte)(i % 256);
            }
            var block1024Crc = CRC32.Calculate(block1024);
            Console.WriteLine($"1024字节测试数据 CRC32: {block1024Crc:X8}");
            
            // 测试用例5：1000字节数据（1个完整块+488字节）
            var block1000 = new byte[1000];
            for (int i = 0; i < 1000; i++)
            {
                block1000[i] = (byte)(i % 256);
            }
            var block1000Crc = CRC32.Calculate(block1000);
            Console.WriteLine($"1000字节测试数据 CRC32: {block1000Crc:X8}");
            
            // 测试用例6：中文文本
            var chineseText = Encoding.UTF8.GetBytes("你好世界，这是一个测试文件。");
            var chineseCrc = CRC32.Calculate(chineseText);
            Console.WriteLine($"中文文本 CRC32: {chineseCrc:X8}");
            
            Console.WriteLine("=== CRC32算法测试完成 ===");
        }
        
        /// <summary>
        /// 创建测试文件并计算CRC
        /// </summary>
        public static void CreateTestFileAndCalculateCRC(string filePath)
        {
            Console.WriteLine($"\n=== 创建测试文件: {filePath} ===");
            
            var testContent = $"""
                测试日志文件
                创建时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}
                
                这是一个用于测试TCP文件上传功能的示例文件。
                文件包含一些中文内容和英文内容。
                
                Test log file content
                This file is used for testing TCP file upload functionality.
                It contains both Chinese and English content.
                
                日志条目1: 系统启动
                日志条目2: 连接建立
                日志条目3: 数据处理
                日志条目4: 交易完成
                日志条目5: 系统关闭
                
                文件结束
                """;
            
            File.WriteAllText(filePath, testContent, Encoding.UTF8);
            
            var fileInfo = new FileInfo(filePath);
            var fileCrc = CRC32.CalculateFile(filePath);
            
            Console.WriteLine($"文件大小: {fileInfo.Length} 字节");
            Console.WriteLine($"文件CRC32: {fileCrc:X8}");
            Console.WriteLine($"测试文件已创建: {filePath}");
        }
    }
}
