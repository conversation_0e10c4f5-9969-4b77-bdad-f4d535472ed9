import React from 'react';
import { NavLink as RouterLink, useNavigate, useLocation } from 'react-router-dom';
import PropTypes from 'prop-types';
import {
  Button,
  ListItem,
  Box,
  Tooltip,
  alpha,
  useTheme as useMuiTheme
} from '@mui/material';
import { useTheme } from '../contexts/ThemeContext';

const NavItem = ({
  href,
  icon: Icon,
  title,
  itemKey,
  isCollapsed = false,
  isExternal = false,
  target = '_self',
  ...rest
}) => {
  const muiTheme = useMuiTheme();
  const { mode, theme } = useTheme();
  const navigate = useNavigate();
  const location = useLocation();

  // 检查当前路由是否匹配
  const isActive = () => {
    if (isExternal && itemKey) {
      // 外部系统路由匹配
      return location.pathname === `/app/external/${itemKey}`;
    }
    // 普通路由匹配
    return location.pathname === href;
  };

  // 处理外部链接的点击事件
  const handleExternalClick = (e) => {
    console.log('NavItem click:', { isExternal, target, href, title, itemKey });
    if (isExternal) {
      e.preventDefault(); // 阻止默认的链接跳转
      if (target === '_iframe' || target === '_iframe_simple') {
        console.log('Opening in iframe:', { url: href, title, itemKey, supportAuth: target === '_iframe' });

        // 使用新的路由结构：/app/external/{itemKey}
        if (itemKey) {
          console.log('导航到外部系统页面:', `/app/external/${itemKey}`);
          navigate(`/app/external/${itemKey}`);
        } else {
          console.warn('缺少itemKey，无法导航到外部系统页面');
          // 降级处理：使用旧的方式
          window.postMessage({
            type: 'OPEN_EXTERNAL_PAGE',
            url: href,
            title: title,
            supportAuth: target === '_iframe'
          }, '*');
        }
      } else {
        console.log('Opening in new window/tab:', { url: href, target });
        // 在新窗口或当前窗口打开
        window.open(href, target);
      }
    }
  };

  const navButton = (
    <Button
      component={isExternal ? 'a' : RouterLink}
      onClick={isExternal ? handleExternalClick : undefined}
      className={isActive() ? 'active' : ''}
      href={isExternal && itemKey ? `/app/external/${itemKey}` : undefined}
      to={!isExternal ? href : undefined}
      sx={{
        color: 'text.secondary',
        fontWeight: 'medium',
        justifyContent: isCollapsed ? 'center' : 'flex-start',
        letterSpacing: 0,
        py: 1.5,
        textTransform: 'none',
        width: '100%',
        borderRadius: 2,
        position: 'relative',
        overflow: 'hidden',
        transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
        '& svg': {
          mr: isCollapsed ? 0 : 1,
          transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
        },
        '&.active': {
          color: mode === 'dark' ? theme.palette.primary.light : theme.palette.primary.main,
          backgroundColor: mode === 'dark'
            ? alpha(theme.palette.primary.main, 0.15)
            : alpha(theme.palette.primary.main, 0.08),
          backdropFilter: 'blur(4px)',
          boxShadow: mode === 'dark'
            ? `0 0 10px ${alpha(theme.palette.primary.main, 0.3)}`
            : `0 0 10px ${alpha(theme.palette.primary.main, 0.1)}`,
          '&::before': {
            content: '""',
            position: 'absolute',
            left: 0,
            top: 0,
            bottom: 0,
            width: 4,
            backgroundColor: mode === 'dark' ? theme.palette.primary.light : theme.palette.primary.main,
            boxShadow: mode === 'dark'
              ? `0 0 8px ${theme.palette.primary.main}`
              : `0 0 8px ${alpha(theme.palette.primary.main, 0.5)}`,
          },
          '& svg': {
            color: mode === 'dark' ? theme.palette.primary.light : theme.palette.primary.main,
            transform: 'scale(1.1)',
          }
        },
        '&:hover': {
          backgroundColor: mode === 'dark'
            ? alpha(theme.palette.primary.main, 0.08)
            : alpha(theme.palette.primary.main, 0.04),
          transform: 'translateY(-1px)',
          '& svg': {
            transform: 'scale(1.1)',
          }
        },
        '&:active': {
          transform: 'scale(0.98)',
        },
        minWidth: 0,
        px: isCollapsed ? 1 : 2
      }}
    >
      {Icon && (
        <Box
          component="span"
          sx={{
            display: 'flex',
            alignItems: 'center',
            mr: isCollapsed ? 0 : 1,
            color: 'inherit',
          }}
        >
          <Icon size="20" />
        </Box>
      )}
      {!isCollapsed && (
        <Box
          component="span"
          sx={{
            fontSize: '0.875rem',
            fontWeight: 500,
            color: 'inherit',
          }}
        >
          {title}
        </Box>
      )}
    </Button>
  );

  return (
    <ListItem
      disableGutters
      sx={{
        display: 'flex',
        py: 0.25,
        px: 1,
      }}
      {...rest}
    >
      {isCollapsed ? (
        <Tooltip
          title={title}
          placement="right"
          arrow
          componentsProps={{
            tooltip: {
              sx: {
                backdropFilter: 'blur(8px)',
                backgroundColor: mode === 'dark'
                  ? 'rgba(255, 255, 255, 0.9)'
                  : 'rgba(0, 0, 0, 0.75)',
                color: mode === 'dark' ? '#121212' : '#FFFFFF',
                boxShadow: '0px 4px 16px rgba(0, 0, 0, 0.2)',
                border: mode === 'dark'
                  ? '1px solid rgba(255, 255, 255, 0.2)'
                  : '1px solid rgba(255, 255, 255, 0.08)',
              }
            }
          }}
        >
          {navButton}
        </Tooltip>
      ) : (
        navButton
      )}
    </ListItem>
  );
};

NavItem.propTypes = {
  href: PropTypes.string,
  icon: PropTypes.elementType,
  title: PropTypes.string,
  isCollapsed: PropTypes.bool,
  isExternal: PropTypes.bool,
  target: PropTypes.string
};

// 使用 JavaScript 默认参数代替 defaultProps

export default NavItem;
