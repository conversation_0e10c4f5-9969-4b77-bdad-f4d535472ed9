using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using SlzrCrossGate.Core.Database;
using SlzrCrossGate.Core.Models;
using SlzrCrossGate.WebAdmin.DTOs;


namespace SlzrCrossGate.WebAdmin.Controllers
{
    /// <summary>
    /// 终端日志记录控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class TerminalLogController : ControllerBase
    {
        private readonly TcpDbContext _context;
        private readonly ILogger<TerminalLogController> _logger;

        public TerminalLogController(TcpDbContext context, ILogger<TerminalLogController> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// 获取终端日志记录列表
        /// </summary>
        /// <param name="query">查询参数</param>
        /// <returns>分页的终端日志记录列表</returns>
        [HttpGet]
        public async Task<ActionResult<TerminalLogPagedResultDto>> GetTerminalLogs([FromQuery] TerminalLogQueryDto query)
        {
            try
            {
                // 检查是否为临时令牌
                if (HttpContext.Items.ContainsKey("IsTemporaryToken") && (bool)HttpContext.Items["IsTemporaryToken"])
                {
                    return Unauthorized("临时令牌无法访问此功能");
                }

                // 获取当前用户信息
                var currentUser = await GetCurrentUserAsync();
                if (currentUser == null)
                {
                    return Unauthorized("用户信息无效");
                }

                // 构建查询
                var queryable = _context.TerminalLogs.AsQueryable();

                // 权限过滤：非系统管理员只能查看自己商户的数据
                if (!await IsSystemAdminAsync(currentUser))
                {
                    var userMerchantId = currentUser.MerchantID;
                    if (string.IsNullOrEmpty(userMerchantId))
                    {
                        return BadRequest("用户未关联商户");
                    }
                    queryable = queryable.Where(x => x.MerchantID == userMerchantId);
                }

                // 应用筛选条件
                if (!string.IsNullOrEmpty(query.MerchantID))
                {
                    queryable = queryable.Where(x => x.MerchantID == query.MerchantID);
                }

                if (query.LogType.HasValue)
                {
                    queryable = queryable.Where(x => x.LogType == query.LogType.Value);
                }

                if (!string.IsNullOrEmpty(query.CardNO))
                {
                    queryable = queryable.Where(x => x.CardNO != null && x.CardNO.Contains(query.CardNO));
                }

                if (!string.IsNullOrEmpty(query.MachineID))
                {
                    queryable = queryable.Where(x => x.MachineID != null && x.MachineID.Contains(query.MachineID));
                }

                if (!string.IsNullOrEmpty(query.MachineNO))
                {
                    queryable = queryable.Where(x => x.MachineNO != null && x.MachineNO.Contains(query.MachineNO));
                }

                if (!string.IsNullOrEmpty(query.LineNO))
                {
                    queryable = queryable.Where(x => x.LineNO != null && x.LineNO.Contains(query.LineNO));
                }

                if (query.LogTimeStart.HasValue)
                {
                    queryable = queryable.Where(x => x.LogTime >= query.LogTimeStart.Value);
                }

                if (query.LogTimeEnd.HasValue)
                {
                    queryable = queryable.Where(x => x.LogTime <= query.LogTimeEnd.Value);
                }

                // 获取总记录数
                var totalCount = await queryable.CountAsync();

                // 排序 - 默认按记录时间倒序
                var sortDirection = !string.IsNullOrEmpty(query.SortDirection) ? query.SortDirection : "desc";
                if (sortDirection.ToLower() == "desc")
                {
                    queryable = queryable.OrderByDescending(x => x.LogTime);
                }
                else
                {
                    queryable = queryable.OrderBy(x => x.LogTime);
                }

                // 分页查询
                var logs = await queryable
                    .Skip(query.Page * query.PageSize)
                    .Take(query.PageSize)
                    .ToListAsync();

                // 获取商户信息
                var merchantIds = logs.Where(x => !string.IsNullOrEmpty(x.MerchantID))
                                     .Select(x => x.MerchantID!)
                                     .Distinct()
                                     .ToList();

                var merchants = await _context.Merchants
                    .Where(m => merchantIds.Contains(m.MerchantID))
                    .ToDictionaryAsync(m => m.MerchantID, m => m.Name);

                // 转换为DTO
                var logDtos = logs.Select(log => new TerminalLogDto
                {
                    ID = log.ID,
                    MerchantID = log.MerchantID,
                    MerchantName = !string.IsNullOrEmpty(log.MerchantID) && merchants.ContainsKey(log.MerchantID) 
                                  ? merchants[log.MerchantID] : "",
                    LogType = log.LogType,
                    LogTypeName = GetLogTypeName(log.LogType),
                    SetMethod = log.SetMethod,
                    SetMethodName = GetSetMethodName(log.SetMethod),
                    CardNO = log.CardNO,
                    MachineID = log.MachineID,
                    MachineNO = log.MachineNO,
                    LineNO = log.LineNO,
                    Price = log.Price,
                    DriverCardNO = log.DriverCardNO,
                    LogTime = log.LogTime,
                    UploadTime = log.UploadTime
                }).ToList();

                var result = new TerminalLogPagedResultDto
                {
                    Items = logDtos,
                    TotalCount = totalCount,
                    TotalPages = (int)Math.Ceiling((double)totalCount / query.PageSize),
                    CurrentPage = query.Page,
                    PageSize = query.PageSize
                };

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取终端日志记录失败");
                return StatusCode(500, "获取终端日志记录失败");
            }
        }

        /// <summary>
        /// 获取记录类型名称
        /// </summary>
        private static string GetLogTypeName(int? logType)
        {
            return logType switch
            {
                10 => "司机卡",
                12 => "线路设置",
                16 => "设备设置",
                19 => "发车卡",
                20 => "到站卡",
                _ => logType?.ToString() ?? ""
            };
        }

        /// <summary>
        /// 获取设置方式名称
        /// </summary>
        private static string GetSetMethodName(int? setMethod)
        {
            return setMethod switch
            {
                0x01 => "M1卡",
                0x02 => "CPU卡",
                0x0B => "交通卡",
                0xA0 => "无线",
                0xA1 => "调度",
                _ => setMethod?.ToString("X") ?? ""
            };
        }

        /// <summary>
        /// 获取当前用户
        /// </summary>
        private async Task<ApplicationUser?> GetCurrentUserAsync()
        {
            var userId = User.FindFirst("sub")?.Value ?? User.FindFirst("id")?.Value;
            if (string.IsNullOrEmpty(userId))
            {
                return null;
            }

            return await _context.Users.FirstOrDefaultAsync(u => u.Id == userId);
        }

        /// <summary>
        /// 检查是否为系统管理员
        /// </summary>
        private async Task<bool> IsSystemAdminAsync(ApplicationUser user)
        {
            var userRoles = await _context.UserRoles
                .Where(ur => ur.UserId == user.Id)
                .Select(ur => ur.RoleId)
                .ToListAsync();

            var roles = await _context.Roles
                .Where(r => userRoles.Contains(r.Id))
                .Select(r => r.Name)
                .ToListAsync();

            return roles.Contains("SystemAdmin");
        }
    }
}
