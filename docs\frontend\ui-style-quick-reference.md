# WebAdmin样式规范快速参考

## 🚀 必须遵循的核心规范

### 1. 页面容器
```javascript
// ✅ 正确
<Container maxWidth={false}>
  <Box sx={{ pt: 3, pb: 3 }}>

// ❌ 错误  
<Container maxWidth="xl">
```

### 2. 字体大小
```javascript
// ✅ 正确 - 使用标准variant
<Typography variant="h4">页面标题</Typography>
<Typography variant="subtitle2">卡片标题</Typography>
<Typography variant="body2">正文内容</Typography>
<Typography variant="caption">说明文字</Typography>

// ❌ 错误 - 手动设置fontSize
<Typography sx={{ fontSize: '14px' }}>文字</Typography>
```

### 3. 响应式布局
```javascript
// ✅ 正确 - 合理的断点设置
<Grid item xs={12} sm={6} md={4} lg={3}>

// ❌ 错误 - 不合理的断点
<Grid item xs={12} sm={12} md={6}>
```

### 4. 颜色使用
```javascript
// ✅ 正确 - 使用主题颜色
color="primary"
color="text.secondary"
sx={{ color: 'error.main' }}

// ❌ 错误 - 硬编码颜色
sx={{ color: '#666666' }}
```

## 📐 常用布局模板

### 页面标题区域
```javascript
<Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
  <Typography variant="h4" component="h1" sx={{ fontWeight: 'bold' }}>
    页面标题
  </Typography>
  <Box sx={{ display: 'flex', gap: 1 }}>
    <Button variant="outlined">刷新</Button>
    <Button variant="contained">新增</Button>
  </Box>
</Box>
```

### 搜索筛选区域
```javascript
<Paper sx={{ p: 2, mb: 3 }}>
  <Grid container spacing={2} alignItems="center">
    <Grid item xs={12} md={6}>
      <TextField fullWidth placeholder="搜索..." size="small" />
    </Grid>
    <Grid item xs={12} md={4}>
      <FormControl fullWidth size="small">
        <InputLabel>筛选</InputLabel>
        <Select>{/* 选项 */}</Select>
      </FormControl>
    </Grid>
  </Grid>
</Paper>
```

### 响应式卡片
```javascript
<Grid item xs={12} sm={6} md={4} lg={3}>
  <Paper variant="outlined" sx={{ p: 2, height: '100%' }}>
    <Typography variant="subtitle2" fontWeight="bold" gutterBottom>
      标题
    </Typography>
    <Typography variant="body2" color="text.secondary">
      描述内容
    </Typography>
  </Paper>
</Grid>
```

## 🎨 字体层次对照表

| 用途 | Variant | 大小 | 使用场景 |
|------|---------|------|----------|
| 页面标题 | `h4` | 2.125rem | 页面主标题 |
| 分组标题 | `h6` | 1.25rem | 卡片/分组标题 |
| 重要子标题 | `subtitle1` | 1rem | 重要子标题 |
| 次要子标题 | `subtitle2` | 0.875rem | 次要子标题 |
| 主要正文 | `body1` | 1rem | 主要内容 |
| 次要正文 | `body2` | 0.875rem | 次要内容 |
| 说明文字 | `caption` | 0.75rem | 提示说明 |

## 🔧 组件规范

### 按钮
```javascript
<Button variant="contained" size="small">主要操作</Button>
<Button variant="outlined" size="small">次要操作</Button>
<IconButton size="small">
  <EditIcon fontSize="small" />
</IconButton>
```

### 开关控件
```javascript
<FormControlLabel
  control={<Switch size="small" />}
  label="开关标签"
/>
```

### 状态标签
```javascript
<Chip label="成功" size="small" color="success" />
<Chip label="禁止" size="small" variant="outlined" color="default" />
```

## 📱 响应式断点

| 断点 | 屏幕尺寸 | 建议列数 |
|------|----------|----------|
| `xs` | <600px | 1列 |
| `sm` | 600-960px | 2列 |
| `md` | 960-1280px | 3列 |
| `lg` | >1280px | 4列 |

## ⚡ 检查工具

### 运行样式检查
```bash
# 样式规范检查
npm run style-check

# ESLint检查
npm run lint
```

### 常见错误检查清单
- [ ] Container使用 `maxWidth={false}`
- [ ] Typography都有variant属性
- [ ] 没有手动设置fontSize
- [ ] 使用主题颜色而非硬编码
- [ ] 响应式断点设置合理
- [ ] 卡片使用 `height: '100%'`

## 🔗 相关文档

- [完整样式规范](./ui-style-guide.md)
- [前端开发规范](../frontend-development.md)
- [Material-UI文档](https://mui.com/)

---
**记住**：规范是强制性的，所有新页面都必须遵循！
