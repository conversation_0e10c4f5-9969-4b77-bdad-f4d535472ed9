import React from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Button,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  User as UserIcon,
  Settings as SettingsIcon,
  Activity as ActivityIcon,
  Star as StarIcon,
  Sun as SunIcon,
  Moon as MoonIcon
} from 'react-feather';
import { useNavigate } from 'react-router-dom';
import { useTheme } from '../contexts/ThemeContext';

const HomePage = ({ userInfo }) => {
  const navigate = useNavigate();
  const { mode, toggleTheme } = useTheme();

  const features = [
    {
      title: '定制功能A',
      description: '这是为客户专门开发的功能A，提供特殊的业务处理能力',
      icon: <SettingsIcon size={24} />,
      path: '/custom-feature',
      color: 'primary'
    },
    {
      title: '数据分析',
      description: '客户专属的数据分析和报表功能',
      icon: <ActivityIcon size={24} />,
      path: '/analytics',
      color: 'secondary'
    },
    {
      title: '高级配置',
      description: '针对客户业务需求的高级配置选项',
      icon: <StarIcon size={24} />,
      path: '/advanced-config',
      color: 'success'
    }
  ];

  const recentActivities = [
    { text: '系统初始化完成', time: '2分钟前', type: 'success' },
    { text: '用户认证成功', time: '5分钟前', type: 'info' },
    { text: '数据同步完成', time: '10分钟前', type: 'success' },
    { text: '配置更新', time: '1小时前', type: 'warning' }
  ];

  return (
    <Box sx={{ p: 3 }}>
      {/* 顶部工具栏 */}
      <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>
        <Tooltip title={`切换到${mode === 'dark' ? '亮色' : '暗色'}模式`}>
          <IconButton onClick={toggleTheme} color="primary">
            {mode === 'dark' ? <SunIcon size={20} /> : <MoonIcon size={20} />}
          </IconButton>
        </Tooltip>
      </Box>

      {/* 欢迎区域 */}
      <Paper
        sx={{
          p: 4,
          mb: 4,
          background: 'linear-gradient(135deg, #7E22CE 0%, #A855F7 100%)',
          color: 'white',
          borderRadius: 3
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <UserIcon size={32} style={{ marginRight: 16 }} />
          <Box sx={{ flex: 1 }}>
            <Typography variant="h4" component="h1" gutterBottom>
              欢迎，{userInfo?.userName || '用户'}！
            </Typography>
            <Typography variant="h6" sx={{ opacity: 0.9 }}>
              客户定制功能中心 - 模板项目
            </Typography>
          </Box>
          <Chip
            label={`${mode === 'dark' ? '暗色' : '亮色'}模式`}
            variant="outlined"
            sx={{
              color: 'white',
              borderColor: 'rgba(255,255,255,0.5)',
              backgroundColor: 'rgba(255,255,255,0.1)'
            }}
          />
        </Box>
        <Typography variant="body1" sx={{ opacity: 0.8 }}>
          这里是专为您定制开发的功能模块，提供专业的业务解决方案。当前使用玻璃拟态+微立体感设计主题。
        </Typography>
      </Paper>

      <Grid container spacing={3}>
        {/* 功能卡片 */}
        <Grid item xs={12} md={8}>
          <Typography variant="h5" gutterBottom sx={{ mb: 3 }}>
            定制功能
          </Typography>
          <Grid container spacing={2}>
            {features.map((feature, index) => (
              <Grid item xs={12} sm={6} key={index}>
                <Card
                  sx={{
                    height: '100%',
                    cursor: 'pointer',
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      boxShadow: 4
                    }
                  }}
                  onClick={() => navigate(feature.path)}
                >
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <Box
                        sx={{
                          p: 1,
                          borderRadius: 2,
                          bgcolor: `${feature.color}.light`,
                          color: `${feature.color}.contrastText`,
                          mr: 2
                        }}
                      >
                        {feature.icon}
                      </Box>
                      <Typography variant="h6" component="h2">
                        {feature.title}
                      </Typography>
                    </Box>
                    <Typography variant="body2" color="text.secondary">
                      {feature.description}
                    </Typography>
                    <Button
                      variant="outlined"
                      color={feature.color}
                      size="small"
                      sx={{ mt: 2 }}
                      onClick={(e) => {
                        e.stopPropagation();
                        navigate(feature.path);
                      }}
                    >
                      了解更多
                    </Button>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Grid>

        {/* 侧边栏信息 */}
        <Grid item xs={12} md={4}>
          {/* 用户信息 */}
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                用户信息
              </Typography>
              <List dense>
                <ListItem>
                  <ListItemText
                    primary="用户名"
                    secondary={userInfo?.userName || '未知'}
                  />
                </ListItem>
                <ListItem>
                  <ListItemText
                    primary="角色"
                    secondary={userInfo?.roles?.join(', ') || '无角色'}
                  />
                  {userInfo?.roles && userInfo.roles.length > 0 && (
                    <Box sx={{ mt: 1 }}>
                      {userInfo.roles.map((role, index) => (
                        <Chip
                          key={index}
                          label={role}
                          size="small"
                          color="primary"
                          variant="outlined"
                          sx={{ mr: 0.5, mb: 0.5 }}
                        />
                      ))}
                    </Box>
                  )}
                </ListItem>
                <ListItem>
                  <ListItemText
                    primary="商户"
                    secondary={userInfo?.merchantName || '未知'}
                  />
                </ListItem>
              </List>
            </CardContent>
          </Card>

          {/* 最近活动 */}
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                最近活动
              </Typography>
              <List dense>
                {recentActivities.map((activity, index) => (
                  <ListItem key={index}>
                    <ListItemIcon>
                      <Box
                        sx={{
                          width: 8,
                          height: 8,
                          borderRadius: '50%',
                          bgcolor: `${activity.type}.main`
                        }}
                      />
                    </ListItemIcon>
                    <ListItemText
                      primary={activity.text}
                      secondary={activity.time}
                      primaryTypographyProps={{ fontSize: '0.875rem' }}
                      secondaryTypographyProps={{ fontSize: '0.75rem' }}
                    />
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default HomePage;
