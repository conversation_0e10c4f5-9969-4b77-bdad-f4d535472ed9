{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"AuthConnection": "Server=localhost;Database=tcpserver;Uid=customer_readonly;Pwd=readonly_password_2024!;", "CustomerConnection": "Server=localhost;Database=tcpserver;Uid=customer_template;Pwd=template_password_2024!;"}, "Jwt": {"Key": "your-secret-key-here-must-be-at-least-32-characters-long", "Issuer": "SlzrCrossGate", "Audience": "SlzrCrossGate.Users"}, "MainApp": {"BaseUrl": "http://localhost:5270"}, "Customer": {"Name": "template", "DisplayName": "客户模板", "TablePrefix": "Customer_Template", "BasePath": "/customer"}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/customer-.log", "rollingInterval": "Day", "retainedFileCountLimit": 7, "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"]}}