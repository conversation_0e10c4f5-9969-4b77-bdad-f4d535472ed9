import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  CircularProgress,
  Alert,
  Chip
} from '@mui/material';
import {
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { zhCN } from '@mui/x-date-pickers/locales';
import axios from 'axios';

const HomePage = ({ userInfo }) => {
  const [queryDate, setQueryDate] = useState(new Date());
  const [ftpStatsResponse, setFtpStatsResponse] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // 加载FTP文件上传统计
  const loadFtpStats = async (date = queryDate) => {
    try {
      setLoading(true);
      setError(null);

      const response = await axios.get('/api/customa/FtpStats', {
        params: {
          queryDate: date.toISOString().split('T')[0]
        }
      });

      console.log('API响应数据:', response.data);
      console.log('uploadTargets类型:', typeof response.data.uploadTargets);
      console.log('uploadTargets是否为数组:', Array.isArray(response.data.uploadTargets));
      setFtpStatsResponse(response.data);
    } catch (err) {
      console.error('加载FTP统计失败:', err);
      setError(err.response?.data?.message || '加载数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 页面加载时自动查询
  useEffect(() => {
    loadFtpStats();
  }, []);

  // 处理查询按钮点击
  const handleQuery = () => {
    loadFtpStats(queryDate);
  };

  // 处理刷新按钮点击
  const handleRefresh = () => {
    loadFtpStats();
  };

  return (
    <LocalizationProvider
      dateAdapter={AdapterDateFns}
      localeText={zhCN.components.MuiLocalizationProvider.defaultProps.localeText}
    >
      <Box sx={{ p: 3 }}>
        {/* 页面标题 - 简化版本 */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h4" gutterBottom sx={{ fontWeight: 600 }}>
            FTP文件上传统计
          </Typography>
        </Box>

        {/* 查询控制区域 */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
              查询条件
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, flexWrap: 'wrap' }}>
              <DatePicker
                label="查询日期"
                value={queryDate}
                onChange={(newValue) => setQueryDate(newValue)}
                format="yyyy-MM-dd"
                slotProps={{
                  textField: {
                    size: "small"
                  }
                }}
              />
              <Button
                variant="contained"
                onClick={handleQuery}
                disabled={loading}
                startIcon={loading ? <CircularProgress size={16} /> : <RefreshIcon />}
              >
                {loading ? '查询中...' : '查询'}
              </Button>
              <Button
                variant="outlined"
                onClick={handleRefresh}
                disabled={loading}
                startIcon={<RefreshIcon />}
              >
                刷新
              </Button>
            </Box>
          </CardContent>
        </Card>

        {/* 错误提示 */}
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {/* 统计结果表格 */}
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
              统计结果
            </Typography>
            {loading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
                <CircularProgress />
              </Box>
            ) : (
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell sx={{ fontWeight: 600 }}>上传目标</TableCell>
                      <TableCell align="center" sx={{ fontWeight: 600 }}>次序</TableCell>
                      <TableCell align="center" sx={{ fontWeight: 600 }}>312文件待上传数</TableCell>
                      <TableCell align="center" sx={{ fontWeight: 600 }}>315文件待上传数</TableCell>
                      <TableCell sx={{ fontWeight: 600 }}>312源目录</TableCell>
                      <TableCell sx={{ fontWeight: 600 }}>312备份目录</TableCell>
                      <TableCell sx={{ fontWeight: 600 }}>315源目录</TableCell>
                      <TableCell sx={{ fontWeight: 600 }}>315备份目录</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {!ftpStatsResponse || !ftpStatsResponse.uploadTargets || ftpStatsResponse.uploadTargets.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={8} align="center" sx={{ py: 4 }}>
                          <Typography color="text.secondary">
                            暂无数据
                          </Typography>
                        </TableCell>
                      </TableRow>
                    ) : (
                      ftpStatsResponse.uploadTargets.map((stat, index) => (
                        <TableRow key={index} hover>
                          <TableCell>
                            <Chip
                              label={stat.uploadTarget}
                              color="primary"
                              variant="outlined"
                              size="small"
                            />
                          </TableCell>
                          <TableCell align="center">{stat.upOrder}</TableCell>
                          <TableCell align="center">
                            <Chip
                              label={stat.file312PendingCount}
                              color={stat.file312PendingCount > 0 ? "warning" : "default"}
                              size="small"
                            />
                          </TableCell>
                          <TableCell align="center">
                            <Chip
                              label={stat.file315PendingCount}
                              color={stat.file315PendingCount > 0 ? "warning" : "default"}
                              size="small"
                            />
                          </TableCell>
                          <TableCell>
                            <Typography
                              variant="body2"
                              sx={{
                                fontFamily: 'monospace',
                                fontSize: '0.75rem',
                                color: 'text.secondary',
                                maxWidth: 200,
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                whiteSpace: 'nowrap',
                              }}
                              title={stat.originPath312 || '-'}
                            >
                              {stat.originPath312 || '-'}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Typography
                              variant="body2"
                              sx={{
                                fontFamily: 'monospace',
                                fontSize: '0.75rem',
                                color: 'text.secondary',
                                maxWidth: 200,
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                whiteSpace: 'nowrap',
                              }}
                              title={stat.localBakPath312 || '-'}
                            >
                              {stat.localBakPath312 || '-'}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Typography
                              variant="body2"
                              sx={{
                                fontFamily: 'monospace',
                                fontSize: '0.75rem',
                                color: 'text.secondary',
                                maxWidth: 200,
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                whiteSpace: 'nowrap',
                              }}
                              title={stat.originPath315 || '-'}
                            >
                              {stat.originPath315 || '-'}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Typography
                              variant="body2"
                              sx={{
                                fontFamily: 'monospace',
                                fontSize: '0.75rem',
                                color: 'text.secondary',
                                maxWidth: 200,
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                whiteSpace: 'nowrap',
                              }}
                              title={stat.localBakPath315 || '-'}
                            >
                              {stat.localBakPath315 || '-'}
                            </Typography>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </TableContainer>
            )}
          </CardContent>
        </Card>

        {/* 汇总信息 */}
        {ftpStatsResponse && (
          <Card sx={{ mt: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ fontWeight: 600, mb: 2 }}>
                上传成功统计（按日期）
              </Typography>
              <Box sx={{ display: 'flex', gap: 3, flexWrap: 'wrap' }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Typography variant="body2" color="text.secondary">
                    查询日期：
                  </Typography>
                  <Chip
                    label={new Date(ftpStatsResponse.queryDate).toLocaleDateString('zh-CN')}
                    color="info"
                    variant="outlined"
                    size="small"
                  />
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Typography variant="body2" color="text.secondary">
                    312文件上传成功数：
                  </Typography>
                  <Chip
                    label={ftpStatsResponse.file312SuccessCount}
                    color={ftpStatsResponse.file312SuccessCount > 0 ? "success" : "default"}
                    size="small"
                  />
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Typography variant="body2" color="text.secondary">
                    315文件上传成功数：
                  </Typography>
                  <Chip
                    label={ftpStatsResponse.file315SuccessCount}
                    color={ftpStatsResponse.file315SuccessCount > 0 ? "success" : "default"}
                    size="small"
                  />
                </Box>
              </Box>
            </CardContent>
          </Card>
        )}

      </Box>
    </LocalizationProvider>
  );
};

export default HomePage;
