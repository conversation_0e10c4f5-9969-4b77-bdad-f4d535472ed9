﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.13.35913.81
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SlzrCrossGate.AppHost", "SlzrCrossGate.AppHost\SlzrCrossGate.AppHost.csproj", "{63268CB0-E3A1-4634-A76A-EBF3B9747EDA}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SlzrCrossGate.ServiceDefaults", "SlzrCrossGate.ServiceDefaults\SlzrCrossGate.ServiceDefaults.csproj", "{ADE5E3B2-DB93-0354-1B9C-060268D7813F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SlzrCrossGate.WebAdmin", "SlzrCrossGate.WebAdmin\SlzrCrossGate.WebAdmin.csproj", "{8646EA65-686D-3EAC-B2EB-CC9E21F7E345}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SlzrCrossGate.Core", "SlzrCrossGate.Core\SlzrCrossGate.Core.csproj", "{36577686-8D32-43E7-ADE4-539DE48A11C8}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SlzrCrossGate.Tcp", "SlzrCrossGate.Tcp\SlzrCrossGate.Tcp.csproj", "{BBFDF433-B887-475E-B21C-F3F04D668195}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SlzrCrossGate.ApiService", "SlzrCrossGate.ApiService\SlzrCrossGate.ApiService.csproj", "{5C157202-40CB-7F5F-06E8-0CC54131056F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SlzrCrossGate.Common", "SlzrCrossGate.Common\SlzrCrossGate.Common.csproj", "{00E8D372-C2ED-4CA3-8581-68776DA35E67}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "解决方案项", "解决方案项", "{9FA3D6BD-1EC1-3BA5-80CB-CE02773A58D5}"
	ProjectSection(SolutionItems) = preProject
		.editorconfig = .editorconfig
		.env = .env
		add_system_settings.sql = add_system_settings.sql
		build-api.sh = build-api.sh
		build-step-by-step.ps1 = build-step-by-step.ps1
		build-step-by-step.sh = build-step-by-step.sh
		build-web.sh = build-web.sh
		deployment-guide.md = deployment-guide.md
		docker-compose-registry.yml = docker-compose-registry.yml
		docker-compose-test.yml = docker-compose-test.yml
		docker-compose.yml = docker-compose.yml
		README.md = README.md
		registry-deployment-guide.md = registry-deployment-guide.md
		registry-password.txt = registry-password.txt
		todo.md = todo.md
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "nginx", "nginx", "{D540FBC1-9D7E-8A17-AA8C-13844FF6478B}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "conf.d", "conf.d", "{8497AFA4-12F4-808F-2562-D4CAF7267B86}"
	ProjectSection(SolutionItems) = preProject
		nginx\conf.d\default.conf = nginx\conf.d\default.conf
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "ssl", "ssl", "{C58BBF29-62A8-60DF-A523-AD2F951F35E6}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "logs", "logs", "{727EA896-A04B-2085-CCAD-F2A11DD16BE4}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "FileUploadTestClient", "FileUploadTestClient\FileUploadTestClient.csproj", "{A2B9423C-0EE5-FFC3-0E01-7799EB016856}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SlzrCrossGate.Migrations.MySql", "SlzrCrossGate.Migrations.MySql\SlzrCrossGate.Migrations.MySql.csproj", "{B209BF26-4F58-47F7-AC36-D2BD4F0525AD}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SlzrCrossGate.Migrations.SqlServer", "SlzrCrossGate.Migrations.SqlServer\SlzrCrossGate.Migrations.SqlServer.csproj", "{1E5D56FB-FBDD-416E-AEBB-877FC1BE97DB}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{63268CB0-E3A1-4634-A76A-EBF3B9747EDA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{63268CB0-E3A1-4634-A76A-EBF3B9747EDA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{63268CB0-E3A1-4634-A76A-EBF3B9747EDA}.Debug|x64.ActiveCfg = Debug|Any CPU
		{63268CB0-E3A1-4634-A76A-EBF3B9747EDA}.Debug|x64.Build.0 = Debug|Any CPU
		{63268CB0-E3A1-4634-A76A-EBF3B9747EDA}.Debug|x86.ActiveCfg = Debug|Any CPU
		{63268CB0-E3A1-4634-A76A-EBF3B9747EDA}.Debug|x86.Build.0 = Debug|Any CPU
		{63268CB0-E3A1-4634-A76A-EBF3B9747EDA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{63268CB0-E3A1-4634-A76A-EBF3B9747EDA}.Release|Any CPU.Build.0 = Release|Any CPU
		{63268CB0-E3A1-4634-A76A-EBF3B9747EDA}.Release|x64.ActiveCfg = Release|Any CPU
		{63268CB0-E3A1-4634-A76A-EBF3B9747EDA}.Release|x64.Build.0 = Release|Any CPU
		{63268CB0-E3A1-4634-A76A-EBF3B9747EDA}.Release|x86.ActiveCfg = Release|Any CPU
		{63268CB0-E3A1-4634-A76A-EBF3B9747EDA}.Release|x86.Build.0 = Release|Any CPU
		{ADE5E3B2-DB93-0354-1B9C-060268D7813F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{ADE5E3B2-DB93-0354-1B9C-060268D7813F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{ADE5E3B2-DB93-0354-1B9C-060268D7813F}.Debug|x64.ActiveCfg = Debug|Any CPU
		{ADE5E3B2-DB93-0354-1B9C-060268D7813F}.Debug|x64.Build.0 = Debug|Any CPU
		{ADE5E3B2-DB93-0354-1B9C-060268D7813F}.Debug|x86.ActiveCfg = Debug|Any CPU
		{ADE5E3B2-DB93-0354-1B9C-060268D7813F}.Debug|x86.Build.0 = Debug|Any CPU
		{ADE5E3B2-DB93-0354-1B9C-060268D7813F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{ADE5E3B2-DB93-0354-1B9C-060268D7813F}.Release|Any CPU.Build.0 = Release|Any CPU
		{ADE5E3B2-DB93-0354-1B9C-060268D7813F}.Release|x64.ActiveCfg = Release|Any CPU
		{ADE5E3B2-DB93-0354-1B9C-060268D7813F}.Release|x64.Build.0 = Release|Any CPU
		{ADE5E3B2-DB93-0354-1B9C-060268D7813F}.Release|x86.ActiveCfg = Release|Any CPU
		{ADE5E3B2-DB93-0354-1B9C-060268D7813F}.Release|x86.Build.0 = Release|Any CPU
		{8646EA65-686D-3EAC-B2EB-CC9E21F7E345}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8646EA65-686D-3EAC-B2EB-CC9E21F7E345}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8646EA65-686D-3EAC-B2EB-CC9E21F7E345}.Debug|x64.ActiveCfg = Debug|Any CPU
		{8646EA65-686D-3EAC-B2EB-CC9E21F7E345}.Debug|x64.Build.0 = Debug|Any CPU
		{8646EA65-686D-3EAC-B2EB-CC9E21F7E345}.Debug|x86.ActiveCfg = Debug|Any CPU
		{8646EA65-686D-3EAC-B2EB-CC9E21F7E345}.Debug|x86.Build.0 = Debug|Any CPU
		{8646EA65-686D-3EAC-B2EB-CC9E21F7E345}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8646EA65-686D-3EAC-B2EB-CC9E21F7E345}.Release|Any CPU.Build.0 = Release|Any CPU
		{8646EA65-686D-3EAC-B2EB-CC9E21F7E345}.Release|x64.ActiveCfg = Release|Any CPU
		{8646EA65-686D-3EAC-B2EB-CC9E21F7E345}.Release|x64.Build.0 = Release|Any CPU
		{8646EA65-686D-3EAC-B2EB-CC9E21F7E345}.Release|x86.ActiveCfg = Release|Any CPU
		{8646EA65-686D-3EAC-B2EB-CC9E21F7E345}.Release|x86.Build.0 = Release|Any CPU
		{36577686-8D32-43E7-ADE4-539DE48A11C8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{36577686-8D32-43E7-ADE4-539DE48A11C8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{36577686-8D32-43E7-ADE4-539DE48A11C8}.Debug|x64.ActiveCfg = Debug|Any CPU
		{36577686-8D32-43E7-ADE4-539DE48A11C8}.Debug|x64.Build.0 = Debug|Any CPU
		{36577686-8D32-43E7-ADE4-539DE48A11C8}.Debug|x86.ActiveCfg = Debug|Any CPU
		{36577686-8D32-43E7-ADE4-539DE48A11C8}.Debug|x86.Build.0 = Debug|Any CPU
		{36577686-8D32-43E7-ADE4-539DE48A11C8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{36577686-8D32-43E7-ADE4-539DE48A11C8}.Release|Any CPU.Build.0 = Release|Any CPU
		{36577686-8D32-43E7-ADE4-539DE48A11C8}.Release|x64.ActiveCfg = Release|Any CPU
		{36577686-8D32-43E7-ADE4-539DE48A11C8}.Release|x64.Build.0 = Release|Any CPU
		{36577686-8D32-43E7-ADE4-539DE48A11C8}.Release|x86.ActiveCfg = Release|Any CPU
		{36577686-8D32-43E7-ADE4-539DE48A11C8}.Release|x86.Build.0 = Release|Any CPU
		{BBFDF433-B887-475E-B21C-F3F04D668195}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BBFDF433-B887-475E-B21C-F3F04D668195}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BBFDF433-B887-475E-B21C-F3F04D668195}.Debug|x64.ActiveCfg = Debug|Any CPU
		{BBFDF433-B887-475E-B21C-F3F04D668195}.Debug|x64.Build.0 = Debug|Any CPU
		{BBFDF433-B887-475E-B21C-F3F04D668195}.Debug|x86.ActiveCfg = Debug|Any CPU
		{BBFDF433-B887-475E-B21C-F3F04D668195}.Debug|x86.Build.0 = Debug|Any CPU
		{BBFDF433-B887-475E-B21C-F3F04D668195}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BBFDF433-B887-475E-B21C-F3F04D668195}.Release|Any CPU.Build.0 = Release|Any CPU
		{BBFDF433-B887-475E-B21C-F3F04D668195}.Release|x64.ActiveCfg = Release|Any CPU
		{BBFDF433-B887-475E-B21C-F3F04D668195}.Release|x64.Build.0 = Release|Any CPU
		{BBFDF433-B887-475E-B21C-F3F04D668195}.Release|x86.ActiveCfg = Release|Any CPU
		{BBFDF433-B887-475E-B21C-F3F04D668195}.Release|x86.Build.0 = Release|Any CPU
		{5C157202-40CB-7F5F-06E8-0CC54131056F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5C157202-40CB-7F5F-06E8-0CC54131056F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5C157202-40CB-7F5F-06E8-0CC54131056F}.Debug|x64.ActiveCfg = Debug|Any CPU
		{5C157202-40CB-7F5F-06E8-0CC54131056F}.Debug|x64.Build.0 = Debug|Any CPU
		{5C157202-40CB-7F5F-06E8-0CC54131056F}.Debug|x86.ActiveCfg = Debug|Any CPU
		{5C157202-40CB-7F5F-06E8-0CC54131056F}.Debug|x86.Build.0 = Debug|Any CPU
		{5C157202-40CB-7F5F-06E8-0CC54131056F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5C157202-40CB-7F5F-06E8-0CC54131056F}.Release|Any CPU.Build.0 = Release|Any CPU
		{5C157202-40CB-7F5F-06E8-0CC54131056F}.Release|x64.ActiveCfg = Release|Any CPU
		{5C157202-40CB-7F5F-06E8-0CC54131056F}.Release|x64.Build.0 = Release|Any CPU
		{5C157202-40CB-7F5F-06E8-0CC54131056F}.Release|x86.ActiveCfg = Release|Any CPU
		{5C157202-40CB-7F5F-06E8-0CC54131056F}.Release|x86.Build.0 = Release|Any CPU
		{00E8D372-C2ED-4CA3-8581-68776DA35E67}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{00E8D372-C2ED-4CA3-8581-68776DA35E67}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{00E8D372-C2ED-4CA3-8581-68776DA35E67}.Debug|x64.ActiveCfg = Debug|Any CPU
		{00E8D372-C2ED-4CA3-8581-68776DA35E67}.Debug|x64.Build.0 = Debug|Any CPU
		{00E8D372-C2ED-4CA3-8581-68776DA35E67}.Debug|x86.ActiveCfg = Debug|Any CPU
		{00E8D372-C2ED-4CA3-8581-68776DA35E67}.Debug|x86.Build.0 = Debug|Any CPU
		{00E8D372-C2ED-4CA3-8581-68776DA35E67}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{00E8D372-C2ED-4CA3-8581-68776DA35E67}.Release|Any CPU.Build.0 = Release|Any CPU
		{00E8D372-C2ED-4CA3-8581-68776DA35E67}.Release|x64.ActiveCfg = Release|Any CPU
		{00E8D372-C2ED-4CA3-8581-68776DA35E67}.Release|x64.Build.0 = Release|Any CPU
		{00E8D372-C2ED-4CA3-8581-68776DA35E67}.Release|x86.ActiveCfg = Release|Any CPU
		{00E8D372-C2ED-4CA3-8581-68776DA35E67}.Release|x86.Build.0 = Release|Any CPU
		{A2B9423C-0EE5-FFC3-0E01-7799EB016856}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A2B9423C-0EE5-FFC3-0E01-7799EB016856}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A2B9423C-0EE5-FFC3-0E01-7799EB016856}.Debug|x64.ActiveCfg = Debug|Any CPU
		{A2B9423C-0EE5-FFC3-0E01-7799EB016856}.Debug|x64.Build.0 = Debug|Any CPU
		{A2B9423C-0EE5-FFC3-0E01-7799EB016856}.Debug|x86.ActiveCfg = Debug|Any CPU
		{A2B9423C-0EE5-FFC3-0E01-7799EB016856}.Debug|x86.Build.0 = Debug|Any CPU
		{A2B9423C-0EE5-FFC3-0E01-7799EB016856}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A2B9423C-0EE5-FFC3-0E01-7799EB016856}.Release|Any CPU.Build.0 = Release|Any CPU
		{A2B9423C-0EE5-FFC3-0E01-7799EB016856}.Release|x64.ActiveCfg = Release|Any CPU
		{A2B9423C-0EE5-FFC3-0E01-7799EB016856}.Release|x64.Build.0 = Release|Any CPU
		{A2B9423C-0EE5-FFC3-0E01-7799EB016856}.Release|x86.ActiveCfg = Release|Any CPU
		{A2B9423C-0EE5-FFC3-0E01-7799EB016856}.Release|x86.Build.0 = Release|Any CPU
		{B209BF26-4F58-47F7-AC36-D2BD4F0525AD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B209BF26-4F58-47F7-AC36-D2BD4F0525AD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B209BF26-4F58-47F7-AC36-D2BD4F0525AD}.Debug|x64.ActiveCfg = Debug|Any CPU
		{B209BF26-4F58-47F7-AC36-D2BD4F0525AD}.Debug|x64.Build.0 = Debug|Any CPU
		{B209BF26-4F58-47F7-AC36-D2BD4F0525AD}.Debug|x86.ActiveCfg = Debug|Any CPU
		{B209BF26-4F58-47F7-AC36-D2BD4F0525AD}.Debug|x86.Build.0 = Debug|Any CPU
		{B209BF26-4F58-47F7-AC36-D2BD4F0525AD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B209BF26-4F58-47F7-AC36-D2BD4F0525AD}.Release|Any CPU.Build.0 = Release|Any CPU
		{B209BF26-4F58-47F7-AC36-D2BD4F0525AD}.Release|x64.ActiveCfg = Release|Any CPU
		{B209BF26-4F58-47F7-AC36-D2BD4F0525AD}.Release|x64.Build.0 = Release|Any CPU
		{B209BF26-4F58-47F7-AC36-D2BD4F0525AD}.Release|x86.ActiveCfg = Release|Any CPU
		{B209BF26-4F58-47F7-AC36-D2BD4F0525AD}.Release|x86.Build.0 = Release|Any CPU
		{1E5D56FB-FBDD-416E-AEBB-877FC1BE97DB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1E5D56FB-FBDD-416E-AEBB-877FC1BE97DB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1E5D56FB-FBDD-416E-AEBB-877FC1BE97DB}.Debug|x64.ActiveCfg = Debug|Any CPU
		{1E5D56FB-FBDD-416E-AEBB-877FC1BE97DB}.Debug|x64.Build.0 = Debug|Any CPU
		{1E5D56FB-FBDD-416E-AEBB-877FC1BE97DB}.Debug|x86.ActiveCfg = Debug|Any CPU
		{1E5D56FB-FBDD-416E-AEBB-877FC1BE97DB}.Debug|x86.Build.0 = Debug|Any CPU
		{1E5D56FB-FBDD-416E-AEBB-877FC1BE97DB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1E5D56FB-FBDD-416E-AEBB-877FC1BE97DB}.Release|Any CPU.Build.0 = Release|Any CPU
		{1E5D56FB-FBDD-416E-AEBB-877FC1BE97DB}.Release|x64.ActiveCfg = Release|Any CPU
		{1E5D56FB-FBDD-416E-AEBB-877FC1BE97DB}.Release|x64.Build.0 = Release|Any CPU
		{1E5D56FB-FBDD-416E-AEBB-877FC1BE97DB}.Release|x86.ActiveCfg = Release|Any CPU
		{1E5D56FB-FBDD-416E-AEBB-877FC1BE97DB}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{D540FBC1-9D7E-8A17-AA8C-13844FF6478B} = {9FA3D6BD-1EC1-3BA5-80CB-CE02773A58D5}
		{8497AFA4-12F4-808F-2562-D4CAF7267B86} = {D540FBC1-9D7E-8A17-AA8C-13844FF6478B}
		{C58BBF29-62A8-60DF-A523-AD2F951F35E6} = {D540FBC1-9D7E-8A17-AA8C-13844FF6478B}
		{727EA896-A04B-2085-CCAD-F2A11DD16BE4} = {D540FBC1-9D7E-8A17-AA8C-13844FF6478B}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {AB189E9F-154F-475E-A9B8-0BDB5B84920F}
	EndGlobalSection
EndGlobal
