using Microsoft.EntityFrameworkCore;
using SlzrCrossGate.Core.Database;
using SlzrCrossGate.Core.Models;
using SlzrCrossGate.WebAdmin.DTOs;
using SlzrCrossGate.WebAdmin.Extensions;
using System.Security.Claims;

namespace SlzrCrossGate.WebAdmin.Services
{
    /// <summary>
    /// 审计日志服务
    /// </summary>
    public class AuditLogService
    {
        private readonly TcpDbContext _dbContext;
        private readonly ILogger<AuditLogService> _logger;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public AuditLogService(
            TcpDbContext dbContext,
            ILogger<AuditLogService> logger,
            IHttpContextAccessor httpContextAccessor)
        {
            _dbContext = dbContext;
            _logger = logger;
            _httpContextAccessor = httpContextAccessor;
        }

        #region 日志记录方法

        /// <summary>
        /// 记录登录日志
        /// </summary>
        public async Task LogLoginAsync(
            string? userId,
            string? userName,
            string? realName,
            string? merchantId,
            string? merchantName,
            string loginType,
            string loginMethod,
            bool isSuccess,
            string? failureReason = null,
            string? sessionId = null)
        {
            try
            {
                var httpContext = _httpContextAccessor.HttpContext;
                var loginLog = new LoginLog
                {
                    UserId = userId,
                    UserName = userName,
                    RealName = realName,
                    MerchantId = merchantId,
                    MerchantName = merchantName,
                    IpAddress = GetClientIpAddress(httpContext),
                    UserAgent = httpContext?.Request.Headers.UserAgent.ToString(),
                    OperationTime = DateTime.Now,
                    IsSuccess = isSuccess,
                    FailureReason = failureReason,
                    LoginType = loginType,
                    LoginMethod = loginMethod,
                    SessionId = sessionId
                };

                _dbContext.LoginLogs.Add(loginLog);
                await _dbContext.SaveChangesAsync();

                _logger.LogDebug("登录日志记录成功: 用户={UserName}, 类型={LoginType}, 结果={IsSuccess}",
                    userName, loginType, isSuccess);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "记录登录日志失败: 用户={UserName}", userName);
            }
        }

        /// <summary>
        /// 记录密码修改日志
        /// </summary>
        public async Task LogPasswordChangeAsync(
            string? userId,
            string? userName,
            string? realName,
            string? merchantId,
            string? merchantName,
            string changeType,
            bool isSuccess,
            string? failureReason = null,
            string? targetUserId = null,
            string? targetUserName = null,
            string? targetRealName = null)
        {
            try
            {
                var httpContext = _httpContextAccessor.HttpContext;
                var passwordChangeLog = new PasswordChangeLog
                {
                    UserId = userId,
                    UserName = userName,
                    RealName = realName,
                    MerchantId = merchantId,
                    MerchantName = merchantName,
                    IpAddress = GetClientIpAddress(httpContext),
                    UserAgent = httpContext?.Request.Headers.UserAgent.ToString(),
                    OperationTime = DateTime.Now,
                    IsSuccess = isSuccess,
                    FailureReason = failureReason,
                    ChangeType = changeType,
                    TargetUserId = targetUserId,
                    TargetUserName = targetUserName,
                    TargetRealName = targetRealName
                };

                _dbContext.PasswordChangeLogs.Add(passwordChangeLog);
                await _dbContext.SaveChangesAsync();

                _logger.LogDebug("密码修改日志记录成功: 操作用户={UserName}, 目标用户={TargetUserName}, 类型={ChangeType}, 结果={IsSuccess}",
                    userName, targetUserName ?? userName, changeType, isSuccess);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "记录密码修改日志失败: 用户={UserName}", userName);
            }
        }

        /// <summary>
        /// 记录操作日志
        /// </summary>
        public async Task LogOperationAsync(
            string? userId,
            string? userName,
            string? realName,
            string? merchantId,
            string? merchantName,
            string module,
            string operationType,
            string? operationTarget,
            bool isSuccess,
            string? failureReason = null,
            string? operationDetails = null,
            string? requestPath = null,
            string? httpMethod = null,
            int? responseStatusCode = null,
            long? executionTime = null)
        {
            try
            {
                var httpContext = _httpContextAccessor.HttpContext;
                var operationLog = new OperationLog
                {
                    UserId = userId,
                    UserName = userName,
                    RealName = realName,
                    MerchantId = merchantId,
                    MerchantName = merchantName,
                    IpAddress = GetClientIpAddress(httpContext),
                    UserAgent = httpContext?.Request.Headers.UserAgent.ToString(),
                    OperationTime = DateTime.Now,
                    IsSuccess = isSuccess,
                    FailureReason = failureReason,
                    Module = module,
                    OperationType = operationType,
                    OperationTarget = operationTarget,
                    OperationDetails = operationDetails,
                    RequestPath = requestPath ?? httpContext?.Request.Path.Value,
                    HttpMethod = httpMethod ?? httpContext?.Request.Method,
                    ResponseStatusCode = responseStatusCode,
                    ExecutionTime = executionTime
                };

                _dbContext.OperationLogs.Add(operationLog);
                await _dbContext.SaveChangesAsync();

                _logger.LogDebug("操作日志记录成功: 用户={UserName}, 模块={Module}, 操作={OperationType}, 对象={OperationTarget}, 结果={IsSuccess}",
                    userName, module, operationType, operationTarget, isSuccess);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "记录操作日志失败: 用户={UserName}, 模块={Module}", userName, module);
            }
        }

        #endregion

        #region 日志查询方法

        /// <summary>
        /// 查询登录日志
        /// </summary>
        public async Task<PaginatedResult<LoginLogDto>> GetLoginLogsAsync(LoginLogQueryDto query, string? currentUserMerchantId, bool isSystemAdmin)
        {
            var dbQuery = _dbContext.LoginLogs.AsQueryable();

            // 权限控制：非系统管理员只能查看自己商户的日志
            if (!isSystemAdmin && !string.IsNullOrEmpty(currentUserMerchantId))
            {
                dbQuery = dbQuery.Where(l => l.MerchantId == currentUserMerchantId);
            }

            // 应用筛选条件
            if (!string.IsNullOrEmpty(query.UserName))
            {
                dbQuery = dbQuery.Where(l => l.UserName != null && l.UserName.Contains(query.UserName));
            }

            if (!string.IsNullOrEmpty(query.RealName))
            {
                dbQuery = dbQuery.Where(l => l.RealName != null && l.RealName.Contains(query.RealName));
            }

            if (!string.IsNullOrEmpty(query.MerchantId))
            {
                dbQuery = dbQuery.Where(l => l.MerchantId == query.MerchantId);
            }

            if (!string.IsNullOrEmpty(query.IpAddress))
            {
                dbQuery = dbQuery.Where(l => l.IpAddress != null && l.IpAddress.Contains(query.IpAddress));
            }

            if (!string.IsNullOrEmpty(query.LoginType))
            {
                dbQuery = dbQuery.Where(l => l.LoginType == query.LoginType);
            }

            if (!string.IsNullOrEmpty(query.LoginMethod))
            {
                dbQuery = dbQuery.Where(l => l.LoginMethod == query.LoginMethod);
            }

            if (query.StartTime.HasValue)
            {
                dbQuery = dbQuery.Where(l => l.OperationTime >= query.StartTime.Value);
            }

            if (query.EndTime.HasValue)
            {
                dbQuery = dbQuery.Where(l => l.OperationTime <= query.EndTime.Value);
            }

            if (query.IsSuccess.HasValue)
            {
                dbQuery = dbQuery.Where(l => l.IsSuccess == query.IsSuccess.Value);
            }

            // 排序
            dbQuery = dbQuery.OrderByDescending(l => l.OperationTime);

            // 分页
            var totalCount = await dbQuery.CountAsync();
            var items = await dbQuery
                .Skip((query.Page - 1) * query.PageSize)
                .Take(query.PageSize)
                .Select(l => new LoginLogDto
                {
                    Id = l.Id,
                    UserId = l.UserId,
                    UserName = l.UserName,
                    RealName = l.RealName,
                    MerchantId = l.MerchantId,
                    MerchantName = l.MerchantName,
                    IpAddress = l.IpAddress,
                    UserAgent = l.UserAgent,
                    OperationTime = l.OperationTime,
                    IsSuccess = l.IsSuccess,
                    FailureReason = l.FailureReason,
                    Remarks = l.Remarks,
                    LoginType = l.LoginType,
                    LoginMethod = l.LoginMethod,
                    SessionId = l.SessionId,
                    LogoutTime = l.LogoutTime
                })
                .ToListAsync();

            return new PaginatedResult<LoginLogDto>
            {
                Items = items,
                TotalCount = totalCount,
                Page = query.Page,
                PageSize = query.PageSize
            };
        }

        /// <summary>
        /// 查询密码修改日志
        /// </summary>
        public async Task<PaginatedResult<PasswordChangeLogDto>> GetPasswordChangeLogsAsync(PasswordChangeLogQueryDto query, string? currentUserMerchantId, bool isSystemAdmin)
        {
            var dbQuery = _dbContext.PasswordChangeLogs.AsQueryable();

            // 权限控制：非系统管理员只能查看自己商户的日志
            if (!isSystemAdmin && !string.IsNullOrEmpty(currentUserMerchantId))
            {
                dbQuery = dbQuery.Where(l => l.MerchantId == currentUserMerchantId);
            }

            // 应用筛选条件
            if (!string.IsNullOrEmpty(query.UserName))
            {
                dbQuery = dbQuery.Where(l => l.UserName != null && l.UserName.Contains(query.UserName));
            }

            if (!string.IsNullOrEmpty(query.RealName))
            {
                dbQuery = dbQuery.Where(l => l.RealName != null && l.RealName.Contains(query.RealName));
            }

            if (!string.IsNullOrEmpty(query.MerchantId))
            {
                dbQuery = dbQuery.Where(l => l.MerchantId == query.MerchantId);
            }

            if (!string.IsNullOrEmpty(query.IpAddress))
            {
                dbQuery = dbQuery.Where(l => l.IpAddress != null && l.IpAddress.Contains(query.IpAddress));
            }

            if (!string.IsNullOrEmpty(query.ChangeType))
            {
                dbQuery = dbQuery.Where(l => l.ChangeType == query.ChangeType);
            }

            if (!string.IsNullOrEmpty(query.TargetUserName))
            {
                dbQuery = dbQuery.Where(l => l.TargetUserName != null && l.TargetUserName.Contains(query.TargetUserName));
            }

            if (query.StartTime.HasValue)
            {
                dbQuery = dbQuery.Where(l => l.OperationTime >= query.StartTime.Value);
            }

            if (query.EndTime.HasValue)
            {
                dbQuery = dbQuery.Where(l => l.OperationTime <= query.EndTime.Value);
            }

            if (query.IsSuccess.HasValue)
            {
                dbQuery = dbQuery.Where(l => l.IsSuccess == query.IsSuccess.Value);
            }

            // 排序
            dbQuery = dbQuery.OrderByDescending(l => l.OperationTime);

            // 分页
            var totalCount = await dbQuery.CountAsync();
            var items = await dbQuery
                .Skip((query.Page - 1) * query.PageSize)
                .Take(query.PageSize)
                .Select(l => new PasswordChangeLogDto
                {
                    Id = l.Id,
                    UserId = l.UserId,
                    UserName = l.UserName,
                    RealName = l.RealName,
                    MerchantId = l.MerchantId,
                    MerchantName = l.MerchantName,
                    IpAddress = l.IpAddress,
                    UserAgent = l.UserAgent,
                    OperationTime = l.OperationTime,
                    IsSuccess = l.IsSuccess,
                    FailureReason = l.FailureReason,
                    Remarks = l.Remarks,
                    ChangeType = l.ChangeType,
                    TargetUserId = l.TargetUserId,
                    TargetUserName = l.TargetUserName,
                    TargetRealName = l.TargetRealName
                })
                .ToListAsync();

            return new PaginatedResult<PasswordChangeLogDto>
            {
                Items = items,
                TotalCount = totalCount,
                Page = query.Page,
                PageSize = query.PageSize
            };
        }

        /// <summary>
        /// 查询操作日志
        /// </summary>
        public async Task<PaginatedResult<OperationLogDto>> GetOperationLogsAsync(OperationLogQueryDto query, string? currentUserMerchantId, bool isSystemAdmin)
        {
            var dbQuery = _dbContext.OperationLogs.AsQueryable();

            // 权限控制：非系统管理员只能查看自己商户的日志
            if (!isSystemAdmin && !string.IsNullOrEmpty(currentUserMerchantId))
            {
                dbQuery = dbQuery.Where(l => l.MerchantId == currentUserMerchantId);
            }

            // 应用筛选条件
            if (!string.IsNullOrEmpty(query.UserName))
            {
                dbQuery = dbQuery.Where(l => l.UserName != null && l.UserName.Contains(query.UserName));
            }

            if (!string.IsNullOrEmpty(query.RealName))
            {
                dbQuery = dbQuery.Where(l => l.RealName != null && l.RealName.Contains(query.RealName));
            }

            if (!string.IsNullOrEmpty(query.MerchantId))
            {
                dbQuery = dbQuery.Where(l => l.MerchantId == query.MerchantId);
            }

            if (!string.IsNullOrEmpty(query.IpAddress))
            {
                dbQuery = dbQuery.Where(l => l.IpAddress != null && l.IpAddress.Contains(query.IpAddress));
            }

            if (!string.IsNullOrEmpty(query.Module))
            {
                dbQuery = dbQuery.Where(l => l.Module == query.Module);
            }

            if (!string.IsNullOrEmpty(query.OperationType))
            {
                dbQuery = dbQuery.Where(l => l.OperationType == query.OperationType);
            }

            if (!string.IsNullOrEmpty(query.OperationTarget))
            {
                dbQuery = dbQuery.Where(l => l.OperationTarget != null && l.OperationTarget.Contains(query.OperationTarget));
            }

            if (!string.IsNullOrEmpty(query.RequestPath))
            {
                dbQuery = dbQuery.Where(l => l.RequestPath != null && l.RequestPath.Contains(query.RequestPath));
            }

            if (!string.IsNullOrEmpty(query.HttpMethod))
            {
                dbQuery = dbQuery.Where(l => l.HttpMethod == query.HttpMethod);
            }

            if (query.StartTime.HasValue)
            {
                dbQuery = dbQuery.Where(l => l.OperationTime >= query.StartTime.Value);
            }

            if (query.EndTime.HasValue)
            {
                dbQuery = dbQuery.Where(l => l.OperationTime <= query.EndTime.Value);
            }

            if (query.IsSuccess.HasValue)
            {
                dbQuery = dbQuery.Where(l => l.IsSuccess == query.IsSuccess.Value);
            }

            // 排序
            dbQuery = dbQuery.OrderByDescending(l => l.OperationTime);

            // 分页
            var totalCount = await dbQuery.CountAsync();
            var items = await dbQuery
                .Skip((query.Page - 1) * query.PageSize)
                .Take(query.PageSize)
                .Select(l => new OperationLogDto
                {
                    Id = l.Id,
                    UserId = l.UserId,
                    UserName = l.UserName,
                    RealName = l.RealName,
                    MerchantId = l.MerchantId,
                    MerchantName = l.MerchantName,
                    IpAddress = l.IpAddress,
                    UserAgent = l.UserAgent,
                    OperationTime = l.OperationTime,
                    IsSuccess = l.IsSuccess,
                    FailureReason = l.FailureReason,
                    Remarks = l.Remarks,
                    Module = l.Module,
                    OperationType = l.OperationType,
                    OperationTarget = l.OperationTarget,
                    OperationDetails = l.OperationDetails,
                    RequestPath = l.RequestPath,
                    HttpMethod = l.HttpMethod,
                    ResponseStatusCode = l.ResponseStatusCode,
                    ExecutionTime = l.ExecutionTime
                })
                .ToListAsync();

            return new PaginatedResult<OperationLogDto>
            {
                Items = items,
                TotalCount = totalCount,
                Page = query.Page,
                PageSize = query.PageSize
            };
        }

        /// <summary>
        /// 获取审计日志统计信息
        /// </summary>
        public async Task<AuditLogStatsDto> GetAuditLogStatsAsync(string? currentUserMerchantId, bool isSystemAdmin)
        {
            var today = DateTime.Today;
            var weekStart = today.AddDays(-(int)today.DayOfWeek);
            var monthStart = new DateTime(today.Year, today.Month, 1);

            var stats = new AuditLogStatsDto();

            // 登录日志统计
            var loginQuery = _dbContext.LoginLogs.AsQueryable();
            if (!isSystemAdmin && !string.IsNullOrEmpty(currentUserMerchantId))
            {
                loginQuery = loginQuery.Where(l => l.MerchantId == currentUserMerchantId);
            }

            stats.TodayLogins = await loginQuery.CountAsync(l => l.OperationTime >= today && l.IsSuccess);
            stats.TodayLoginFailures = await loginQuery.CountAsync(l => l.OperationTime >= today && !l.IsSuccess);
            stats.WeekLogins = await loginQuery.CountAsync(l => l.OperationTime >= weekStart && l.IsSuccess);
            stats.MonthLogins = await loginQuery.CountAsync(l => l.OperationTime >= monthStart && l.IsSuccess);

            // 密码修改日志统计
            var passwordQuery = _dbContext.PasswordChangeLogs.AsQueryable();
            if (!isSystemAdmin && !string.IsNullOrEmpty(currentUserMerchantId))
            {
                passwordQuery = passwordQuery.Where(l => l.MerchantId == currentUserMerchantId);
            }

            stats.TodayPasswordChanges = await passwordQuery.CountAsync(l => l.OperationTime >= today);

            // 操作日志统计
            var operationQuery = _dbContext.OperationLogs.AsQueryable();
            if (!isSystemAdmin && !string.IsNullOrEmpty(currentUserMerchantId))
            {
                operationQuery = operationQuery.Where(l => l.MerchantId == currentUserMerchantId);
            }

            stats.TodayOperations = await operationQuery.CountAsync(l => l.OperationTime >= today);

            // 活跃用户统计
            stats.ActiveUsers = await loginQuery
                .Where(l => l.OperationTime >= weekStart && l.IsSuccess)
                .Select(l => l.UserId)
                .Distinct()
                .CountAsync();

            return stats;
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 获取客户端IP地址
        /// </summary>
        private string? GetClientIpAddress(HttpContext? httpContext)
        {
            if (httpContext == null) return null;

            // 检查是否有代理转发的真实IP
            var forwardedFor = httpContext.Request.Headers["X-Forwarded-For"].FirstOrDefault();
            if (!string.IsNullOrEmpty(forwardedFor))
            {
                return forwardedFor.Split(',')[0].Trim();
            }

            var realIp = httpContext.Request.Headers["X-Real-IP"].FirstOrDefault();
            if (!string.IsNullOrEmpty(realIp))
            {
                return realIp;
            }

            return httpContext.Connection.RemoteIpAddress?.ToString();
        }

        #endregion
    }
}
