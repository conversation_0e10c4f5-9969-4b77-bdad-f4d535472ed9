/**
 * 功能权限常量定义 - 严格按照用户指定的功能权限列表
 * 注意：功能权限只控制具体操作，页面访问通过菜单权限控制
 */
export const PERMISSIONS = {
  // 终端管理
  TERMINAL: {
    SEND_MESSAGE: 'terminal.send_message',
    PUBLISH_FILE: 'terminal.publish_file'
  },

  // 文件类型
  FILE_TYPE: {
    CREATE: 'file_type.create',
    EDIT: 'file_type.edit',
    DELETE: 'file_type.delete'
  },

  // 文件版本
  FILE_VERSION: {
    UPLOAD: 'file_version.upload',
    PUBLISH: 'file_version.publish',
    DELETE: 'file_version.delete'
  },

  // 发布记录
  PUBLISH_RECORD: {
    DELETE: 'publish_record.delete'
  },

  // 消息类型
  MESSAGE_TYPE: {
    CREATE: 'message_type.create',
    EDIT: 'message_type.edit',
    DELETE: 'message_type.delete'
  },

  // 消息管理
  MESSAGE: {
    SEND: 'message.send',
    DELETE: 'message.delete'
  },

  // 线路参数
  LINE_PRICE: {
    CREATE: 'line_price.create',
    EDIT: 'line_price.edit',
    DELETE: 'line_price.delete',
    VIEW_VERSIONS: 'line_price.view_versions'
  },

  // 线路参数版本
  LINE_PRICE_VERSION: {
    CREATE_VERSION: 'line_price_version.create_version',
    COPY_CREATE: 'line_price_version.copy_create',
    COPY_TO_OTHER_LINE: 'line_price_version.copy_to_other_line',
    EDIT: 'line_price_version.edit',
    SUBMIT: 'line_price_version.submit'
  },

  // 银联密钥
  UNIONPAY_KEY: {
    CREATE: 'unionpay_key.create',
    BATCH_IMPORT: 'unionpay_key.batch_import',
    EDIT: 'unionpay_key.edit',
    UNBIND: 'unionpay_key.unbind',
    DELETE: 'unionpay_key.delete',
    EXPORT: 'unionpay_key.export'
  },

  // 商户字典
  MERCHANT_DICTIONARY: {
    CREATE: 'merchant_dictionary.create',
    EDIT: 'merchant_dictionary.edit',
    DELETE: 'merchant_dictionary.delete'
  },

  // 票价折扣方案
  FARE_DISCOUNT_SCHEME: {
    CREATE: 'fare_discount_scheme.create',
    EDIT: 'fare_discount_scheme.edit',
    DELETE: 'fare_discount_scheme.delete',
    SUBMIT_VERSION: 'fare_discount_scheme.submit_version'
  },

  // 系统设置中字段管理
  SYSTEM_FIELD_MANAGEMENT: {
    OPEN_FIELD_CONFIG_MANAGER: 'system.field_management.open_config_manager'
  },

  // 菜单管理
  MENU_MANAGEMENT: {
    INIT_DEFAULT_MENUS: 'menu.init_default_menus',
    CREATE_GROUP: 'menu.create_group',
    EDIT_GROUP: 'menu.edit_group',
    DELETE_GROUP: 'menu.delete_group',
    CREATE_MENU_ITEM: 'menu.create_menu_item',
    EDIT_MENU_ITEM: 'menu.edit_menu_item',
    DELETE_MENU_ITEM: 'menu.delete_menu_item'
  },

  // 商户管理
  MERCHANT: {
    CREATE: 'merchant.create',
    EDIT: 'merchant.edit',
    TOGGLE_ACTIVE: 'merchant.toggle_active',
    DELETE: 'merchant.delete'
  },

  // 用户管理
  USER: {
    CREATE: 'user.create',
    EDIT: 'user.edit',
    LOCK: 'user.lock',
    DELETE: 'user.delete'
  },

  // 角色管理
  ROLE: {
    CREATE: 'role.create',
    EDIT: 'role.edit',
    DELETE: 'role.delete'
  },

  // 功能权限管理
  FEATURE_PERMISSION: {
    SAVE_CHANGES: 'feature_permission.save_changes'
  }
};

/**
 * 获取所有权限标识符
 */
export const getAllPermissionKeys = () => {
  const keys = [];
  const traverse = (obj) => {
    Object.values(obj).forEach(value => {
      if (typeof value === 'string') {
        keys.push(value);
      } else if (typeof value === 'object') {
        traverse(value);
      }
    });
  };
  traverse(PERMISSIONS);
  return keys;
};

/**
 * 根据模块获取权限
 */
export const getPermissionsByModule = (module) => {
  return PERMISSIONS[module] || {};
};

/**
 * 检查权限标识符是否有效
 */
export const isValidPermission = (permission) => {
  return getAllPermissionKeys().includes(permission);
};
