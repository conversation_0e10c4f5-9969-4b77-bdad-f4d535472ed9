# WebAdmin 版本更新日志

## 版本说明
本文件记录WebAdmin系统的版本变更历史，包括新功能、修复问题、性能优化等内容。

## 版本号规则
- **格式**: `主版本.次版本.修订版本.构建版本` (例如: 1.0.2.1)
- **主版本**: 重大架构变更或不兼容更新
- **次版本**: 新功能添加或重要功能改进
- **修订版本**: 问题修复和小幅改进
- **构建版本**: 构建号，每次构建自动递增

## 版本历史

### v1.0.4.0 (2025-07-30)
增加功能按钮权限控制系统


### v1.0.3.0 (2025-07-17)
**增加微前端架构，增加模板项目，增加珠海通定制项目**

#### 新功能
- **微前端架构**: 实现WebAdmin作为主框架，支持加载子应用的功能
- **模板项目**: 创建客户项目模板，包含基础功能和项目结构
- **珠海通定制项目**: 作为第一个客户项目，实现定制化查询功能
---

## 版本发布流程

### 1. 版本号更新
修改 `Directory.Build.props` 文件中的版本号：
```xml
<PropertyGroup>
  <MajorVersion>1</MajorVersion>
  <MinorVersion>0</MinorVersion>
  <PatchVersion>2</PatchVersion>
  <BuildVersion>1</BuildVersion>
</PropertyGroup>
```

### 2. 更新版本日志
在本文件中添加新版本的变更记录，包括：
- 版本号和发布日期
- 新功能描述
- 修复问题列表
- 技术改进说明
- API变更(如有)

### 3. 构建和部署
```bash
# 构建项目
dotnet build -c Release

# 发布项目
dotnet publish -c Release

# 构建前端
cd ClientApp
npm run build
```

### 4. 验证版本信息
- 检查导航栏版本显示是否正确
- 验证版本检测功能是否正常工作
- 确认新功能和修复是否生效

---

## 注意事项

1. **版本号管理**: 使用Directory.Build.props统一管理解决方案版本号
2. **构建时间**: 系统自动添加构建时间戳到程序集元数据
3. **开发版本**: Debug版本自动添加"-dev"后缀
4. **版本检测**: 系统会自动检测版本变化并提示用户刷新
5. **向后兼容**: 确保版本更新不破坏现有功能和数据

---

*最后更新: 2025-01-04*
