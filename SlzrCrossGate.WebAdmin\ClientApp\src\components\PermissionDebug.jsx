import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Card, 
  CardContent, 
  Typography, 
  Button, 
  Chip,
  Grid,
  Alert,
  CircularProgress
} from '@mui/material';
import { useFeaturePermission } from '../hooks/useFeaturePermission';

/**
 * 权限调试组件
 * 用于测试和调试权限系统的性能
 * 仅在开发环境中使用
 */
const PermissionDebug = () => {
  // 所有Hooks必须在条件检查之前调用
  const [requestCount, setRequestCount] = useState(0);
  const [startTime, setStartTime] = useState(null);
  const [endTime, setEndTime] = useState(null);

  // 创建多个权限Hook实例来模拟多组件使用场景
  const permission1 = useFeaturePermission();
  const permission2 = useFeaturePermission();
  const permission3 = useFeaturePermission();
  const permission4 = useFeaturePermission();
  const permission5 = useFeaturePermission();

  const allPermissions = [permission1, permission2, permission3, permission4, permission5];

  // 生产环境中不显示调试组件
  if (process.env.NODE_ENV === 'production') {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="info">
          权限调试组件仅在开发环境中可用。
        </Alert>
      </Box>
    );
  }

  // 监听网络请求
  useEffect(() => {
    const originalFetch = window.fetch;
    let count = 0;
    
    window.fetch = function(...args) {
      const url = args[0];
      if (typeof url === 'string' && url.includes('user-permissions')) {
        count++;
        setRequestCount(count);
        
        if (count === 1) {
          setStartTime(Date.now());
        }
        
        // 权限请求计数，用于性能监控
      }
      
      return originalFetch.apply(this, args);
    };

    return () => {
      window.fetch = originalFetch;
    };
  }, []);

  // 监听加载完成
  useEffect(() => {
    const allLoaded = allPermissions.every(p => !p.loading);
    if (allLoaded && startTime && !endTime) {
      setEndTime(Date.now());
    }
  }, [allPermissions, startTime, endTime]);

  const handleRefresh = () => {
    setRequestCount(0);
    setStartTime(null);
    setEndTime(null);
    
    // 刷新所有权限
    allPermissions.forEach(p => {
      if (p.refreshPermissions) {
        p.refreshPermissions();
      }
    });
  };

  const loadingCount = allPermissions.filter(p => p.loading).length;
  const loadTime = startTime && endTime ? endTime - startTime : null;

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        权限系统性能调试
      </Typography>
      
      <Alert severity="info" sx={{ mb: 3 }}>
        此页面创建了5个useFeaturePermission Hook实例来模拟多组件使用场景，
        用于测试权限请求的优化效果。
      </Alert>

      <Grid container spacing={3}>
        {/* 性能统计 */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                性能统计
              </Typography>
              
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" color="text.secondary">
                  网络请求次数
                </Typography>
                <Chip 
                  label={requestCount} 
                  color={requestCount <= 1 ? 'success' : requestCount <= 5 ? 'warning' : 'error'}
                  size="large"
                />
              </Box>

              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" color="text.secondary">
                  正在加载的Hook数量
                </Typography>
                <Chip 
                  label={`${loadingCount}/5`} 
                  color={loadingCount === 0 ? 'success' : 'warning'}
                />
              </Box>

              {loadTime && (
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="text.secondary">
                    总加载时间
                  </Typography>
                  <Chip 
                    label={`${loadTime}ms`} 
                    color={loadTime < 1000 ? 'success' : 'warning'}
                  />
                </Box>
              )}

              <Button 
                variant="contained" 
                onClick={handleRefresh}
                disabled={loadingCount > 0}
                fullWidth
              >
                刷新权限
              </Button>
            </CardContent>
          </Card>
        </Grid>

        {/* Hook状态 */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Hook状态
              </Typography>
              
              {allPermissions.map((permission, index) => (
                <Box key={index} sx={{ mb: 2, p: 2, border: '1px solid #e0e0e0', borderRadius: 1 }}>
                  <Typography variant="subtitle2">
                    Hook #{index + 1}
                  </Typography>
                  
                  <Box sx={{ display: 'flex', gap: 1, mt: 1 }}>
                    <Chip 
                      label={permission.loading ? '加载中' : '已完成'} 
                      color={permission.loading ? 'warning' : 'success'}
                      size="small"
                      icon={permission.loading ? <CircularProgress size={16} /> : null}
                    />
                    
                    {permission.error && (
                      <Chip 
                        label="错误" 
                        color="error"
                        size="small"
                      />
                    )}
                  </Box>

                  <Typography variant="caption" color="text.secondary">
                    权限数量: {permission.permissions ? permission.permissions.size : 0}
                  </Typography>
                </Box>
              ))}
            </CardContent>
          </Card>
        </Grid>

        {/* 优化建议 */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                优化效果分析
              </Typography>
              
              {requestCount <= 1 ? (
                <Alert severity="success">
                  ✅ 优化成功！只发送了 {requestCount} 次权限请求，多个组件共享了同一份权限数据。
                </Alert>
              ) : requestCount <= 5 ? (
                <Alert severity="warning">
                  ⚠️ 部分优化！发送了 {requestCount} 次权限请求，仍有改进空间。
                </Alert>
              ) : (
                <Alert severity="error">
                  ❌ 优化失败！发送了 {requestCount} 次权限请求，每个Hook都在独立请求数据。
                </Alert>
              )}

              <Typography variant="body2" sx={{ mt: 2 }}>
                <strong>期望结果：</strong> 无论有多少个组件使用useFeaturePermission，
                都应该只发送1次网络请求，其他组件通过订阅机制获取权限数据。
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default PermissionDebug;
