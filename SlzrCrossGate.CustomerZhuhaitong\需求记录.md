# 需求描述
做一个FTP文件上传结果的展示页面，主要是统计FTP目录中指定文件的数量并展示。展示字段如下:

| 字段名称 | 字段说明 |
| --- | --- |
| 上传目标 | 对应表中 sServerDesc 字段 |
| 上传次序 | 对应表中 UpOrder 字段 |
| 312文件待上传数 | originPath312 路径中获取 .312 后缀的文件数量 |
| 312文件上传成功数 | /DataPacks/312/日期 目录中获取 .312 后缀的文件数量，这个FTP的路径需要根据查询日期手动拼接，日期格式为yyyyMMdd |
| 315文件待上传数 | originPath315 路径中获取 .315 后缀的文件数量 |
| 315文件上传成功数 | /DataPacks/315/日期 目录中获取 .315 后缀的文件数量，这个FTP的路径需要根据查询日期手动拼接，日期格式为yyyyMMdd |
| 312源目录 | 对应表中 originPath312 字段 |
| 312备份目录 | 对应表中 LocalBakPath312 字段 |
| 315源目录 | 对应表中 originPath315 字段 |
| 312备份目录 | 对应表中 LocalBakPath315 字段 |


需要有一个日期选择框，默认当天日期，有一个查询按钮，点击后查询指定日期的数据并展示到页面上。


# 对应文件上传FTP配置表结构
DROP TABLE IF EXISTS `zhtong_uploadftpserver`;
CREATE TABLE `zhtong_uploadftpserver`  (
  `ID` int(11) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `UpOrder` int(11) NULL DEFAULT NULL COMMENT '上传次序号',
  `isEnable` int(11) NULL DEFAULT NULL COMMENT '是否启用',
  `protocol` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '传输协议,ftp还是SFTP',
  `ftpIp` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '远程地址',
  `ftpPort` int(11) NULL DEFAULT NULL COMMENT '远程端口',
  `ftpUser` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '远程用户',
  `ftpPassword` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '远程密码',
  `originPath312` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '312文件源路径',
  `LocalBakPath312` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '上传成功后312文件备份路径',
  `originPath313` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '313文件源路径',
  `LocalBakPath313` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '上传成功后313文件备份路径',
  `originPath315` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '315文件源地址',
  `LocalBakPath315` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '上传成功后315文件备份路径',
  `RemotePathTemp` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '远程临时目录,为空则代表没有远程临时目录',
  `RemotePath312` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '312文件远程目录,为空则代表312文件不发送',
  `RemotePath313` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '313文件远程目录,为空则代表313文件不发送',
  `RemotePath315` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '315文件远程目录,为空则代表315文件不发送',
  `sServerDesc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'FTP服务器描述',
  `FtpType` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'FTP类型',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 16 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;
