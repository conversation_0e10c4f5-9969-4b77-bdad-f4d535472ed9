import { createTheme } from '@mui/material/styles';

// 颜色配置 - 玻璃拟态+微立体感风格
const PRIMARY = {
  lighter: '#F3E8FF',
  light: '#C084FC',
  main: '#7E22CE',  // 紫色主题 - 根据要求使用 #7E22CE
  dark: '#6B21A8',
  darker: '#581C87',
};

// 辅助色按Material You算法生成
const SECONDARY = {
  lighter: '#FDF4FF',
  light: '#E879F9',
  main: '#D946EF',  // 粉紫色辅助色
  dark: '#A21CAF',
  darker: '#701A75',
};

const INFO = {
  lighter: '#EFF6FF',
  light: '#93C5FD',
  main: '#3B82F6',  // 蓝色 - 信息色
  dark: '#1D4ED8',
  darker: '#1E3A8A',
};

const SUCCESS = {
  lighter: '#ECFDF5',
  light: '#6EE7B7',
  main: '#10B981',  // 绿色 - 成功色
  dark: '#059669',
  darker: '#065F46',
};

const WARNING = {
  lighter: '#FFFBEB',
  light: '#FCD34D',
  main: '#F59E0B',  // 黄色 - 警告色
  dark: '#D97706',
  darker: '#92400E',
};

const ERROR = {
  lighter: '#FEF2F2',
  light: '#FCA5A5',
  main: '#EF4444',  // 红色 - 错误色
  dark: '#DC2626',
  darker: '#991B1B',
};

const GREY = {
  0: '#FFFFFF',
  100: '#F9FAFB',
  200: '#F3F4F6',
  300: '#E5E7EB',
  400: '#9CA3AF',
  500: '#6B7280',
  600: '#4B5563',
  700: '#374151',
  800: '#1F2937',
  900: '#111827',
};

// 创建亮色主题 - 玻璃拟态+微立体感设计
export const lightTheme = createTheme({
  palette: {
    mode: 'light',
    primary: PRIMARY,
    secondary: SECONDARY,
    info: INFO,
    success: SUCCESS,
    warning: WARNING,
    error: ERROR,
    grey: GREY,
    background: {
      default: '#F5F3FF', // 浅紫色背景，与主题色相匹配
      paper: 'rgba(255, 255, 255, 0.85)', // 半透明纸张背景，实现玻璃拟态
    },
    text: {
      primary: GREY[800],
      secondary: GREY[600],
      disabled: GREY[500],
    },
    divider: 'rgba(0, 0, 0, 0.08)', // 半透明分隔线
  },
  typography: {
    fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
    h1: {
      fontWeight: 700,
      fontSize: '2.5rem',
      lineHeight: 1.2,
    },
    h2: {
      fontWeight: 700,
      fontSize: '2rem',
      lineHeight: 1.3,
    },
    h3: {
      fontWeight: 600,
      fontSize: '1.75rem',
      lineHeight: 1.3,
    },
    h4: {
      fontWeight: 600,
      fontSize: '1.5rem',
      lineHeight: 1.4,
    },
    h5: {
      fontWeight: 600,
      fontSize: '1.25rem',
      lineHeight: 1.4,
    },
    h6: {
      fontWeight: 600,
      fontSize: '1.125rem',
      lineHeight: 1.4,
    },
    body1: {
      fontSize: '1rem',
      lineHeight: 1.6,
    },
    body2: {
      fontSize: '0.875rem',
      lineHeight: 1.6,
    },
    button: {
      fontWeight: 600,
      textTransform: 'none',
    },
  },
  shape: {
    borderRadius: 12,
  },
  shadows: [
    'none',
    '0px 2px 4px rgba(0, 0, 0, 0.05)',
    '0px 4px 8px rgba(0, 0, 0, 0.1)',
    '0px 8px 16px rgba(0, 0, 0, 0.1)',
    '0px 12px 24px rgba(0, 0, 0, 0.15)',
    '0px 16px 32px rgba(0, 0, 0, 0.15)',
    '0px 20px 40px rgba(0, 0, 0, 0.2)',
    '0px 24px 48px rgba(0, 0, 0, 0.2)',
    '0px 28px 56px rgba(0, 0, 0, 0.25)',
    '0px 32px 64px rgba(0, 0, 0, 0.25)',
    '0px 36px 72px rgba(0, 0, 0, 0.3)',
    '0px 40px 80px rgba(0, 0, 0, 0.3)',
    '0px 44px 88px rgba(0, 0, 0, 0.35)',
    '0px 48px 96px rgba(0, 0, 0, 0.35)',
    '0px 52px 104px rgba(0, 0, 0, 0.4)',
    '0px 56px 112px rgba(0, 0, 0, 0.4)',
    '0px 60px 120px rgba(0, 0, 0, 0.45)',
    '0px 64px 128px rgba(0, 0, 0, 0.45)',
    '0px 68px 136px rgba(0, 0, 0, 0.5)',
    '0px 72px 144px rgba(0, 0, 0, 0.5)',
    '0px 76px 152px rgba(0, 0, 0, 0.55)',
    '0px 80px 160px rgba(0, 0, 0, 0.55)',
    '0px 84px 168px rgba(0, 0, 0, 0.6)',
    '0px 88px 176px rgba(0, 0, 0, 0.6)',
    '0px 92px 184px rgba(0, 0, 0, 0.65)',
  ],
  components: {
    MuiCssBaseline: {
      styleOverrides: {
        body: {
          scrollbarWidth: 'thin',
          scrollbarColor: `${GREY[400]} ${GREY[100]}`,
          '&::-webkit-scrollbar': {
            width: 8,
            height: 8,
          },
          '&::-webkit-scrollbar-track': {
            background: GREY[100],
          },
          '&::-webkit-scrollbar-thumb': {
            background: GREY[400],
            borderRadius: 4,
          },
          '&::-webkit-scrollbar-thumb:hover': {
            background: GREY[500],
          },
        },
      },
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          backgroundImage: 'none',
          backdropFilter: 'blur(10px)',
          border: '1px solid rgba(255, 255, 255, 0.18)',
          boxShadow: `
            0px 8px 32px rgba(0, 0, 0, 0.1),
            0px 2px 8px rgba(0, 0, 0, 0.05),
            inset 0px 0px 0px 1px rgba(255, 255, 255, 0.2)
          `,
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: 16,
          backdropFilter: 'blur(10px)',
          backgroundColor: 'rgba(255, 255, 255, 0.8)',
          border: '1px solid rgba(255, 255, 255, 0.18)',
          boxShadow: `
            0px 8px 32px rgba(0, 0, 0, 0.1),
            0px 2px 8px rgba(0, 0, 0, 0.05),
            inset 0px 0px 0px 1px rgba(255, 255, 255, 0.2)
          `,
        },
      },
    },
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: 10,
          boxShadow: 'none',
          fontWeight: 600,
          '&:hover': {
            boxShadow: '0px 4px 8px rgba(0, 0, 0, 0.15)',
            transform: 'translateY(-2px)',
          },
          '&:active': {
            transform: 'scale(0.98)',
          },
          transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
        },
        sizeLarge: {
          height: 48,
        },
        containedPrimary: {
          background: PRIMARY.main,
          '&:hover': {
            background: PRIMARY.dark,
          },
          boxShadow: `0 4px 14px 0 ${PRIMARY.main}40`,
        },
        outlinedPrimary: {
          border: `1px solid ${PRIMARY.main}`,
          '&:hover': {
            backgroundColor: `${PRIMARY.lighter}`,
            boxShadow: `0 4px 14px 0 ${PRIMARY.main}30`,
          },
        },
      },
    },
    MuiChip: {
      styleOverrides: {
        root: {
          borderRadius: 8,
          fontWeight: 500,
          '&.MuiChip-colorPrimary': {
            backgroundColor: `${PRIMARY.main}20`,
            color: PRIMARY.dark,
            border: `1px solid ${PRIMARY.main}40`,
          },
          '&.MuiChip-colorSecondary': {
            backgroundColor: `${SECONDARY.main}20`,
            color: SECONDARY.dark,
            border: `1px solid ${SECONDARY.main}40`,
          },
          '&.MuiChip-colorSuccess': {
            backgroundColor: `${SUCCESS.main}20`,
            color: SUCCESS.dark,
            border: `1px solid ${SUCCESS.main}40`,
          },
          '&.MuiChip-colorWarning': {
            backgroundColor: `${WARNING.main}20`,
            color: WARNING.dark,
            border: `1px solid ${WARNING.main}40`,
          },
          '&.MuiChip-colorError': {
            backgroundColor: `${ERROR.main}20`,
            color: ERROR.dark,
            border: `1px solid ${ERROR.main}40`,
          },
          '&.MuiChip-colorInfo': {
            backgroundColor: `${INFO.main}20`,
            color: INFO.dark,
            border: `1px solid ${INFO.main}40`,
          },
        },
      },
    },
    MuiAlert: {
      styleOverrides: {
        root: {
          borderRadius: 10,
          backdropFilter: 'blur(8px)',
          border: '1px solid rgba(255, 255, 255, 0.18)',
        },
      },
    },
    MuiTableContainer: {
      styleOverrides: {
        root: {
          borderRadius: 12,
          overflow: 'hidden',
          backgroundColor: 'rgba(255, 255, 255, 0.7)',
          backdropFilter: 'blur(10px)',
          border: '1px solid rgba(255, 255, 255, 0.18)',
        },
      },
    },
    MuiTableHead: {
      styleOverrides: {
        root: {
          backgroundColor: 'rgba(0, 0, 0, 0.02)',
          '& .MuiTableCell-head': {
            fontWeight: 600,
            borderBottom: '1px solid rgba(255, 255, 255, 0.08)',
          },
        },
      },
    },
    MuiTableRow: {
      styleOverrides: {
        root: {
          '&:nth-of-type(even)': {
            backgroundColor: 'rgba(0, 0, 0, 0.01)', // 斑马纹
          },
          '&:hover': {
            backgroundColor: 'rgba(0, 0, 0, 0.04)',
          },
        },
      },
    },
    MuiTableCell: {
      styleOverrides: {
        root: {
          borderBottom: '1px solid rgba(255, 255, 255, 0.08)',
        },
      },
    },
  },
});

// 创建暗色主题 - 玻璃拟态+微立体感设计
export const darkTheme = createTheme({
  palette: {
    mode: 'dark',
    primary: {
      ...PRIMARY,
      main: '#C084FC', // 更亮的紫色，适合暗色模式
    },
    secondary: {
      ...SECONDARY,
      main: '#E879F9', // 更亮的粉紫色
    },
    info: {
      ...INFO,
      main: '#93C5FD', // 更亮的浅蓝色
    },
    success: {
      ...SUCCESS,
      main: '#6EE7B7', // 更亮的绿色
    },
    warning: {
      ...WARNING,
      main: '#FCD34D', // 更亮的黄色
    },
    error: {
      ...ERROR,
      main: '#FCA5A5', // 更亮的红色
    },
    background: {
      default: '#0F172A', // 深色背景，带有一点蓝色调
      paper: 'rgba(30, 41, 59, 0.85)', // 半透明纸张背景，实现玻璃拟态
    },
    text: {
      primary: '#FFFFFF',
      secondary: 'rgba(255, 255, 255, 0.7)',
      disabled: 'rgba(255, 255, 255, 0.5)',
    },
    divider: 'rgba(255, 255, 255, 0.12)',
  },
  typography: {
    fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
    h1: {
      fontWeight: 700,
      fontSize: '2.5rem',
      lineHeight: 1.2,
    },
    h2: {
      fontWeight: 700,
      fontSize: '2rem',
      lineHeight: 1.3,
    },
    h3: {
      fontWeight: 600,
      fontSize: '1.75rem',
      lineHeight: 1.3,
    },
    h4: {
      fontWeight: 600,
      fontSize: '1.5rem',
      lineHeight: 1.4,
    },
    h5: {
      fontWeight: 600,
      fontSize: '1.25rem',
      lineHeight: 1.4,
    },
    h6: {
      fontWeight: 600,
      fontSize: '1.125rem',
      lineHeight: 1.4,
    },
    body1: {
      fontSize: '1rem',
      lineHeight: 1.6,
    },
    body2: {
      fontSize: '0.875rem',
      lineHeight: 1.6,
    },
    button: {
      fontWeight: 600,
      textTransform: 'none',
    },
  },
  shape: {
    borderRadius: 12,
  },
  shadows: [
    'none',
    '0px 2px 4px rgba(0, 0, 0, 0.2)',
    '0px 4px 8px rgba(0, 0, 0, 0.3)',
    '0px 8px 16px rgba(0, 0, 0, 0.3)',
    '0px 12px 24px rgba(0, 0, 0, 0.4)',
    '0px 16px 32px rgba(0, 0, 0, 0.4)',
    '0px 20px 40px rgba(0, 0, 0, 0.5)',
    '0px 24px 48px rgba(0, 0, 0, 0.5)',
    '0px 28px 56px rgba(0, 0, 0, 0.6)',
    '0px 32px 64px rgba(0, 0, 0, 0.6)',
    '0px 36px 72px rgba(0, 0, 0, 0.7)',
    '0px 40px 80px rgba(0, 0, 0, 0.7)',
    '0px 44px 88px rgba(0, 0, 0, 0.8)',
    '0px 48px 96px rgba(0, 0, 0, 0.8)',
    '0px 52px 104px rgba(0, 0, 0, 0.9)',
    '0px 56px 112px rgba(0, 0, 0, 0.9)',
    '0px 60px 120px rgba(0, 0, 0, 1.0)',
    '0px 64px 128px rgba(0, 0, 0, 1.0)',
    '0px 68px 136px rgba(0, 0, 0, 1.0)',
    '0px 72px 144px rgba(0, 0, 0, 1.0)',
    '0px 76px 152px rgba(0, 0, 0, 1.0)',
    '0px 80px 160px rgba(0, 0, 0, 1.0)',
    '0px 84px 168px rgba(0, 0, 0, 1.0)',
    '0px 88px 176px rgba(0, 0, 0, 1.0)',
    '0px 92px 184px rgba(0, 0, 0, 1.0)',
  ],
  components: {
    MuiCssBaseline: {
      styleOverrides: {
        body: {
          scrollbarWidth: 'thin',
          scrollbarColor: `${GREY[600]} ${GREY[800]}`,
          '&::-webkit-scrollbar': {
            width: 8,
            height: 8,
          },
          '&::-webkit-scrollbar-track': {
            background: GREY[800],
          },
          '&::-webkit-scrollbar-thumb': {
            background: GREY[600],
            borderRadius: 4,
          },
          '&::-webkit-scrollbar-thumb:hover': {
            background: GREY[500],
          },
        },
      },
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          backgroundImage: 'none',
          backdropFilter: 'blur(10px)',
          border: '1px solid rgba(255, 255, 255, 0.08)',
          boxShadow: `
            0px 8px 32px rgba(0, 0, 0, 0.4),
            0px 2px 8px rgba(0, 0, 0, 0.2),
            inset 0px 0px 0px 1px rgba(255, 255, 255, 0.1)
          `,
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: 16,
          backdropFilter: 'blur(10px)',
          backgroundColor: 'rgba(30, 41, 59, 0.8)',
          border: '1px solid rgba(255, 255, 255, 0.08)',
          boxShadow: `
            0px 8px 32px rgba(0, 0, 0, 0.4),
            0px 2px 8px rgba(0, 0, 0, 0.2),
            inset 0px 0px 0px 1px rgba(255, 255, 255, 0.1)
          `,
        },
      },
    },
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: 10,
          boxShadow: 'none',
          fontWeight: 600,
          '&:hover': {
            boxShadow: '0px 4px 8px rgba(0, 0, 0, 0.3)',
            transform: 'translateY(-2px)',
          },
          '&:active': {
            transform: 'scale(0.98)',
          },
          transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
        },
        sizeLarge: {
          height: 48,
        },
        containedPrimary: {
          background: PRIMARY.main,
          '&:hover': {
            background: PRIMARY.light,
          },
          boxShadow: `0 4px 14px 0 ${PRIMARY.main}40`,
        },
        outlinedPrimary: {
          border: `1px solid ${PRIMARY.light}`,
          '&:hover': {
            backgroundColor: `${PRIMARY.main}20`,
            boxShadow: `0 4px 14px 0 ${PRIMARY.main}30`,
          },
        },
      },
    },
    MuiChip: {
      styleOverrides: {
        root: {
          borderRadius: 8,
          fontWeight: 500,
          '&.MuiChip-colorPrimary': {
            backgroundColor: `${PRIMARY.main}30`,
            color: PRIMARY.light,
            border: `1px solid ${PRIMARY.main}60`,
          },
          '&.MuiChip-colorSecondary': {
            backgroundColor: `${SECONDARY.main}30`,
            color: SECONDARY.light,
            border: `1px solid ${SECONDARY.main}60`,
          },
          '&.MuiChip-colorSuccess': {
            backgroundColor: `${SUCCESS.main}30`,
            color: SUCCESS.light,
            border: `1px solid ${SUCCESS.main}60`,
          },
          '&.MuiChip-colorWarning': {
            backgroundColor: `${WARNING.main}30`,
            color: WARNING.light,
            border: `1px solid ${WARNING.main}60`,
          },
          '&.MuiChip-colorError': {
            backgroundColor: `${ERROR.main}30`,
            color: ERROR.light,
            border: `1px solid ${ERROR.main}60`,
          },
          '&.MuiChip-colorInfo': {
            backgroundColor: `${INFO.main}30`,
            color: INFO.light,
            border: `1px solid ${INFO.main}60`,
          },
        },
      },
    },
    MuiAlert: {
      styleOverrides: {
        root: {
          borderRadius: 10,
          backdropFilter: 'blur(8px)',
          border: '1px solid rgba(255, 255, 255, 0.08)',
        },
      },
    },
    MuiTableContainer: {
      styleOverrides: {
        root: {
          borderRadius: 12,
          overflow: 'hidden',
          backgroundColor: 'rgba(30, 41, 59, 0.7)',
          backdropFilter: 'blur(10px)',
          border: '1px solid rgba(255, 255, 255, 0.08)',
        },
      },
    },
    MuiTableHead: {
      styleOverrides: {
        root: {
          backgroundColor: 'rgba(255, 255, 255, 0.05)',
          '& .MuiTableCell-head': {
            fontWeight: 600,
            borderBottom: '1px solid rgba(255, 255, 255, 0.08)',
          },
        },
      },
    },
    MuiTableRow: {
      styleOverrides: {
        root: {
          '&:nth-of-type(even)': {
            backgroundColor: 'rgba(255, 255, 255, 0.02)', // 斑马纹
          },
          '&:hover': {
            backgroundColor: 'rgba(255, 255, 255, 0.04)',
          },
        },
      },
    },
    MuiTableCell: {
      styleOverrides: {
        root: {
          borderBottom: '1px solid rgba(255, 255, 255, 0.08)',
        },
      },
    },
  },
});

// 创建主题函数，支持动态配置
export const createAppTheme = (themeConfig = {}) => {
  const mode = themeConfig.mode || 'dark';
  const baseTheme = mode === 'dark' ? darkTheme : lightTheme;

  // 如果有自定义配置，则合并
  if (themeConfig.primaryColor || themeConfig.backgroundColor || themeConfig.paperColor) {
    const customPrimary = themeConfig.primaryColor ? {
      main: themeConfig.primaryColor,
      light: themeConfig.primaryColor + '80',
      dark: themeConfig.primaryColor + 'CC',
    } : baseTheme.palette.primary;

    const customBackground = {
      default: themeConfig.backgroundColor || baseTheme.palette.background.default,
      paper: themeConfig.paperColor || baseTheme.palette.background.paper,
    };

    return createTheme({
      ...baseTheme,
      palette: {
        ...baseTheme.palette,
        primary: customPrimary,
        background: customBackground,
      },
    });
  }

  return baseTheme;
};
