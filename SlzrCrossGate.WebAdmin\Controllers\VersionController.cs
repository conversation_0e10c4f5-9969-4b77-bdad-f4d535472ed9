using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Reflection;

namespace SlzrCrossGate.WebAdmin.Controllers
{
    /// <summary>
    /// 版本信息控制器
    /// 提供不需要认证的版本信息API
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [AllowAnonymous]
    public class VersionController : ControllerBase
    {
        /// <summary>
        /// 获取应用版本信息（无需认证）
        /// </summary>
        /// <returns>版本信息</returns>
        [HttpGet]
        public IActionResult GetVersion()
        {
            try
            {
                // 获取版本信息
                var assembly = Assembly.GetExecutingAssembly();
                var version = assembly.GetName().Version?.ToString() ?? "1.0.0.0";
                var buildTime = GetBuildTime(assembly);

                return Ok(new
                {
                    version = version,
                    buildTime = buildTime,
                    buildTimeString = buildTime.ToString("yyyy-MM-dd HH:mm:ss"),
                    serverTime = DateTime.Now,
                    serverTimeString = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "获取版本信息失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 获取程序集构建时间
        /// </summary>
        private static DateTime GetBuildTime(Assembly assembly)
        {
            try
            {
                // 尝试从程序集的自定义属性获取构建时间
                var buildTimeAttribute = assembly.GetCustomAttribute<System.Reflection.AssemblyMetadataAttribute>();
                if (buildTimeAttribute?.Key == "BuildTime" && DateTime.TryParse(buildTimeAttribute.Value, out var buildTime))
                {
                    return buildTime;
                }

                // 如果没有自定义属性，使用程序集文件的创建时间
                var location = assembly.Location;
                if (!string.IsNullOrEmpty(location) && System.IO.File.Exists(location))
                {
                    return System.IO.File.GetCreationTime(location);
                }

                // 如果都获取不到，返回当前时间
                return DateTime.Now;
            }
            catch
            {
                // 如果出现任何异常，返回当前时间
                return DateTime.Now;
            }
        }
    }
}
