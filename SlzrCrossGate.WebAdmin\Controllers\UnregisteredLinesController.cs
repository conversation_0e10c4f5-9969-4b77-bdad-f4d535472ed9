using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using SlzrCrossGate.Core.Database;
using SlzrCrossGate.Core.Models;
using SlzrCrossGate.WebAdmin.DTOs;

namespace SlzrCrossGate.WebAdmin.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class UnregisteredLinesController : ControllerBase
    {
        private readonly TcpDbContext _dbContext;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly ILogger<UnregisteredLinesController> _logger;

        public UnregisteredLinesController(
            TcpDbContext dbContext,
            UserManager<ApplicationUser> userManager,
            ILogger<UnregisteredLinesController> logger)
        {
            _dbContext = dbContext;
            _userManager = userManager;
            _logger = logger;
        }

        /// <summary>
        /// 获取未注册线路列表
        /// </summary>
        /// <param name="merchantId">商户ID筛选</param>
        /// <param name="page">页码</param>
        /// <param name="pageSize">每页大小</param>
        /// <returns>未注册线路列表</returns>
        [HttpGet]
        public async Task<ActionResult<PaginatedResult<UnregisteredLineDto>>> GetUnregisteredLines(
            [FromQuery] string? merchantId,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 10)
        {
            try
            {
                // 获取当前用户
                var currentUser = await _userManager.GetUserAsync(User);
                if (currentUser == null)
                {
                    return Unauthorized();
                }

                // 检查是否是系统管理员
                var isSystemAdmin = await _userManager.IsInRoleAsync(currentUser, "SystemAdmin");

                _logger.LogInformation("查询未注册线路 - 用户: {UserId}, 是否系统管理员: {IsSystemAdmin}, 商户ID筛选: {MerchantId}",
                    currentUser.Id, isSystemAdmin, merchantId);

                // 构建查询：查找在Terminal表中存在但在LinePriceInfo表中不存在的线路
                var query = from t in _dbContext.Terminals
                            join m in _dbContext.Merchants on t.MerchantID equals m.MerchantID
                            where !t.IsDeleted
                            group t by new { t.MerchantID, t.LineNO, m.Name } into g
                            where !_dbContext.LinePriceInfos.Any(lp => lp.MerchantID == g.Key.MerchantID && (lp.LineNumber == g.Key.LineNO || lp.LineNumber + lp.GroupNumber == g.Key.LineNO))
                            select new UnregisteredLineDto
                            {
                                MerchantID = g.Key.MerchantID,
                                MerchantName = g.Key.Name ?? "",
                                LineNO = g.Key.LineNO,
                                TerminalCount = g.Count()
                            };

                // 如果不是系统管理员，只能查看自己商户的数据
                if (!isSystemAdmin && !string.IsNullOrEmpty(currentUser.MerchantID))
                {
                    query = query.Where(ul => ul.MerchantID == currentUser.MerchantID);
                }
                // 如果传入了商户ID参数，则按商户ID筛选
                else if (!string.IsNullOrEmpty(merchantId))
                {
                    query = query.Where(ul => ul.MerchantID == merchantId);
                }

                // 排序
                query = query.OrderBy(ul => ul.MerchantID).ThenBy(ul => ul.LineNO);

                // 计算总记录数
                var totalCount = await query.CountAsync();

                // 分页
                var unregisteredLines = await query
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                // 返回分页结果
                return Ok(new PaginatedResult<UnregisteredLineDto>
                {
                    Items = unregisteredLines,
                    TotalCount = totalCount,
                    Page = page,
                    PageSize = pageSize
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取未注册线路列表时发生错误");
                return StatusCode(500, new { message = "服务器内部错误" });
            }
        }

        /// <summary>
        /// 导出未注册线路数据
        /// </summary>
        /// <param name="merchantId">商户ID筛选</param>
        /// <returns>CSV文件</returns>
        [HttpGet("export")]
        public async Task<IActionResult> ExportUnregisteredLines([FromQuery] string? merchantId)
        {
            try
            {
                // 获取当前用户
                var currentUser = await _userManager.GetUserAsync(User);
                if (currentUser == null)
                {
                    return Unauthorized();
                }

                // 检查是否是系统管理员
                var isSystemAdmin = await _userManager.IsInRoleAsync(currentUser, "SystemAdmin");

                // 构建查询
                var query = from t in _dbContext.Terminals
                           join m in _dbContext.Merchants on t.MerchantID equals m.MerchantID
                           where !t.IsDeleted
                           group t by new { t.MerchantID, t.LineNO, m.Name } into g
                           where !_dbContext.LinePriceInfos.Any(lp => lp.MerchantID == g.Key.MerchantID && lp.LineNumber == g.Key.LineNO)
                           select new UnregisteredLineDto
                           {
                               MerchantID = g.Key.MerchantID,
                               MerchantName = g.Key.Name ?? "",
                               LineNO = g.Key.LineNO,
                               TerminalCount = g.Count()
                           };

                // 权限控制
                if (!isSystemAdmin && !string.IsNullOrEmpty(currentUser.MerchantID))
                {
                    query = query.Where(ul => ul.MerchantID == currentUser.MerchantID);
                }
                else if (!string.IsNullOrEmpty(merchantId))
                {
                    query = query.Where(ul => ul.MerchantID == merchantId);
                }

                // 获取数据（限制最大5000条记录）
                var unregisteredLines = await query
                    .OrderBy(ul => ul.MerchantID)
                    .ThenBy(ul => ul.LineNO)
                    .Take(5000)
                    .ToListAsync();

                // 创建CSV内容
                var csv = new System.Text.StringBuilder();
                csv.AppendLine("商户ID,商户名称,线路编号,终端数量");

                foreach (var line in unregisteredLines)
                {
                    csv.AppendLine($"{line.MerchantID},{line.MerchantName},{line.LineNO},{line.TerminalCount}");
                }

                // 返回CSV文件
                var csvBytes = System.Text.Encoding.UTF8.GetBytes(csv.ToString());
                var fileName = $"未注册线路_{DateTime.Now:yyyyMMddHHmmss}.csv";
                return File(csvBytes, "text/csv", fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导出未注册线路数据时发生错误");
                return StatusCode(500, new { message = "服务器内部错误" });
            }
        }
    }
}
