using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace SlzrCrossGate.Core.Models
{
    /// <summary>
    /// 菜单项
    /// </summary>
    [Table("MenuItems")]
    public class MenuItem
    {
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// 所属菜单分组ID
        /// </summary>
        public int MenuGroupId { get; set; }

        /// <summary>
        /// 菜单项标识符（用于前端识别）
        /// </summary>
        [Required]
        [StringLength(50)]
        public required string ItemKey { get; set; }

        /// <summary>
        /// 菜单标题
        /// </summary>
        [Required]
        [StringLength(100)]
        public required string Title { get; set; }

        /// <summary>
        /// 路由路径
        /// </summary>
        [Required]
        [StringLength(200)]
        public required string Href { get; set; }

        /// <summary>
        /// 图标名称（对应前端图标组件）
        /// </summary>
        [StringLength(50)]
        public string? IconName { get; set; }

        /// <summary>
        /// 排序顺序
        /// </summary>
        public int SortOrder { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 系统管理员是否可见
        /// </summary>
        public bool VisibleToSystemAdmin { get; set; } = true;

        /// <summary>
        /// 商户管理员是否可见
        /// </summary>
        public bool VisibleToMerchantAdmin { get; set; } = true;

        /// <summary>
        /// 普通用户是否可见
        /// </summary>
        public bool VisibleToUser { get; set; } = true;

        /// <summary>
        /// 是否为外部链接
        /// </summary>
        public bool IsExternal { get; set; } = false;

        /// <summary>
        /// 链接打开方式（_self, _blank, _iframe）
        /// </summary>
        [StringLength(20)]
        public string Target { get; set; } = "_self";

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 所属菜单分组
        /// </summary>
        [ForeignKey("MenuGroupId")]
        public virtual MenuGroup? MenuGroup { get; set; }
    }
}
