# SlzrCrossGate 生产环境部署指南

## 文档信息

- **项目名称**: SlzrCrossGate 终端管理系统
- **部署环境**: 生产环境 (广佛通客户服务器)
- **服务器路径**: `/opt/tcpserver`
- **文档版本**: v1.0
- **创建日期**: 2025-08-10
- **适用范围**: 运维团队、部署工程师

## 目录

1. [部署架构概述](#1-部署架构概述)
2. [服务器环境要求](#2-服务器环境要求)
3. [部署文件结构](#3-部署文件结构)
4. [Docker Compose配置](#4-docker-compose配置)
5. [环境变量配置](#5-环境变量配置)
6. [部署步骤](#6-部署步骤)
7. [服务管理](#7-服务管理)
8. [监控和维护](#8-监控和维护)
9. [故障排除](#9-故障排除)
10. [备份和恢复](#10-备份和恢复)

## 1. 部署架构概述

### 1.1 系统架构

SlzrCrossGate系统采用微服务架构，包含以下核心组件：

```
生产环境部署架构
├── MySQL 数据库 (端口: 3306)
│   ├── 数据持久化: mysql_data volume
│   └── 健康检查: mysqladmin ping
├── RabbitMQ 消息队列 (端口: 5672, 15672)
│   ├── 数据持久化: rabbitmq_data, rabbitmq_logs volumes
│   └── 管理界面: http://server:15672
├── MinIO 对象存储 (内部端口: 9000, 9001)
│   ├── 数据持久化: minio_data volume
│   └── 健康检查: /minio/health/live
├── API Service (端口: 8822)
│   ├── TCP服务: 处理终端连接
│   ├── HTTP API: 内部服务接口
│   └── 数据存储: api_storage volume
└── WebAdmin 管理界面 (端口: 18822)
    ├── Web界面: React前端
    ├── 配置文件: webadmin-config目录
    └── 数据存储: webadmin_storage, webadmin_keys volumes
```

### 1.2 网络配置

- **内部网络**: slzr-network (bridge模式)
- **外部访问端口**:
  - WebAdmin: 18822 (HTTP)
  - API TCP服务: 8822 (TCP)
  - MySQL: 3306 (数据库)
  - RabbitMQ管理界面: 15672 (HTTP)

### 1.3 数据持久化

所有关键数据通过Docker volumes进行持久化：
- `mysql_data`: MySQL数据库文件
- `rabbitmq_data`: RabbitMQ数据文件
- `rabbitmq_logs`: RabbitMQ日志文件
- `minio_data`: MinIO对象存储数据
- `api_storage`: API服务存储文件
- `webadmin_storage`: WebAdmin存储文件
- `webadmin_keys`: WebAdmin密钥文件

## 2. 服务器环境要求

### 2.1 硬件要求

**最低配置**:
- CPU: 4核心
- 内存: 8GB RAM
- 存储: 100GB可用空间
- 网络: 100Mbps带宽

**推荐配置**:
- CPU: 8核心
- 内存: 16GB RAM
- 存储: 500GB SSD
- 网络: 1Gbps带宽

### 2.2 软件要求

- **操作系统**: Linux (Ubuntu 20.04+ / CentOS 8+ 推荐)
- **Docker**: 20.10.0+
- **Docker Compose**: 2.0.0+
- **时区**: Asia/Shanghai

### 2.3 网络要求

- **防火墙端口开放**:
  - 18822 (WebAdmin HTTP)
  - 8822 (API TCP服务)
  - 3306 (MySQL，可选)
  - 15672 (RabbitMQ管理界面，可选)

## 3. 部署文件结构

### 3.1 服务器目录结构

```
/opt/tcpserver/
├── docker-compose-registry.yml    # Docker Compose配置文件
├── registry-password.txt          # 镜像仓库密码
├── webadmin-config/               # WebAdmin配置目录
│   └── terminalFields.json       # 终端字段配置文件
├── .env                          # 环境变量文件 (可选)
├── logs/                         # 日志目录 (可选)
└── backups/                      # 备份目录 (可选)
```

### 3.2 配置文件说明

#### docker-compose-registry.yml
主要的Docker Compose配置文件，定义了所有服务的配置。

#### registry-password.txt
包含私有镜像仓库的访问密码，用于拉取镜像。

#### webadmin-config/terminalFields.json
WebAdmin终端字段配置文件，支持动态字段配置和自定义显示。

## 4. Docker Compose配置

### 4.1 镜像仓库配置

系统使用私有镜像仓库：`devtest.pointlife365.net:5180`

**镜像列表**:
- `devtest.pointlife365.net:5180/library/mysql:8.0`
- `devtest.pointlife365.net:5180/library/rabbitmq:3.13.7-management`
- `devtest.pointlife365.net:5180/library/minio:latest`
- `devtest.pointlife365.net:5180/slzr/tcpserver-api:latest`
- `devtest.pointlife365.net:5180/slzr/tcpserver-web:latest`

### 4.2 服务配置详解

#### MySQL数据库服务
```yaml
mysql:
  image: devtest.pointlife365.net:5180/library/mysql:8.0
  container_name: slzr-mysql
  command: --default-authentication-plugin=mysql_native_password
  environment:
    MYSQL_ROOT_PASSWORD: **********
    MYSQL_DATABASE: tcpserver
    MYSQL_USER: slzr_user
    MYSQL_PASSWORD: sldb!12345
  ports:
    - "3306:3306"
  volumes:
    - mysql_data:/var/lib/mysql
  healthcheck:
    test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
    interval: 10s
    timeout: 5s
    retries: 5
```

#### RabbitMQ消息队列服务
```yaml
rabbitmq:
  image: devtest.pointlife365.net:5180/library/rabbitmq:3.13.7-management
  container_name: slzr-rabbitmq
  hostname: slzr-rabbitmq
  environment:
    - RABBITMQ_DEFAULT_USER=guest
    - RABBITMQ_DEFAULT_PASS=guest
    - RABBITMQ_NODENAME=rabbit@slzr-rabbitmq
    - RABBITMQ_USE_LONGNAME=true
  ports:
    - "5672:5672"   # AMQP端口
    - "15672:15672" # 管理界面端口
  volumes:
    - rabbitmq_data:/var/lib/rabbitmq
    - rabbitmq_logs:/var/log/rabbitmq
```

#### MinIO对象存储服务
```yaml
minio:
  image: devtest.pointlife365.net:5180/library/minio:latest
  container_name: minio
  command: server /data --console-address ":9001"
  environment:
    - MINIO_ROOT_USER=minioadmin
    - MINIO_ROOT_PASSWORD=minioadmin123
  volumes:
    - minio_data:/data
  healthcheck:
    test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
    interval: 30s
    timeout: 20s
    retries: 3
```

#### API服务
```yaml
api-service:
  image: devtest.pointlife365.net:5180/slzr/tcpserver-api:latest
  container_name: tcpserver-api
  ports:
    - "8822:8001"  # TCP服务端口
  environment:
    - ASPNETCORE_ENVIRONMENT=Production
    - HTTP_PORT=8000
    - TCP_PORT=8001
    - ConnectionStrings__DefaultConnection=Server=host.docker.internal;Port=3306;Database=tcpserver;User=slzr_user;Password=**********;SslMode=None;AllowLoadLocalInfile=true;
    - DatabaseProvider=MySql
    - RabbitMQ__HostName=slzr-rabbitmq
    - FileService__DefaultStorageType=MinIO
    - FileService__MinIO__Endpoint=minio:9000
    - TZ=Asia/Shanghai
  volumes:
    - api_storage:/app/storage
    - /etc/localtime:/etc/localtime:ro
```

#### WebAdmin管理界面服务
```yaml
web-admin:
  image: devtest.pointlife365.net:5180/slzr/tcpserver-web:latest
  container_name: tcpserver-web
  ports:
    - "18822:80"
  environment:
    - ASPNETCORE_ENVIRONMENT=Production
    - ConnectionStrings__DefaultConnection=Server=host.docker.internal;Port=3306;Database=tcpserver;User=slzr_user;Password=**********;SslMode=None;AllowLoadLocalInfile=true;
    - DatabaseProvider=MySql
    - RabbitMQ__HostName=slzr-rabbitmq
    - Jwt__Key=YourSecretKeyHere12345678901234567890
    - FileService__DefaultStorageType=MinIO
    - FileService__MinIO__Endpoint=minio:9000
    - TZ=Asia/Shanghai
  volumes:
    - webadmin_storage:/app/storage
    - webadmin_keys:/app/Keys
    - /etc/localtime:/etc/localtime:ro
    - ./webadmin-config:/app/wwwroot/config
```

## 5. 环境变量配置

### 5.1 默认环境变量

系统使用以下默认环境变量，可通过`.env`文件或系统环境变量覆盖：

```bash
# 数据库配置
MYSQL_HOST=host.docker.internal
MYSQL_ROOT_PASSWORD=**********
MYSQL_DATABASE=tcpserver
MYSQL_USER=slzr_user
MYSQL_PASSWORD=sldb!12345

# RabbitMQ配置
RABBITMQ_HOST=slzr-rabbitmq
RABBITMQ_PORT=5672
RABBITMQ_USER=guest
RABBITMQ_PASS=guest

# MinIO配置
MINIO_ROOT_USER=minioadmin
MINIO_ROOT_PASSWORD=minioadmin123
MINIO_BUCKET_NAME=slzr-files

# 应用配置
JWT_KEY=YourSecretKeyHere12345678901234567890
FILE_STORAGE_TYPE=MinIO
```

### 5.2 安全配置建议

**生产环境安全配置**:
```bash
# 强密码配置
MYSQL_ROOT_PASSWORD=your_strong_mysql_password
MYSQL_PASSWORD=your_strong_user_password
RABBITMQ_PASS=your_strong_rabbitmq_password
MINIO_ROOT_PASSWORD=your_strong_minio_password

# JWT密钥 (至少32位随机字符串)
JWT_KEY=your_very_long_and_random_jwt_secret_key_here_32_chars_minimum
```

## 6. 部署步骤

### 6.1 前置准备

#### 6.1.1 安装Docker和Docker Compose
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install docker.io docker-compose-plugin

# CentOS/RHEL
sudo yum install docker docker-compose-plugin

# 启动Docker服务
sudo systemctl start docker
sudo systemctl enable docker

# 验证安装
docker --version
docker compose version
```

#### 6.1.2 创建部署目录
```bash
# 创建部署目录
sudo mkdir -p /opt/tcpserver
cd /opt/tcpserver

# 设置目录权限
sudo chown -R $USER:$USER /opt/tcpserver
```

#### 6.1.3 登录私有镜像仓库
```bash
# 使用registry-password.txt中的密码登录
docker login devtest.pointlife365.net:5180 -u slzr -p slzr.12345
```

### 6.2 部署文件准备

#### 6.2.1 上传部署文件
将以下文件上传到 `/opt/tcpserver` 目录：
- `docker-compose-registry.yml`
- `registry-password.txt`
- `webadmin-config/terminalFields.json`

#### 6.2.2 创建环境变量文件 (可选)
```bash
# 创建.env文件
cat > .env << EOF
MYSQL_ROOT_PASSWORD=your_production_password
MYSQL_PASSWORD=your_production_user_password
RABBITMQ_PASS=your_production_rabbitmq_password
MINIO_ROOT_PASSWORD=your_production_minio_password
JWT_KEY=your_production_jwt_key_32_chars_minimum
EOF

# 设置文件权限
chmod 600 .env
```

### 6.3 启动服务

#### 6.3.1 拉取镜像
```bash
cd /opt/tcpserver
docker compose -f docker-compose-registry.yml pull
```

#### 6.3.2 启动所有服务
```bash
# 后台启动所有服务
docker compose -f docker-compose-registry.yml up -d

# 查看服务状态
docker compose -f docker-compose-registry.yml ps
```

#### 6.3.3 验证服务启动
```bash
# 检查容器状态
docker ps

# 检查服务日志
docker compose -f docker-compose-registry.yml logs -f web-admin
docker compose -f docker-compose-registry.yml logs -f api-service
```

### 6.4 初始化验证

#### 6.4.1 健康检查
```bash
# WebAdmin健康检查
curl -f http://localhost:18822/health

# API服务健康检查 (内部)
docker exec tcpserver-api curl -f http://localhost:8000/health

# MinIO健康检查
docker exec minio curl -f http://localhost:9000/minio/health/live
```

#### 6.4.2 访问验证
- **WebAdmin管理界面**: http://server_ip:18822
- **RabbitMQ管理界面**: http://server_ip:15672 (guest/guest)
- **TCP服务端口**: server_ip:8822

## 7. 服务管理

### 7.1 常用管理命令

#### 7.1.1 服务控制
```bash
cd /opt/tcpserver

# 启动所有服务
docker compose -f docker-compose-registry.yml up -d

# 停止所有服务
docker compose -f docker-compose-registry.yml down

# 重启所有服务
docker compose -f docker-compose-registry.yml restart

# 重启单个服务
docker compose -f docker-compose-registry.yml restart web-admin
docker compose -f docker-compose-registry.yml restart api-service
```

#### 7.1.2 服务状态查看
```bash
# 查看服务状态
docker compose -f docker-compose-registry.yml ps

# 查看服务日志
docker compose -f docker-compose-registry.yml logs -f
docker compose -f docker-compose-registry.yml logs -f web-admin
docker compose -f docker-compose-registry.yml logs -f api-service

# 查看资源使用情况
docker stats
```

#### 7.1.3 镜像更新
```bash
# 拉取最新镜像
docker compose -f docker-compose-registry.yml pull

# 重新创建并启动服务
docker compose -f docker-compose-registry.yml up -d --force-recreate

# 清理旧镜像
docker image prune -f
```

### 7.2 配置文件管理

#### 7.2.1 WebAdmin配置更新
```bash
# 编辑终端字段配置
vi /opt/tcpserver/webadmin-config/terminalFields.json

# 重启WebAdmin服务使配置生效
docker compose -f docker-compose-registry.yml restart web-admin
```

#### 7.2.2 环境变量更新
```bash
# 编辑环境变量
vi /opt/tcpserver/.env

# 重新启动相关服务
docker compose -f docker-compose-registry.yml up -d --force-recreate
```

## 8. 监控和维护

### 8.1 系统监控

#### 8.1.1 服务健康监控脚本
```bash
#!/bin/bash
# health-check.sh - 服务健康检查脚本

echo "=== 服务状态检查 ==="
cd /opt/tcpserver
docker compose -f docker-compose-registry.yml ps

echo ""
echo "=== WebAdmin健康检查 ==="
curl -f http://localhost:18822/health || echo "WebAdmin健康检查失败"

echo ""
echo "=== 容器资源使用情况 ==="
docker stats --no-stream

echo ""
echo "=== 系统资源使用情况 ==="
free -h
df -h

echo ""
echo "=== 网络连接检查 ==="
netstat -tulpn | grep -E "(18822|8822|3306|5672|15672)"
```

#### 8.1.2 日志监控
```bash
# 创建日志监控脚本
#!/bin/bash
# log-monitor.sh - 日志监控脚本

LOG_DIR="/opt/tcpserver/logs"
mkdir -p $LOG_DIR

# 导出容器日志
docker compose -f docker-compose-registry.yml logs --since 24h web-admin > $LOG_DIR/webadmin-$(date +%Y%m%d).log
docker compose -f docker-compose-registry.yml logs --since 24h api-service > $LOG_DIR/api-service-$(date +%Y%m%d).log

# 检查错误日志
echo "=== 最近的错误日志 ==="
docker compose -f docker-compose-registry.yml logs --since 1h | grep -i error | tail -20
```

### 8.2 性能监控

#### 8.2.1 数据库性能监控
```bash
# MySQL性能检查
docker exec slzr-mysql mysql -uroot -p********** -e "SHOW PROCESSLIST;"
docker exec slzr-mysql mysql -uroot -p********** -e "SHOW ENGINE INNODB STATUS\G" | grep -A 20 "LATEST DETECTED DEADLOCK"
```

#### 8.2.2 存储空间监控
```bash
# Docker volumes使用情况
docker system df -v

# 数据库大小检查
docker exec slzr-mysql mysql -uroot -p********** -e "SELECT table_schema AS 'Database', ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'Size (MB)' FROM information_schema.tables WHERE table_schema = 'tcpserver' GROUP BY table_schema;"
```

## 9. 故障排除

### 9.1 常见问题及解决方案

#### 9.1.1 服务启动失败

**问题**: 容器启动失败或异常退出
```bash
# 检查容器状态和错误信息
docker compose -f docker-compose-registry.yml ps
docker compose -f docker-compose-registry.yml logs service_name

# 常见解决方案
# 1. 检查端口占用
netstat -tulpn | grep :18822
netstat -tulpn | grep :8822

# 2. 检查磁盘空间
df -h

# 3. 检查内存使用
free -h

# 4. 重新创建容器
docker compose -f docker-compose-registry.yml down
docker compose -f docker-compose-registry.yml up -d --force-recreate
```

#### 9.1.2 数据库连接问题

**问题**: 应用无法连接到MySQL数据库
```bash
# 检查MySQL容器状态
docker exec slzr-mysql mysqladmin ping -h localhost -u root -p**********

# 检查数据库连接
docker exec slzr-mysql mysql -uroot -p********** -e "SHOW DATABASES;"

# 检查用户权限
docker exec slzr-mysql mysql -uroot -p********** -e "SELECT User, Host FROM mysql.user WHERE User='slzr_user';"

# 重置用户权限
docker exec slzr-mysql mysql -uroot -p********** -e "
GRANT ALL PRIVILEGES ON tcpserver.* TO 'slzr_user'@'%';
FLUSH PRIVILEGES;"
```

#### 9.1.3 RabbitMQ连接问题

**问题**: 消息队列连接失败
```bash
# 检查RabbitMQ状态
docker exec slzr-rabbitmq rabbitmqctl status

# 检查队列状态
docker exec slzr-rabbitmq rabbitmqctl list_queues

# 重置RabbitMQ数据 (谨慎操作)
docker compose -f docker-compose-registry.yml stop rabbitmq
docker volume rm tcpserver_rabbitmq_data
docker compose -f docker-compose-registry.yml up -d rabbitmq
```

#### 9.1.4 MinIO存储问题

**问题**: 文件上传或访问失败
```bash
# 检查MinIO健康状态
docker exec minio curl -f http://localhost:9000/minio/health/live

# 检查存储桶
docker exec minio mc ls local/

# 重新创建存储桶
docker exec minio mc mb local/slzr-files
```

#### 9.1.5 网络连接问题

**问题**: 服务间网络通信失败
```bash
# 检查Docker网络
docker network ls
docker network inspect tcpserver_slzr-network

# 测试容器间连通性
docker exec tcpserver-web ping slzr-mysql
docker exec tcpserver-web ping slzr-rabbitmq
docker exec tcpserver-web ping minio

# 重建网络 (谨慎操作)
docker compose -f docker-compose-registry.yml down
docker network prune
docker compose -f docker-compose-registry.yml up -d
```

### 9.2 性能问题排查

#### 9.2.1 高CPU使用率
```bash
# 查看容器CPU使用情况
docker stats --no-stream

# 查看系统进程
top -p $(docker inspect --format '{{.State.Pid}}' tcpserver-web)
top -p $(docker inspect --format '{{.State.Pid}}' tcpserver-api)

# 检查应用日志中的性能问题
docker compose -f docker-compose-registry.yml logs --since 1h | grep -i "slow\|timeout\|performance"
```

#### 9.2.2 内存不足
```bash
# 检查内存使用情况
free -h
docker stats --no-stream

# 检查是否有内存泄漏
docker compose -f docker-compose-registry.yml logs | grep -i "OutOfMemory\|memory"

# 重启高内存使用的服务
docker compose -f docker-compose-registry.yml restart service_name
```

#### 9.2.3 磁盘空间不足
```bash
# 检查磁盘使用情况
df -h
docker system df

# 清理Docker资源
docker system prune -f
docker volume prune -f
docker image prune -a -f

# 清理日志文件
find /var/lib/docker/containers -name "*.log" -exec truncate -s 0 {} \;
```

## 10. 备份和恢复

### 10.1 数据备份

#### 10.1.1 数据库备份
```bash
#!/bin/bash
# mysql-backup.sh - MySQL数据库备份脚本

BACKUP_DIR="/opt/tcpserver/backups/mysql"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="tcpserver"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 执行备份
docker exec slzr-mysql mysqldump -uroot -p********** \
  --single-transaction \
  --routines \
  --triggers \
  --events \
  --hex-blob \
  $DB_NAME > $BACKUP_DIR/backup_${DB_NAME}_${DATE}.sql

# 压缩备份文件
gzip $BACKUP_DIR/backup_${DB_NAME}_${DATE}.sql

# 删除7天前的备份
find $BACKUP_DIR -name "backup_${DB_NAME}_*.sql.gz" -mtime +7 -delete

echo "数据库备份完成: backup_${DB_NAME}_${DATE}.sql.gz"
```

#### 10.1.2 配置文件备份
```bash
#!/bin/bash
# config-backup.sh - 配置文件备份脚本

BACKUP_DIR="/opt/tcpserver/backups/config"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份配置文件
tar -czf $BACKUP_DIR/config_backup_${DATE}.tar.gz \
  -C /opt/tcpserver \
  docker-compose-registry.yml \
  webadmin-config/ \
  .env 2>/dev/null || true

echo "配置文件备份完成: config_backup_${DATE}.tar.gz"
```

#### 10.1.3 Docker volumes备份
```bash
#!/bin/bash
# volumes-backup.sh - Docker volumes备份脚本

BACKUP_DIR="/opt/tcpserver/backups/volumes"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份各个volume
docker run --rm -v tcpserver_mysql_data:/data -v $BACKUP_DIR:/backup alpine \
  tar -czf /backup/mysql_data_${DATE}.tar.gz -C /data .

docker run --rm -v tcpserver_rabbitmq_data:/data -v $BACKUP_DIR:/backup alpine \
  tar -czf /backup/rabbitmq_data_${DATE}.tar.gz -C /data .

docker run --rm -v tcpserver_minio_data:/data -v $BACKUP_DIR:/backup alpine \
  tar -czf /backup/minio_data_${DATE}.tar.gz -C /data .

echo "Docker volumes备份完成"
```

### 10.2 数据恢复

#### 10.2.1 数据库恢复
```bash
#!/bin/bash
# mysql-restore.sh - MySQL数据库恢复脚本

BACKUP_FILE=$1
if [ -z "$BACKUP_FILE" ]; then
  echo "用法: $0 <backup_file.sql.gz>"
  exit 1
fi

# 停止相关服务
docker compose -f docker-compose-registry.yml stop web-admin api-service

# 解压并恢复数据库
gunzip -c $BACKUP_FILE | docker exec -i slzr-mysql mysql -uroot -p********** tcpserver

# 重启服务
docker compose -f docker-compose-registry.yml start web-admin api-service

echo "数据库恢复完成"
```

#### 10.2.2 配置文件恢复
```bash
#!/bin/bash
# config-restore.sh - 配置文件恢复脚本

BACKUP_FILE=$1
if [ -z "$BACKUP_FILE" ]; then
  echo "用法: $0 <config_backup.tar.gz>"
  exit 1
fi

# 停止所有服务
docker compose -f docker-compose-registry.yml down

# 恢复配置文件
tar -xzf $BACKUP_FILE -C /opt/tcpserver

# 重启服务
docker compose -f docker-compose-registry.yml up -d

echo "配置文件恢复完成"
```

### 10.3 自动化备份

#### 10.3.1 定时备份配置
```bash
# 添加到crontab
crontab -e

# 每天凌晨2点执行数据库备份
0 2 * * * /opt/tcpserver/scripts/mysql-backup.sh

# 每周日凌晨3点执行完整备份
0 3 * * 0 /opt/tcpserver/scripts/full-backup.sh
```

#### 10.3.2 备份监控脚本
```bash
#!/bin/bash
# backup-monitor.sh - 备份监控脚本

BACKUP_DIR="/opt/tcpserver/backups"
LOG_FILE="/opt/tcpserver/logs/backup.log"

# 检查最近24小时内是否有备份
LATEST_BACKUP=$(find $BACKUP_DIR -name "*.gz" -mtime -1 | wc -l)

if [ $LATEST_BACKUP -eq 0 ]; then
  echo "$(date): 警告 - 最近24小时内没有备份文件" >> $LOG_FILE
  # 发送告警邮件或通知
else
  echo "$(date): 备份检查正常 - 发现 $LATEST_BACKUP 个备份文件" >> $LOG_FILE
fi
```

## 11. 安全配置

### 11.1 防火墙配置

```bash
# Ubuntu/Debian (ufw)
sudo ufw allow 18822/tcp  # WebAdmin
sudo ufw allow 8822/tcp   # API TCP服务
sudo ufw allow 22/tcp     # SSH
sudo ufw enable

# CentOS/RHEL (firewalld)
sudo firewall-cmd --permanent --add-port=18822/tcp
sudo firewall-cmd --permanent --add-port=8822/tcp
sudo firewall-cmd --reload
```

### 11.2 SSL/TLS配置 (可选)

如需启用HTTPS，可在前端配置反向代理：

```nginx
# nginx配置示例
server {
    listen 443 ssl;
    server_name your-domain.com;

    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;

    location / {
        proxy_pass http://localhost:18822;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

---

## 附录

### A. 端口列表

| 服务 | 内部端口 | 外部端口 | 协议 | 说明 |
|------|----------|----------|------|------|
| WebAdmin | 80 | 18822 | HTTP | Web管理界面 |
| API Service | 8001 | 8822 | TCP | 终端TCP连接 |
| MySQL | 3306 | 3306 | TCP | 数据库服务 |
| RabbitMQ AMQP | 5672 | 5672 | TCP | 消息队列 |
| RabbitMQ Management | 15672 | 15672 | HTTP | 管理界面 |
| MinIO API | 9000 | - | HTTP | 对象存储API |
| MinIO Console | 9001 | - | HTTP | 管理控制台 |

### B. 默认账户信息（非实际密码）

| 服务 | 用户名 | 密码 | 说明 |
|------|--------|------|------|
| MySQL root | root | ********** | 数据库管理员 |
| MySQL user | slzr_user | sldb!12345 | 应用数据库用户 |
| RabbitMQ | guest | guest | 消息队列管理 |
| MinIO | minioadmin | minioadmin123 | 对象存储管理 |

### C. 重要文件路径

| 类型 | 路径 | 说明 |
|------|------|------|
| 部署配置 | /opt/tcpserver/docker-compose-registry.yml | 主配置文件 |
| WebAdmin配置 | /opt/tcpserver/webadmin-config/ | 前端配置目录 |
| 数据库数据 | Docker volume: mysql_data | MySQL数据文件 |
| 消息队列数据 | Docker volume: rabbitmq_data | RabbitMQ数据文件 |
| 对象存储数据 | Docker volume: minio_data | MinIO数据文件 |
| 应用存储 | Docker volume: webadmin_storage | WebAdmin存储文件 |

---

**文档版本**: v1.0
**最后更新**: 2025-08-27
**维护人员**: 运维团队

*本文档基于广佛通客户服务器的实际部署配置编写，请根据具体环境调整相关参数。*
