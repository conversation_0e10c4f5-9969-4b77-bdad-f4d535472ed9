using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Text.Json;

namespace SlzrCrossGate.WebAdmin.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class ConfigController : ControllerBase
    {
        private readonly ILogger<ConfigController> _logger;
        private readonly IWebHostEnvironment _environment;

        public ConfigController(ILogger<ConfigController> logger, IWebHostEnvironment environment)
        {
            _logger = logger;
            _environment = environment;
        }

        /// <summary>
        /// 获取终端字段配置
        /// </summary>
        [HttpGet("terminal-fields")]
        public async Task<ActionResult> GetTerminalFieldsConfig()
        {
            try
            {
                var configPath = Path.Combine(_environment.WebRootPath, "config", "terminalFields.json");
                
                if (!System.IO.File.Exists(configPath))
                {
                    return NotFound("配置文件不存在");
                }

                var configContent = await System.IO.File.ReadAllTextAsync(configPath);
                var config = JsonSerializer.Deserialize<object>(configContent);
                
                return Ok(config);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "读取终端字段配置失败");
                return StatusCode(500, "读取配置失败");
            }
        }

        /// <summary>
        /// 保存终端字段配置
        /// </summary>
        [HttpPost("terminal-fields")]
        [Authorize(Roles = "SystemAdmin")]
        public async Task<ActionResult> SaveTerminalFieldsConfig([FromBody] JsonElement config)
        {
            try
            {
                var configPath = Path.Combine(_environment.WebRootPath, "config", "terminalFields.json");
                var configDir = Path.GetDirectoryName(configPath);
                
                // 确保目录存在
                if (!Directory.Exists(configDir))
                {
                    Directory.CreateDirectory(configDir!);
                }

                // 备份原配置文件
                if (System.IO.File.Exists(configPath))
                {
                    var backupPath = Path.Combine(configDir!, $"terminalFields.backup.{DateTime.Now:yyyyMMddHHmmss}.json");
                    System.IO.File.Copy(configPath, backupPath);
                    _logger.LogInformation("已备份原配置文件到: {BackupPath}", backupPath);
                }

                // 格式化JSON并保存
                var options = new JsonSerializerOptions
                {
                    WriteIndented = true,
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                };

                var formattedJson = JsonSerializer.Serialize(config, options);
                await System.IO.File.WriteAllTextAsync(configPath, formattedJson);

                _logger.LogInformation("终端字段配置已保存到: {ConfigPath}", configPath);
                
                return Ok(new { message = "配置保存成功", path = configPath });
            }
            catch (JsonException ex)
            {
                _logger.LogError(ex, "配置JSON格式错误");
                return BadRequest("配置格式错误: " + ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存终端字段配置失败");
                return StatusCode(500, "保存配置失败: " + ex.Message);
            }
        }

        /// <summary>
        /// 验证终端字段配置
        /// </summary>
        [HttpPost("terminal-fields/validate")]
        [Authorize(Roles = "SystemAdmin")]
        public ActionResult ValidateTerminalFieldsConfig([FromBody] JsonElement config)
        {
            try
            {
                var validationErrors = new List<string>();

                // 检查必需的顶级属性
                if (!config.TryGetProperty("version", out _))
                {
                    validationErrors.Add("缺少 'version' 属性");
                }

                if (!config.TryGetProperty("fieldCategories", out var fieldCategories))
                {
                    validationErrors.Add("缺少 'fieldCategories' 属性");
                }
                else
                {
                    // 验证字段分类
                    foreach (var category in fieldCategories.EnumerateObject())
                    {
                        if (!category.Value.TryGetProperty("name", out _))
                        {
                            validationErrors.Add($"分类 '{category.Name}' 缺少 'name' 属性");
                        }

                        if (category.Value.TryGetProperty("fields", out var fields))
                        {
                            // 验证字段
                            foreach (var field in fields.EnumerateObject())
                            {
                                var fieldPath = $"分类 '{category.Name}' 字段 '{field.Name}'";
                                
                                if (!field.Value.TryGetProperty("key", out _))
                                {
                                    validationErrors.Add($"{fieldPath} 缺少 'key' 属性");
                                }
                                
                                if (!field.Value.TryGetProperty("displayName", out _))
                                {
                                    validationErrors.Add($"{fieldPath} 缺少 'displayName' 属性");
                                }
                                
                                if (!field.Value.TryGetProperty("dataPath", out _))
                                {
                                    validationErrors.Add($"{fieldPath} 缺少 'dataPath' 属性");
                                }
                                
                                if (!field.Value.TryGetProperty("type", out _))
                                {
                                    validationErrors.Add($"{fieldPath} 缺少 'type' 属性");
                                }
                            }
                        }
                    }
                }

                if (validationErrors.Count > 0)
                {
                    return BadRequest(new { 
                        valid = false, 
                        errors = validationErrors 
                    });
                }

                return Ok(new { 
                    valid = true, 
                    message = "配置验证通过" 
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证配置时发生错误");
                return BadRequest(new { 
                    valid = false, 
                    errors = new[] { "配置验证失败: " + ex.Message } 
                });
            }
        }

        /// <summary>
        /// 获取配置文件备份列表
        /// </summary>
        [HttpGet("terminal-fields/backups")]
        [Authorize(Roles = "SystemAdmin")]
        public ActionResult GetConfigBackups()
        {
            try
            {
                var configDir = Path.Combine(_environment.WebRootPath, "config");
                
                if (!Directory.Exists(configDir))
                {
                    return Ok(new List<object>());
                }

                var backupFiles = Directory.GetFiles(configDir, "terminalFields.backup.*.json")
                    .Select(file => new
                    {
                        fileName = Path.GetFileName(file),
                        fullPath = file,
                        createdTime = System.IO.File.GetCreationTime(file),
                        size = new FileInfo(file).Length
                    })
                    .OrderByDescending(f => f.createdTime)
                    .Take(10) // 只返回最近10个备份
                    .ToList();

                return Ok(backupFiles);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取配置备份列表失败");
                return StatusCode(500, "获取备份列表失败");
            }
        }

        /// <summary>
        /// 恢复配置文件备份
        /// </summary>
        [HttpPost("terminal-fields/restore/{backupFileName}")]
        [Authorize(Roles = "SystemAdmin")]
        public ActionResult RestoreConfigBackup(string backupFileName)
        {
            try
            {
                var configDir = Path.Combine(_environment.WebRootPath, "config");
                var backupPath = Path.Combine(configDir, backupFileName);
                var configPath = Path.Combine(configDir, "terminalFields.json");

                if (!System.IO.File.Exists(backupPath))
                {
                    return NotFound("备份文件不存在");
                }

                // 验证备份文件名格式
                if (!backupFileName.StartsWith("terminalFields.backup.") || !backupFileName.EndsWith(".json"))
                {
                    return BadRequest("无效的备份文件名");
                }

                // 创建当前配置的备份
                if (System.IO.File.Exists(configPath))
                {
                    var currentBackupPath = Path.Combine(configDir, $"terminalFields.backup.{DateTime.Now:yyyyMMddHHmmss}.json");
                    System.IO.File.Copy(configPath, currentBackupPath);
                }

                // 恢复备份
                System.IO.File.Copy(backupPath, configPath, true);

                _logger.LogInformation("已从备份 {BackupFileName} 恢复配置", backupFileName);
                
                return Ok(new { message = "配置恢复成功" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "恢复配置备份失败");
                return StatusCode(500, "恢复配置失败: " + ex.Message);
            }
        }
    }
}
