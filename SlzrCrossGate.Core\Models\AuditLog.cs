using System.ComponentModel.DataAnnotations;

namespace SlzrCrossGate.Core.Models
{
    /// <summary>
    /// 审计日志基础模型
    /// </summary>
    public abstract class AuditLog
    {
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        [MaxLength(128)]
        public string? UserId { get; set; }

        /// <summary>
        /// 用户名
        /// </summary>
        [MaxLength(128)]
        public string? UserName { get; set; }

        /// <summary>
        /// 真实姓名
        /// </summary>
        [MaxLength(100)]
        public string? RealName { get; set; }

        /// <summary>
        /// 商户ID
        /// </summary>
        [MaxLength(8)]
        public string? MerchantId { get; set; }

        /// <summary>
        /// 商户名称
        /// </summary>
        [MaxLength(100)]
        public string? MerchantName { get; set; }

        /// <summary>
        /// IP地址
        /// </summary>
        [MaxLength(45)]
        public string? IpAddress { get; set; }

        /// <summary>
        /// 用户代理
        /// </summary>
        [MaxLength(500)]
        public string? UserAgent { get; set; }

        /// <summary>
        /// 操作时间
        /// </summary>
        public DateTime OperationTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 操作结果 (成功/失败)
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 失败原因
        /// </summary>
        [MaxLength(500)]
        public string? FailureReason { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [MaxLength(1000)]
        public string? Remarks { get; set; }
    }

    /// <summary>
    /// 登录日志
    /// </summary>
    public class LoginLog : AuditLog
    {
        /// <summary>
        /// 登录类型 (用户名密码/微信扫码/双因素认证)
        /// </summary>
        [MaxLength(50)]
        public string? LoginType { get; set; }

        /// <summary>
        /// 登录方式 (Web/API/Mobile)
        /// </summary>
        [MaxLength(50)]
        public string? LoginMethod { get; set; }

        /// <summary>
        /// 会话ID
        /// </summary>
        [MaxLength(100)]
        public string? SessionId { get; set; }

        /// <summary>
        /// 登出时间
        /// </summary>
        public DateTime? LogoutTime { get; set; }
    }

    /// <summary>
    /// 密码修改日志
    /// </summary>
    public class PasswordChangeLog : AuditLog
    {
        /// <summary>
        /// 修改类型 (用户自己修改/管理员重置)
        /// </summary>
        [MaxLength(50)]
        public string? ChangeType { get; set; }

        /// <summary>
        /// 目标用户ID (管理员重置时记录)
        /// </summary>
        [MaxLength(128)]
        public string? TargetUserId { get; set; }

        /// <summary>
        /// 目标用户名 (管理员重置时记录)
        /// </summary>
        [MaxLength(128)]
        public string? TargetUserName { get; set; }

        /// <summary>
        /// 目标用户真实姓名 (管理员重置时记录)
        /// </summary>
        [MaxLength(100)]
        public string? TargetRealName { get; set; }
    }

    /// <summary>
    /// 操作日志
    /// </summary>
    public class OperationLog : AuditLog
    {
        /// <summary>
        /// 操作模块 (用户管理/终端管理/文件管理等)
        /// </summary>
        [MaxLength(50)]
        public string? Module { get; set; }

        /// <summary>
        /// 操作类型 (创建/更新/删除/查询/导出等)
        /// </summary>
        [MaxLength(50)]
        public string? OperationType { get; set; }

        /// <summary>
        /// 操作对象 (具体操作的资源)
        /// </summary>
        [MaxLength(200)]
        public string? OperationTarget { get; set; }

        /// <summary>
        /// 操作详情 (JSON格式存储详细信息)
        /// </summary>
        [MaxLength(2000)]
        public string? OperationDetails { get; set; }

        /// <summary>
        /// 请求路径
        /// </summary>
        [MaxLength(500)]
        public string? RequestPath { get; set; }

        /// <summary>
        /// HTTP方法
        /// </summary>
        [MaxLength(10)]
        public string? HttpMethod { get; set; }

        /// <summary>
        /// 响应状态码
        /// </summary>
        public int? ResponseStatusCode { get; set; }

        /// <summary>
        /// 执行耗时 (毫秒)
        /// </summary>
        public long? ExecutionTime { get; set; }
    }

    /// <summary>
    /// 审计日志类型枚举
    /// </summary>
    public enum AuditLogType
    {
        Login = 1,
        PasswordChange = 2,
        Operation = 3
    }

    /// <summary>
    /// 登录类型枚举
    /// </summary>
    public enum LoginType
    {
        UsernamePassword = 1,
        WechatScan = 2,
        TwoFactor = 3
    }

    /// <summary>
    /// 操作类型枚举
    /// </summary>
    public enum OperationType
    {
        Create = 1,
        Update = 2,
        Delete = 3,
        Query = 4,
        Export = 5,
        Import = 6,
        Publish = 7,
        Send = 8
    }
}
