using SlzrCrossGate.Core.Models;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace SlzrCrossGate.WebAdmin.DTOs
{
    #region LinePriceInfo DTOs

    // 线路票价信息基础DTO
    public class LinePriceInfoDto
    {
        public int ID { get; set; }
        public string MerchantID { get; set; } = string.Empty;
        public string MerchantName { get; set; } = string.Empty;
        public string LineNumber { get; set; } = string.Empty;
        public string GroupNumber { get; set; } = string.Empty;
        public string LineName { get; set; } = string.Empty;
        public string? Branch { get; set; }
        public int Fare { get; set; }
        public bool IsActive { get; set; }
        public string CurrentVersion { get; set; } = string.Empty;
        public int? CurrentFareDiscountSchemeID { get; set; }
        public string? CurrentSchemeName { get; set; }
        public DateTime CreateTime { get; set; }
        public DateTime UpdateTime { get; set; }
        public string? Creator { get; set; }
        public string? Updater { get; set; }
        public string? Remark { get; set; }
    }

    // 创建线路票价信息DTO
    public class CreateLinePriceInfoDto
    {
        public string MerchantID { get; set; } = string.Empty;
        public string LineNumber { get; set; } = string.Empty;
        public string? GroupNumber { get; set; }
        public string LineName { get; set; } = string.Empty;
        public string? Branch { get; set; }
        public int Fare { get; set; }
        public bool IsActive { get; set; } = true;
        public string? Remark { get; set; }
    }

    // 更新线路票价信息DTO
    public class UpdateLinePriceInfoDto
    {
        public string? LineName { get; set; }
        public string? Branch { get; set; }
        public int? Fare { get; set; }
        public bool? IsActive { get; set; }
        public string? Remark { get; set; }
    }

    #endregion

    #region LinePriceInfoVersion DTOs

    // 线路票价版本信息DTO
    public class LinePriceInfoVersionDto
    {
        public int ID { get; set; }
        public string MerchantID { get; set; } = string.Empty;
        public string MerchantName { get; set; } = string.Empty;
        public int LinePriceInfoID { get; set; }
        public string LineNumber { get; set; } = string.Empty;
        public string? GroupNumber { get; set; }
        public string LineName { get; set; } = string.Empty;
        public int Fare { get; set; }
        public string Version { get; set; } = string.Empty;
        
        // 使用JsonPropertyName确保前端接收到的属性名与DTO定义一致
        [JsonPropertyName("extraParams")]
        public object? ExtraParams { get; set; }
        
        [JsonPropertyName("cardDiscountInfo")]
        public object? CardDiscountInfo { get; set; }
        
        public LinePriceVersionStatus Status { get; set; }
        public bool IsPublished { get; set; }
        public int? FileVerID { get; set; }
        public int? FareDiscountSchemeID { get; set; }
        public string? SchemeName { get; set; }
        public DateTime CreateTime { get; set; }
        public DateTime UpdateTime { get; set; }
        public DateTime? SubmitTime { get; set; }
        public string? Creator { get; set; }
        public string? Updater { get; set; }
        public string? Submitter { get; set; }
    }

    // 创建线路票价版本DTO
    public class CreateLinePriceInfoVersionDto
    {
        public string MerchantID { get; set; } = string.Empty;
        public int LinePriceInfoID { get; set; }
        public string? Version { get; set; }
        public int? FareDiscountSchemeID { get; set; }  // 选择的折扣方案ID
        public object? ExtraParams { get; set; }
        public object? CardDiscountInfo { get; set; }
    }

    // 更新线路票价版本DTO
    public class UpdateLinePriceInfoVersionDto
    {
        public int? FareDiscountSchemeID { get; set; }  // 选择的折扣方案ID
        public object? ExtraParams { get; set; }
        public object? CardDiscountInfo { get; set; }
    }

    // 提交线路票价版本DTO
    public class SubmitLinePriceInfoVersionDto
    {
        public int ID { get; set; }
        public string MerchantID { get; set; } = string.Empty;
    }

    // 预览线路票价文件DTO
    public class PreviewLinePriceFileDto
    {
        public int VersionID { get; set; }
        public string MerchantID { get; set; } = string.Empty;
    }

    // 预览响应DTO
    public class PreviewLinePriceFileResponseDto
    {
        public object FileContent { get; set; } = new();
    }

    // 发布线路票价文件DTO
    public class PublishLinePriceFileDto
    {
        public int VersionID { get; set; }
        public string MerchantID { get; set; } = string.Empty;
        public PublishTypeOption PublishType { get; set; }
        public string PublishTarget { get; set; } = string.Empty;
        public string? Remark { get; set; }
    }

    // 跨线路复制版本DTO
    public class CopyToLinesRequestDto
    {
        public int VersionId { get; set; }
        public List<int> TargetLineIds { get; set; } = new List<int>();
        public string MerchantId { get; set; } = string.Empty;
    }

    // 搜索线路请求DTO
    public class SearchLinePricesRequestDto
    {
        public string? Search { get; set; }
        public int ExcludeLineId { get; set; }
        public string MerchantId { get; set; } = string.Empty;
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 20;
    }

    #endregion

    #region Dictionary Configuration DTOs

    // 字典配置DTO，用于动态表单配置
    public class DictionaryConfigDto
    {
        public string DictionaryCode { get; set; } = string.Empty;
        public string? DictionaryLabel { get; set; }
        public string? DictionaryValue { get; set; } // 默认值
        public string? ControlType { get; set; } // 控件类型，存储在ExtraValue1
        public object? ControlConfig { get; set; } // 控件配置，存储在ExtraValue2
    }

    #endregion

    #region Fare Discount Scheme DTOs

    // 票价折扣方案DTO
    public class FareDiscountSchemeDto
    {
        public int ID { get; set; }
        public string MerchantID { get; set; } = string.Empty;
        public string MerchantName { get; set; } = string.Empty;
        public string SchemeName { get; set; } = string.Empty;
        public string SchemeCode { get; set; } = string.Empty;
        public string? Description { get; set; }

        [JsonPropertyName("extraParams")]
        public object? ExtraParams { get; set; }

        [JsonPropertyName("cardDiscountInfo")]
        public object? CardDiscountInfo { get; set; }

        public bool IsActive { get; set; }
        public int UsageCount { get; set; }
        public DateTime? LastUsedTime { get; set; }
        public string? CurrentVersion { get; set; }
        public string? CurrentFilePara { get; set; }
        public int? CurrentFileVerID { get; set; }
        public DateTime CreateTime { get; set; }
        public DateTime UpdateTime { get; set; }
        public string? Creator { get; set; }
        public string? Updater { get; set; }
    }

    // 创建票价折扣方案DTO
    public class CreateFareDiscountSchemeDto
    {
        [MaxLength(8)]
        public string? MerchantID { get; set; }

        [Required]
        [MaxLength(100)]
        public required string SchemeName { get; set; }

        [Required]
        [MaxLength(50)]
        public required string SchemeCode { get; set; }

        [MaxLength(500)]
        public string? Description { get; set; }

        public object? ExtraParams { get; set; }
        public object? CardDiscountInfo { get; set; }
        public bool IsActive { get; set; } = true;
    }

    // 更新票价折扣方案DTO
    public class UpdateFareDiscountSchemeDto
    {
        [Required]
        [MaxLength(100)]
        public required string SchemeName { get; set; }

        [MaxLength(500)]
        public string? Description { get; set; }

        public object? ExtraParams { get; set; }
        public object? CardDiscountInfo { get; set; }
        public bool IsActive { get; set; }
    }

    // 票价折扣方案查询DTO
    public class FareDiscountSchemeQueryDto
    {
        public string? Search { get; set; }
        public bool? IsActive { get; set; }
        public string? MerchantId { get; set; }
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 20;
    }

    #endregion

    #region 票价折扣方案版本管理 DTOs

    // 票价折扣方案版本信息DTO
    public class FareDiscountSchemeVersionDto
    {
        public int ID { get; set; }
        public string MerchantID { get; set; } = string.Empty;
        public string MerchantName { get; set; } = string.Empty;
        public int FareDiscountSchemeID { get; set; }
        public string SchemeName { get; set; } = string.Empty;
        public string Version { get; set; } = string.Empty;
        public string FilePara { get; set; } = string.Empty;

        [JsonPropertyName("extraParams")]
        public object? ExtraParams { get; set; }

        [JsonPropertyName("cardDiscountInfo")]
        public object? CardDiscountInfo { get; set; }

        public FareDiscountVersionStatus Status { get; set; }
        public bool IsPublished { get; set; }
        public int? FileVerID { get; set; }
        public DateTime CreateTime { get; set; }
        public DateTime UpdateTime { get; set; }
        public DateTime? SubmitTime { get; set; }
        public string? Creator { get; set; }
        public string? Updater { get; set; }
        public string? Submitter { get; set; }
        public string? Remarks { get; set; }
    }



    // 提交票价折扣方案DTO
    public class SubmitFareDiscountSchemeVersionDto
    {
        [RegularExpression(@"^[0-9A-Fa-f]{4}$", ErrorMessage = "版本号必须是4位16进制字符")]
        public string? Version { get; set; } // 可选，为空时自动生成

        [RegularExpression(@"^[A-Za-z0-9]{1,8}$", ErrorMessage = "文件参数只能包含英文和数字，最多8个字符")]
        public string? FilePara { get; set; } // 可选，为空时使用当前文件参数

        [MaxLength(500, ErrorMessage = "备注信息不能超过500个字符")]
        public string? Remarks { get; set; } // 可选，版本备注信息
    }

    // 票价折扣方案版本查询DTO
    public class FareDiscountSchemeVersionQueryDto
    {
        public FareDiscountVersionStatus? Status { get; set; }
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 20;
    }

    // 预览票价折扣方案文件响应DTO
    public class PreviewFareDiscountSchemeFileResponseDto
    {
        public object FileContent { get; set; } = new();
    }

    #endregion
}
