-- 客户项目数据库安全配置脚本
-- 为客户项目创建只读用户，限制数据库访问权限

-- 1. 创建只读用户（用于认证验证）
CREATE USER IF NOT EXISTS 'customer_readonly'@'%' IDENTIFIED BY 'readonly_password_2024!';

-- 2. 授予认证相关表的只读权限
GRANT SELECT ON tcpserver.Users TO 'customer_readonly'@'%';
GRANT SELECT ON tcpserver.UserRoles TO 'customer_readonly'@'%';
GRANT SELECT ON tcpserver.Merchants TO 'customer_readonly'@'%';
GRANT SELECT ON tcpserver.MenuGroups TO 'customer_readonly'@'%';
GRANT SELECT ON tcpserver.MenuItems TO 'customer_readonly'@'%';

-- 3. 授予系统配置表的只读权限（如果需要）
GRANT SELECT ON tcpserver.SystemSettings TO 'customer_readonly'@'%';
GRANT SELECT ON tcpserver.MerchantDictionary TO 'customer_readonly'@'%';

-- 4. 为每个客户创建专用用户（示例：客户A）
CREATE USER IF NOT EXISTS 'customer_a'@'%' IDENTIFIED BY 'customer_a_password_2024!';

-- 5. 授予客户A用户的权限
-- 认证相关表的只读权限
GRANT SELECT ON tcpserver.Users TO 'customer_a'@'%';
GRANT SELECT ON tcpserver.UserRoles TO 'customer_a'@'%';
GRANT SELECT ON tcpserver.Merchants TO 'customer_a'@'%';

-- 客户A专用表的完整权限（表名以Customer_A_开头）
GRANT ALL PRIVILEGES ON tcpserver.Customer_A_* TO 'customer_a'@'%';

-- 6. 创建客户A的专用数据表示例
CREATE TABLE IF NOT EXISTS tcpserver.Customer_A_CustomData (
    Id INT AUTO_INCREMENT PRIMARY KEY,
    MerchantId VARCHAR(8) NOT NULL,
    DataType VARCHAR(50) NOT NULL,
    DataValue TEXT,
    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_merchant (MerchantId),
    INDEX idx_type (DataType)
) COMMENT='客户A的定制数据表';

CREATE TABLE IF NOT EXISTS tcpserver.Customer_A_Settings (
    Id INT AUTO_INCREMENT PRIMARY KEY,
    MerchantId VARCHAR(8) NOT NULL,
    SettingKey VARCHAR(100) NOT NULL,
    SettingValue TEXT,
    Description VARCHAR(500),
    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_merchant_key (MerchantId, SettingKey)
) COMMENT='客户A的配置设置表';

-- 7. 为客户B创建类似的配置（可选）
CREATE USER IF NOT EXISTS 'customer_b'@'%' IDENTIFIED BY 'customer_b_password_2024!';

GRANT SELECT ON tcpserver.Users TO 'customer_b'@'%';
GRANT SELECT ON tcpserver.UserRoles TO 'customer_b'@'%';
GRANT SELECT ON tcpserver.Merchants TO 'customer_b'@'%';
GRANT ALL PRIVILEGES ON tcpserver.Customer_B_* TO 'customer_b'@'%';

-- 8. 刷新权限
FLUSH PRIVILEGES;

-- 9. 验证用户创建
SELECT User, Host FROM mysql.user WHERE User LIKE 'customer_%';

-- 10. 显示权限信息
SHOW GRANTS FOR 'customer_readonly'@'%';
SHOW GRANTS FOR 'customer_a'@'%';

-- 使用说明：
-- 1. 在主数据库中执行此脚本
-- 2. 客户项目使用对应的用户连接数据库
-- 3. 认证验证使用只读连接
-- 4. 业务数据使用客户专用连接

-- 连接字符串示例：
-- AuthConnection: "Server=mysql;Database=tcpserver;Uid=customer_readonly;Pwd=readonly_password_2024!;"
-- CustomerConnection: "Server=mysql;Database=tcpserver;Uid=customer_a;Pwd=customer_a_password_2024!;"

-- 安全注意事项：
-- 1. 定期更换密码
-- 2. 监控数据库访问日志
-- 3. 限制客户项目只能访问必要的数据
-- 4. 使用SSL连接（生产环境）
