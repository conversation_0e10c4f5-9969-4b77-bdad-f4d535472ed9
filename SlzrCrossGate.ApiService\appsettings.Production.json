{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "ConnectionStrings": {"DefaultConnection": "Server=localhost;Port=3306;Database=tcpserver;User=root;Password=**********;SslMode=Required;AllowLoadLocalInfile=true;"}, "DatabaseProvider": "MySql", "RabbitMQ": {"HostName": "localhost", "UserName": "guest", "Password": "guest", "Port": 5672}, "FileService": {"DefaultStorageType": "Local", "LocalFilePath": "/app/storage/files", "MinIO": {"Endpoint": "minio.example.com", "AccessKey": "your-access-key", "SecretKey": "your-secret-key", "BucketName": "your-bucket-name"}}, "Kestrel": {"EndpointDefaults": {"Protocols": "Http1AndHttp2"}}}