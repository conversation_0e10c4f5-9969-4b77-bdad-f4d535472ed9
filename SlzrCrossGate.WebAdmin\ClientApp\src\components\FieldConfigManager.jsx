import React, { useState, useEffect } from 'react';
import CodeMirror from '@uiw/react-codemirror';
import { json } from '@codemirror/lang-json';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  FormControlLabel,
  Checkbox,
  Chip,
  Divider,
  Alert,
  CircularProgress,
  Tooltip,
  IconButton,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Grid,
  Paper,
  Switch,
  Slider,
  Autocomplete,
  Snackbar,
  ToggleButton,
  ToggleButtonGroup
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Refresh as RefreshIcon,
  Info as InfoIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  Edit as EditIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
  DragIndicator as DragIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
  Code as CodeIcon,
  ViewList as FormIcon
} from '@mui/icons-material';

/**
 * 字段配置管理器组件
 * 提供可视化的字段配置管理界面
 */
const FieldConfigManager = ({
  open,
  onClose,
  fieldConfig,
  loading,
  error,
  onReloadConfig,
  getConfigStats,
  getFieldCategories
}) => {
  const [expandedCategories, setExpandedCategories] = useState(['basic']);
  const [editMode, setEditMode] = useState(false);
  const [editedConfig, setEditedConfig] = useState(null);
  const [saving, setSaving] = useState(false);
  const [editingField, setEditingField] = useState(null);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });

  // 新增：编辑模式状态 - 'form' 表单编辑, 'json' JSON编辑
  const [editViewMode, setEditViewMode] = useState('form');
  const [jsonContent, setJsonContent] = useState('');
  const [jsonError, setJsonError] = useState('');

  // 当配置加载时，初始化编辑状态
  useEffect(() => {
    if (fieldConfig && !editedConfig) {
      setEditedConfig(JSON.parse(JSON.stringify(fieldConfig)));
      setJsonContent(JSON.stringify(fieldConfig, null, 2));
    }
  }, [fieldConfig]);

  // 切换编辑视图模式时同步数据
  useEffect(() => {
    if (editMode && editedConfig) {
      if (editViewMode === 'json') {
        // 切换到JSON模式时，将表单数据同步到JSON
        setJsonContent(JSON.stringify(editedConfig, null, 2));
        setJsonError('');
      } else {
        // 切换到表单模式时，尝试解析JSON数据
        try {
          if (jsonContent.trim()) {
            const parsed = JSON.parse(jsonContent);
            setEditedConfig(parsed);
            setJsonError('');
          }
        } catch (err) {
          setJsonError(`JSON格式错误: ${err.message}`);
        }
      }
    }
  }, [editViewMode, editMode]);

  // 重置编辑状态
  const resetEditState = () => {
    setEditMode(false);
    setEditingField(null);
    setEditedConfig(fieldConfig ? JSON.parse(JSON.stringify(fieldConfig)) : null);
    setEditViewMode('form');
    setJsonContent(fieldConfig ? JSON.stringify(fieldConfig, null, 2) : '');
    setJsonError('');
  };





  const handleCategoryToggle = (categoryKey) => {
    setExpandedCategories(prev =>
      prev.includes(categoryKey)
        ? prev.filter(key => key !== categoryKey)
        : [...prev, categoryKey]
    );
  };

  const handleFieldToggle = (categoryKey, fieldKey, enabled) => {
    if (editMode && editedConfig) {
      // 编辑模式下更新本地配置
      const newConfig = { ...editedConfig };
      if (newConfig.fieldCategories[categoryKey]?.fields[fieldKey]) {
        newConfig.fieldCategories[categoryKey].fields[fieldKey].enabled = enabled;
        setEditedConfig(newConfig);
      }
    }
    // 非编辑模式下不允许直接切换，需要进入编辑模式
  };

  // 更新字段属性
  const updateFieldProperty = (categoryKey, fieldKey, property, value) => {
    if (!editedConfig) return;

    const newConfig = { ...editedConfig };
    if (newConfig.fieldCategories[categoryKey]?.fields[fieldKey]) {
      newConfig.fieldCategories[categoryKey].fields[fieldKey][property] = value;
      setEditedConfig(newConfig);
    }
  };

  // 添加新字段
  const addNewField = (categoryKey) => {
    if (!editedConfig) return;

    const newFieldKey = `newField_${Date.now()}`;
    const newConfig = { ...editedConfig };

    if (!newConfig.fieldCategories[categoryKey]) {
      newConfig.fieldCategories[categoryKey] = { name: '新分类', fields: {} };
    }

    // 计算新字段的排序值
    const existingFields = Object.values(newConfig.fieldCategories[categoryKey].fields || {});
    const maxOrder = existingFields.length > 0 ? Math.max(...existingFields.map(f => f.order || 0)) : 0;

    newConfig.fieldCategories[categoryKey].fields[newFieldKey] = {
      key: newFieldKey,
      displayName: '新字段',
      dataPath: 'newField',
      type: 'text',
      enabled: false,
      order: maxOrder + 1,
      width: 120,
      sortable: false,
      hideOn: [],
      tooltip: ''
    };

    setEditedConfig(newConfig);
    setEditingField(newFieldKey);
  };

  // 删除字段
  const deleteField = (categoryKey, fieldKey) => {
    if (!editedConfig) return;

    const newConfig = { ...editedConfig };
    if (newConfig.fieldCategories[categoryKey]?.fields[fieldKey]) {
      delete newConfig.fieldCategories[categoryKey].fields[fieldKey];
      setEditedConfig(newConfig);

      // 如果正在编辑这个字段，取消编辑状态
      if (editingField === fieldKey) {
        setEditingField(null);
      }
    }
  };

  // 保存配置
  const handleSaveConfig = async () => {
    let configToSave = editedConfig;

    // 如果当前是JSON模式，先验证并解析JSON
    if (editViewMode === 'json') {
      try {
        if (jsonContent.trim()) {
          configToSave = JSON.parse(jsonContent);
          setEditedConfig(configToSave);
        } else {
          throw new Error('JSON内容不能为空');
        }
      } catch (err) {
        setJsonError(`JSON格式错误: ${err.message}`);
        setSnackbar({
          open: true,
          message: `保存失败: JSON格式错误 - ${err.message}`,
          severity: 'error'
        });
        setSaving(false);
        return;
      }
    }

    if (!configToSave) return;

    setSaving(true);
    try {
      // 更新版本号和时间戳
      const finalConfig = {
        ...configToSave,
        lastUpdated: new Date().toISOString()
      };

      const response = await fetch('/api/config/terminal-fields', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(finalConfig)
      });

      if (!response.ok) {
        throw new Error(`保存失败: ${response.statusText}`);
      }

      setSnackbar({
        open: true,
        message: '配置保存成功！',
        severity: 'success'
      });

      setEditMode(false);
      setEditingField(null);

      // 重新加载配置
      if (onReloadConfig) {
        onReloadConfig();
      }
    } catch (err) {
      console.error('保存配置失败:', err);
      setSnackbar({
        open: true,
        message: `保存失败: ${err.message}`,
        severity: 'error'
      });
    } finally {
      setSaving(false);
    }
  };

  const stats = getConfigStats;
  const categories = getFieldCategories;
  const currentConfig = editMode ? editedConfig : fieldConfig;

  // 渲染字段编辑表单
  const renderFieldEditForm = (categoryKey, field) => (
    <Paper elevation={1} sx={{ p: 2, mb: 2, bgcolor: 'background.default' }}>
      <Grid container spacing={2}>
        <Grid item xs={12} sm={6}>
          <TextField
            label="显示名称"
            value={field.displayName}
            onChange={(e) => updateFieldProperty(categoryKey, field.key, 'displayName', e.target.value)}
            size="small"
            fullWidth
          />
        </Grid>

        <Grid item xs={12} sm={6}>
          <TextField
            label="数据路径"
            value={field.dataPath}
            onChange={(e) => updateFieldProperty(categoryKey, field.key, 'dataPath', e.target.value)}
            size="small"
            fullWidth
          />
        </Grid>

        <Grid item xs={12} sm={4}>
          <FormControl size="small" fullWidth>
            <InputLabel>字段类型</InputLabel>
            <Select
              value={field.type}
              label="字段类型"
              onChange={(e) => updateFieldProperty(categoryKey, field.key, 'type', e.target.value)}
            >
              <MenuItem value="text">文本</MenuItem>
              <MenuItem value="date">日期</MenuItem>
              <MenuItem value="datetime">日期时间</MenuItem>
              <MenuItem value="status">状态</MenuItem>
              <MenuItem value="fileVersion">文件版本</MenuItem>
              <MenuItem value="keyValue">键值对</MenuItem>
              <MenuItem value="percentage">百分比</MenuItem>
              <MenuItem value="signal">信号强度</MenuItem>
              <MenuItem value="temperature">温度</MenuItem>
              <MenuItem value="networkType">网络类型</MenuItem>
            </Select>
          </FormControl>
        </Grid>

        <Grid item xs={12} sm={4}>
          <TextField
            label="列宽度"
            type="number"
            value={field.width || 120}
            onChange={(e) => updateFieldProperty(categoryKey, field.key, 'width', parseInt(e.target.value))}
            size="small"
            fullWidth
            InputProps={{ inputProps: { min: 50, max: 500 } }}
          />
        </Grid>

        <Grid item xs={12} sm={4}>
          <TextField
            label="排序"
            type="number"
            value={field.order || 0}
            onChange={(e) => updateFieldProperty(categoryKey, field.key, 'order', parseInt(e.target.value))}
            size="small"
            fullWidth
            InputProps={{ inputProps: { min: 0, max: 100 } }}
          />
        </Grid>

        <Grid item xs={12} sm={6}>
          <FormControlLabel
            control={
              <Switch
                checked={field.sortable || false}
                onChange={(e) => updateFieldProperty(categoryKey, field.key, 'sortable', e.target.checked)}
              />
            }
            label="可排序"
          />
        </Grid>

        <Grid item xs={12} sm={6}>
          <Autocomplete
            multiple
            options={['xs', 'sm', 'md', 'lg', 'xl']}
            value={field.hideOn || []}
            onChange={(e, newValue) => updateFieldProperty(categoryKey, field.key, 'hideOn', newValue)}
            renderInput={(params) => (
              <TextField {...params} label="响应式隐藏" size="small" />
            )}
            size="small"
          />
        </Grid>

        <Grid item xs={12}>
          <TextField
            label="工具提示文本"
            value={typeof field.tooltip === 'string' ? field.tooltip : (field.tooltip?.text || '')}
            onChange={(e) => {
              const value = e.target.value;
              if (value) {
                updateFieldProperty(categoryKey, field.key, 'tooltip', value);
              } else {
                updateFieldProperty(categoryKey, field.key, 'tooltip', '');
              }
            }}
            size="small"
            fullWidth
            multiline
            rows={2}
            helperText="支持模板变量，如：{merchantID}、{deviceNO}、{lineNO} 等"
          />
        </Grid>

        <Grid item xs={12} sm={6}>
          <TextField
            label="动态Tooltip数据路径"
            value={typeof field.tooltip === 'object' ? field.tooltip?.dataPath || '' : ''}
            onChange={(e) => {
              const dataPath = e.target.value;
              const currentTooltip = field.tooltip || {};

              if (dataPath) {
                updateFieldProperty(categoryKey, field.key, 'tooltip', {
                  ...currentTooltip,
                  dataPath: dataPath,
                  template: currentTooltip.template || '数据: {tooltipValue}'
                });
              } else {
                // 如果清空数据路径，回退到简单字符串模式
                const text = currentTooltip.text || '';
                updateFieldProperty(categoryKey, field.key, 'tooltip', text);
              }
            }}
            size="small"
            fullWidth
            helperText="从终端数据中获取tooltip值的路径，如：merchantID"
          />
        </Grid>

        <Grid item xs={12} sm={6}>
          <TextField
            label="动态Tooltip模板"
            value={typeof field.tooltip === 'object' ? field.tooltip?.template || '' : ''}
            onChange={(e) => {
              const template = e.target.value;
              const currentTooltip = field.tooltip || {};

              if (currentTooltip.dataPath && template) {
                updateFieldProperty(categoryKey, field.key, 'tooltip', {
                  ...currentTooltip,
                  template: template
                });
              }
            }}
            size="small"
            fullWidth
            disabled={!field.tooltip?.dataPath}
            helperText="模板文本，使用 {tooltipValue} 显示动态值"
          />
        </Grid>
      </Grid>
    </Paper>
  );

  const renderFieldItem = (categoryKey, field) => (
    <Box key={field.key} sx={{ mb: 2 }}>
      <Paper
        elevation={editingField === field.key ? 2 : 0}
        sx={{
          p: 1,
          border: editingField === field.key ? '2px solid' : '1px solid',
          borderColor: editingField === field.key ? 'primary.main' : 'divider',
          bgcolor: editingField === field.key ? 'action.selected' : 'transparent'
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          {/* 拖拽图标 */}
          {editMode && (
            <DragIcon sx={{ color: 'text.secondary', cursor: 'grab' }} />
          )}

          {/* 启用/禁用复选框 */}
          <FormControlLabel
            control={
              <Checkbox
                checked={field.enabled}
                onChange={(e) => handleFieldToggle(categoryKey, field.key, e.target.checked)}
                disabled={!editMode}
                size="small"
              />
            }
            label=""
            sx={{ m: 0 }}
          />

          {/* 字段信息 */}
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, flex: 1 }}>
            <Typography variant="body2" fontWeight={field.enabled ? 'bold' : 'normal'}>
              {field.displayName}
            </Typography>

            <Chip
              label={field.type}
              size="small"
              variant="outlined"
              sx={{ fontSize: '0.7rem', height: 20 }}
            />

            {field.hideOn && field.hideOn.length > 0 && (
              <Chip
                label={`隐藏: ${field.hideOn.join(', ')}`}
                size="small"
                color="warning"
                variant="outlined"
                sx={{ fontSize: '0.7rem', height: 20 }}
              />
            )}

            {field.tooltip && (
              <Tooltip title={typeof field.tooltip === 'string' ? field.tooltip : (field.tooltip?.text || '')} arrow>
                <InfoIcon sx={{ fontSize: 16, color: 'text.secondary' }} />
              </Tooltip>
            )}

            {field.enabled ? (
              <VisibilityIcon sx={{ fontSize: 16, color: 'success.main' }} />
            ) : (
              <VisibilityOffIcon sx={{ fontSize: 16, color: 'text.disabled' }} />
            )}
          </Box>

          {/* 编辑和删除按钮 */}
          {editMode && (
            <Box sx={{ display: 'flex', gap: 0.5 }}>
              <Tooltip title={editingField === field.key ? "收起编辑" : "编辑字段"}>
                <IconButton
                  size="small"
                  onClick={() => setEditingField(editingField === field.key ? null : field.key)}
                  color={editingField === field.key ? "primary" : "default"}
                >
                  <EditIcon fontSize="small" />
                </IconButton>
              </Tooltip>

              <Tooltip title="删除字段">
                <IconButton
                  size="small"
                  onClick={() => {
                    if (window.confirm(`确定要删除字段"${field.displayName}"吗？`)) {
                      deleteField(categoryKey, field.key);
                    }
                  }}
                  color="error"
                >
                  <DeleteIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Box>
          )}
        </Box>

        {/* 字段基本信息 */}
        {!editMode && (
          <Box sx={{ ml: 4, mt: 1 }}>
            <Typography variant="caption" color="text.secondary">
              数据路径: {field.dataPath} | 宽度: {field.width}px | 排序: {field.order}
            </Typography>
          </Box>
        )}

        {/* 编辑表单 */}
        {editMode && editingField === field.key && renderFieldEditForm(categoryKey, field)}
      </Paper>
    </Box>
  );

  const renderCategoryAccordion = (category) => {
    const configToUse = editMode ? editedConfig : fieldConfig;
    const categoryData = configToUse?.fieldCategories[category.key];
    const fields = categoryData ? Object.values(categoryData.fields) : [];

    return (
      <Accordion
        key={category.key}
        expanded={expandedCategories.includes(category.key)}
        onChange={() => handleCategoryToggle(category.key)}
        sx={{ mb: 1 }}
      >
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, width: '100%' }}>
            <Typography variant="subtitle1" fontWeight="bold">
              {categoryData?.name || category.name}
            </Typography>

            <Chip
              label={`${fields.filter(f => f.enabled).length}/${fields.length}`}
              size="small"
              color={fields.some(f => f.enabled) ? 'primary' : 'default'}
            />

            {categoryData?.description && (
              <Typography variant="caption" color="text.secondary" sx={{ flex: 1 }}>
                {categoryData.description}
              </Typography>
            )}

            {/* 添加字段按钮 */}
            {editMode && (
              <Tooltip title="添加新字段">
                <IconButton
                  size="small"
                  onClick={(e) => {
                    e.stopPropagation(); // 防止触发折叠/展开
                    addNewField(category.key);
                  }}
                  color="primary"
                >
                  <AddIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            )}
          </Box>
        </AccordionSummary>

        <AccordionDetails>
          <Box>
            {fields
              .sort((a, b) => (a.order || 999) - (b.order || 999))
              .map(field => renderFieldItem(category.key, field))
            }
          </Box>
        </AccordionDetails>
      </Accordion>
    );
  };

  return (
    <Dialog 
      open={open} 
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: { height: '80vh' }
      }}
    >
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Typography variant="h6">
            字段配置管理 {editMode && <Chip label="编辑模式" color="warning" size="small" sx={{ ml: 1 }} />}
          </Typography>

          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            {/* 配置统计 */}
            <Chip
              label={`${stats.enabled}/${stats.total} 字段启用`}
              color="primary"
              size="small"
            />

            {/* 编辑视图模式切换 */}
            {editMode && (
              <ToggleButtonGroup
                value={editViewMode}
                exclusive
                onChange={(event, newMode) => {
                  if (newMode !== null) {
                    setEditViewMode(newMode);
                  }
                }}
                size="small"
              >
                <ToggleButton value="form" aria-label="表单编辑">
                  <Tooltip title="表单编辑">
                    <FormIcon fontSize="small" />
                  </Tooltip>
                </ToggleButton>
                <ToggleButton value="json" aria-label="JSON编辑">
                  <Tooltip title="JSON编辑">
                    <CodeIcon fontSize="small" />
                  </Tooltip>
                </ToggleButton>
              </ToggleButtonGroup>
            )}

            {/* 编辑模式切换 */}
            {!editMode && (
              <Tooltip title="进入编辑模式">
                <span>
                  <IconButton
                    onClick={() => setEditMode(true)}
                    disabled={loading || !fieldConfig}
                    size="small"
                    color="primary"
                  >
                    <EditIcon />
                  </IconButton>
                </span>
              </Tooltip>
            )}

            {/* 重新加载按钮 */}
            <Tooltip title="重新加载配置">
              <span>
                <IconButton
                  onClick={onReloadConfig}
                  disabled={loading || editMode}
                  size="small"
                >
                  {loading ? <CircularProgress size={20} /> : <RefreshIcon />}
                </IconButton>
              </span>
            </Tooltip>
          </Box>
        </Box>
      </DialogTitle>

      <DialogContent dividers sx={{
        display: 'flex',
        flexDirection: 'column',
        height: '100%'
      }}>
        {/* 错误提示 */}
        {error && (
          <Alert severity="error" sx={{ mb: 2, flexShrink: 0 }}>
            配置加载失败: {error}
          </Alert>
        )}

        {/* 配置信息 */}
        {currentConfig && (
          <Alert severity={editMode ? "warning" : "info"} sx={{ mb: 2, flexShrink: 0 }}>
            <Typography variant="body2">
              配置版本: {currentConfig.version} |
              最后更新: {currentConfig.lastUpdated ? new Date(currentConfig.lastUpdated).toLocaleString('zh-CN') : '未知'} |
              分类数: {stats.categories}
              {editMode && " | ⚠️ 编辑模式：修改后需要保存"}
            </Typography>
          </Alert>
        )}

        {/* 加载状态 */}
        {loading && (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3, flex: 1, alignItems: 'center' }}>
            <CircularProgress />
          </Box>
        )}

        {/* 编辑内容区域 */}
        <Box sx={{
          flex: 1,
          display: 'flex',
          flexDirection: 'column',
          minHeight: 0
        }}>
        {editMode ? (
          editViewMode === 'json' ? (
            /* JSON编辑器 */
            <Box sx={{ display: 'flex', flexDirection: 'column', flex: 1, minHeight: 0 }}>
              {jsonError && (
                <Alert severity="error" sx={{ mb: 2, flexShrink: 0 }}>
                  {jsonError}
                </Alert>
              )}

              <Box sx={{ mb: 2, display: 'flex', gap: 1, alignItems: 'center', flexShrink: 0 }}>
                <Typography variant="subtitle2" color="text.secondary">
                  JSON编辑模式
                </Typography>
                <Typography variant="caption" color="text.secondary" sx={{ ml: 2 }}>
                  直接编辑JSON配置，点击"保存配置"按钮保存到服务器
                </Typography>
              </Box>

              <Box sx={{
                flex: 1,
                border: jsonError ? '1px solid #f44336' : '1px solid rgba(0, 0, 0, 0.23)',
                borderRadius: 1,
                minHeight: 0,
                '& .cm-editor': {
                  height: '100%'
                },
                '& .cm-scroller': {
                  fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace'
                }
              }}>
                <CodeMirror
                  value={jsonContent}
                  onChange={(value) => {
                    setJsonContent(value);
                    // 实时验证JSON格式
                    try {
                      if (value.trim()) {
                        JSON.parse(value);
                        setJsonError('');
                      }
                    } catch (err) {
                      setJsonError(`JSON格式错误: ${err.message}`);
                    }
                  }}
                  extensions={[json()]}
                  placeholder="请输入JSON配置..."
                  basicSetup={{
                    lineNumbers: true,
                    foldGutter: true,
                    dropCursor: false,
                    allowMultipleSelections: true,
                    indentOnInput: true,
                    bracketMatching: true,
                    closeBrackets: true,
                    autocompletion: true,
                    highlightSelectionMatches: false,
                    searchKeymap: true
                  }}
                  height="100%"
                />
              </Box>

              {/* 错误提示 */}
              {jsonError && (
                <Typography variant="caption" color="error" sx={{ mt: 1, display: 'block' }}>
                  {jsonError}
                </Typography>
              )}

              {/* 帮助文本 */}
              {!jsonError && (
                <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                  直接编辑JSON配置文件内容
                </Typography>
              )}
            </Box>
          ) : (
            /* 表单编辑器 */
            !loading && categories.length > 0 && (
              <Box>
                {categories.map(renderCategoryAccordion)}
              </Box>
            )
          )
        ) : (
          /* 只读视图 */
          !loading && categories.length > 0 && (
            <Box>
              {categories.map(renderCategoryAccordion)}
            </Box>
          )
        )}
        </Box>

        {/* 空状态 */}
        {!loading && categories.length === 0 && (
          <Box sx={{ textAlign: 'center', p: 3, flexShrink: 0 }}>
            <Typography color="text.secondary">
              没有可用的字段配置
            </Typography>
          </Box>
        )}

        {/* 分隔线和使用说明 - 只在非JSON编辑模式下显示 */}
        {(!editMode || editViewMode !== 'json') && (
          <>
            <Divider sx={{ my: 2, flexShrink: 0 }} />
            <Box sx={{ p: 2, bgcolor: 'background.paper', borderRadius: 1, flexShrink: 0 }}>
            <Typography variant="subtitle2" gutterBottom>
              💡 使用说明
            </Typography>
            {editMode ? (
              <>
                <Typography variant="body2" color="text.secondary" paragraph>
                  • 勾选/取消勾选字段来启用/禁用列显示
                </Typography>
                <Typography variant="body2" color="text.secondary" paragraph>
                  • 点击编辑按钮可以修改字段的详细属性
                </Typography>
                <Typography variant="body2" color="text.secondary" paragraph>
                  • 修改完成后点击"保存配置"按钮保存更改
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  • 点击"取消编辑"可以放弃所有更改
                </Typography>
              </>
            ) : (
              <>
                <Typography variant="body2" color="text.secondary" paragraph>
                  • 勾选字段可以在表格中显示该列
                </Typography>
                <Typography variant="body2" color="text.secondary" paragraph>
                  • 点击编辑按钮进入编辑模式，可以修改字段属性
                </Typography>
                <Typography variant="body2" color="text.secondary" paragraph>
                  • 配置文件位置: <code>/config/terminalFields.json</code>
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  • 修改配置文件后点击刷新按钮重新加载
                </Typography>
              </>
            )}
            </Box>
          </>
        )}
      </DialogContent>

      <DialogActions>
        {editMode ? (
          <>
            <Button
              onClick={resetEditState}
              disabled={saving}
              startIcon={<CancelIcon />}
            >
              取消编辑
            </Button>
            <Button
              onClick={handleSaveConfig}
              disabled={saving || !editedConfig}
              variant="contained"
              startIcon={saving ? <CircularProgress size={16} /> : <SaveIcon />}
            >
              {saving ? '保存中...' : '保存配置'}
            </Button>
          </>
        ) : (
          <Button onClick={onClose}>
            关闭
          </Button>
        )}
      </DialogActions>

      {/* 消息提示 */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Dialog>
  );
};

export default FieldConfigManager;
