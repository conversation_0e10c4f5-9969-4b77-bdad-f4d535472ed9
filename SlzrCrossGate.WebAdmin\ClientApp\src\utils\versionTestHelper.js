/**
 * 版本检测测试辅助工具
 * 用于模拟和测试版本变化场景
 */

/**
 * 模拟版本更新场景
 * 用于测试版本检测机制
 */
export const simulateVersionUpdate = () => {
  console.log('=== 模拟版本更新场景 ===');
  
  // 1. 显示当前localStorage中的版本信息
  const currentStored = localStorage.getItem('app_version_info');
  console.log('当前localStorage版本:', currentStored ? JSON.parse(currentStored) : '无');
  
  // 2. 模拟旧版本信息
  const oldVersion = {
    version: "1.0.2.0",
    buildTime: "2025-01-04T10:00:00Z",
    buildTimeString: "2025-01-04 18:00:00",
    timestamp: Date.now() - 3600000 // 1小时前
  };
  
  localStorage.setItem('app_version_info', JSON.stringify(oldVersion));
  console.log('已设置模拟旧版本:', oldVersion);
  
  console.log('现在刷新页面，应该会检测到版本不匹配并提示更新');
};

/**
 * 清除版本信息
 * 模拟全新安装场景
 */
export const clearVersionInfo = () => {
  localStorage.removeItem('app_version_info');
  localStorage.removeItem('app_version_last_check');
  localStorage.removeItem('version_mismatch_warning');

  // 移除警告横幅
  const warningBanner = document.getElementById('version-mismatch-warning');
  if (warningBanner) {
    warningBanner.remove();
  }

  console.log('已清除所有版本信息和警告');
};

/**
 * 显示当前版本状态
 */
export const showVersionStatus = () => {
  console.log('=== 当前版本状态 ===');

  const stored = localStorage.getItem('app_version_info');
  const lastCheck = localStorage.getItem('app_version_last_check');
  const mismatchWarning = localStorage.getItem('version_mismatch_warning');

  console.log('localStorage版本信息:', stored ? JSON.parse(stored) : '无');
  console.log('最后检查时间:', lastCheck ? new Date(parseInt(lastCheck)).toLocaleString() : '无');
  console.log('版本不匹配警告:', mismatchWarning ? JSON.parse(mismatchWarning) : '无');

  // 检查内存中的版本信息（如果appVersionService已初始化）
  if (window.appVersionService) {
    const current = window.appVersionService.getCurrentVersion();
    console.log('内存中版本信息:', current);
  } else {
    console.log('版本服务未初始化');
  }

  // 检查页面上是否有警告横幅
  const warningBanner = document.getElementById('version-mismatch-warning');
  console.log('页面警告横幅:', warningBanner ? '存在' : '不存在');
};

/**
 * 测试session过期场景（简化版）
 */
export const testSessionExpireScenario = () => {
  console.log('=== 测试Session过期场景（简化版）===');

  // 1. 设置一个旧版本
  const oldVersion = {
    version: "1.0.1.9",
    buildTime: "2025-01-03T15:30:00Z",
    buildTimeString: "2025-01-03 23:30:00",
    timestamp: Date.now() - 7200000 // 2小时前
  };

  localStorage.setItem('app_version_info', JSON.stringify(oldVersion));
  console.log('已设置旧版本信息:', oldVersion);

  // 2. 清除token模拟session过期
  localStorage.removeItem('token');
  console.log('已清除token，模拟session过期');

  console.log('现在访问任何需要认证的页面，应该会：');
  console.log('1. 自动跳转到登录页面');
  console.log('2. 登录页面检测到版本不匹配');
  console.log('3. 自动刷新页面（无提示）');
  console.log('4. 刷新后获取最新的前端资源');
};

// 将工具函数暴露到全局，方便在控制台调试
if (typeof window !== 'undefined') {
  window.versionTestHelper = {
    simulateVersionUpdate,
    clearVersionInfo,
    showVersionStatus,
    testSessionExpireScenario
  };
  
  console.log('版本测试工具已加载，可在控制台使用：');
  console.log('- window.versionTestHelper.simulateVersionUpdate()');
  console.log('- window.versionTestHelper.clearVersionInfo()');
  console.log('- window.versionTestHelper.showVersionStatus()');
  console.log('- window.versionTestHelper.testSessionExpireScenario()');
}

export default {
  simulateVersionUpdate,
  clearVersionInfo,
  showVersionStatus,
  testSessionExpireScenario
};
