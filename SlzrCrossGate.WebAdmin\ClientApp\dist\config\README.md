# 动态字段配置系统使用说明

## 📋 概述

动态字段配置系统允许您在不重新编译前端代码的情况下，动态添加、修改和管理终端列表页面的显示字段。

## 🗂️ 文件结构

```
public/config/
├── terminalFields.json    # 字段配置文件
└── README.md             # 本说明文档
```

## ⚙️ 配置文件说明

### 基本结构

```json
{
  "version": "1.0.0",
  "lastUpdated": "2025-01-04T00:00:00Z",
  "description": "配置文件描述",
  
  "fieldCategories": {
    "分类名": {
      "name": "分类显示名称",
      "description": "分类描述",
      "fields": {
        "字段配置..."
      }
    }
  },
  
  "renderTypes": {
    "渲染类型定义..."
  },
  
  "defaultSettings": {
    "默认设置..."
  }
}
```

### 字段配置详解

每个字段包含以下属性：

```json
{
  "key": "字段唯一标识",
  "displayName": "显示名称",
  "dataPath": "数据路径",
  "sortable": true,
  "width": 120,
  "hideOn": ["xs", "sm"],
  "type": "渲染类型",
  "enabled": true,
  "order": 1,
  "tooltip": "提示信息",
  "config": {
    "特定配置..."
  }
}
```

#### 属性说明

- **key**: 字段的唯一标识符
- **displayName**: 在表格中显示的列名
- **dataPath**: 从终端数据中提取值的路径（支持嵌套，如 `status.propertyMetadata.battery_level`）
- **sortable**: 是否支持排序
- **width**: 列宽度（像素）
- **hideOn**: 在哪些屏幕尺寸下隐藏（`xs`, `sm`, `md`, `lg`, `xl`）
- **type**: 渲染类型（见下方支持的类型）
- **enabled**: 是否默认启用
- **order**: 显示顺序
- **tooltip**: 鼠标悬停提示
- **config**: 特定渲染器的配置

## 🎨 支持的渲染类型

### 1. text - 普通文本
```json
{
  "type": "text"
}
```

### 2. date - 日期
```json
{
  "type": "date"
}
```

### 3. datetime - 日期时间
```json
{
  "type": "datetime"
}
```

### 4. status - 状态芯片
```json
{
  "type": "status"
}
```

### 5. fileVersion - 文件版本
```json
{
  "type": "fileVersion",
  "config": {
    "versionKeys": ["APK{terminalType}", "PRO{terminalType}"],
    "showVersionKey": true
  }
}
```

### 6. percentage - 百分比
```json
{
  "type": "percentage"
}
```

### 7. signal - 信号强度
```json
{
  "type": "signal"
}
```

### 8. temperature - 温度
```json
{
  "type": "temperature"
}
```

### 9. networkType - 网络类型
```json
{
  "type": "networkType"
}
```

## 📝 添加新字段示例

### 示例1: 添加简单文本字段

```json
{
  "newField": {
    "key": "newField",
    "displayName": "新字段",
    "dataPath": "status.propertyMetadata.new_property",
    "sortable": true,
    "width": 100,
    "hideOn": ["xs"],
    "type": "text",
    "enabled": false,
    "order": 20,
    "tooltip": "这是一个新添加的字段"
  }
}
```

### 示例2: 添加百分比字段

```json
{
  "cpuUsage": {
    "key": "cpuUsage",
    "displayName": "CPU使用率",
    "dataPath": "status.propertyMetadata.cpu_usage",
    "sortable": true,
    "width": 100,
    "hideOn": ["xs", "sm"],
    "type": "percentage",
    "enabled": false,
    "order": 21,
    "tooltip": "设备CPU使用率百分比"
  }
}
```

### 示例3: 添加文件版本字段

```json
{
  "customVersion": {
    "key": "customVersion",
    "displayName": "自定义版本",
    "dataPath": "status.fileVersionMetadata",
    "sortable": false,
    "width": 150,
    "hideOn": ["xs", "sm"],
    "type": "fileVersion",
    "enabled": false,
    "order": 22,
    "config": {
      "versionKeys": ["CUSTOM{terminalType}", "CFG{lineNO}"],
      "showVersionKey": true
    }
  }
}
```

## 🔄 使用流程

### 1. 修改配置文件
编辑 `/public/config/terminalFields.json` 文件

### 2. 重新加载配置
- 方法1: 在页面中点击"字段管理"按钮，然后点击刷新图标
- 方法2: 刷新浏览器页面

### 3. 启用新字段
在"字段管理"界面中勾选新添加的字段

## 🎯 最佳实践

### 1. 字段命名
- 使用有意义的key名称
- displayName要简洁明了
- 添加适当的tooltip说明

### 2. 性能考虑
- 避免添加过多字段
- 合理设置hideOn属性
- 非必要字段默认设置enabled为false

### 3. 数据路径
- 确保dataPath指向的数据确实存在
- 使用嵌套路径时要小心null值
- 测试不同终端类型的数据兼容性

### 4. 响应式设计
- 在小屏幕上隐藏次要字段
- 合理设置列宽度
- 考虑移动端用户体验

## 🐛 故障排除

### 1. 字段不显示
- 检查字段的enabled属性是否为true
- 确认dataPath路径正确
- 查看浏览器控制台是否有错误

### 2. 配置加载失败
- 检查JSON格式是否正确
- 确认文件路径是否正确
- 查看网络请求是否成功

### 3. 渲染错误
- 确认type类型是否支持
- 检查config配置是否正确
- 查看控制台错误信息

## 📚 扩展开发

如需添加新的渲染类型，请修改：
1. `src/components/FieldRenderers.jsx` - 添加新的渲染方法
2. `public/config/terminalFields.json` - 在renderTypes中添加类型说明

## 🔗 相关文件

- `src/hooks/useFieldConfig.js` - 配置管理Hook
- `src/components/FieldRenderers.jsx` - 字段渲染器
- `src/components/FieldConfigManager.jsx` - 配置管理界面
- `src/pages/terminals/TerminalList.jsx` - 终端列表页面
