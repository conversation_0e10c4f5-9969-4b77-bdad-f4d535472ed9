# WebAdmin 开发备忘录

## 📋 文档结构说明

本项目文档已按功能模块重新组织，详细信息请查看：**[docs/README.md](./docs/README.md)**

### 📁 文档结构
- 📋 **[完整文档索引](./docs/README.md)** - 所有技术文档的分类索引
- 📝 **[更新日志](./CHANGELOG.md)** - 版本变更和功能更新记录

### 🔗 快速导航
- 📖 **[WebAdmin详细设计说明书](./docs/WEBADMIN_DETAILED_DESIGN_SPECIFICATION.md)** - **新增** 完整的系统设计说明书
- 🏗️ [系统架构](./docs/system-architecture.md) - 整体架构设计
- 💻 [前端开发](./docs/frontend-development.md) - React开发规范
- 🎨 [UI样式规范](./docs/frontend/ui-style-guide.md) - 前端UI样式规范
- ⚙️ [后端开发](./docs/backend-development.md) - .NET API设计
- 🗄️ [数据库表结构](./docs/database/DATABASE_SCHEMA_DOCUMENTATION.md) - 完整数据库表结构说明
- 📋 [客户项目集成](./docs/guides/CUSTOMER_PROJECT_INTEGRATION_GUIDE.md) - 客户项目集成指南

## ⚠️ 重要配置说明

### JSON序列化配置 🚨 严禁修改
- **全局配置**: Program.cs中使用 `JsonNamingPolicy.CamelCase`
- **枚举序列化**: 使用数字格式，不使用JsonStringEnumConverter
- **影响范围**: 此配置影响所有API响应格式，修改会导致全局问题
- **前端约定**: 所有API请求和响应都使用camelCase命名
- **日期格式**: 前端发送日期时间必须使用ISO 8601格式（如：`2025-08-07T17:10:00`），严禁替换T为空格
- **经验教训**: 全局配置变更影响巨大，任何修改都需要全面测试所有功能
- **重要规则**: 🚨 **JSON序列化、全局配置、数据格式等可能产生破坏性影响的修改，必须先询问用户意见，详细说明风险和影响范围，获得明确同意后才能进行**

## 最新更新

### 2025-08-27 - WebAdmin详细设计说明书完成 ✅
- **文档完成**: 完整的WebAdmin详细设计说明书已全部完成
- **已完成章节**:
  1. **系统概述** - 项目背景、系统目标、核心功能、技术特点、系统边界
  2. **系统架构设计** - 总体架构、技术栈选型、模块划分、部署架构、数据流设计
  3. **数据库设计** - 数据库架构、核心表结构、关系设计、索引策略、数据迁移
  4. **前端设计** - React架构设计、组件设计规范、路由设计、状态管理、UI设计规范
  5. **后端设计** - .NET架构设计、API设计规范、服务层设计、中间件设计、权限系统设计
  6. **功能模块设计** - 用户管理、商户管理、终端管理、文件管理、消息管理、系统管理模块
  7. **安全设计** - 身份认证、权限控制、数据安全、通信安全、审计日志设计
  8. **接口设计** - RESTful API、TCP通信协议、消息队列接口、文件服务接口、第三方集成
  9. **部署运维设计** - Docker容器化部署、环境配置管理、监控健康检查、备份恢复策略、自动化部署、运维管理
  10. **总结与展望** - 设计总结、技术亮点、未来规划、建议和改进
- **文档特点**:
  - 基于项目实际代码编写，确保内容准确性和实用性
  - 包含详细的架构图、代码示例和配置文件
  - 涵盖完整的系统生命周期，从设计到部署运维
  - 支持开发团队、测试团队、运维团队、项目管理等多个角色使用
  - 总计6000+行，是一份完整的企业级系统设计文档
- **文档版本**: v2.0（已完成）
- **文档状态**: 已完成，可作为项目的权威技术文档使用

### 2025-08-15 - 多数据库迁移架构重构（独立迁移程序集）✅
- **架构升级**: 实现了完全自动化的多数据库迁移支持，无需手动调整迁移文件
- **新增项目**:
  - `SlzrCrossGate.Migrations.MySql` - MySQL专用迁移程序集
  - `SlzrCrossGate.Migrations.SqlServer` - SQL Server专用迁移程序集
- **核心改进**:
  1. **TcpDbContext重构**: 添加OnConfiguring方法，根据DatabaseProvider自动选择迁移程序集
  2. **迁移完全隔离**: 每种数据库维护独立的迁移历史和文件
  3. **自动化生成**: EF Core根据数据库类型自动生成正确的SQL语法，无需手动调整
  4. **生产环境安全**: MySQL生产环境完全不受影响，保持现有迁移文件
- **使用文档**:
  - 📋 [多数据库迁移使用指南](./docs/MULTI_DATABASE_MIGRATION_GUIDE.md) - 完整使用说明
  - 🚀 [迁移快速参考](./docs/MIGRATION_QUICK_REFERENCE.md) - 常用命令参考
- **工作流程**:
  - 日常开发使用MySQL环境生成迁移
  - 需要SQL Server支持时切换配置并生成对应迁移
  - 两个数据库环境完全独立，互不影响

### 2025-08-13 - 多数据库支持完善（MySQL + SQL Server）✅
- **问题发现**: 项目中有多处只考虑了MySQL而没有支持SQL Server，包括Program.cs中的数据库迁移诊断代码、健康检查配置等
- **主要修复内容**:
  1. **数据库连接诊断多数据库支持**:
     - 修改WebAdmin和ApiService的Program.cs，根据DatabaseProvider配置动态解析连接字符串
     - 支持MySQL使用MySqlConnectionStringBuilder，SQL Server使用SqlConnectionStringBuilder
     - 添加端口解析逻辑，MySQL默认3306，SQL Server默认1433
  2. **健康检查配置多数据库支持**:
     - 根据DatabaseProvider动态添加对应的健康检查：MySQL使用AddMySql，SQL Server使用AddSqlServer
     - 为不支持的数据库类型提供基本的健康检查占位符
  3. **TestDbConnection类重构**:
     - 支持多数据库的连接测试，使用DbConnection基类
     - 根据数据库类型使用不同的测试查询语句
     - 为MySQL和SQL Server提供专门的错误诊断信息
  4. **NuGet包依赖完善**:
     - 为WebAdmin和ApiService项目添加AspNetCore.HealthChecks.SqlServer包
     - Core项目已包含Microsoft.EntityFrameworkCore.SqlServer和EFCore.BulkExtensions.SqlServer
- **配置示例文件**:
  - 创建appsettings.sqlserver-example.json提供SQL Server配置示例
  - 包含连接字符串、DatabaseProvider、迁移配置等完整配置
- **文档更新**:
  - 更新docs/backend-development.md，添加MySQL和SQL Server的配置示例
  - 更新健康检查和DbContext配置的文档说明
- **技术实现**:
  - Extensions.cs中的AddConfiguredDbContext方法已支持多数据库切换
  - 通过DatabaseProvider配置项控制使用的数据库类型
  - 保持向后兼容，默认使用MySQL
- **用户体验**:
  - 数据库连接诊断现在支持两种数据库类型
  - 错误提示更加准确，针对不同数据库提供专门的诊断信息
  - 配置更加灵活，只需修改appsettings.json中的DatabaseProvider即可切换数据库

### 2025-08-08 - 消息发布功能权限控制实现 ✅
- **权限常量定义**: 在前端权限常量中添加了`MESSAGE.SEND`权限
- **后端权限配置**:
  - 在`FeaturePermissionService.cs`中添加了"message.send"功能配置
  - 设置风险等级为Medium，描述为"允许发布消息到终端"
  - 为SystemAdmin和MerchantAdmin角色默认分配了消息发布权限
- **前端权限控制**: 在消息管理页面(MessageList.jsx)的"消息发布"按钮上添加了`FeatureGuard`权限保护
- **后端API权限验证**: 为所有消息发送相关的API方法添加了权限验证特性：
  - `MessagesController.SendMessage` - 使用`[RequireFeaturePermission("message.send")]`
  - `MessagesController.SendMessageByLine` - 使用`[RequireFeaturePermission("message.send")]`
  - `MessagesController.SendMessageToMerchant` - 使用`[RequireFeaturePermission("message.send")]`
  - `TerminalsController.SendMessage` - 使用`[RequireFeaturePermission("terminal.send_message")]`
- **权限标识符说明**:
  - `message.send` - 消息管理页面的消息发布功能权限
  - `terminal.send_message` - 终端管理页面的发送消息功能权限（已存在）
  - 两个权限分别控制不同场景下的消息发送功能，保持功能权限的细粒度控制
- **权限控制效果**:
  - 无权限用户无法看到"消息发布"按钮
  - 即使通过其他方式访问发布页面，后端API也会进行权限验证
  - 系统管理员和商户管理员默认拥有消息发布权限

### 2025-08-08 - 启用并优化消息管理中的消息发布功能 ✅
- **功能恢复**: 启用了消息管理页面中被隐藏的"消息发布"按钮
- **API完善**: 在api.js中添加了缺失的消息发布API方法
  - `sendMessageToTerminals` - 发送到指定终端
  - `sendMessageToLine` - 发送到指定线路
  - `sendMessageToMerchant` - 发送到商户所有设备
- **用户体验优化**:
  - 将"发送消息"改为"消息发布"，更符合业务术语
  - 优化发布范围选项描述：
    - "指定终端设备" - 可选择具体的设备
    - "指定线路的所有设备" - 某条线路的所有设备
    - "商户的所有设备" - 当前商户的所有设备
  - 支持先选择商户，再根据商户动态加载消息类型
- **界面优化**:
  - **成功提示位置**: 将发布成功提示移到发布按钮附近，提升用户体验
  - **线路选择**: 改用对话框选择方式，支持显示线路统计信息（在线/离线/总计终端数）
  - **终端选择**: 改用对话框多选方式，支持全选/取消全选，显示终端详细信息和状态
  - **返回按钮**: 将"重置"按钮改为"返回消息管理"按钮，更符合用户操作习惯
- **功能特点**:
  - 完整的权限控制（SystemAdmin和MerchantAdmin角色）
  - 商户选择后自动加载对应的消息类型
  - 支持三种发布范围选择
  - 表单验证和错误处理
  - 发布成功后显示详细结果信息
  - 参考文件发布页面的选择界面设计，提供更好的数据筛选体验
- **技术实现**: 基于现有的MessageSend.jsx页面，该页面已完全实现用户需求的功能

### 2025-08-07 18:10 - 优化票价显示和输入步长 ✅
- **票价输入步长优化**:
  - 将所有票价输入框的步长从 `0.01` 改为 `0.5`
  - 最小值从 `0.01` 改为 `0.5`
  - 更符合实际票价设置习惯（通常为0.5元的倍数）
- **票价显示统一**:
  - 线路参数管理页面的票价列改为元显示
  - 从 `${params.value}分` 改为 `¥${(params.value / 100).toFixed(2)}`
  - 保持前端显示一致性（都使用元）
- **影响文件**:
  - `LinePriceCreateDialog.jsx`、`LinePriceEditDialog.jsx`、`CreateLineDialog.jsx` - 步长调整
  - `LinePriceListView.jsx` - 列表显示调整
- **用户体验**: 票价输入更符合实际使用场景，显示更直观

### 2025-08-07 18:05 - 优化票价输入用户体验 ✅
- **问题**: 票价字段使用元显示时，用户无法连续输入小数（如输入1后自动变成1.00，无法继续输入.50）
- **解决方案**:
  - 添加独立的 `fareDisplay` 状态管理显示值
  - `onChange` 事件只更新显示值，不进行转换
  - `onBlur` 事件时才进行元转分的转换和格式化
  - 关闭对话框时重置显示状态
- **影响文件**:
  - `LinePriceCreateDialog.jsx` - 线路参数创建弹出窗口
  - `LinePriceEditDialog.jsx` - 线路参数编辑弹出窗口
  - `CreateLineDialog.jsx` - 未注册线路创建弹出窗口
- **用户体验**: 现在可以流畅输入任意小数值，如1.50、2.35等

### 2025-08-07 18:00 - 修复刷新问题和优化未注册线路布局 ✅
- **修复刷新问题**:
  - 修复新建或编辑成功后页面没有自动刷新的问题
  - 在 `handleCreateSuccess` 和 `handleEditSuccess` 中使用强制刷新 `fetchLinePrices(false, true)`
  - 避免防重复请求机制阻止必要的数据刷新
- **优化未注册线路新建布局**:
  - 参考线路参数管理页面的合理布局，重新组织 `CreateLineDialog.jsx` 的字段布局
  - **布局调整**: 商户ID独占一行，线路编号和组号同行，线路名称独占一行，分公司和票价同行
  - **字段优化**: 添加更清晰的占位符提示，统一票价显示为元（后端仍保存分）
  - **用户体验**: 保持与线路参数管理页面一致的交互体验
- **影响**: 提升操作便利性和界面一致性

### 2025-08-07 17:50 - 调整线路参数校验规则和提示 ✅
- **校验规则调整**:
  - **线路编号**: 改为支持数字和字母，共4位（如：101A、B002）
  - **组号**: 可以为空或者是2位数字（如：01、02）
- **界面文本更新**:
  - 统一将"线路号"改为"线路编号"
  - 更新占位符提示文本，提供更清晰的格式说明
  - 添加输入长度限制（线路编号4位，组号2位）
- **验证规则**:
  - 线路编号：`/^[A-Za-z0-9]{4}$/` - 必须是4位数字或字母
  - 组号：`/^(\d{2}|)$/` - 必须为空或2位数字
- **影响**: 支持更灵活的线路编号格式，提供更准确的用户提示

### 2025-08-07 17:45 - 线路参数管理改为弹出窗口模式 ✅
- **问题**: 线路参数管理的新建和编辑功能使用独立页面，与其他管理页面不统一
- **改进**:
  - 创建 `LinePriceCreateDialog.jsx` 和 `LinePriceEditDialog.jsx` 弹出窗口组件
  - 修改 `LinePriceListView.jsx` 使用弹出窗口而不是页面导航
  - 保持与用户管理、商户管理等页面的一致性
- **功能特点**:
  - 创建弹出窗口支持商户选择、表单验证、票价格式转换
  - 编辑弹出窗口显示只读的线路基本信息，支持可编辑字段修改
  - 统一的错误处理和成功提示
- **影响**: 提升用户体验，保持界面操作的一致性

### 2025-08-07 17:30 - 增强功能权限管理安全保护 ✅
- **问题**: 功能权限管理的"启用功能"开关可以直接关闭，没有危险操作提醒
- **风险**: 一旦关闭功能权限管理功能，无法通过界面重新开启，只能数据库手动修复
- **解决方案**:
  - 在 `handlePermissionChange` 中添加对功能权限管理"启用功能"开关的特殊保护
  - 修改确认对话框支持两种类型：角色权限变更和功能开关变更
  - 为功能权限管理关闭操作提供"极度危险操作确认"提示
- **影响**: 防止误操作导致功能权限管理功能被意外关闭

### 2025-08-07 17:25 - 添加消息删除权限控制 ✅
- **功能**: 为消息管理的删除功能添加权限控制
- **前端**: 添加PERMISSIONS.MESSAGE.DELETE权限常量，删除按钮使用权限控制
- **后端**: 添加message.delete权限配置，DeleteMessage API使用RequireFeaturePermission特性
- **权限初始化**: 系统管理员和商户管理员默认拥有消息删除权限
- **影响**: 消息删除功能现在受权限系统控制，提高安全性

### 2025-08-07 17:10 - 修复枚举显示问题 ✅
- **问题**: 所有前端页面的枚举字段显示为"未知"
- **原因**: 之前添加了JsonStringEnumConverter导致枚举序列化为字符串格式，但前端期望数字格式
- **解决**: 移除JsonStringEnumConverter，保持枚举数字格式序列化
- **影响**: 修复了终端事件、消息管理等所有页面的枚举显示问题

### 2025-08-05 预约发布功能问题修复和UI优化 ✅
- **日期选择器优化**:
  - **宽度调整**: 日期选择器宽度从全屏改为合适宽度（md={6}或md={2.5}），提升操作便利性
  - **中文界面**: 添加 `adapterLocale={dateFnsZhCN}` 使日历界面显示中文（月份、星期等）
  - **格式统一**: 输入框显示值保持 `YYYY-MM-DD HH:mm` 格式，便于用户识别和输入
  - **24小时制**: 统一使用24小时制，避免上午/下午混淆
- **UI布局优化**:
  - 备注输入框改为独立一行显示，不再与日期选择器同行
  - 立即发布模式下也显示备注输入框，提升用户体验
  - 根据发布模式动态调整备注帮助文本
  - 筛选条件按钮区域调整为小尺寸，节省空间
- **API调用修复**:
  - 修复前端API调用字段名与后端DTO不匹配的问题
  - 创建预约发布：字段名改为PascalCase格式（MerchantID、FileVersionID等）
  - 更新预约发布：同样修复字段名格式问题
  - 确保FileVersionID正确转换为整数类型
- **用户体验提升**:
  - 日期选择器界面中文化，降低使用门槛
  - 合理的控件宽度，提升操作效率
  - 统一的时间格式，提升操作一致性

### 2025-08-05 预约发布业务逻辑修复和UI布局优化 ✅
- **后端业务逻辑修复**:
  - **商户级别发布优化**: 移除发布目标的必填验证，商户级别发布自动使用商户ID作为发布目标
  - **DTO验证调整**: 修改 `CreateScheduledFilePublishDto` 和 `UpdateScheduledFilePublishDto`，发布目标不再强制必填
  - **控制器验证逻辑**: 添加智能验证，仅线路级别和终端级别需要指定发布目标
  - **自动填充逻辑**: 商户级别发布时自动将商户ID设置为发布目标
- **前端UI布局优化**:
  - **控件位置调整**: 将线路/终端选择界面移动到发布模式和发布类型之间，逻辑流程更清晰
  - **操作流程优化**: 用户先选择发布模式→选择发布类型→选择具体目标（如需要）→设置时间和备注
  - **界面逻辑**: 商户级别发布不显示目标选择，线路/终端级别才显示相应的选择界面
- **技术实现**:
  - 后端验证逻辑根据发布类型动态调整
  - 前端UI组件按逻辑顺序重新排列
  - 保持现有的验证和错误处理机制
- **用户体验**:
  - 操作流程更符合用户思维习惯
  - 减少不必要的输入步骤
  - 界面布局更加合理直观

### 2025-08-05 预约发布时区问题修复 ✅
- **问题描述**: 前端发送UTC时间（如 `2025-08-07T06:30:00.000Z`），后端使用本地时间验证，导致时区差异造成"预约时间不能是过去时间"的错误
- **解决方案**: 保持后端本地时间逻辑不变，修改前端时间处理逻辑，发送本地时间而不是UTC时间
- **技术实现**:
  - 使用 `getTimezoneOffset()` 方法计算时区偏移
  - 将用户选择的本地时间转换为不带时区信息的ISO字符串格式
  - 格式：`YYYY-MM-DDTHH:mm:ss`（移除毫秒和时区标识）
- **修改范围**:
  - 文件发布页面的预约发布API调用
  - 预约管理页面的更新预约API调用
- **时间处理逻辑**:
  ```javascript
  const localTimeString = new Date(scheduledTime.getTime() - scheduledTime.getTimezoneOffset() * 60000)
    .toISOString()
    .slice(0, 19); // 移除毫秒和时区信息
  ```
- **效果**: 确保前后端使用相同的时间基准，避免时区差异导致的验证错误

### 2025-08-05 预约管理页面问题修复 ✅
- **数据结构问题修复**:
  - **错误**: `TypeError: Cannot read properties of undefined (reading 'items')`
  - **原因**: API响应数据结构与前端期望不一致
  - **解决**: 添加容错处理，支持多种响应数据格式
  - **代码**: 使用 `data.items || []` 和 `data.totalCount || 0` 进行安全访问
- **菜单图标问题修复**:
  - **问题**: `ScheduleIcon` 图标不存在，菜单项不显示图标
  - **解决**: 添加 `Clock` 图标到图标映射系统
  - **图标映射**: 添加 `Clock`、`Schedule`、`ScheduleIcon` 别名映射
  - **数据库更新**: 将菜单项图标名称改为 `Clock`
- **技术实现**:
  - 在 `iconMapping.js` 中添加 `Clock` 图标导入和映射
  - 添加多个别名支持不同的命名方式
  - 增强API响应数据的容错处理
- **SQL更新脚本**:
  ```sql
  UPDATE MenuItems SET IconName = 'Clock' WHERE ItemKey = 'scheduled-publish';
  ```

### 2025-08-05 预约发布操作人信息和时区问题修复 ✅
- **操作人信息记录问题修复**:
  - **问题**: 预约发布时操作人显示为 "Unknown"
  - **原因**: 使用 `User.Identity?.Name` 获取用户名不准确
  - **解决**: 改用 `UserService.GetUserNameForOperator(User)` 获取完整的用户信息
  - **格式**: 显示为 "真实姓名(用户名)" 的格式，提供更详细的操作人信息
- **预约管理日期筛选时区修复**:
  - **问题**: 筛选条件中的日期选择器发送UTC时间，与后端本地时间不匹配
  - **解决**: 在API调用前将日期转换为本地时间字符串格式
  - **实现**: 使用相同的时区转换逻辑，确保筛选和创建使用一致的时间格式
- **预约管理界面增强**:
  - **新增操作人列**: 在预约管理列表中显示操作人信息
  - **表格结构优化**: 调整列宽和显示优先级，操作人列在小屏幕上隐藏
  - **信息完整性**: 现在可以清楚看到每个预约是由谁创建的
- **技术实现**:
  - 后端使用 `UserService.GetUserNameForOperator()` 获取格式化的用户信息
  - 前端筛选API调用使用本地时间转换逻辑
  - 表格增加操作人列，支持响应式显示
  - 更新colSpan数量以适应新增的列

### 2025-08-05 全系统日期选择器统一优化 ✅
- **预约管理页面重复请求修复**:
  - **问题**: 页面加载时会请求两次API
  - **原因**: useEffect依赖项可能导致重复触发
  - **解决**: 优化useEffect依赖项，避免不必要的重复请求
- **终端日志管理页面优化**:
  - **日期格式**: 改为 `yyyy-MM-dd HH:mm` 格式，统一显示标准
  - **24小时制**: 添加 `ampm={false}` 属性
  - **中文界面**: 添加 `adapterLocale={dateFnsZhCN}` 和中文本地化配置
  - **时区修复**: API调用时转换为本地时间字符串，避免UTC时区问题
- **终端事件列表页面优化**:
  - **日期格式**: 从 `yyyy/MM/dd HH:mm` 改为 `yyyy-MM-dd HH:mm`
  - **已有配置**: 该页面已正确配置中文界面和24小时制
  - **时区处理**: 使用 `formatDateForAPI` 函数，已正确处理本地时间
- **终端记录页面优化**:
  - **日期格式**: 从 `yyyy/MM/dd HH:mm` 改为 `yyyy-MM-dd HH:mm`
  - **已有配置**: 该页面已正确配置中文界面和24小时制
  - **时区处理**: 使用 `formatDateForAPI` 函数，已正确处理本地时间
- **统一标准**:
  - **日期格式**: 全系统统一使用 `YYYY-MM-DD HH:mm` 格式
  - **时间制式**: 全部使用24小时制，避免上午/下午混淆
  - **界面语言**: 日历弹窗显示中文月份和星期
  - **时区处理**: 统一使用本地时间，避免UTC转换问题

### 2025-08-05 日期时间格式标准化修复 ✅
- **问题发现**:
  - 预约管理页面API请求中的日期格式为 `2025-08-07T04:00:00`（带T分隔符）
  - 某些后端系统可能不正确解析ISO格式中的T分隔符
- **格式统一修复**:
  - **预约管理页面**: 筛选和更新API调用中的时间格式改为空格分隔
  - **文件发布页面**: 创建预约API调用中的时间格式改为空格分隔
  - **终端日志页面**: API调用中的时间格式改为空格分隔
  - **其他页面**: 终端事件和终端记录页面使用 `formatDateForAPI` 函数，已是正确格式
- **技术实现**:
  ```javascript
  // 修改前：2025-08-07T14:30:00
  .toISOString().slice(0, 19)

  // 修改后：2025-08-07 14:30:00
  .toISOString().slice(0, 19).replace('T', ' ')
  ```
- **工具函数验证**:
  - `formatDateForAPI()` 和 `formatDateTimeForAPI()` 使用 `date-fns` 的 `format()` 函数
  - 生成格式为 `yyyy-MM-dd HH:mm:ss`（带空格），无需修改
- **最终格式标准**:
  - **API传输格式**: `YYYY-MM-DD HH:mm:ss`（空格分隔）
  - **界面显示格式**: `YYYY-MM-DD HH:mm`（空格分隔）
  - **兼容性**: 确保所有后端系统都能正确解析

### 2025-08-05 终端日志页面异常和预约页面重复请求修复 ✅
- **终端日志页面异常修复**:
  - **错误**: `Cannot read properties of undefined (reading 'MuiLocalizationProvider')`
  - **原因**: 中文本地化配置引用了不存在的属性路径
  - **解决**: 移除错误的 `localeText` 配置，简化LocalizationProvider配置
  - **修复**: 只保留 `dateAdapter={AdapterDateFns}` 和 `adapterLocale={dateFnsZhCN}`
- **预约管理页面重复请求修复**:
  - **问题**: 页面加载时重复请求API `https://localhost:7296/api/ScheduledFilePublish?page=1&pageSize=10`
  - **参考**: 采用消息管理页面的处理方式
  - **解决方案**:
    - 添加 `hasLoadedRef` 标记防止重复加载
    - 分离初始加载和参数变化的useEffect
    - 搜索和清空筛选时重置页码到第一页
- **技术实现**:
  ```javascript
  // 防止重复加载
  const hasLoadedRef = useRef(false);

  // 初始加载（只执行一次）
  useEffect(() => {
    if (hasLoadedRef.current) return;
    hasLoadedRef.current = true;
    loadScheduledPublishes();
  }, []);

  // 参数变化时重新加载
  useEffect(() => {
    if (hasLoadedRef.current) {
      loadScheduledPublishes();
    }
  }, [page, pageSize]);
  ```
- **用户体验改进**:
  - 页面加载更快，避免重复网络请求
  - 搜索和清空筛选时自动回到第一页
  - 添加控制台日志便于调试和监控

### 2025-08-05 预约页面重复请求问题深度修复 ✅
- **问题升级**: 修复后发现请求次数从2次增加到3次，问题更严重
- **根本原因分析**:
  - `setPage(0)` 触发useEffect中的 `loadScheduledPublishes()`
  - 同时 `applyFilters()` 和 `clearFilters()` 又直接调用 `loadScheduledPublishes(true)`
  - 导致双重调用，产生重复请求
- **最终解决方案**:
  ```javascript
  // 应用筛选 - 避免重复调用
  const applyFilters = () => {
    if (page === 0) {
      // 已在第一页，直接加载
      loadScheduledPublishes(true);
    } else {
      // 不在第一页，设置页码为0，useEffect自动触发
      setPage(0);
    }
  };

  // 清空筛选 - 避免重复调用
  const clearFilters = () => {
    // ... 清空筛选条件
    if (page === 0) {
      // 已在第一页，延迟加载确保状态更新
      setTimeout(() => loadScheduledPublishes(true), 100);
    } else {
      // 不在第一页，设置页码为0，useEffect自动触发
      setPage(0);
    }
  };
  ```
- **技术要点**:
  - 避免 `setPage()` 和直接调用 `loadScheduledPublishes()` 的冲突
  - 根据当前页码状态选择不同的加载策略
  - 保持单一数据源原则，避免多路径触发同一操作
- **预期效果**: 页面加载时只发起一次API请求，搜索和筛选操作也只发起必要的请求

### 2025-08-05 React.StrictMode重复请求终极解决方案 ✅
- **深层原因发现**: React.StrictMode在开发模式下故意双重执行useEffect来检测副作用
- **问题本质**: 不是代码逻辑问题，而是React开发模式的特性
- **终极解决方案**: 基于请求标识符的去重机制
  ```javascript
  const loadingRequestRef = useRef(null);

  const loadScheduledPublishes = async (resetPage = false) => {
    // 生成唯一请求标识符
    const currentPage = resetPage ? 0 : page;
    const requestId = `${currentPage}-${pageSize}-${JSON.stringify(filters)}`;

    // 如果正在进行相同的请求，直接返回
    if (loadingRequestRef.current === requestId) {
      console.log('跳过重复请求', requestId);
      return;
    }

    loadingRequestRef.current = requestId;
    // ... 执行API请求
    loadingRequestRef.current = null; // 请求完成后清除标识
  };
  ```
- **技术优势**:
  - 基于请求参数生成唯一标识，精确识别重复请求
  - 兼容React.StrictMode的双重执行机制
  - 不影响正常的用户操作和参数变化
  - 适用于生产环境和开发环境
- **最终效果**: 无论在开发模式还是生产模式，都能确保相同参数的请求只执行一次

### 2025-08-05 预约发布功能完整集成 ✅
- **菜单集成完善**:
  - ✅ **菜单初始化**: 已在MenuService中添加预约发布菜单项
  - ✅ **菜单名称优化**: 从"预约管理"改为"预约发布"，更明确功能用途
  - ✅ **菜单排序调整**: 将预约发布放在文件版本(SortOrder=2)和发布记录(SortOrder=4)之间(SortOrder=3)
  - ✅ **路由配置**: 已在routes.jsx中配置对应路由
- **操作日志集成**:
  - ✅ **全局过滤器**: ActionLoggingFilter已配置为全局过滤器，无需在控制器单独添加
  - ✅ **模块映射**: 在AuditLogMiddleware中添加"scheduledfilepublish" => "预约管理"映射
  - ✅ **资源映射**: 添加"scheduledfilepublish" => "预约发布"资源名称映射
  - ✅ **前端模板**: 在操作日志页面moduleOptions中添加"预约发布"选项
- **页面标题统一**: 将页面标题从"预约发布管理"简化为"预约发布"
- **技术实现**:
  ```javascript
  // 菜单配置
  {
    ItemKey = "scheduled-publish",
    Title = "预约发布",
    Href = "/app/files/scheduled-publish",
    IconName = "ScheduleIcon",
    SortOrder = 3  // 在文件版本和发布记录之间
  }

  // 操作日志模块映射
  "scheduledfilepublish" => "预约管理"
  ```
- **用户体验**: 菜单结构更合理，操作日志记录完整，功能命名更清晰

### 2025-08-07 仪表盘字段命名问题修复 ✅
- **问题描述**: 仪表盘页面报错 `Cannot read properties of undefined (reading 'totalCount')`
- **根本原因**: 今天添加预约发布功能时，在Program.cs中设置了 `PropertyNamingPolicy = null`，导致JSON序列化从默认的camelCase改为PascalCase，影响了所有API响应格式
- **影响范围**: 全局API响应格式变化，前端期望camelCase但后端返回PascalCase
- **解决方案**:
  1. **恢复camelCase配置**: 修改Program.cs中的JSON序列化配置
  2. **保持一致性**: 确保前后端使用相同的命名约定
- **技术实现**:
  ```csharp
  // Program.cs - 恢复camelCase命名策略
  .AddJsonOptions(options => {
    options.JsonSerializerOptions.Converters.Add(new JsonStringEnumConverter());
    options.JsonSerializerOptions.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
  })
  ```
- **经验教训**: 全局配置变更影响巨大，需要谨慎处理

### 2025-08-07 预约发布API参数绑定修复 ✅
- **问题描述**: POST请求报错"model field is required"和日期时间转换错误
- **错误信息**:
  ```json
  {
    "errors": {
      "model": ["The model field is required."],
      "$.ScheduledTime": ["The JSON value could not be converted to System.DateTime"]
    }
  }
  ```
- **根本原因**:
  1. **缺少[FromBody]属性**: 控制器方法无法正确绑定JSON请求体
  2. **日期格式问题**: 前端发送 `"2025-08-07 17:10:00"` 格式，ASP.NET Core期望 `"2025-08-07T17:10:00"` 格式
- **解决方案**:
  1. **添加[FromBody]属性**: 确保POST/PUT方法正确绑定JSON请求体
  2. **修复日期格式**: 前端发送ISO 8601格式的日期时间字符串
- **技术实现**:
  ```csharp
  // 控制器方法
  [HttpPost]
  public async Task<ActionResult> CreateScheduledFilePublish([FromBody] CreateScheduledFilePublishDto model)

  [HttpPut("{id}")]
  public async Task<IActionResult> UpdateScheduledFilePublish(int id, [FromBody] UpdateScheduledFilePublishDto model)
  ```
  ```javascript
  // 前端日期格式化（移除.replace('T', ' ')）
  const localTimeString = new Date(scheduledTime.getTime() - scheduledTime.getTimezoneOffset() * 60000)
    .toISOString()
    .slice(0, 19); // 保持ISO格式: "2025-08-07T17:10:00"
  ```
- **经验教训**:
  - POST/PUT方法处理JSON请求体时必须使用[FromBody]属性
  - 日期时间格式必须符合ASP.NET Core的期望格式

### 2025-08-07 终端日志页面日期格式修复 ✅
- **问题发现**: 终端日志页面（TerminalLogs.jsx）中仍使用 `.replace('T', ' ')` 格式化日期
- **影响**: 可能导致API请求参数格式不正确
- **修复内容**: 移除 `.replace('T', ' ')` 操作，保持ISO 8601格式
- **技术实现**:
  ```javascript
  // 修改前
  .toISOString().slice(0, 19).replace('T', ' ')

  // 修改后
  .toISOString().slice(0, 19)
  ```
- **检查结果**:
  - ✅ 终端记录页面：使用 `formatDateForAPI` 函数，格式正确
  - ✅ 终端事件页面：使用 `formatDateForAPI` 函数，格式正确
  - ✅ 终端日志页面：已修复，现在使用ISO格式
- **重要提醒**: 所有API请求参数中的日期时间必须使用ISO 8601格式，严禁替换T为空格

### 2025-08-05 文件版本管理预约发布/定时发布功能实现 ✅
- **功能需求**: 在文件版本管理中增加预约发布/定时发布功能，用户可以设定时间自动发布，支持预约前取消，方便查看预约信息
- **设计方案**: 采用方案二（独立预约管理），创建独立的预约发布管理模块，功能完整且维护成本合理
- **后端实现**:
  1. **数据模型设计**:
     - 创建 `ScheduledFilePublish` 模型，包含完整的发布信息
     - 创建 `ScheduledPublishStatus` 枚举：待发布、已发布、已取消、发布失败
     - 数据库迁移 `AddScheduledFilePublish` 成功应用
  2. **API接口**:
     - `ScheduledFilePublishController` 提供完整的CRUD操作
     - 支持分页查询、状态过滤、时间范围过滤
     - 权限控制：非管理员只能操作自己商户的预约
     - 业务验证：预约时间不能是过去时间、避免重复预约
  3. **后台调度服务**:
     - `ScheduledPublishService` 后台服务，每分钟检查待执行的预约
     - 自动执行到期的预约发布，创建发布记录和历史记录
     - 发送发布事件消息，与现有发布流程完全兼容
     - 完善的错误处理和日志记录
- **前端实现**:
  1. **发布页面增强**:
     - 在 `FilePublish.jsx` 中添加发布模式选择（立即发布/预约发布）
     - 集成 `DateTimePicker` 组件，支持精确的预约时间选择
     - 添加备注信息输入，提升用户体验
     - 根据发布模式动态调整按钮文本和验证逻辑
  2. **预约管理页面**:
     - 创建 `ScheduledFilePublishList.jsx` 独立管理页面
     - 支持多条件筛选：商户、状态、时间范围
     - 响应式表格设计，适配移动端
     - 提供编辑和取消预约功能，仅限待发布状态
  3. **文件版本列表增强**:
     - 在操作按钮中添加预约发布快捷入口
     - 区分立即发布和预约发布按钮，提升用户体验
     - 支持直接跳转到预约发布模式
  4. **系统集成**:
     - 在路由配置中添加预约管理页面路由
     - 在菜单服务中添加"预约管理"菜单项
     - API服务中添加预约发布相关接口调用
- **技术实现亮点**:
  - 与现有发布系统完全兼容，复用发布逻辑
  - 后台服务使用 `IHostedService` 实现，稳定可靠
  - 完善的权限控制和业务验证
  - 详细的日志记录，便于运维监控
  - 前端组件化设计，代码复用性强
  - 响应式UI设计，支持多设备访问

### 2025-08-05 文件版本管理页面增加备注字段功能 ✅
- **功能需求**: 在文件版本管理页面增加备注字段显示，在上传文件时增加备注字段的编辑
- **后端实现**:
  1. **数据模型扩展**: 在 `FileVer` 模型中添加 `Remarks` 字段（最大500字符）
  2. **DTO更新**:
     - `FileVersionDto` 添加 `Remarks` 属性
     - `CreateFileVersionDto` 添加 `Remarks` 属性，带验证注解
  3. **API功能**:
     - 上传文件时保存备注信息到数据库
     - 查询文件版本时返回备注信息
     - 单个文件版本详情包含备注信息
  4. **数据库迁移**: 成功创建并应用迁移 `AddRemarksToFileVer`
- **前端实现**:
  1. **上传对话框**:
     - 添加备注信息输入字段（多行文本框，3行高度）
     - 提供友好的提示文本："可选，记录本次文件上传的说明信息"
     - 支持最大500字符的备注输入
  2. **文件版本列表**:
     - 在表格中添加"备注信息"列
     - 备注信息显示最大宽度200px，超出部分省略号显示
     - 鼠标悬停显示完整备注内容
     - 无备注时显示"-"
  3. **状态管理**: 在上传表单状态中添加 `remarks` 字段
- **用户体验优化**:
  - 备注信息为可选字段，用户可以选择是否填写
  - 多行文本框支持换行输入，便于记录详细信息
  - 文件版本列表中备注信息紧凑显示，不影响表格布局
  - 提供tooltip显示完整备注内容，便于查看
- **功能价值**:
  - 帮助用户记录每次文件上传的目的和变更说明
  - 便于团队协作时了解文件版本历史
  - 提升文件版本管理的可追溯性和可维护性
- **用户体验修复**:
  - **问题**: 文件上传成功后没有成功提示信息，上传成功的记录没有添加到列表或者刷新列表
  - **解决方案**:
    1. 导入并使用 `useSnackbar` 通知系统
    2. 导入 `parseErrorMessage` 统一错误处理工具
    3. 修改上传成功处理：显示"文件上传成功"通知，自动刷新列表
    4. 修改删除成功处理：显示"文件版本删除成功"通知
    5. 统一错误处理：使用 `parseErrorMessage` 解析错误信息并通过通知显示
  - **技术实现**:
    - 上传成功：`enqueueSnackbar('文件上传成功', { variant: 'success' })`
    - 删除成功：`enqueueSnackbar('文件版本删除成功', { variant: 'success' })`
    - 错误处理：`enqueueSnackbar(parseErrorMessage(error, '操作失败'), { variant: 'error' })`
  - **用户体验提升**:
    - 操作成功时有明确的成功提示
    - 操作失败时显示具体的错误信息
    - 成功操作后自动刷新列表，用户能立即看到最新数据
    - 替换了原有的 `alert()` 弹窗，使用更现代的通知系统
  - **列表刷新修复**:
    - **问题**: 上传成功后需要手动刷新才能看到新上传的文件
    - **原因**: `loadFileVersions()` 函数有防重复请求逻辑，当参数未变化时会跳过请求
    - **解决方案**: 在上传和删除成功后调用 `loadFileVersions(false, true)` 强制刷新
    - **技术实现**:
      - 上传成功：`loadFileVersions(false, true); // 强制刷新列表`
      - 删除成功：`loadFileVersions(false, true); // 强制刷新列表`
    - **效果**: 用户操作成功后立即看到最新的文件版本列表，无需手动刷新

### 2025-08-01 新增字段配置JSON编辑功能，修复文件版本发布按钮显示问题，调整编号规则并优化导入功能性能 ✅
- **字段配置JSON编辑功能**:
  1. **功能需求**: 在字段配置管理中增加直接编辑原始JSON文件的功能，提供更直接的编辑方式
  2. **实现方案**:
     - 在现有字段配置管理器中添加编辑模式切换功能
     - 使用ToggleButtonGroup提供表单编辑和JSON编辑两种模式
     - 表单编辑模式：原有的可视化字段编辑界面
     - JSON编辑模式：直接编辑JSON配置文件内容
  3. **技术实现**:
     - 新增状态：`editViewMode`（'form'/'json'）、`jsonContent`、`jsonError`
     - 实时JSON格式验证，显示语法错误提示
     - 双向数据同步：表单↔JSON内容自动同步
     - 专用的等宽字体显示JSON内容，提升编辑体验
  4. **用户体验**:
     - 编辑模式下显示切换按钮（表单图标/代码图标）
     - JSON模式提供"应用更改"按钮，手动同步到表单
     - 保存时自动验证JSON格式，防止无效配置
     - 错误提示清晰，帮助用户快速定位问题
  5. **使用场景**:
     - 批量修改字段配置时，JSON编辑更高效
     - 复制粘贴配置内容时更方便
     - 高级用户可以直接编辑复杂的配置结构
  6. **用户体验优化**:
     - JSON编辑模式下隐藏表单编辑的使用说明，避免混淆
     - 移除"应用更改"按钮，简化操作流程
     - 统一使用"保存配置"按钮保存到服务器
     - 添加清晰的提示文字："直接编辑JSON配置，点击'保存配置'按钮保存到服务器"
  7. **布局优化**:
     - JSON编辑框自动占满剩余空间，提供更大的编辑区域
     - 使用Flexbox布局：DialogContent、编辑区域、JSON容器都采用flex布局
     - 固定元素设置flexShrink: 0，防止被压缩（错误提示、配置信息、使用说明等）
     - JSON编辑框设置flex: 1，自动占满剩余高度
     - 移除固定的rows属性，改为动态高度适应
  8. **语法高亮和格式校验增强**:
     - 集成@uiw/react-codemirror编辑器，提供专业的JSON编辑体验
     - JSON语法高亮：键、值、字符串、数字等用不同颜色显示
     - 实时格式校验：输入时即时检测JSON语法错误
     - 基础编辑功能：行号显示、括号匹配、自动缩进
     - 错误提示优化：在编辑器下方显示详细的错误信息
     - 保持轻量级：只启用基础功能，避免过度复杂
     - 滚动功能修复：使用固定高度(400px)替代复杂的flex布局，确保CodeMirror正常工作
     - 键盘导航恢复：回退过度复杂的CSS设置，保证方向键等基本操作正常
     - 编辑器高度优化：JSON模式下编辑器占满可用空间，隐藏使用说明区域，最大化编辑区域
- **文件版本发布按钮修复**:
  1. **问题描述**: 用户拥有文件版本发布权限，但页面上看不到发布按钮
  2. **问题根因**: 前端代码检查 `fileVersion.status === 'draft'` 条件，但后端 `FileVersionDto` 中没有 `status` 字段
  3. **数据结构不匹配**:
     - 前端期望: `fileVersion.status` 字段用于状态判断
     - 后端实际: `FileVer` 模型和 `FileVersionDto` 都没有状态字段
  4. **修复方案**:
     - 移除前端中不存在的状态检查条件
     - 简化发布按钮显示逻辑：只检查权限和编辑权限
     - 删除按钮同样移除了不存在的状态检查
  5. **修复后逻辑**:
     - `canPublish = permissions[PERMISSIONS.FILE_VERSION.PUBLISH]`
     - `canDelete = permissions[PERMISSIONS.FILE_VERSION.DELETE]`
  6. **权限判断简化**:
     - 移除前端重复的业务逻辑判断（商户权限、用户角色等）
     - 后端已有完整权限控制，前端只需检查功能权限即可
     - 避免前后端权限逻辑不一致的问题
- **车辆导入功能优化**:
  1. **问题**: 车辆导入遇到唯一键冲突时只显示通用错误信息，无法定位具体问题
  2. **错误表现**: `Duplicate entry '00009998-80012346' for key 'vehicleinfos.UK_Vehicle_MerchantDevice'`
  3. **优化方案**:
     - **简化检查逻辑**: 在读取Excel时检查车牌号和设备编号唯一性（带商户号）
     - **批量插入**: 通过检查的数据直接批量插入，避免重复检查
     - **错误信息优化**: 显示行号和具体错误，如"第5行：该商户下车牌号京A12345已存在"
     - **性能提升**: 避免逐条保存的性能开销，采用批量操作
  4. **技术改进**:
     - 移除重复的数据库查询检查
     - 简化错误处理逻辑，专注于Excel读取阶段的验证
     - 保持与线路参数导入一致的处理模式

- **银联密钥导入功能优化**:
  1. **权限控制优化**:
     - 非系统管理员只能导入自己商户的数据
     - 在读取阶段就进行权限验证，提前拦截无权限数据
  2. **唯一性检查优化**:
     - 检查商户ID+银联商户号+银联终端号的三字段组合唯一性
     - 在Excel/CSV读取阶段进行检查，避免重复验证
     - 错误信息包含行号和具体冲突字段
  3. **性能提升**:
     - 移除读取后的二次检查逻辑
     - 直接批量插入通过验证的数据
     - **商户ID检查优化**: 一次性获取所有商户ID到内存HashSet，避免每行数据都查询数据库
     - 简化代码逻辑，提高导入效率

- **线路参数导入功能优化**:
  1. **商户ID检查性能优化**:
     - 从每行数据执行数据库查询优化为一次性获取所有商户ID到内存
     - 使用HashSet进行O(1)时间复杂度的商户ID存在性检查
     - 显著提高大批量导入的性能

- **车辆导入功能优化**:
  1. **商户ID检查性能优化**:
     - 同样采用一次性获取+内存检查的方式
     - 避免每行数据都执行`_dbContext.Merchants.FindAsync()`查询
     - 大幅提升导入性能，特别是在处理大量数据时

- **编号规则调整**:
  1. **线路编号规则优化**:
     - 从"必须为4位数字"调整为"支持4位数字或英文字母"
     - 修改验证正则表达式：`/^\d+$/` → `/^[A-Za-z0-9]+$/`
     - 更新相关错误提示信息和导入模板说明
     - 涉及页面：线路参数管理、未注册线路创建、导入模板
     - 补充修复：线路参数编辑页面的UI提示信息（label和helperText）
  2. **设备编号规则优化**:
     - 车辆管理中设备编号支持"8位数字或英文字母"
     - 更新导入模板中的说明文字和验证提示
     - 保持8位长度限制，但允许字母和数字混合
  3. **影响范围**:
     - 后端验证逻辑：`IsValidLineNumber()` 方法
     - 前端验证：线路参数编辑页面、创建线路对话框
     - 导入模板：Excel验证规则和说明文字
     - 错误提示：所有相关的错误信息都已更新

### 2025-08-01 修复线路参数导入错误信息显示问题 ✅
- **问题描述**: 线路参数导入功能在出现错误时，前端显示技术错误信息而不是用户友好的错误信息
- **问题根因**: 前端代码错误地使用了 `response.data` 访问响应数据，但axios响应拦截器已经返回了 `response.data`
- **错误表现**:
  - 后端正确返回: `{"totalRows": 2, "successCount": 0, "failureCount": 2, "errors": ["第5行：票价不能为空", "第6行：票价不能为空"]}`
  - 前端显示: "Cannot read properties of undefined (reading 'successCount')"
- **修复内容**:
  1. **LinePriceListView.jsx**: 修复第292-297行，将 `response.data` 改为 `response`
  2. **数据访问修正**: 直接使用 `response.successCount` 而不是 `response.data.successCount`
  3. **错误信息正确显示**: 现在能正确显示后端返回的具体错误信息
- **影响范围**: 线路参数导入功能的错误处理和结果显示
- **测试状态**: 前端构建成功，修复已生效
- **其他导入功能**: 检查确认车辆导入和银联密钥导入功能代码正确，无需修复

### 2025-07-31 新增RabbitMQ通用订阅方法并优化终端日志处理服务 ✅
- **问题1**: TerminalLogProcessingHostedService中的批量确认方法没有生效，因为确认时获取不到对应的channel
- **问题2**: 原有的`SubscribeConsumeDataAsync`方法硬编码使用默认配置队列，无法指定自定义队列，会与ApiService产生冲突
- **根本原因**:
  1. RabbitMQ要求在接收消息的同一个channel上确认消息，而原实现没有保存channel信息
  2. 缺少可以指定队列参数的通用订阅方法
- **解决方案**:
  1. **新增通用订阅方法**: 在IRabbitMQService和RabbitMQService中新增重载方法
     ```csharp
     Task SubscribeConsumeDataAsync(string exchange, string queue, string routingKey,
         Func<SlzrDatatransferModel.ConsumeData, IChannel, ulong, Task> handler, bool autoAck);
     ```
  2. **重构内部实现**: 将原有的内部方法改造为通用方法，支持自定义exchange、queue、routingKey参数
  3. **优化数据结构**: 将`(ConsumeData, ulong)`改为`(ConsumeData, ulong, IChannel)`
  4. **修改终端日志服务**: 使用新的通用方法指定专用队列，避免与ApiService冲突
- **技术细节**:
  - 添加`using RabbitMQ.Client;`引用
  - 新增方法重载：`SubscribeConsumeDataAsync(string exchange, string queue, string routingKey, ...)`
  - 重构内部方法：`SubscribeConsumeDataInternalAsync`支持参数化配置
  - 修改Channel类型为`Channel<(ConsumeData Data, ulong DeliveryTag, IChannel Channel)>`
  - 修改成功/失败标签列表为`List<(ulong DeliveryTag, IChannel Channel)>`
  - 使用`_rabbitMQService.Ack(channel, deliveryTag)`和`_rabbitMQService.NAck(channel, deliveryTag, requeue)`
- **优化效果**:
  - ✅ 解决队列冲突：WebAdmin和ApiService可以使用不同的队列
  - ✅ 批量消息确认正确工作，提高消息处理可靠性
  - ✅ 避免消息确认失败导致的重复处理问题
  - ✅ 提供了灵活的队列配置能力，支持多服务场景
  - ✅ 提高RabbitMQ消息处理的整体性能和稳定性

### 2025-07-31 修复文件类型页面商户下拉框显示undefined问题 ✅
- **问题**: 文件类型页面商户下拉框选择后显示"undefined - "，其他页面正常
- **根本原因**: MerchantAutocomplete组件期望value是商户对象，但文件类型页面传递的是字符串(merchantID)
- **错误实现**:
  ```jsx
  // 错误：传递字符串给期望对象的组件
  <MerchantAutocomplete value={filters.merchantID} />
  ```
- **正确实现**:
  ```jsx
  // 正确：传递商户对象
  <MerchantAutocomplete value={selectedMerchant} />
  ```
- **修复方案**:
  1. **添加商户对象状态**: 新增`selectedMerchant`和`dialogSelectedMerchant`状态
  2. **导入商户Context**: 使用`useMerchants`获取商户列表
  3. **添加同步逻辑**: 通过useEffect同步merchantID和商户对象
  4. **修改组件属性**: 将value从字符串改为商户对象
- **技术细节**:
  - 筛选条件：`value={selectedMerchant}` 替代 `value={filters.merchantID}`
  - 对话框：`value={dialogSelectedMerchant}` 替代 `value={currentFileType.merchantID}`
  - 双向同步：merchantID变化时自动找到对应商户对象，商户对象变化时更新merchantID
- **修复效果**:
  - ✅ 商户下拉框正确显示"ID - 名称"格式
  - ✅ 选择商户后不再显示"undefined"
  - ✅ 与其他页面保持一致的用户体验

### 2025-07-31 更换终端日志记录菜单图标 ✅
- **问题**: 终端日志记录菜单使用的FileTextIcon与文件类型管理菜单图标重复
- **解决方案**: 将图标从FileTextIcon更改为ActivityIcon
- **图标选择理由**:
  - ActivityIcon更符合"日志记录"的语义，表示活动/操作记录
  - 避免与文件类型管理(FileTextIcon)的图标冲突
  - 在终端管理菜单组中具有良好的视觉区分度
- **更新范围**:
  - MenuService.cs中的菜单配置
  - 维护脚本中的SQL语句
  - FeaturePermissionManagement.jsx中的图标映射

### 2025-07-31 修复终端日志页面宽度自适应问题 ✅
- **问题**: 终端日志记录页面宽度固定，没有像其他页面一样自适应屏幕宽度
- **原因**: 使用了`<Container maxWidth="xl">`而不是`<Container maxWidth={false}>`
- **解决方案**:
  1. 将Container的maxWidth属性从"xl"改为false，实现全屏宽度自适应
  2. 统一Box的padding样式为`sx={{ pt: 3, pb: 3 }}`，与其他页面保持一致
- **效果**: 终端日志页面现在可以充分利用屏幕宽度，与其他管理页面保持一致的布局风格

### 2025-07-31 配置ActionLoggingFilter为全局过滤器 ✅
- **问题**: 终端日志API访问时报错"No service for type 'ActionLoggingFilter' has been registered"
- **解决方案**:
  1. 在Program.cs中注册ActionLoggingFilter服务：`builder.Services.AddScoped<ActionLoggingFilter>();`
  2. 配置为全局过滤器：`options.Filters.Add<ActionLoggingFilter>();`
  3. 从TerminalLogController中移除单独的ServiceFilter声明，使用全局配置
- **效果**:
  - ActionLoggingFilter现在作为全局过滤器应用于所有控制器
  - 提供详细的调试日志记录，与AuditLogMiddleware形成互补
  - AuditLogMiddleware负责业务审计日志，ActionLoggingFilter负责技术调试日志

### 2025-07-31 优化前端权限缓存机制 ✅
- **优化目标**: 提高权限更新的响应速度，减少用户等待时间
- **问题分析**:
  1. 原有双层缓存（内存+localStorage）导致权限更新后需要等待5分钟才能生效
  2. localStorage缓存在页面刷新后仍然存在，阻止了及时的权限更新
- **优化方案**:
  1. **移除localStorage缓存**: 只保留内存缓存，简化缓存机制
  2. **页面刷新即更新**: 页面刷新后内存缓存清空，立即从后端获取最新权限
  3. **保持性能优化**: 内存缓存仍然避免同一会话中的重复请求
- **用户体验提升**:
  - 管理员修改权限配置后，用户只需刷新页面即可看到最新权限
  - 从"最多等待5分钟"优化为"刷新页面立即生效"

### 2025-07-31 修复票价折扣方案管理页面按钮显示问题 ✅
- **问题**: 版本历史和提交版本按钮不显示
- **根本原因分析**:
  1. 版本历史按钮被错误地添加了权限控制，但权限常量中缺少VIEW_VERSIONS定义
  2. 提交版本按钮检查了不存在的`scheme.status === 'draft'`条件（FareDiscountScheme模型中没有status字段）
- **解决方案**:
  1. **版本历史按钮**: 移除权限控制，只保留业务逻辑判断（currentVersion > 0）
  2. **提交版本按钮**: 移除错误的status检查，只要有SUBMIT_VERSION权限和编辑权限即可
  3. **业务逻辑**: 票价折扣方案本身没有状态概念，每次提交都创建新版本记录

### 2025-07-31 实现终端日志记录后台处理服务 ✅
- **功能需求**: 创建后台服务订阅RabbitMQ消费数据，自动解析并保存到终端日志表
- **重要更新**: 使用独立的RabbitMQ队列，避免与ApiService的队列冲突
- **核心功能**:
  1. **独立队列**: 创建专用的终端日志队列（SlzrCrossGate.TerminalLog.Queue）
  2. **数据订阅**: 订阅专用队列中的消费数据消息，实现实时数据处理
  3. **数据解析**: 将消费数据的Buffer字段解析为结构化的终端日志记录
  4. **批量处理**: 支持批量处理和保存，提高数据处理效率
  5. **批量消息确认**: 实现真正的批量RabbitMQ消息确认机制
  6. **错误处理**: 完善的异常处理和消息确认机制
- **技术实现**:
  1. **解析服务**:
     - 创建TerminalLogParserService.cs，负责Buffer数据解析
     - 支持BCD编码解析、日期时间解析、多字段数据提取
     - 完善的日志记录和错误处理机制
  2. **后台服务**:
     - 创建TerminalLogProcessingHostedService.cs，实现后台数据处理
     - 使用Channel进行异步数据队列处理
     - 支持批量插入和定时处理机制
     - 集成RabbitMQ消息确认和重试机制
  3. **配置管理**:
     - 在appsettings.json中添加TerminalLogProcessing配置节
     - 支持启用/禁用、批量大小、处理间隔等参数配置
     - 配置独立的Exchange、Queue和RoutingKey
     - 支持调试日志开关，便于开发调试
  4. **服务注册**:
     - 在Program.cs中注册TerminalLogParserService、TerminalLogPublishService和TerminalLogProcessingHostedService
     - 修复依赖注入问题：单例HostedService通过IServiceScopeFactory获取作用域服务
     - 自动启动后台服务，无需手动干预
  5. **数据发布服务**:
     - 创建TerminalLogPublishService.cs，专门用于向终端日志队列发布数据
     - 支持单条和批量数据发布
     - 完善的错误处理和日志记录
- **数据流程**:
  1. **数据接收**: 从RabbitMQ接收消费数据消息（手动ACK模式）
  2. **数据缓存**: 将消息和DeliveryTag存入内部队列
  3. **批量处理**: 定时或达到批量大小时批量处理数据
  4. **数据解析**: 解析Buffer字段为终端日志记录
  5. **批量保存**: 批量插入到TerminalLogs表
  6. **批量确认**: 成功处理的消息批量ACK，失败的消息批量NACK并重新入队
- **测试支持**:
  - 创建TerminalLogTestController.cs，提供测试接口
  - 支持生成测试数据、验证解析功能、清理测试数据
  - 新增发布测试数据到终端日志队列的功能
  - 便于开发阶段验证数据解析逻辑和队列处理的正确性
- **队列配置**:
  - **Exchange**: SlzrCrossGate.TerminalLog
  - **Queue**: SlzrCrossGate.TerminalLog.Queue
  - **RoutingKey**: TerminalLog.#
  - 与ApiService使用的队列完全独立，避免数据冲突
- **批量消息确认机制**:
  - 使用手动ACK模式（autoAck=false）
  - 消息接收后存储DeliveryTag，不立即确认
  - 批量处理成功后，批量确认所有成功的消息
  - 处理失败的消息批量拒绝并重新入队
  - 避免消息丢失，确保数据处理的可靠性

### 2025-07-31 实现终端日志记录查询功能 ✅
- **功能需求**: 根据需求文档实现终端日志记录的查询页面，展示商户号、商户名称、记录类型、设置方式、卡号、设备序列号、设备编号、线路号、票价、司机卡号、记录时间、上传时间等字段
- **核心功能**:
  1. **数据模型设计**: 创建TerminalLog实体模型，映射到TerminalLogs表，包含完整的字段定义和枚举映射
  2. **多条件筛选**: 支持按商户、记录类型、卡号、设备序列号、设备编号、线路号、记录时间区间进行筛选
  3. **权限控制**: 非系统管理员只能查看自己商户的数据，系统管理员可查看所有数据
  4. **数据关联**: 自动关联商户表获取商户名称，提供友好的显示信息
- **技术实现**:
  1. **后端实现**:
     - 创建TerminalLog.cs实体模型，包含LogType和SetMethod枚举
     - 创建TerminalLogDto.cs数据传输对象，支持查询参数和分页结果
     - 在TcpDbContext中添加TerminalLogs DbSet和性能优化索引
     - 实现TerminalLogController.cs，提供完整的查询API和权限控制
  2. **前端实现**:
     - 创建TerminalLogs.jsx页面组件，支持响应式布局和移动端适配
     - 集成MerchantAutocomplete组件，统一商户选择体验
     - 使用DateTimePicker支持精确的时间范围筛选
     - 实现记录类型和设置方式的友好显示和颜色标识
  3. **系统集成**:
     - 在routes.jsx中添加终端日志页面路由配置
     - 在MenuService.cs中添加终端日志菜单项到终端管理分组
     - 在api.js中添加终端日志相关的API调用方法
- **数据映射**:
  - **记录类型**: 10-司机卡、12-线路设置、16-设备设置、19-发车卡、20-到站卡
  - **设置方式**: 0x01-M1卡、0x02-CPU卡、0x0B-交通卡、0xA0-无线、0xA1-调度
  - **票价显示**: 自动转换分为元，格式化显示
- **用户体验**:
  - ✅ 直观的筛选条件界面，支持多种筛选组合
  - ✅ 记录类型使用彩色Chip标签，便于快速识别
  - ✅ 分页表格支持大数据量浏览
  - ✅ 响应式设计，支持桌面端和移动端访问
  - ✅ 统一的商户选择体验，与其他页面保持一致
- **性能优化**:
  - 数据库索引优化：为常用查询字段添加复合索引
  - 分页查询：支持大数据量的高效分页浏览
  - 权限过滤：在数据库层面进行权限过滤，提升查询效率
- **菜单集成**: 添加到终端管理菜单组，使用FileTextIcon图标，排序为第6位

### 2025-07-31 商户管理添加商户ID格式校验 ✅
- **功能需求**: 在商户创建对话框中对商户ID添加严格的格式校验
- **校验规则**:
  1. **长度要求**: 必须是8位数字
  2. **前缀要求**: 前4位必须是0000
  3. **格式示例**: 00000001, 00000002, 00009999等
- **技术实现**:
  1. **Yup表单验证**:
     - 使用正则表达式 `/^\d{8}$/` 验证8位数字
     - 使用正则表达式 `/^0000\d{4}$/` 验证前4位为0000
  2. **智能输入处理**:
     - 自动过滤非数字字符
     - 限制最大输入长度为8位
     - 智能补充0000前缀（当用户输入不足4位时）
     - 自动纠正格式（确保前4位始终为0000）
  3. **用户体验优化**:
     - 提供清晰的格式说明和示例
     - 实时输入验证和错误提示
     - 占位符显示正确格式示例
- **业务价值**:
  - 确保商户ID格式的一致性和规范性
  - 避免因格式错误导致的系统问题
  - 提升数据质量和系统稳定性

### 2025-07-31 未注册线路页面添加快速创建线路功能 ✅
- **功能需求**: 在未注册线路页面为每行数据添加"创建线路"操作按钮，支持基于未注册线路信息快速创建线路票价配置
- **核心功能**:
  1. **智能数据解析**: 自动解析未注册线路编号，前4位作为线路编号，第5、6位作为组号
  2. **自动填充表单**: 商户ID、线路编号、组号自动填充，支持用户编辑调整
  3. **完整线路配置**: 支持设置线路名称、票价、启用状态、备注等完整信息
  4. **数据验证**: 线路编号必须4位，组号必须2位或留空，票价单位为分
- **技术实现**:
  1. **新增CreateLineDialog组件**:
     - 响应式对话框设计，支持移动端和桌面端
     - 智能表单验证和错误提示
     - 集成现有的linePriceAPI.createLinePrice接口
  2. **优化UnregisteredLines页面**:
     - 添加操作列，每行显示"创建线路"按钮
     - 集成创建线路对话框，支持数据传递和状态管理
     - 创建成功后自动刷新列表数据
- **用户体验提升**:
  - ✅ 一键快速创建线路，减少手动输入工作量
  - ✅ 智能数据解析，自动填充关键字段
  - ✅ 支持字段编辑，保持灵活性
  - ✅ 完整的表单验证和错误提示
  - ✅ 创建成功后自动刷新数据，保持界面同步
- **业务价值**:
  - 大幅提升线路注册效率，从未注册状态快速转为已注册
  - 减少数据录入错误，通过自动解析确保数据准确性
  - 简化运营流程，运营人员可以批量处理未注册线路

### 2025-07-31 终端管理页面添加线路统计功能 ✅
- **功能需求**: 在终端管理页面的统计卡片中增加已注册线路和未注册线路的统计信息，并支持点击未注册线路卡片跳转到未注册线路页面
- **后端实现**:
  1. **扩展TerminalStatsDto**: 添加`RegisteredLineCount`和`UnregisteredLineCount`字段
  2. **优化统计API**: 修改`TerminalsController.GetTerminalStats`方法，增加线路统计逻辑
     - 总线路数：从Terminals表统计所有线路数量（按商户和线路去重）
     - 未注册线路：查询Terminals表中存在但LinePriceInfos表中不存在的线路
     - 已注册线路：总线路数 - 未注册线路数（确保数据一致性）
     - 支持商户筛选和权限控制（系统管理员vs商户管理员）
     - 考虑线路号和组号的组合匹配逻辑
- **前端实现**:
  1. **统计卡片布局优化**: 从3列改为5列布局，使用`md={2.4}`平均分配空间
  2. **新增统计卡片**:
     - 已注册线路卡片（蓝色主题色）
     - 未注册线路卡片（橙色警告色，支持点击跳转）
  3. **交互功能**:
     - 为未注册线路卡片添加hover效果和点击跳转功能
     - 点击跳转到`/app/unregistered-lines`页面
     - 更新stats初始状态，包含新的统计字段
- **用户体验提升**:
  - ✅ 用户可以直观看到线路注册情况的统计信息
  - ✅ 点击已注册线路卡片跳转到线路参数管理页面（`/app/fare-params`）
  - ✅ 点击未注册线路卡片跳转到未注册线路页面（`/app/unregistered-lines`）
  - ✅ 统计数据与终端筛选条件联动，保持数据一致性
  - ✅ 响应式布局，在不同屏幕尺寸下都有良好的显示效果
  - ✅ 统一的卡片hover效果，提供良好的交互反馈
- **技术特点**:
  - 统计查询性能优化，使用高效的SQL查询
  - 完整的权限控制，遵循现有的商户隔离规则
  - 前后端数据结构一致，易于维护和扩展

### 2025-07-30 系统性优化所有页面重复请求问题（重大性能提升）✅
- **问题发现**: 用户反馈`/app/terminal-events`页面进入时连续4次请求`/api/Terminals/events?page=1&pageSize=10`
- **根本原因**: TerminalEventsList.jsx有3个独立的useEffect监听不同状态变化，页面初始化时连锁触发
  1. `useEffect(() => loadEvents(), [page, rowsPerPage])` - 监听分页变化
  2. `useEffect(() => loadEvents(), [filters])` - 监听筛选条件变化
  3. `useEffect(() => loadEvents(), [usePreciseTime])` - 监听精确时间开关变化
- **优化方案**:
  1. **使用useCallback优化loadEvents**: 添加依赖项`[page, rowsPerPage, filters]`
  2. **添加请求去重机制**: 使用useRef检查参数变化，避免相同参数的重复请求
  3. **合并useEffect逻辑**: 统一数据加载逻辑，分离页码重置逻辑
  4. **区分初始加载**: 标记初始加载，避免去重机制影响首次请求
- **优化效果**:
  - ✅ 页面进入时从4次请求减少到1次请求
  - ✅ 保持原有的筛选和分页功能正常
  - ✅ 避免用户操作时的不必要重复请求

### 2025-07-30 修复页面刷新时的重复请求问题（重要性能优化）✅
- **问题确认**: 用户提供具体数据显示页面刷新时的重复请求情况：
  - `/api/Version` 请求8次 ⚠️ 最严重
  - `/api/Session/status` 请求4次 ⚠️ 严重
  - `/api/ConsumeData` 请求2次
  - `/api/Menus/user-menus` 请求2次
  - `/api/merchants?pageSize=100` 请求1次 ✅ 正常
- **根本原因分析**:
  1. **VersionManager重复初始化**: 每次路由变化都触发版本检查
  2. **SessionManager重复调用**: AuthContext初始化时多次调用会话状态检查
  3. **菜单重复加载**: DashboardSidebar在用户状态变化时重复请求菜单
  4. **TerminalRecords useEffect链式触发**: 状态初始化导致多次数据请求
- **针对性优化方案**:
  1. **VersionManager优化**:
     - 添加`initializationRef`防止重复初始化
     - 只在首次非登录页面访问时执行版本检查
     - 避免路由变化时的重复调用
  2. **SessionManager优化**:
     - 添加`isCheckingImmediately`标志防止并发检查
     - 避免AuthContext初始化时的重复会话状态检查
  3. **DashboardSidebar优化**:
     - 只在菜单数据为空且用户可用时加载菜单
     - 避免用户状态变化时的重复请求
  4. **TerminalRecords优化**:
     - 使用useRef检测参数变化，避免不必要的重复请求
     - 区分初始加载和后续加载
- **性能提升预期**:
  - ✅ Version API请求从8次减少到1次
  - ✅ Session status请求从4次减少到1次
  - ✅ 菜单请求从2次减少到1次
  - ✅ 终端记录请求从2次减少到1次
  - 🚀 页面刷新速度显著提升，用户体验大幅改善

### 2025-07-30 优化页面刷新时的重复请求问题 ✅
- **问题分析**: 用户反馈终端记录页面刷新时网络日志显示多个重复请求
- **主要问题源头**:
  1. **MerchantAutocomplete组件重复请求**: 每个页面的商户下拉框都独立请求商户数据
  2. **TerminalRecords页面useEffect链式触发**: 状态初始化时多次触发数据加载
  3. **缺乏全局数据缓存机制**: 相同数据在不同组件间重复请求
- **优化方案实施**:
  1. **创建全局MerchantContext** (`src/contexts/MerchantContext.jsx`):
     - 实现全局商户数据管理和缓存机制
     - 5分钟缓存时间，避免频繁请求
     - 提供刷新、清除缓存、搜索等功能
     - 支持强制刷新和缓存检查
  2. **优化MerchantAutocomplete组件**:
     - 使用全局MerchantContext替代独立API请求
     - 保持向后兼容，支持外部数据源
     - 大幅减少商户数据重复请求
  3. **优化TerminalRecords页面useEffect**:
     - 使用useRef避免参数未变化时的重复请求
     - 添加请求参数变化检测机制
     - 区分初始加载和后续加载
  4. **集成到App.jsx**: 在应用根级别提供MerchantProvider
- **性能提升**:
  - ✅ 商户数据全局缓存，避免重复请求
  - ✅ 智能请求去重，减少不必要的API调用
  - ✅ 页面刷新时请求次数显著减少
  - ✅ 提升用户体验和系统响应速度
- **影响范围**: 所有使用MerchantAutocomplete的页面都将受益于此优化

### 2025-07-30 修复前端Vite构建警告 ✅
- **问题描述**: 前端启动时出现Vite警告 "Duplicate 'to' attribute in JSX element"
- **问题位置**: `NavItem.jsx`文件中Button组件有重复的"to"属性
- **问题原因**: 在第75行和第133行都定义了"to"属性，导致JSX元素属性重复
- **修复内容**: 移除重复的"to"属性（第133行），保留第75行的条件性"to"属性
- **修复代码**:
  ```javascript
  // 修复前：有两个to属性
  <Button
    to={!isExternal ? href : undefined}  // 第75行
    sx={{ ... }}
    to={isExternal ? undefined : href}   // 第133行 - 重复！
  >

  // 修复后：只保留一个to属性
  <Button
    to={!isExternal ? href : undefined}  // 第75行
    sx={{ ... }}
  >
  ```
- **影响**: 消除了Vite构建警告，提高了代码质量

### 2025-07-30 修复银联终端密钥管理操作按钮问题 ✅
- **问题描述**: 用户反馈银联终端密钥管理中表格操作列的按钮配置有问题
  1. 导出按钮不应该在每行操作中出现，应该只在页面顶部
  2. 解绑按钮缺失，但后端有解绑功能和权限控制
- **修复内容**:
  1. **移除表格操作中的导出按钮**: 导出功能应该是批量操作，不是单行操作
  2. **添加解绑按钮**: 为已绑定设备的密钥提供解绑功能
  3. **权限控制修复**:
     - 移除`PERMISSIONS.UNIONPAY_KEY.EXPORT`权限检查（从操作按钮中）
     - 添加`PERMISSIONS.UNIONPAY_KEY.UNBIND`权限检查
  4. **业务逻辑优化**:
     - 解绑按钮只对已绑定设备的密钥显示（`keyData.isInUse === true`）
     - 删除按钮只对未绑定设备的密钥显示（`keyData.isInUse === false`）
- **操作按钮最终配置**:
  - ✅ 编辑按钮: 所有密钥都可编辑（有权限时）
  - ✅ 解绑按钮: 仅已绑定设备的密钥显示
  - ✅ 删除按钮: 仅未绑定设备的密钥显示
- **影响**: 操作按钮更符合业务逻辑，用户体验更好

### 2025-07-30 恢复线路参数版本管理正确的权限控制 ✅
- **问题纠正**: 之前错误地简化了权限检查，但用户明确指出线路参数版本管理需要5个按钮的权限控制
- **后端权限配置**:
  - `line_price_version.create_version` - 新建版本
  - `line_price_version.copy_create` - 复制创建
  - `line_price_version.copy_to_other_line` - 复制到其它线路
  - `line_price_version.edit` - 编辑
  - `line_price_version.submit` - 提交
- **修复内容**:
  1. **恢复权限检查**: 重新添加`FeatureGuard`、`useFeaturePermission`和`PERMISSIONS`导入
  2. **恢复权限逻辑**: 在`LinePriceVersionActionButtons`中恢复完整的权限检查逻辑
  3. **修复按钮权限**: 为编辑、提交、新建版本按钮添加正确的权限控制
  4. **权限常量确认**: 确认前端权限常量与后端配置一致
- **当前状态**:
  - ✅ 编辑按钮: `PERMISSIONS.LINE_PRICE_VERSION.EDIT`
  - ✅ 提交按钮: `PERMISSIONS.LINE_PRICE_VERSION.SUBMIT`
  - ✅ 新建版本按钮: `PERMISSIONS.LINE_PRICE_VERSION.CREATE_VERSION`
  - ⏳ 复制创建按钮: 功能存在但UI中未显示
  - ⏳ 复制到其它线路按钮: 功能存在但UI中未显示
- **影响**: 恢复了正确的权限控制，确保按钮权限与后端配置保持一致

### 2025-07-30 简化线路参数版本管理权限检查 ✅
- **用户反馈**: 不需要对所有按钮都进行权限检查，只对明确要求的功能进行权限管理
- **修复内容**:
  1. **移除过度权限检查**: 移除`LinePriceVersionsView.jsx`中的`FeatureGuard`组件
  2. **简化权限逻辑**: 改为基于用户角色和商户归属的简单判断
  3. **清理冗余代码**: 移除不再使用的权限相关导入和变量
  4. **保持核心功能**: 保留预览、编辑、提交功能，移除删除功能
- **权限简化原则**:
  - ✅ 只对明确要求权限管理的功能进行权限检查
  - ✅ 其他功能基于用户角色和业务逻辑进行控制
  - ✅ 避免过度的权限检查，保持功能的可用性
- **影响**: 版本管理功能更加简洁易用，减少了不必要的权限复杂性

### 2025-07-30 修复线路参数管理查看版本按钮权限key错误 ✅
- **问题描述**: 用户反馈线路参数管理中的查看版本按钮权限已开启但页面上没有显示
- **问题根因**: 前端代码中使用了错误的权限key
  - `LinePriceListView.jsx`中使用了`PERMISSIONS.LINE_PRICE_VERSION.VIEW`，但权限常量中不存在此key
  - 正确的权限key应该是`PERMISSIONS.LINE_PRICE.VIEW_VERSIONS`
  - `LinePriceVersionsView.jsx`中使用了多个不存在的权限key：`DELETE`、`PUBLISH`、`PREVIEW`
- **修复内容**:
  1. **修复查看版本权限**: 将`PERMISSIONS.LINE_PRICE_VERSION.VIEW`改为`PERMISSIONS.LINE_PRICE.VIEW_VERSIONS`
  2. **修复权限检查列表**: 更新`checkMultiple`中的权限列表，移除不存在的权限key
  3. **修复版本管理页面权限**:
     - 将`PUBLISH`改为`SUBMIT`（使用现有的提交权限）
     - 移除`PREVIEW`权限检查（所有用户都可以预览）
     - 移除`DELETE`功能（后端未定义相应权限）
- **权限对应关系**:
  - ✅ `line_price.view_versions` → 查看线路参数版本历史
  - ✅ `line_price_version.edit` → 编辑线路参数版本
  - ✅ `line_price_version.submit` → 提交线路参数版本
- **影响**: 查看版本按钮现在可以正常显示，权限控制与后端配置保持一致

### 2025-07-30 完善权限管理系统的操作日志和菜单配置 ✅
- **需求背景**: 用户反馈权限控制controller缺少操作日志模块名称，操作日志页面模块下拉框不全，功能权限管理菜单需要加到菜单初始化中
- **修复内容**:
  1. **添加权限管理模块映射**: 在`AuditLogMiddleware.GetModuleFromPath`中添加`"permissions" => "权限管理"`映射
  2. **完善操作日志模块选项**: 更新`OperationLogs.jsx`中的`moduleOptions`，添加所有缺失的模块选项
  3. **修复菜单排序问题**: 修复`MenuService.GetDefaultMenuData`中菜单管理的SortOrder从5改为6，避免与功能权限管理冲突
- **新增模块选项**: 仪表盘、车辆管理、字典管理、票价参数、票价折扣方案、银联密钥管理、终端记录、终端事件、终端代理、未注册线路、菜单管理、会话管理、身份认证、权限管理、其他
- **技术说明**: 功能权限管理菜单已存在于菜单初始化中，只需修复排序冲突问题
- **影响**: 权限管理操作现在会正确记录到操作日志，操作日志筛选功能更加完整，菜单排序正确

### 2025-07-30 修复票价折扣方案页面TextField类型警告 ✅
- **问题描述**: 票价折扣方案管理页面出现React警告："Invalid prop 'error' of type 'string' supplied to 'ForwardRef(TextField)', expected boolean"
- **问题根因**: TextField组件的error属性接收到了非布尔类型的值
  - 第596行和第609行的error属性表达式可能返回字符串或其他类型
  - `scheme.currentFilePara` 可能是null或非字符串类型，传递给验证函数时产生意外结果
- **修复内容**:
  1. **确保error属性为布尔值**: 使用 `Boolean()` 包装error表达式
  2. **确保验证函数输入为字符串**: 使用 `String()` 转换输入值
  3. **修复数据初始化**: 确保 `filePara` 在设置时始终为字符串类型
- **技术细节**:
  - 第596行: `error={Boolean(submitData.version && !validateVersion(String(submitData.version)))}`
  - 第609行: `error={Boolean(submitData.filePara && !validateFilePara(String(submitData.filePara)))}`
  - 第279行: `String(scheme.currentFilePara)` 确保类型安全
- **影响**: 消除了React类型警告，提升了组件的类型安全性和稳定性

### 2025-07-30 修复消息类型页面React警告 - 重复key问题 ✅
- **问题描述**: 消息类型页面出现React开发者工具警告："Encountered two children with the same key"
- **问题根因**: 代码中存在字段名不一致问题
  - 数据结构中的字段名是 `merchantID`（大写ID）
  - 但在多处使用了 `merchantId`（小写id）
  - 导致第468行的key中 `messageType.merchantId` 为undefined，造成重复key警告
  - 第65行的权限检查中 `messageType.merchantId` 也为undefined，可能导致权限判断错误
- **修复内容**:
  1. **修复key生成**: 将第468行的 `messageType.merchantId` 改为 `messageType.merchantID`
  2. **修复权限检查**: 将第65行的 `messageType.merchantId` 改为 `messageType.merchantID`
- **技术说明**: 消息类型使用消息编码和商户号为组合主键，key的正确性对React渲染性能和状态管理至关重要
- **影响**: 消除了React警告，修复了潜在的权限判断错误，提升了页面渲染性能

### 2025-07-30 为消息类型页面添加编辑按钮权限 ✅
- **需求背景**: 用户需要为消息类型页面的编辑按钮增加权限控制
- **实现内容**:
  1. **前端权限常量**: 在`PERMISSIONS.MESSAGE_TYPE`中添加`EDIT: 'message_type.edit'`权限
  2. **后端权限配置**: 在`FeaturePermissionService`中添加消息类型编辑权限配置
  3. **权限初始化**: 为系统管理员和商户管理员默认分配消息类型编辑权限
  4. **前端UI实现**: 恢复消息类型页面的编辑按钮并添加`FeatureGuard`权限控制
  5. **业务逻辑**: 编辑权限检查用户角色和商户归属，删除权限依赖编辑权限
- **权限控制逻辑**:
  - 系统管理员：可编辑所有消息类型
  - 商户管理员：只能编辑自己商户的消息类型
  - 删除权限需要同时具备编辑权限且消息类型非系统内置

### 2025-07-30 优化权限系统性能 - 解决重复请求问题 ✅
- **问题发现**: 页面刷新时发现user-permissions接口被重复请求20次，严重影响性能
- **问题分析**: 多组件重复请求、缓存机制失效、Hook设计缺陷导致重复加载
- **解决方案**: 创建GlobalPermissionManager全局权限管理器，实现防重复请求机制和订阅者模式
- **技术实现**: 重构useFeaturePermission Hook，创建PermissionDebug调试组件，添加性能监控
- **性能提升**: 请求次数从20次减少到1次，性能提升95%，大幅改善用户体验
- **架构改进**: 从分散式权限管理升级为集中式权限管理，提升系统整体性能
- **代码清理**: 移除调试日志和临时代码，调试组件仅在开发环境可用，确保生产环境代码干净
- **权限状态简化**: 将三状态（允许/明确禁止/默认禁止）简化为二状态（允许/禁止），大幅提升用户体验
- **修复权限保存问题**: 修复单个角色权限修改时影响其他角色权限的问题，确保数据一致性
- **权限管理界面优化**: 按数据库SortOrder排序权限分组，为功能权限管理添加安全保护确认对话框
- **修复分组排序**: 确保权限分组本身也按照SortOrder排序，而不仅仅是分组内项目排序
- **统一图标系统**: 权限管理页面图标与菜单系统保持一致，使用统一的React Feather图标
- **响应式布局优化**: 权限管理页面从单列改为响应式多列网格布局，大幅提升空间利用率
- **页面宽度适配**: 修复权限管理页面固定宽度问题，改为适应屏幕宽度，与其他页面保持一致
- **字体大小标准化**: 修复权限管理页面字体偏小问题，恢复Material-UI标准字体大小，提升可读性
- **建立UI样式规范**: 创建完整的UI样式规范体系，包含详细规范、快速参考、检查工具等
- **文档管理规范**: 所有文档统一放在docs目录下分类管理，重要文档添加到备忘录快速导航中

### 2025-07-29 修复功能权限逻辑缺陷 - 启用权限但不勾选角色时应拒绝访问 ✅
- **问题描述**: 当功能启用权限控制但所有角色都不勾选时，系统错误地允许了访问，而不是拒绝所有角色访问
- **问题根因**: 权限检查逻辑中存在逻辑错误，当没有找到任何角色权限记录时，使用了 `!hasExplicitDeny` 逻辑，默认允许访问
- **修复内容**: 修改权限逻辑，当功能启用权限控制但没有配置任何角色权限时，明确拒绝访问
- **安全影响**: 修复了重要的权限控制漏洞，确保权限系统按预期工作，提升了系统安全性
- **测试文档**: 创建了 `test_permission_fix.md` 详细说明修复内容和测试方法

### 2025-07-29 修复终端管理页面Hook错误和权限UI显示问题 ✅
- **问题1**: 终端管理页面出现React Hook错误 "Rendered more hooks than during the previous render"
  - **根因**: Hook调用在条件返回之前，违反了React Hook规则
  - **修复**: 将所有Hook调用移到条件返回之前，确保Hook调用顺序一致
- **问题2**: 权限管理页面"需要权限"开关显示不正确
  - **根因**: 前端判断权限开关状态的逻辑错误，基于rolePermissions是否有内容而不是isGloballyEnabled状态
  - **修复**: 修改前端逻辑，让"需要权限"开关正确反映isGloballyEnabled状态
  - **改进**: 更新角色权限配置的提示文本，明确说明留空表示拒绝该角色访问

### 2025-07-29 修复权限设置UI逻辑混乱问题 ✅
- **问题描述**: 选择"启用功能"但不启用"需要权限"时，保存后两个开关都变成不启用状态
- **问题根因**: 前端UI设计混淆了功能启用和权限控制两个概念，保存逻辑错误地将requiresPermission映射到isGloballyEnabled
- **修复内容**:
  - 重新设计前端逻辑：启用功能对应isGloballyEnabled，需要权限通过rolePermissions判断
  - 修复保存逻辑：分别处理功能启用和权限控制的状态
  - 添加UI状态联动：功能未启用时禁用"需要权限"开关
- **UI改进**: "需要权限"开关在功能未启用时自动禁用，避免用户混淆

### 2025-07-29 修复终端管理页面权限访问问题 ✅
- **问题描述**: 终端管理页面提示"访问受限"，即使终端相关功能已启用权限
- **问题根因**: 后端权限初始化时遗漏了终端管理相关权限，SystemAdmin和MerchantAdmin角色缺少终端权限配置
- **修复内容**:
  - 为SystemAdmin角色添加终端管理权限：terminal.view_details, terminal.view_events, terminal.send_message, terminal.publish_file
  - 为MerchantAdmin角色添加相同的终端管理权限
  - 权限初始化成功，终端管理页面现在可以正常访问
- **技术细节**: 修改了FeaturePermissionService中的权限初始化逻辑，确保管理员角色拥有终端管理权限

### 2025-07-29 修复权限初始化外键约束错误 ✅
- **问题描述**: 后端启动时出现外键约束错误，权限初始化失败
- **错误信息**: `Cannot add or update a child row: a foreign key constraint fails (FK_RoleFeaturePermissions_FeatureConfigs_FeatureKey)`
- **问题根因**: 角色权限初始化中引用了不存在的FeatureKey，缺少merchant.toggle_active、user.lock、user.reset_password等功能配置
- **修复内容**:
  - 在GetDefaultFeatureConfigs()中添加缺失的功能配置：merchant.toggle_active、user.lock、user.reset_password
  - 实现EnsureRequiredFeatureConfigsExistAsync()方法，确保在角色权限初始化前所有需要的功能配置都存在
  - 修改InitializeDefaultConfigsAsync()逻辑，支持增量添加缺失的功能配置
- **修复结果**: 权限系统初始化完全成功，30条角色权限记录正确插入数据库，无外键约束错误

### 2025-07-29 修复功能权限页面数据显示问题 ✅
- **问题描述**: 功能权限管理页面看不到要设置的权限信息，页面显示空白
- **问题分析**:
  1. 后端API `/api/permissions/feature-configs` 正常返回权限配置数据
  2. 前端代码中字段名大小写不匹配：使用了 `config.Category` 但API返回的是 `config.category`
  3. 导致按分组过滤功能配置时无法正确匹配，所有分组都显示为空
- **修复内容**:
  1. 修复 `FeaturePermissionManagement.jsx` 中的字段名大小写问题
  2. 将 `config.Category` 改为 `config.category` (第428行和353行)
  3. 重新构建前端应用
- **修复结果**: 功能权限页面现在可以正常显示权限配置信息，包含商户管理、用户管理、终端管理、系统功能等分组
- **技术细节**: 前后端字段命名约定需要保持一致，API返回的JSON字段使用camelCase格式

### 2025-07-28 完成功能按钮控制系统完整实现 + 数据库迁移 ✅
- **需求背景**: 用户询问主项目中控制页面功能按钮启用/禁用的合适方案，需要分析现有权限控制机制并提供改进建议
- **现状分析**:
  1. **现有权限控制机制**:
     - 路由级别：使用 `RoleGuard` 组件控制页面访问
     - 页面级别：组件内检查用户角色显示不同内容
     - 菜单级别：MenuItem模型中的角色可见性字段
     - API级别：控制器方法的 `[Authorize]` 特性
     - 缺少：功能按钮级别的权限控制
  2. **原设计方案问题**:
     - 数据存储使用Dictionary序列化，查询困难、并发不安全
     - 权限标识符硬编码，重构困难
     - 主要依赖前端控制，安全性不足
     - 缓存策略简单，性能有优化空间
- **优化设计方案**:
  1. **数据模型改进**:
     - 创建独立的 `FeatureConfig` 和 `RoleFeaturePermission` 表
     - 支持功能分组、风险等级、排序等属性
     - 规范化存储，支持复杂查询和并发操作
  2. **权限常量管理**:
     - 统一的 `PERMISSIONS` 常量定义，避免硬编码
     - 支持嵌套分组，便于管理和重构
     - 提供工具函数获取所有权限标识符
  3. **前后端双重验证**:
     - 前端：`useFeaturePermission` Hook + `FeatureGuard` 组件
     - 后端：`RequireFeaturePermissionAttribute` 特性 + `IFeaturePermissionService` 服务
     - 多层缓存策略：内存缓存 + localStorage缓存
  4. **权限管理界面**:
     - 功能分组展示，支持全局开关和角色权限覆盖
     - 风险等级标识，高风险操作需要确认
     - 批量配置和实时生效
  5. **安全和审计**:
     - 完整的权限变更和使用日志
     - 权限检查失败时的降级策略
     - 支持权限变更实时通知
- **技术特点**:
  - **架构清晰**: 数据层、服务层、应用层职责分明
  - **性能优化**: 多层缓存、批量检查、智能预加载
  - **安全可靠**: 前后端双重验证、错误降级、审计日志
  - **易于维护**: 统一常量管理、规范化存储、完善文档
- **实施计划**: 分4个阶段实施，包含完整的测试计划和部署策略
- **文档位置**: [功能按钮控制系统设计文档](./docs/design/FEATURE_BUTTON_CONTROL_DESIGN.md)
- **实施进度**:
  - ✅ **阶段一完成**: 基础框架搭建
    - 权限常量定义 (`constants/permissions.js`)
    - 权限Hook实现 (`hooks/useFeaturePermission.js`)
    - 权限组件实现 (`components/FeatureGuard.jsx`)
    - 后端数据模型 (`Models/FeatureConfig.cs`, `Models/RoleFeaturePermission.cs`)
    - 权限服务实现 (`Services/FeaturePermissionService.cs`)
    - 权限验证特性 (`Attributes/RequireFeaturePermissionAttribute.cs`)
    - 权限管理API (`Controllers/PermissionsController.cs`)
    - 数据库迁移脚本 (`docs/database/feature-permissions-migration.sql`)
    - 权限初始化服务 (`Services/PermissionInitializationService.cs`)
    - 权限管理页面 (`pages/settings/FeaturePermissionManagement.jsx`)
    - 权限管理菜单项和路由配置
  - ✅ **阶段二完成**: 核心页面改造
    - 终端管理页面权限控制改造
    - 文件类型管理页面权限控制改造
    - 票价折扣方案页面权限控制改造
    - 文件版本管理页面权限控制改造
    - 发布记录页面权限控制改造
    - 消息类型页面权限控制改造
    - 线路参数管理页面权限控制改造
    - 线路参数版本管理页面权限控制改造
    - 银联密钥管理页面权限控制改造
  - ✅ **系统完整性**: 所有9个核心页面权限控制改造完成
    - 统一的权限控制模式和业务逻辑集成
    - 用户体验优化和高安全级别功能保护
    - 企业级功能按钮权限控制系统完全就绪
  - ✅ **数据库迁移完成**: EF Core迁移成功应用
    - 创建迁移: `20250728074407_AddFeaturePermissionSystem`
    - 成功创建 `FeatureConfigs` 和 `RoleFeaturePermissions` 表
    - 自动初始化14个功能配置记录
    - 权限初始化服务成功运行并完成数据初始化
  - ✅ **前端API导入修复**: 解决模块导入错误
    - 修复 useFeaturePermission.js 中的API导入问题
    - 修复权限管理页面中的API导入问题
    - 添加权限相关的API方法到服务文件
    - 前后端成功启动并运行，权限系统完全就绪

### 2025-07-25 实现珠海通FTP文件上传统计功能 ✅
- **需求背景**: 根据需求记录.md，将珠海通项目的示例页面替换为实际的FTP文件上传统计功能
- **功能实现**:
  1. **数据模型**:
     - 添加 `FtpUploadServer` 模型映射 `zhtong_uploadftpserver` 表
     - 添加 `FtpUploadStatsDto` 统计结果DTO
     - 使用 `[Column]` 特性正确映射数据库字段名
  2. **后端服务**:
     - 创建 `FtpFileStatsService` 提供文件统计功能
     - 实现本地文件数量统计（读取指定目录的.312/.315文件）
     - 实现远程文件数量统计（模拟实现，可扩展为真实FTP连接）
     - 创建 `FtpStatsController` 提供API接口
  3. **前端页面**:
     - 重构 `HomePage.jsx` 为FTP文件上传统计页面
     - 添加日期选择器支持查询指定日期的统计
     - 实现统计结果表格展示，包含文件数量、路径信息等
     - 添加 `@mui/x-date-pickers` 依赖支持日期选择
  4. **数据库集成**:
     - 在 `CustomerDataContext` 中添加 `FtpUploadServers` DbSet
     - 配置实体映射到现有的 `zhtong_uploadftpserver` 表
- **功能特点**:
  - **实时统计**: 支持查询指定日期的文件上传统计
  - **多服务器支持**: 支持多个FTP服务器配置的统计
  - **文件类型区分**: 分别统计312和315文件的数量
  - **路径展示**: 显示源目录和备份目录路径信息
  - **状态指示**: 使用颜色区分待上传和已上传文件数量
- **技术实现**:
  - 使用EF Core映射现有数据库表，不影响现有数据
  - 模拟远程文件统计，为后续真实FTP连接预留接口
  - 响应式UI设计，支持移动端访问
- **部署状态**: ✅ 已构建并部署到Docker容器，服务正常运行

### 2025-07-23 完善客户项目模板和微前端架构优化 ✅
- **需求背景**: 将今天对珠海通项目的微前端架构优化同步到客户项目模板中，确保后续客户项目都能受益
- **模板更新内容**:
  1. **前端配置优化**:
     - 更新 `vite.config.js` 支持环境变量控制基础路径
     - 添加 `package.json`、`index.html`、`main.jsx` 等完整前端配置
     - 优化 `AuthService.js` 添加 `setupAxiosDefaults` 方法自动配置axios基础路径
  2. **后端配置优化**:
     - 更新 `Program.cs` 添加 `UsePathBase` 支持，通过 `ASPNETCORE_BASEPATH` 环境变量配置
     - 更新 `.csproj` 文件移除硬编码的 `SpaProxyServerUrl`
     - 添加完整的 `appsettings.json` 配置模板
  3. **Docker配置**:
     - 更新 `Dockerfile` 支持 `VITE_BASE_PATH` 环境变量
     - 添加 `entrypoint.sh` 启动脚本
  4. **文档完善**:
     - 创建详细的 `README.md` 使用指南
     - 包含快速开始、配置修改、部署说明等完整文档
- **架构改进**:
  - **动态路径配置**: 前后端都支持通过环境变量配置基础路径
  - **认证集成优化**: AuthService自动配置axios基础路径，简化API调用
  - **构建流程标准化**: 统一的构建配置和部署流程
  - **文档标准化**: 提供完整的项目创建和配置指南
- **使用方式**:
  - 复制模板项目: `cp -r SlzrCrossGate.CustomerTemplate SlzrCrossGate.Customer[客户名称]`
  - 按照README.md指南修改配置
  - 支持快速创建新的客户定制项目

### 2025-07-23 集成珠海通客户项目到生产环境Docker Compose ✅
- **需求背景**: 将珠海通客户项目和Nginx代理集成到现有的 `docker-compose-registry.yml` 生产环境配置中
- **集成内容**:
  1. **docker-compose-registry.yml 扩展**:
     - 添加 `zhuhaitong` 服务配置，使用 `tcpserver-zhuhaitong:latest` 镜像
     - 添加 `nginx` 服务配置，提供统一的反向代理入口
     - 配置服务间依赖关系和网络通信
     - 添加 `zhuhaitong_logs` 持久化卷
  2. **Nginx 配置优化**:
     - 更新 `nginx/nginx.conf` 添加上游服务器定义和API服务HTTP代理
     - 更新 `nginx/conf.d/default.conf` 添加珠海通项目路由规则
     - 支持 `/customa/` 前缀路由到珠海通项目
     - 支持 `/api/customa/` 前缀路由到珠海通项目API
     - 添加静态文件缓存和健康检查端点
  3. **端口映射和代理**:
     - Nginx HTTP: 80端口 (主入口)
     - Nginx HTTPS: 443端口 (预留)
     - API服务HTTP代理: 6000端口
     - TCP服务代理: 8822端口
  4. **启动脚本**:
     - 创建 `start-registry-with-zhuhaitong.sh` 一键启动脚本
     - 包含环境检查、镜像构建、服务启动和健康检查
     - 提供详细的访问地址和管理命令说明
- **架构优势**:
  - **统一入口**: 通过Nginx提供所有服务的统一访问入口
  - **路由隔离**: 不同项目使用不同路径前缀，避免冲突
  - **服务发现**: 容器间通过服务名进行通信，无需硬编码IP
  - **负载均衡**: Nginx支持后续的负载均衡和高可用配置
- **访问地址**:
  - **主项目**: http://localhost/
  - **珠海通项目**: http://localhost/customa/
  - **API服务**: http://localhost:6000/
  - **TCP服务**: localhost:8822
- **使用方法**: 运行 `./start-registry-with-zhuhaitong.sh` 启动完整环境
- **影响**: 实现了生产级的微前端部署架构，支持多客户项目的统一管理和访问

### 2025-07-23 为珠海通客户项目添加Docker部署支持 ✅
- **需求背景**: 用户需要为 SlzrCrossGate.CustomerZhuhaitong 项目添加 Docker 支持，并创建完整的外部链接部署测试方案
- **实现内容**:
  1. **Docker化支持**:
     - 创建 `SlzrCrossGate.CustomerZhuhaitong/Dockerfile` - 基于WebAdmin项目优化的多阶段构建
     - 创建 `SlzrCrossGate.CustomerZhuhaitong/scripts/entrypoint.sh` - 容器启动脚本，处理日志目录初始化
     - 支持前端React应用和后端.NET应用的完整构建流程
  2. **构建脚本**:
     - 创建 `build-zhuhaitong.sh` - 参照 `build-web.sh` 的珠海通项目专用构建脚本
     - 支持镜像构建、标签管理、私有仓库推送和重试机制
     - 镜像命名: `devtest.pointlife365.net:5180/slzr/tcpserver-zhuhaitong:latest`
  3. **完整部署测试方案**:
     - 创建 `docker-compose-zhuhaitong-test.yml` - 包含MySQL、MinIO、WebAdmin、珠海通项目和Nginx的完整测试环境
     - 创建 `nginx/conf.d/zhuhaitong.conf` - 支持外部链接路由的Nginx配置，使用 `/customa/` 前缀
     - 创建 `test-zhuhaitong-deployment.sh` - 自动化部署测试脚本，包含健康检查和服务验证
- **技术特点**:
  - **微前端架构**: 通过Nginx反向代理实现主项目和客户项目的统一访问
  - **路由规则**: 主项目使用根路径 `/`，珠海通项目使用 `/customa/` 前缀
  - **服务隔离**: 每个项目独立容器，通过Docker网络通信
  - **配置管理**: 支持环境变量配置数据库连接、JWT密钥等
- **部署架构**:
  - **主项目**: http://localhost:7296 (直接) / http://localhost/ (通过Nginx)
  - **珠海通项目**: http://localhost:5001 (直接) / http://localhost/customa/ (通过Nginx)
  - **管理服务**: MinIO控制台 http://localhost:9001
- **使用方法**: 运行 `./test-zhuhaitong-deployment.sh` 即可完成完整的部署测试
- **影响**: 为客户项目提供了完整的Docker化部署方案，支持外部链接的微前端架构测试

### 2025-07-17 React DOM嵌套警告修复 ✅
- **问题**: 珠海通项目中出现 `<div> cannot appear as a descendant of <p>` 警告
- **原因**: `ListItemText` 的 `secondary` 属性中使用了 `<Box>` 组件，导致DOM嵌套冲突
- **解决方案**: 将复杂JSX从secondary属性中移出，作为独立元素渲染
- **修复文件**:
  - `SlzrCrossGate.CustomerZhuhaitong/ClientApp/src/pages/HomePage.jsx`
  - `SlzrCrossGate.CustomerTemplate/ClientApp/src/pages/HomePage.jsx`
- **同步状态**: 已同步修复到客户模板项目
- **经验**: Material-UI组件属性要注意DOM嵌套规则，客户项目修复必须同步到模板

### 2025-07-17 文档整理和规范制定 ⚠️
- **问题**: AI助手创建了大量文档堆积在根目录，造成混乱，且包含很多临时修复文档
- **整理结果**:
  - 删除15个临时修复文档
  - 重新组织文档到docs/下的分类目录（guides/, architecture/, deployment/, database/）
  - 项目特定文档移动到对应项目目录
  - 更新文档索引和导航
- **制定规范**:
  - 临时文档必须放在单独的temp/目录中，用后及时清理
  - 正式文档必须分类放置，不得堆积在根目录
  - 每次创建文档后必须及时整理和分类
- **经验教训**: 不要创建一大堆文档又不整理，要养成良好的文档管理习惯

### 2025-07-17 客户模板项目同步完成 ✅
- **背景**: 在WebAdmin主项目中完成外部系统路由重构后，需要同步到客户模板项目
- **分析结果**: CustomerTemplate项目本身不需要路由重构（它是被嵌入的iframe应用）
- **同步内容**:
  - 更新 `scripts/create-customer-project.sh` 添加详细的菜单配置说明和SQL示例
  - 更新 `EXTERNAL_LINK_MENU_GUIDE.md` 添加新路由系统说明和ItemKey重要性
  - 创建 `CUSTOMER_PROJECT_INTEGRATION_GUIDE.md` 完整的客户项目集成指南
  - 创建 `CUSTOMER_TEMPLATE_SYNC_GUIDE.md` 记录同步工作详情
- **重点改进**: 强调ItemKey字段的重要性和命名规范，确保新客户项目能正确享受路由改进
- **效果**: 新创建的客户项目菜单将正确高亮，URL专业化

### 2025-07-17 外部系统路由重构完成 ✅
- **问题**: iframe外部系统菜单选中状态错误，URL不专业（`?iframe=珠海通查询`）
- **解决方案**: 实施专用路由架构 `/app/external/{itemKey}`
- **关键改进**:
  - 创建 `ExternalSystemPage` 组件处理外部系统显示
  - 修改 `NavItem` 支持itemKey参数和新路由逻辑
  - 更新 `DashboardSidebar` 传递itemKey到NavItem
  - 清理 `DashboardLayout` 移除旧的iframe逻辑
- **效果**: 菜单正确高亮，URL专业化（`/app/external/zhuhaitong-query`），架构清晰

### 2025-07-14 优化票价折扣方案版本管理功能交互设计
- **需求背景**：用户反馈初版设计交互复杂，需要在多个页面重复编辑相同信息，希望简化操作流程
- **优化方案**：
  1. **简化工作流程**：`编辑方案 → 保存方案 → 提交生成版本 → 查看版本历史`
  2. **交互优化**：
     - 保持现有方案编辑功能不变，用户在方案页面完成所有配置
     - 添加简单的"提交版本"功能，只需输入版本号和文件参数
     - 独立的版本历史页面，支持预览和下载历史文件
  3. **API设计简化**：
     - 移除复杂的版本创建和编辑API
     - 核心API：`POST /api/FareDiscountScheme/{id}/submit` - 直接提交当前方案生成版本
     - 保留：`GET /api/FareDiscountScheme/{id}/versions` - 版本历史查询
     - 保留：`POST /api/FareDiscountScheme/versions/{versionId}/preview` - 历史版本预览
- **版本管理逻辑**：
  1. **文件参数管理**：
     - 创建方案时，`CurrentFilePara` 默认设置为方案编号（`SchemeCode`）
     - 提交时可修改文件参数，修改后更新方案的 `CurrentFilePara`
     - 下次提交默认使用上次设置的参数
  2. **版本号递增**：
     - 默认基于方案当前版本递增，首次提交从0001开始
     - 支持手动输入版本号（允许跳号）
     - 版本冲突处理：参考文件版本管理逻辑，已发布版本不能重复创建
  3. **权限控制**：
     - 版本历史查看权限：非系统管理员只能查看自己商户的数据
     - 无需特殊权限，基于商户隔离即可
- **技术实现**：
  - 数据库迁移：`OptimizeFareDiscountSchemeVersionManagement`
  - 简化DTO设计，移除不必要的复杂数据传输对象
  - 优化文件生成逻辑，支持直接从方案生成文件内容
- **影响**：大幅简化了用户操作流程，减少了界面复杂度，提升了用户体验

### 2025-07-14 完成前端界面实现和系统集成测试
- **前端界面实现**：
  1. **票价折扣方案编辑页面优化**：
     - 添加"提交版本"和"版本历史"按钮
     - 实现提交版本对话框，支持版本号和文件参数编辑
     - 集成版本提交API调用和错误处理
  2. **版本历史页面**：
     - 创建 `FareDiscountSchemeVersionsView.jsx` 组件
     - 实现版本列表展示、分页、状态显示
     - 支持文件预览和下载功能
     - 完整的权限控制和错误处理
  3. **API服务扩展**：
     - 在 `api.js` 中添加版本管理相关API方法
     - 支持提交方案、获取版本历史、预览文件等功能
  4. **路由配置**：
     - 添加版本历史页面路由：`/app/fare-discount-schemes/:id/versions`
     - 配置适当的角色权限控制
- **系统集成测试**：
  1. **数据库迁移**：成功应用两个迁移文件，数据库结构更新完成
  2. **前端构建**：Vite构建成功，生成优化的生产版本
  3. **应用启动**：WebAdmin应用成功启动，运行在 http://localhost:5270
  4. **API测试**：基础API响应正常，权限控制工作正常
- **技术成果**：
  - 完整的票价折扣方案版本管理功能已实现
  - 前后端完全集成，支持完整的用户工作流程
  - 数据库结构已更新，支持版本历史存储
  - 系统可以正常启动和运行

### 2025-07-14 优化版本管理按钮位置，提升用户体验
- **用户反馈**：版本历史和提交版本功能按钮应该放在票价折扣方案管理列表页面的操作按钮区域，而不是编辑页面
- **界面优化**：
  1. **方案列表页面**：
     - 在操作列添加"提交版本"按钮（蓝色发布图标）
     - 在操作列添加"版本历史"按钮（历史图标）
     - 调整按钮顺序：编辑 → 提交版本 → 版本历史 → 复制 → 删除
     - 添加完整的提交版本对话框，支持版本号和文件参数编辑
  2. **方案编辑页面**：
     - 移除"提交版本"和"版本历史"按钮
     - 简化界面，只保留"返回"和"保存更改"按钮
     - 移除相关的状态管理和处理函数
- **用户体验提升**：
  - 用户可以直接在列表页面对任意方案进行版本管理操作
  - 减少页面跳转，提高操作效率
  - 界面更加直观，符合用户操作习惯
  - 编辑页面更加专注于方案内容编辑
- **技术实现**：
  - 在列表页面添加版本管理相关的状态和处理函数
  - 集成API调用和错误处理
  - 前端构建成功，界面更新完成

### 2025-07-14 界面细节优化和用户体验改进
- **版本历史页面优化**：
  - 修改返回按钮：从"返回编辑"改为"返回列表"
  - 返回路径：从编辑页面改为方案管理列表页面
  - 提升用户导航体验，保持操作流程的一致性
- **操作按钮样式统一**：
  - 参考文件版本管理和文件类型管理页面的设计规范
  - 统一图标按钮的颜色配色方案：
    * 编辑：`color="primary"` (蓝色)
    * 提交版本：`color="success"` (绿色)
    * 版本历史：`color="info"` (信息蓝)
    * 复制：`color="secondary"` (紫色)
    * 删除：`color="error"` (红色)
  - 调整按钮间距：从 `gap: 1` 改为 `gap: 0.5`，添加 `flexWrap: 'wrap'`
  - 提升界面视觉一致性和美观度
- **技术成果**：
  - 前端构建成功，所有样式更新生效
  - 界面风格与系统其他页面保持一致
  - 用户体验进一步优化

### 2025-07-14 修复版本历史下载编码问题和图标样式优化
- **版本历史图标样式优化**：
  - 统一图标按钮样式：预览按钮使用 `color="info"`，下载按钮使用 `color="primary"`
  - 调整按钮间距：从 `gap: 1` 改为 `gap: 0.5`，添加 `flexWrap: 'wrap'`
  - 图标大小统一：使用 `fontSize="small"`
  - 与系统其他页面保持一致的视觉风格
- **版本历史下载编码问题修复**：
  - **问题**：版本历史页面下载文件时使用前端默认UTF-8编码，而不是后端生成时的GB2312编码
  - **解决方案**：
    1. 新增后端下载API：`GET /api/FareDiscountScheme/versions/{versionId}/download`
    2. 后端API使用GB2312编码生成文件字节流，直接返回文件下载
    3. 前端调用新的下载API，使用 `responseType: 'blob'` 接收二进制数据
    4. 确保下载的文件与提交时生成的文件编码完全一致
  - **技术实现**：
    - 后端使用 `Encoding.GetEncoding("gb2312").GetBytes()` 确保正确编码
    - 前端使用 `api.get()` 配置 `responseType: 'blob'` 接收二进制数据
    - 保持文件内容的完整性和编码一致性
- **系统状态**：
  - 后端编译成功（6个警告，无错误）
  - 前端构建成功（Vite构建完成）
  - 所有功能已集成并可正常使用

### 2025-07-14 修复版本历史下载功能的数据处理问题
- **问题诊断**：
  - 前端错误：`下载响应数据为空或无效`
  - 后端返回正确的JSON数据，但前端配置了 `responseType: 'blob'`
  - axios无法正确处理响应数据，导致 `response.data` 不是有效的blob对象
- **解决方案**：
  - 增强前端下载逻辑的容错性和兼容性
  - 添加响应数据类型检测：支持Blob、字符串和对象类型
  - 自动转换非Blob数据为Blob格式
  - 增加详细的调试日志，便于问题排查
- **技术实现**：
  - 检查 `response.data instanceof Blob` 判断数据类型
  - 对于非Blob数据，使用 `new Blob([content], { type: 'application/json;charset=gb2312' })` 转换
  - 保持GB2312编码设置，确保中文字符正确显示
  - 添加响应状态、数据类型、大小等详细日志
- **系统状态**：
  - 前端构建成功，修复已生效
  - 下载功能现在可以处理多种响应数据格式
  - 保持了文件编码的正确性

### 2025-07-14 票价折扣方案管理列表显示当前版本和文件参数
- **用户需求**：在票价折扣方案管理列表中显示当前版本和文件参数信息
- **前端界面优化**：
  1. **表头更新**：在商户列后添加"当前版本"和"文件参数"列
  2. **数据显示**：
     - 当前版本：显示 `scheme.currentVersion`，无版本时显示"-"
     - 文件参数：显示 `scheme.currentFilePara`，无参数时显示"-"
  3. **表格布局**：调整列顺序为方案名称 → 方案编码 → 商户 → 当前版本 → 文件参数 → 描述 → 状态 → 使用次数 → 最后使用时间 → 创建时间 → 操作
- **后端数据支持**：
  1. **DTO扩展**：在 `FareDiscountSchemeDto` 中添加 `CurrentVersion`、`CurrentFilePara`、`CurrentFileVerID` 字段
  2. **API更新**：所有返回方案列表和详情的API都包含版本信息
  3. **数据映射**：确保所有控制器方法都正确映射版本相关字段
- **用户体验提升**：
  - 用户可以直接在列表中查看每个方案的当前版本状态
  - 便于识别哪些方案已经有版本，哪些还没有提交过版本
  - 文件参数信息有助于了解生成文件时使用的参数标识
- **技术实现**：
  - 后端编译成功（6个警告，无错误）
  - 前端构建成功（Vite构建完成，51.89秒）
  - 所有功能已集成并可正常使用

### 2025-07-14 修复提交版本时空字符串文件参数不生效的问题
- **问题描述**：用户在提交版本时将文件参数更改为空字符串，但实际提交时仍使用原先的文件参数
- **问题根因分析**：
  1. **前端问题**：在 `handleConfirmSubmit` 中使用了 `submitData.filePara || undefined`
  2. **逻辑错误**：空字符串被 `||` 操作符转换为 `undefined`
  3. **后端处理**：收到 `undefined` 后使用 `?? scheme.CurrentFilePara` 逻辑，导致使用原来的文件参数
- **解决方案**：
  1. **前端修复**：将 `submitData.filePara || undefined` 改为 `submitData.filePara`
  2. **保持原值**：允许空字符串被正确发送到后端
  3. **后端逻辑**：保持 `model.FilePara ?? scheme.CurrentFilePara` 的null合并逻辑
- **修复效果**：
  - 用户现在可以将文件参数设置为空字符串并正确保存
  - 空字符串和null值的处理逻辑更加清晰
  - 只有当前端明确不传递filePara字段时，后端才使用默认值
- **技术验证**：
  - 后端编译成功（6个警告，无错误）
  - 前端构建成功（Vite构建完成，17.33秒）
  - 空字符串文件参数现在可以正确提交和保存

### 2025-07-14 修复提交版本对话框默认文件参数不使用上次保存值的问题
- **问题描述**：提交空字符串文件参数后，再次打开提交版本对话框时，默认值不是上次保存的空字符串，而是使用了原先的默认值（方案编号）
- **问题根因**：在 `handleSubmitVersion` 函数中使用了 `scheme.currentFilePara || scheme.schemeCode || ''`
- **逻辑错误**：`||` 操作符将空字符串视为falsy值，导致使用 `scheme.schemeCode` 作为默认值
- **修复方案**：
  ```javascript
  // 修复前
  filePara: scheme.currentFilePara || scheme.schemeCode || ''

  // 修复后
  filePara: scheme.currentFilePara !== null && scheme.currentFilePara !== undefined
      ? scheme.currentFilePara
      : (scheme.schemeCode || '')
  ```
- **修复逻辑**：
  - 只有当 `currentFilePara` 为 `null` 或 `undefined` 时才使用默认值
  - 空字符串被视为有效的用户设置值，会被保留和使用
  - 确保用户上次保存的文件参数（包括空字符串）被正确加载为默认值
- **用户体验提升**：
  - 用户设置的文件参数值会被记住，包括空字符串
  - 提交版本对话框的默认值与用户上次的设置保持一致
  - 避免用户需要重复清空文件参数的操作
- **技术验证**：
  - 前端构建成功（Vite构建完成，17.77秒）
  - 空字符串文件参数的完整工作流程现在正常

### 2025-07-14 优化用户管理页面错误信息显示
- **问题描述**：用户管理中的锁定操作，当锁定自己时后端返回400错误和具体信息"不能锁定当前登录用户"，但前端只显示"操作失败，请求400"，没有显示具体的message
- **问题分析**：
  - 前端错误处理只使用了 `error.message`，这通常是HTTP错误的通用信息
  - 后端返回的具体错误信息在 `error.response.data.message` 中
  - 用户看不到后端返回的具体错误原因，影响用户体验
- **优化方案**：
  1. **锁定/解锁操作**：优化 `handleToggleLock` 函数的错误处理
  2. **删除用户操作**：优化 `handleDeleteUser` 函数的错误处理
  3. **加载用户列表**：优化 `loadUsers` 函数的错误处理
- **错误处理逻辑**：
  ```javascript
  // 优化前
  enqueueSnackbar(`操作失败: ${error.message}`, { variant: 'error' });

  // 优化后
  const errorMessage = error.response?.data?.message || error.message || '操作失败';
  enqueueSnackbar(errorMessage, { variant: 'error' });
  ```
- **优化效果**：
  - 优先显示后端返回的具体错误信息（如"不能锁定当前登录用户"）
  - 如果后端没有返回具体信息，则显示HTTP错误信息
  - 如果都没有，则显示默认的友好提示
  - 添加console.error日志，便于开发调试
- **用户体验提升**：
  - 用户能看到明确的错误原因，而不是模糊的"操作失败"
  - 错误信息更加友好和具体，帮助用户理解操作失败的原因
  - 提升了系统的专业性和用户满意度
- **技术验证**：
  - 前端构建成功（Vite构建完成，16.61秒）
  - 错误处理逻辑已优化，现在能正确显示后端返回的具体错误信息

### 2025-07-14 暂时隐藏微信绑定功能
- **功能状态**：微信绑定功能尚未完全实现，暂时从前端界面中隐藏
- **隐藏范围**：
  1. **系统设置页面**：隐藏微信绑定相关的配置选项
  2. **用户信息页面**：隐藏微信绑定状态和操作按钮
  3. **相关组件**：`WechatBindingSection.jsx` 等微信绑定相关组件暂时不显示
- **实现状态**：
  - 前端组件已开发：`SlzrCrossGate.WebAdmin\ClientApp\src\pages\account\WechatBindingSection.jsx`
  - 后端接口可能部分实现，但功能不完整
  - 微信扫码登录等相关功能需要进一步开发
- **隐藏方式**：用户手动隐藏了相关界面元素
- **后续计划**：
  - 当微信绑定功能完全实现后，可以重新启用这些界面元素
  - 需要完善微信API集成、二维码生成、绑定流程等功能
  - 需要测试微信登录、绑定、解绑等完整流程
- **重启提醒**：
  - 重启功能时需要检查 `WechatBindingSection.jsx` 组件
  - 需要在系统设置和用户信息页面重新显示微信相关选项
  - 需要确保后端微信相关API完整可用

### 2025-07-15 票价折扣方案版本管理添加备注信息功能
- **功能需求**：在提交版本时增加备注信息输入，在版本历史中显示每次提交的备注信息
- **后端实现**：
  1. **数据模型扩展**：在 `FareDiscountSchemeVersion` 模型中添加 `Remarks` 字段（最大500字符）
  2. **DTO更新**：
     - `FareDiscountSchemeVersionDto` 添加 `Remarks` 属性
     - `SubmitFareDiscountSchemeVersionDto` 添加 `Remarks` 属性
  3. **API功能**：
     - 提交版本时保存备注信息到数据库
     - 查询版本历史时返回备注信息
     - 获取单个版本详情时包含备注信息
  4. **数据库迁移**：成功创建并应用迁移 `AddRemarksToFareDiscountSchemeVersion`
- **前端实现**：
  1. **提交版本对话框**：
     - 添加备注信息输入字段（多行文本框，3行高度）
     - 提供友好的提示文本："可选，记录本次版本提交的说明信息"
     - 支持最大500字符的备注输入
  2. **版本历史页面**：
     - 在表格中添加"备注信息"列
     - 备注信息显示最大宽度200px，超出部分省略号显示
     - 鼠标悬停显示完整备注内容
     - 无备注时显示"-"
  3. **状态管理**：在提交数据状态中添加 `remarks` 字段
- **用户体验优化**：
  - 备注信息为可选字段，用户可以选择是否填写
  - 多行文本框支持换行输入，便于记录详细信息
  - 版本历史中备注信息紧凑显示，不影响表格布局
  - 提供tooltip显示完整备注内容，便于查看
- **功能价值**：
  - 帮助用户记录每次版本提交的目的和变更说明
  - 便于团队协作时了解版本变更历史
  - 提升版本管理的可追溯性和可维护性
- **测试结果**：
  - 用户启动程序测试，功能正常工作
  - 备注信息可以正确提交、保存和显示
  - 前后端数据传输和存储完整无误

### 2025-07-15 提交版本对话框添加输入验证和格式限制
- **功能需求**：对提交版本时的输入进行格式验证和限制
  - 版本号：只能输入4位16进制字符串（如：0001、A1B2）
  - 文件参数：只能输入最多8个英文或数字字符
  - 两个字段都支持留空，保持原有的自动处理逻辑
- **前端验证实现**：
  1. **输入过滤**：
     - `handleVersionChange`: 实时过滤非16进制字符，限制4位长度
     - `handleFileParaChange`: 实时过滤非英文数字字符，限制8位长度
  2. **格式验证**：
     - `validateVersion`: 验证4位16进制格式 `/^[0-9A-Fa-f]{4}$/`
     - `validateFilePara`: 验证英文数字格式 `/^[A-Za-z0-9]{1,8}$/`
  3. **用户界面优化**：
     - 版本号输入自动转换为大写显示
     - 输入字段添加maxLength限制
     - 格式错误时显示红色边框提示
     - 更新帮助文本，提供清晰的格式说明
  4. **提交验证**：
     - 提交前验证输入格式，格式错误时显示具体错误信息
     - 阻止格式错误的数据提交到后端
- **用户体验优化**：
  - **实时反馈**：输入时立即过滤无效字符，用户无法输入错误格式
  - **视觉提示**：错误格式时显示红色边框，正确格式时正常显示
  - **友好提示**：具体的错误信息和格式要求说明
  - **保持兼容**：空字段仍然支持自动生成，不影响现有使用习惯
- **验证规则**：
  - **版本号**：4位16进制（0-9, A-F），如：0001、A1B2、FFFF
  - **文件参数**：1-8位英文或数字，如：A、ABC123、PARAM01
  - **备注信息**：无限制，支持任意文本内容
- **技术实现**：
  - 使用正则表达式进行格式验证
  - 实时输入过滤，防止无效字符输入
  - 提交前二次验证，确保数据格式正确
  - 前端构建成功（Vite构建完成，15.87秒）

### 2025-07-15 后端接口添加版本号和文件参数格式验证
- **功能需求**：在后端API层面对前端提交的版本号和文件参数进行严格的格式验证
- **后端验证实现**：
  1. **DTO数据注解验证**：
     - `Version`: `[RegularExpression(@"^[0-9A-Fa-f]{4}$", ErrorMessage = "版本号必须是4位16进制字符")]`
     - `FilePara`: `[RegularExpression(@"^[A-Za-z0-9]{1,8}$", ErrorMessage = "文件参数只能包含英文和数字，最多8个字符")]`
     - `Remarks`: `[MaxLength(500, ErrorMessage = "备注信息不能超过500个字符")]`
  2. **控制器手动验证**：
     - 检查 `ModelState.IsValid`，收集所有验证错误信息
     - 额外的正则表达式验证，确保格式严格符合要求
     - 返回具体的错误信息，帮助前端用户理解问题
  3. **验证规则**：
     - **版本号验证**：`^[0-9A-Fa-f]{4}$` - 严格4位16进制字符
     - **文件参数验证**：`^[A-Za-z0-9]{1,8}$` - 1-8位英文或数字
     - **备注信息验证**：最大长度500字符
  4. **错误处理**：
     - 验证失败时返回400 Bad Request
     - 提供清晰的错误信息，指导用户正确输入
     - 支持多个验证错误的合并显示
- **安全性提升**：
  - **双重验证**：前端验证+后端验证，确保数据安全
  - **格式严格**：防止恶意或错误格式的数据进入系统
  - **错误明确**：具体的错误信息，避免模糊的验证失败
- **兼容性保持**：
  - 空值仍然支持自动处理逻辑
  - 不影响现有的版本自动生成功能
  - 保持向后兼容性
- **技术验证**：
  - 后端编译成功（6个警告，无错误）
  - 数据注解验证和手动验证双重保障
  - 错误信息友好，便于前端显示和用户理解

### 2025-07-12 重新创建基于实际项目的准确文档
- **问题发现**：之前创建的文档包含大量不准确和夸大的内容，不符合实际项目情况
- **解决方案**：删除不准确文档，基于实际代码重新创建准确的技术文档
- **新建文档**：
  1. **[frontend-development.md](./docs/frontend-development.md)** - 基于实际package.json和代码的前端开发规范
  2. **[backend-development.md](./docs/backend-development.md)** - 基于实际.csproj和控制器的后端开发规范
- **文档特点**：
  - 所有技术栈信息都基于实际项目配置文件
  - 业务功能描述基于实际控制器和页面
  - 避免了之前的过度理想化和功能夸大
  - 确保文档内容的真实性和可验证性
- **影响**：提供了准确可靠的技术文档，便于开发人员参考和新人上手

### 2025-07-12 修复票价折扣方案卡类折扣表格滚动问题
- **问题描述**：在票价折扣方案创建或编辑页面中，当卡类折扣列表很长出现滚动条时，滚动到顶部会超过表格标题栏
- **根本原因**：表格使用了 `stickyHeader` 属性，但表头单元格缺少关键的 `top: 0` 样式属性
- **修复方案**：
  1. **LinePriceVersionEditForm.jsx**：为所有表头单元格添加粘性定位样式
  2. **LinePriceVersionEditView.jsx**：修复复杂表格的粘性表头
- **技术细节**：使用 `position: 'sticky'` + `top: 0` 确保表头在垂直滚动时固定在容器顶部
- **影响**：解决了表格滚动时表头位置不正确的问题，提升了用户体验

### 2025-07-12 使用统一商户下拉框组件优化终端文件上传管理页面
- **改进内容**：将商户筛选从普通下拉框替换为项目统一的 `MerchantAutocomplete` 组件
- **技术实现**：
  1. **组件替换**：使用 `MerchantAutocomplete` 替换原有的 `FormControl + Select` 组合
  2. **状态管理**：添加 `selectedMerchant` 状态存储选中的商户对象
  3. **事件处理**：实现 `handleMerchantChange` 函数处理商户选择变更
  4. **重置功能**：修改重置函数同时清空选中的商户对象
  5. **代码简化**：移除了手动获取商户列表的代码，因为组件内部自动处理
- **参考其他页面实现**：
  - 参考了 TerminalRecords、TerminalEventsList、TerminalList 等页面的用法
  - 使用标准的 `(event, newValue) => {}` 回调函数签名
  - 移除了不必要的 `merchants` 属性传递和手动数据获取
- **用户体验提升**：
  - 支持按商户ID和名称快速筛选
  - 统一的交互体验，与其他页面保持一致
  - 更好的可访问性和键盘导航支持
- **影响**：提升了用户体验，保持了项目组件使用的一致性，便于后续维护

### 2025-07-12 修复终端文件上传管理页面报错问题
- **问题描述**：页面出现 `merchants.map is not a function` 错误，导致页面无法正常显示
- **解决方案**：
  1. **前端安全检查**：在 `fetchMerchants` 函数中添加响应数据类型检查，确保只有数组类型才设置到 state
  2. **渲染保护**：在使用 `merchants.map` 时添加 `Array.isArray(merchants)` 检查，防止非数组数据导致错误
  3. **错误处理增强**：在 catch 块中确保设置空数组，避免 undefined 状态
  4. **调试日志**：添加 console.log 输出商户列表API响应，便于排查问题
- **影响**：解决了页面崩溃问题，提升了代码健壮性，确保在API异常时页面仍能正常显示

### 2025-07-12 优化终端文件上传管理功能权限控制和商户信息显示
- **需求背景**：用户反馈终端文件上传管理页面需要改进商户筛选方式，添加商户信息显示，并实现基于用户角色的权限控制
- **解决方案**：
  1. **后端权限控制优化**：在TerminalFileUploadsController中添加UserManager依赖注入，实现基于用户角色的数据访问控制，在所有API方法中添加权限检查
  2. **商户信息显示增强**：修改TerminalFileUploadDto添加MerchantName字段，在查询中联接Merchants表获取商户名称
  3. **前端界面改进**：商户筛选从文本框改为下拉选择框，添加获取商户列表API接口，表格中添加商户列显示商户名称
  4. **权限控制实现**：非管理员只能查看/操作自己商户的数据，系统管理员可以查看所有商户数据
- **影响**：提升了数据安全性，改善了用户体验，简化了商户筛选操作，保持了与其他模块一致的权限控制模式
- **详细信息**：查看 [TCP终端文件上传文档](./docs/tcp-file-upload.md)

### 2025-07-11 完成TCP终端文件上传功能
- **需求背景**：用户希望在TCP服务中增加终端上传文件的支持，使用一个TCP服务端口，通过协议区分处理，支持断点续传
- **主要成果**：完成了协议设计、数据库设计、服务层实现、TCP集成、后台清理服务和测试客户端
- **详细信息**：查看 [TCP终端文件上传文档](./docs/tcp-file-upload.md)

---

## 📝 文档更新说明

- 本备忘录已重新组织，详细技术信息已迁移到对应的模块文档中
- 主备忘录只保留最新的重要更新记录（最多10条）
- 查找具体技术信息请参考上方的模块文档链接
- 新的开发记录优先更新到主备忘录，定期整理到对应模块文档

## 使用指南

1. **查找信息**：根据功能模块在对应文档中查找相关信息
2. **更新记录**：新的开发记录优先更新到主备忘录，定期整理到对应模块文档
3. **文档维护**：每个模块文档保持独立，便于查找和维护
4. **问题记录**：技术问题和解决方案记录到 [问题解决记录](./docs/troubleshooting.md)

### 2025-07-30 全站页面重复请求优化（重大性能提升）✅
- **优化范围**: 基于成功经验，系统性优化所有主要页面的重复请求问题
  - ✅ **核心列表页面**：UserListView、MerchantListView、TerminalList、VehicleList、RoleListView
  - ✅ **文件管理页面**：FilePublishList、FileVersionList、FileTypeList
  - ✅ **终端相关页面**：TerminalRecords、TerminalEventsList、TerminalFileUploads
  - ✅ **未注册线路页面**：UnregisteredLines
  - ✅ **仪表盘页面**：MerchantDashboard、PlatformDashboard
- **优化技术方案**:
  1. **移除useCallback依赖**：避免函数重新创建导致useEffect重复触发
  2. **双useEffect模式**：首次加载useEffect + 参数变化useEffect分离
  3. **防重复请求机制**：使用useRef缓存请求参数，避免相同参数重复请求
  4. **强制加载标志**：为搜索/刷新按钮提供forceLoad参数，确保用户操作正常响应
- **最终优化效果**:
  - ✅ **所有主要页面**：从多次请求减少到1次请求
  - ✅ **保持所有搜索/筛选/分页功能正常**
  - ✅ **显著提升页面加载性能和用户体验**
  - ✅ **减少服务器负载和网络流量**
  - ✅ **解决了用户反馈的页面加载慢问题**
  - ✅ **修复了商户下拉框重复请求问题**

### 2025-07-30 补充页面优化和重复请求修复 ✅
- **修复的重复请求问题**:
  - ✅ **LinePriceVersionsView**：修复线路信息重复加载问题
  - ✅ **DictionaryListView**：移除手动商户加载，使用MerchantAutocomplete内置功能
  - ✅ **OperationLogs**：完成操作日志页面优化
- **新增优化页面**:
  - ✅ **消息管理页面**：MessageTypeList、MessageList
  - ✅ **线路参数页面**：LinePriceListView、LinePriceVersionsView
  - ✅ **银联密钥页面**：UnionPayTerminalKeyList
  - ✅ **商户字典页面**：DictionaryListView
  - ✅ **票价折扣方案页面**：FareDiscountSchemesView
  - ✅ **系统设置页面**：SystemSettings、SystemSettingsTab
  - ✅ **菜单管理页面**：MenuManagement
  - ✅ **功能权限管理页面**：FeaturePermissionManagement
- **最终统计**: 已优化约35个主要页面，覆盖WebAdmin系统的所有核心功能模块

### 2025-07-30 审计日志页面商户下拉框统一优化 ✅
- **优化背景**: 用户建议将审计日志页面的手动商户Select替换为统一的MerchantAutocomplete组件
- **优化理由**: MerchantAutocomplete组件支持不选择任何商户（即为空），相当于"全部"的意思，且能避免重复请求
- **已优化页面**:
  - ✅ **LoginLogs**：登录日志页面
  - ✅ **PasswordChangeLogs**：改密日志页面
  - ✅ **OperationLogs**：操作日志页面
- **优化内容**:
  1. **移除手动商户管理**：删除merchants状态和loadMerchants函数
  2. **使用MerchantAutocomplete**：替换手动Select为统一组件
  3. **修改数据加载逻辑**：使用selectedMerchant而不是filters.merchantId
  4. **更新依赖关系**：在useEffect中添加selectedMerchant依赖
  5. **修改清除筛选**：同时清除selectedMerchant状态
- **优化效果**:
  - ✅ **避免重复商户请求**：每个页面减少1次/api/merchants请求
  - ✅ **界面一致性**：所有页面使用统一的商户选择组件
  - ✅ **功能完整性**：保持"全部商户"功能（留空即表示全部）
  - ✅ **用户体验**：商户选择更加直观和一致

### 2025-07-30 修复终端管理页面useCallback导入错误 ✅
- **问题描述**: 终端管理页面报错 `useCallback is not defined`
- **根本原因**: 在优化过程中移除了useCallback导入，但页面中的renderFieldValue函数仍在使用useCallback
- **修复方案**: 在TerminalList.jsx的React导入中添加useCallback
- **修复内容**:
  ```javascript
  // 修复前
  import React, { useState, useEffect, useRef, useMemo } from 'react';

  // 修复后
  import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
  ```
- **影响范围**: 仅影响终端管理页面，其他页面未发现类似问题
- **测试结果**: 终端管理页面恢复正常，所有功能正常工作

### 2025-07-30 修复终端管理页面字段配置重复请求问题 ✅
- **问题描述**: 终端管理页面有两次 `/config/terminalFields.json` 请求
- **根本原因**: useFieldConfig Hook存在重复请求问题
  1. **useCallback依赖问题**: loadFieldConfig依赖defaultConfig，导致函数重新创建
  2. **useEffect依赖问题**: useEffect依赖loadFieldConfig，函数重新创建时重复触发
- **修复方案**: 应用与其他页面相同的防重复请求模式
  1. **移除useCallback**: 将loadFieldConfig改为普通async函数，避免依赖问题
  2. **添加防重复机制**: 使用hasLoadedRef防止重复加载
  3. **优化useEffect**: 移除loadFieldConfig依赖，只在首次加载时执行
  4. **修复reloadConfig**: 重置加载标志，支持强制重新加载
- **修复内容**:
  ```javascript
  // 添加防重复请求标志
  const hasLoadedRef = useRef(false);

  // 移除useCallback，改为普通函数
  const loadFieldConfig = async (forceReload = false) => { ... };

  // 优化useEffect，防止重复加载
  useEffect(() => {
    if (hasLoadedRef.current) return;
    hasLoadedRef.current = true;
    loadFieldConfig();
  }, []);
  ```
- **优化效果**:
  - ✅ **减少重复请求**: 从2次请求减少到1次请求
  - ✅ **保持功能完整**: 字段配置加载和重新加载功能正常
  - ✅ **统一优化模式**: 与其他页面使用相同的防重复请求机制
- **影响范围**: 所有使用useFieldConfig Hook的页面都将受益于此优化

### 2025-07-30 修复商户字典页面merchants未定义错误 ✅
- **问题描述**: 商户字典页面打开报错 `merchants is not defined`
- **根本原因**: 在优化过程中移除了merchants状态，但DictionaryFormDialog组件仍在使用merchants属性
- **错误位置**:
  1. **DictionaryListView.jsx**: 第446行传递了不存在的merchants属性
  2. **DictionaryFormDialog.jsx**: 组件参数中期望接收merchants属性，并传递给MerchantAutocomplete
- **修复方案**: 使用MerchantAutocomplete组件的自动模式（全局MerchantContext）
  1. **移除属性传递**: 在DictionaryListView中移除merchants属性传递
  2. **移除参数接收**: 在DictionaryFormDialog中移除merchants参数
  3. **使用全局数据**: 通过useMerchants Hook获取商户数据
  4. **移除组件属性**: 移除MerchantAutocomplete的merchants属性
- **修复内容**:
  ```javascript
  // DictionaryListView.jsx - 移除merchants属性
  <DictionaryFormDialog
    open={formDialogOpen}
    onClose={handleFormClose}
    onSubmit={handleFormSubmit}
    dictionary={editingDictionary}
    // merchants={merchants} // 已移除
  />

  // DictionaryFormDialog.jsx - 使用全局数据
  import { useMerchants } from '../../contexts/MerchantContext';

  const DictionaryFormDialog = ({ open, onClose, onSubmit, dictionary }) => {
    const { merchants } = useMerchants(); // 使用全局数据
    // ...
  };
  ```
- **优化效果**:
  - ✅ **修复页面错误**: 商户字典页面恢复正常访问
  - ✅ **统一数据管理**: 使用全局MerchantContext，避免重复请求
  - ✅ **保持功能完整**: 商户选择和表单验证功能正常
  - ✅ **代码一致性**: 与其他页面使用相同的商户数据管理模式

### 2025-07-30 优化文件版本管理页面商户重复请求问题 ✅
- **问题描述**: 文件版本管理页面每次打开还是会有 `/api/merchants?pageSize=100` 的请求
- **根本原因**: 该页面使用了混合模式，既使用MerchantAutocomplete组件，又保留了自己的商户管理逻辑
- **问题分析**:
  1. **使用了MerchantAutocomplete**: 在筛选条件和上传对话框中使用了统一组件
  2. **保留了重复逻辑**: 还有自己的merchants状态、loadMerchants函数和merchantAPI调用
  3. **双重请求**: 全局MerchantContext请求一次，页面自己再请求一次
- **优化方案**: 移除页面内部的商户管理逻辑，完全依赖MerchantAutocomplete的自动模式
  1. **移除API导入**: 删除merchantAPI导入，只保留fileAPI
  2. **移除状态管理**: 删除merchants状态和loadingMerchants状态
  3. **移除函数**: 删除loadMerchants函数
  4. **移除初始化调用**: 在useEffect中移除loadMerchants调用
  5. **简化上传对话框**: 移除商户数据检查逻辑
- **修复内容**:
  ```javascript
  // 移除重复的商户管理
  // const [merchants, setMerchants] = useState([]);
  // const [loadingMerchants, setLoadingMerchants] = useState(false);
  // const loadMerchants = async () => { ... };

  // 简化上传对话框
  const openUploadFileDialog = () => {
    // 移除商户数据检查
    // if (merchants.length === 0) { await loadMerchants(); }
    setUploadForm({ ... });
    setOpenUploadDialog(true);
  };
  ```
- **优化效果**:
  - ✅ **消除重复请求**: 不再有额外的/api/merchants请求
  - ✅ **统一数据管理**: 完全使用全局MerchantContext
  - ✅ **保持功能完整**: 商户选择和文件上传功能正常
  - ✅ **代码一致性**: 与其他已优化页面保持一致
- **影响范围**: 文件版本管理页面的商户选择功能，包括筛选和上传对话框

### 2025-07-30 修复仪表盘页面重复请求CurrentUser问题 ✅
- **问题描述**: /app/dashboard 仪表盘页面会请求两次 `/api/users/CurrentUser`
- **根本原因**: DashboardView组件重复请求用户信息来检查角色，而AuthContext中已有用户信息
- **问题分析**:
  1. **AuthContext已有用户信息**: 在初始化时已经获取并存储了用户信息和角色
  2. **DashboardView重复请求**: 在checkUserRole函数中调用userAPI.getCurrentUser()获取角色信息
  3. **不必要的API调用**: 相同的用户信息被请求了两次
- **修复方案**: 使用AuthContext中的用户信息，移除重复的API请求
  1. **导入useAuth Hook**: 替换userAPI导入为useAuth Hook
  2. **使用全局用户信息**: 直接从user.roles获取角色信息
  3. **移除重复请求**: 删除checkUserRole函数和相关的API调用
  4. **简化状态管理**: 移除isSystemAdmin状态，直接计算角色
- **修复内容**:
  ```javascript
  // 修复前
  import { userAPI } from '../../services/api';
  const [isSystemAdmin, setIsSystemAdmin] = useState(false);

  const checkUserRole = async () => {
    const response = await userAPI.getCurrentUser(); // ❌ 重复请求
    const roles = response.roles || [];
    setIsSystemAdmin(roles.includes('SystemAdmin'));
  };

  // 修复后
  import { useAuth } from '../../contexts/AuthContext';
  const { user } = useAuth();
  const isSystemAdmin = user?.roles?.includes('SystemAdmin') || false; // ✅ 直接使用
  ```
- **优化效果**:
  - ✅ **消除重复请求**: 从2次/api/users/CurrentUser请求减少到1次
  - ✅ **提升页面性能**: 减少不必要的API调用和网络延迟
  - ✅ **简化代码逻辑**: 移除异步状态管理，直接使用全局状态
  - ✅ **保持功能完整**: 角色检查和标签页切换功能正常
- **影响范围**: 仪表盘页面的用户角色检查和标签页初始化逻辑
