import React, { useState } from 'react';
import {
    Box,
    Paper,
    Typography,
    Button,
    TextField,
    FormControl,
    FormControlLabel,
    Switch,
    Alert,
    Snackbar,
    Divider
} from '@mui/material';
import {
    Save as SaveIcon,
    ArrowBack as ArrowBackIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { fareDiscountSchemeAPI, linePriceAPI } from '../../services/api';
import LinePriceVersionEditForm from '../fare-params/LinePriceVersionEditForm';
import MerchantAutocomplete from '../../components/MerchantAutocomplete';

const FareDiscountSchemeCreateView = () => {
    const navigate = useNavigate();
    const [loading, setLoading] = useState(false);
    const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });
    
    const [selectedMerchant, setSelectedMerchant] = useState(null);
    const [formData, setFormData] = useState({
        schemeName: '',
        schemeCode: '',
        description: '',
        isActive: true
    });

    const [extraParams, setExtraParams] = useState({

    });

    const [cardDiscountInfo, setCardDiscountInfo] = useState([
        {

        }
    ]);

    const [errors, setErrors] = useState({});

    // 字典配置
    const [extraParamsConfig, setExtraParamsConfig] = useState([]);
    const [cardDiscountConfig, setCardDiscountConfig] = useState([]);

    const showSnackbar = (message, severity = 'success') => {
        setSnackbar({ open: true, message, severity });
    };

    // 加载字典配置
    const fetchDictionaryConfigs = async (merchantID) => {
        try {
            console.log("加载字典配置 - merchantID:", merchantID);
            if (!merchantID) {
                console.error('商户ID为空，无法加载字典配置');
                return;
            }

            // 加载线路参数字典配置
            const extraParamsConfigResponse = await linePriceAPI.getDictionaryConfig(merchantID, 'BUS_LINE_EXTRA_FARE_CONFIG');
            console.log('获取到的额外参数配置:', extraParamsConfigResponse);
            setExtraParamsConfig(extraParamsConfigResponse || []);

            // 加载卡类折扣字典配置
            const cardDiscountConfigResponse = await linePriceAPI.getDictionaryConfig(merchantID, 'BUS_ICCARD_FARE_RULE_CONFIG');
            console.log('获取到的卡类折扣配置:', cardDiscountConfigResponse);
            setCardDiscountConfig(cardDiscountConfigResponse || []);
        } catch (error) {
            console.error('获取字典配置失败:', error);
            showSnackbar('获取字典配置失败', 'error');
        }
    };

    // 商户选择变化时加载字典配置
    const handleMerchantChange = (event, newValue) => {
        setSelectedMerchant(newValue);
        if (newValue?.merchantID) {
            fetchDictionaryConfigs(newValue.merchantID);
            // 重置表单数据
            setExtraParams({

            });
            // 清空卡类配置，等待字典配置加载完成后自动创建默认配置
            setCardDiscountInfo([]);
        } else {
            setExtraParamsConfig([]);
            setCardDiscountConfig([]);
            // 恢复默认的硬编码配置
            setCardDiscountInfo([
                {

                }
            ]);
        }
    };

    const validateForm = () => {
        const newErrors = {};

        if (!formData.schemeName.trim()) {
            newErrors.schemeName = '方案名称不能为空';
        }

        if (!formData.schemeCode.trim()) {
            newErrors.schemeCode = '方案编码不能为空';
        } else if (!/^[A-Z0-9_]+$/.test(formData.schemeCode)) {
            newErrors.schemeCode = '方案编码只能包含大写字母、数字和下划线';
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleInputChange = (field, value) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }));
        
        // 清除对应字段的错误
        if (errors[field]) {
            setErrors(prev => ({
                ...prev,
                [field]: undefined
            }));
        }
    };

    const handleSave = async () => {
        if (!selectedMerchant) {
            showSnackbar('请先选择商户', 'error');
            return;
        }

        if (!validateForm()) {
            showSnackbar('请检查表单输入', 'error');
            return;
        }

        setLoading(true);
        try {

            const data = {
                ...formData,
                merchantID: selectedMerchant.merchantID,
                extraParams: extraParams,
                cardDiscountInfo: cardDiscountInfo
            };

            await fareDiscountSchemeAPI.createScheme(data);
            showSnackbar('创建票价折扣方案成功');
            
            // 延迟跳转，让用户看到成功消息
            setTimeout(() => {
                navigate('/app/fare-discount-schemes');
            }, 1500);
        } catch (error) {
            console.error('创建票价折扣方案失败:', error);
            showSnackbar(error.response?.data?.message || '创建失败', 'error');
        } finally {
            setLoading(false);
        }
    };

    const handleBack = () => {
        navigate('/app/fare-discount-schemes');
    };

    return (
        <Box sx={{ p: 3 }}>
            <Paper sx={{ p: 3 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                    <Typography variant="h5" component="h1">
                        创建票价折扣方案
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 2 }}>
                        <Button
                            variant="outlined"
                            startIcon={<ArrowBackIcon />}
                            onClick={handleBack}
                        >
                            返回
                        </Button>
                        <Button
                            variant="contained"
                            startIcon={<SaveIcon />}
                            onClick={handleSave}
                            disabled={loading}
                        >
                            保存方案
                        </Button>
                    </Box>
                </Box>

                {/* 商户选择 */}
                <Typography variant="h6" sx={{ mb: 2 }}>
                    商户选择
                </Typography>

                <Box sx={{ mb: 3 }}>
                    <MerchantAutocomplete
                        value={selectedMerchant}
                        onChange={handleMerchantChange}
                        required
                        fullWidth
                        label="选择商户"
                        helperText="请先选择商户，以便加载对应的配置信息"
                    />
                </Box>

                <Divider sx={{ my: 3 }} />

                {/* 基本信息 */}
                <Typography variant="h6" sx={{ mb: 2 }}>
                    基本信息
                </Typography>

                <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(2, 1fr)', gap: 3, mb: 3 }}>
                    <TextField
                        label="方案名称"
                        required
                        value={formData.schemeName}
                        onChange={(e) => handleInputChange('schemeName', e.target.value)}
                        error={!!errors.schemeName}
                        helperText={errors.schemeName}
                        placeholder="如：标准折扣方案"
                    />
                    <TextField
                        label="方案编码"
                        required
                        value={formData.schemeCode}
                        onChange={(e) => handleInputChange('schemeCode', e.target.value.toUpperCase())}
                        error={!!errors.schemeCode}
                        helperText={errors.schemeCode || '只能包含大写字母、数字和下划线'}
                        placeholder="如：STANDARD"
                    />
                </Box>

                <TextField
                    label="方案描述"
                    multiline
                    rows={3}
                    fullWidth
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    placeholder="描述此方案的用途和特点..."
                    sx={{ mb: 3 }}
                />

                <FormControlLabel
                    control={
                        <Switch
                            checked={formData.isActive}
                            onChange={(e) => handleInputChange('isActive', e.target.checked)}
                        />
                    }
                    label="启用方案"
                    sx={{ mb: 3 }}
                />

                <Divider sx={{ my: 3 }} />

                {/* 票价参数配置 */}
                <Typography variant="h6" sx={{ mb: 2 }}>
                    票价参数配置
                </Typography>
                
                {selectedMerchant ? (
                    <LinePriceVersionEditForm
                        extraParams={extraParams}
                        cardDiscountInfo={cardDiscountInfo}
                        onExtraParamsChange={setExtraParams}
                        onCardDiscountInfoChange={setCardDiscountInfo}
                        hideSubmitButton={true}
                        extraParamsConfig={extraParamsConfig}
                        cardDiscountConfig={cardDiscountConfig}
                    />
                ) : (
                    <Box sx={{ textAlign: 'center', py: 4 }}>
                        <Typography variant="body1" color="text.secondary">
                            请先选择商户以加载配置信息
                        </Typography>
                    </Box>
                )}
            </Paper>

            {/* 消息提示 */}
            <Snackbar
                open={snackbar.open}
                autoHideDuration={6000}
                onClose={() => setSnackbar({ ...snackbar, open: false })}
            >
                <Alert
                    onClose={() => setSnackbar({ ...snackbar, open: false })}
                    severity={snackbar.severity}
                    sx={{ width: '100%' }}
                >
                    {snackbar.message}
                </Alert>
            </Snackbar>
        </Box>
    );
};

export default FareDiscountSchemeCreateView;
