# 文档管理规范

## 🎯 目的

制定文档创建、组织和维护的规范，避免文档混乱和堆积问题。

## 📁 目录结构规范

### 根目录文档（仅限重要项目级文档）
```
/
├── README.md              # 项目主说明
├── NOTES.md              # 开发备忘录（仅最新更新）
├── CHANGELOG.md          # 版本变更日志
└── rules.md              # 项目规则
```

### docs/ 目录结构
```
docs/
├── README.md                    # 文档索引
├── guides/                      # 用户指南
├── architecture/                # 架构设计文档
├── deployment/                  # 部署相关文档
├── database/                    # 数据库文档
├── troubleshooting/             # 故障排除文档
├── frontend-development.md      # 前端开发规范
├── backend-development.md       # 后端开发规范
└── system-architecture.md       # 系统架构
```

### temp/ 目录（临时文档）
```
temp/
├── fixes/                       # 临时修复记录
├── analysis/                    # 临时分析文档
├── drafts/                      # 草稿文档
└── README.md                    # 临时目录说明
```

### 项目特定文档
```
SlzrCrossGate.ProjectName/
├── README.md                    # 项目说明
├── docs/                        # 项目特定文档
└── PROJECT_SUMMARY.md           # 项目总结（如需要）
```

## 📝 文档创建规范

### 1. 临时文档规则
- **位置**: 必须放在 `temp/` 目录下
- **命名**: 使用日期前缀，如 `temp/fixes/2025-07-17-iframe-fix.md`
- **内容**: 可以是修复记录、临时分析、草稿等
- **清理**: 问题解决后必须及时删除或转为正式文档

### 2. 正式文档规则
- **位置**: 根据类型放在docs/下的对应子目录
- **命名**: 使用描述性名称，全大写+下划线，如 `CUSTOMER_PROJECT_INTEGRATION_GUIDE.md`
- **内容**: 经过整理的、有长期价值的文档
- **维护**: 定期更新，保持内容准确性

### 3. 项目特定文档规则
- **位置**: 放在对应项目目录下
- **范围**: 仅与该项目相关的文档
- **示例**: 客户项目总结、项目特定配置等

## 🔄 文档生命周期

### 创建阶段
1. **确定类型**: 临时文档 → temp/，正式文档 → docs/
2. **选择位置**: 根据内容选择合适的子目录
3. **规范命名**: 使用清晰的描述性名称

### 维护阶段
1. **定期检查**: 每周检查temp/目录，清理过期文档
2. **内容更新**: 正式文档保持最新状态
3. **结构调整**: 根据需要调整分类和组织

### 清理阶段
1. **临时文档**: 问题解决后立即清理
2. **过时文档**: 定期清理不再需要的文档
3. **重复文档**: 合并或删除重复内容

## ⚠️ 禁止行为

### ❌ 不允许的做法
- 在根目录创建大量文档
- 创建临时修复文档后不清理
- 文档命名不规范（如使用中文、特殊字符）
- 创建重复内容的文档
- 不更新文档索引

### ✅ 推荐做法
- 临时文档放temp/目录
- 及时整理和分类文档
- 使用清晰的命名规范
- 定期更新docs/README.md索引
- 删除过时和无用文档

## 📋 检查清单

### 创建文档时
- [ ] 确定文档类型（临时/正式）
- [ ] 选择正确的目录位置
- [ ] 使用规范的文件命名
- [ ] 添加到相应的索引文件

### 完成工作后
- [ ] 清理temp/目录中的临时文档
- [ ] 将有价值的内容转为正式文档
- [ ] 更新docs/README.md索引
- [ ] 删除不再需要的文档

### 定期维护
- [ ] 每周检查temp/目录
- [ ] 每月检查文档结构
- [ ] 更新过时的文档内容
- [ ] 优化文档组织结构

## 🎯 目标效果

通过遵循这些规范，实现：
- 📁 清晰的文档结构
- 🔍 快速的文档查找
- 🧹 整洁的项目根目录
- 📝 高质量的文档内容
- ⚡ 高效的文档维护

## 📚 相关文档

- [docs/README.md](./README.md) - 完整文档索引
- [../NOTES.md](../NOTES.md) - 开发备忘录
- [../CHANGELOG.md](../CHANGELOG.md) - 版本变更日志

---

**重要提醒**: 这是血的教训！必须严格遵循文档管理规范，避免再次出现文档混乱问题。
