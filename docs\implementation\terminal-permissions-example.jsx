// 终端管理页面权限控制改造示例
import React, { useState, useMemo } from 'react';
import { Box, IconButton, Tooltip } from '@mui/material';
import {
  Visibility as VisibilityIcon,
  Message as MessageIcon,
  Publish as PublishIcon,
  History as HistoryIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { FeatureGuard } from '../components/FeatureGuard';
import { useFeaturePermission } from '../hooks/useFeaturePermission';
import { PERMISSIONS } from '../constants/permissions';

// 终端操作按钮组件
const TerminalActionButtons = ({ terminal, onSendMessage, onPublishFile }) => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { checkMultiple } = useFeaturePermission();
  
  // 批量检查权限，提高性能
  const permissions = useMemo(() => 
    checkMultiple([
      PERMISSIONS.TERMINAL.VIEW_DETAILS,
      PERMISSIONS.TERMINAL.SEND_MESSAGE,
      PERMISSIONS.TERMINAL.PUBLISH_FILE,
      PERMISSIONS.TERMINAL.VIEW_EVENTS
    ]), 
    [checkMultiple]
  );

  // 业务逻辑判断
  const canSendMessage = permissions[PERMISSIONS.TERMINAL.SEND_MESSAGE] && 
    terminal.status === 'online';
    
  const canPublishFile = permissions[PERMISSIONS.TERMINAL.PUBLISH_FILE] && 
    terminal.status === 'online';

  return (
    <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
      {/* 查看详情 - 基础权限 */}
      <FeatureGuard 
        featureKey={PERMISSIONS.TERMINAL.VIEW_DETAILS}
        fallback={
          <Tooltip title="无查看权限">
            <span>
              <IconButton size="small" disabled>
                <VisibilityIcon fontSize="small" />
              </IconButton>
            </span>
          </Tooltip>
        }
      >
        <Tooltip title="查看详情">
          <IconButton
            size="small"
            color="primary"
            onClick={() => navigate(`/app/terminals/${terminal.id}`)}
          >
            <VisibilityIcon fontSize="small" />
          </IconButton>
        </Tooltip>
      </FeatureGuard>

      {/* 发送消息 - 需要终端在线 */}
      <FeatureGuard 
        featureKey={PERMISSIONS.TERMINAL.SEND_MESSAGE}
        additionalCheck={terminal.status === 'online'}
        fallback={
          <Tooltip title={
            !permissions[PERMISSIONS.TERMINAL.SEND_MESSAGE] 
              ? "无发送消息权限" 
              : "终端离线，无法发送消息"
          }>
            <span>
              <IconButton size="small" disabled>
                <MessageIcon fontSize="small" />
              </IconButton>
            </span>
          </Tooltip>
        }
      >
        <Tooltip title="发送消息">
          <IconButton
            size="small"
            color="secondary"
            onClick={() => onSendMessage(terminal)}
          >
            <MessageIcon fontSize="small" />
          </IconButton>
        </Tooltip>
      </FeatureGuard>

      {/* 发布文件 - 需要终端在线 */}
      <FeatureGuard 
        featureKey={PERMISSIONS.TERMINAL.PUBLISH_FILE}
        additionalCheck={terminal.status === 'online'}
        fallback={
          <Tooltip title={
            !permissions[PERMISSIONS.TERMINAL.PUBLISH_FILE] 
              ? "无发布文件权限" 
              : "终端离线，无法发布文件"
          }>
            <span>
              <IconButton size="small" disabled>
                <PublishIcon fontSize="small" />
              </IconButton>
            </span>
          </Tooltip>
        }
      >
        <Tooltip title="发布文件">
          <IconButton
            size="small"
            color="success"
            onClick={() => onPublishFile(terminal)}
          >
            <PublishIcon fontSize="small" />
          </IconButton>
        </Tooltip>
      </FeatureGuard>

      {/* 查看事件 */}
      <FeatureGuard featureKey={PERMISSIONS.TERMINAL.VIEW_EVENTS}>
        <Tooltip title="查看事件">
          <IconButton
            size="small"
            color="info"
            onClick={() => navigate(`/app/terminals/${terminal.id}/events`)}
          >
            <HistoryIcon fontSize="small" />
          </IconButton>
        </Tooltip>
      </FeatureGuard>
    </Box>
  );
};

// 在表格中使用
const TerminalTableRow = ({ terminal, onSendMessage, onPublishFile }) => {
  return (
    <ResponsiveTableRow key={terminal.id}>
      {/* 其他列... */}
      
      <ResponsiveTableCell sticky={true}>
        <TerminalActionButtons 
          terminal={terminal}
          onSendMessage={onSendMessage}
          onPublishFile={onPublishFile}
        />
      </ResponsiveTableCell>
    </ResponsiveTableRow>
  );
};

// 重要：不使用页面级权限检查，只对按钮功能进行权限控制
const TerminalListPage = () => {
  // 不要使用页面级权限检查！
  // 页面本身对所有用户开放，只对具体功能按钮进行权限控制

  return (
    <Container>
      {/* 页面内容 */}
    </Container>
  );
};

export { TerminalActionButtons, TerminalListPage };
