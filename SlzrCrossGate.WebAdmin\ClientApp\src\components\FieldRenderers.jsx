import React from 'react';
import { Box, Typography, Chip, Tooltip } from '@mui/material';
import { 
  SignalCellular4Bar as SignalIcon,
  SignalCellular3Bar as Signal3Icon,
  SignalCellular2Bar as Signal2Icon,
  SignalCellular1Bar as Signal1Icon,
  SignalCellular0Bar as Signal0Icon,
  Wifi as WifiIcon,
  NetworkCell as NetworkIcon,
  Bluetooth as BluetoothIcon
} from '@mui/icons-material';

/**
 * 字段渲染器集合
 * 每个渲染器负责将数据转换为可视化组件
 */
export class FieldRenderers {
  
  /**
   * 获取嵌套对象的值
   */
  static getNestedValue(obj, path) {
    if (!obj || !path) return null;
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  /**
   * 获取Tooltip内容
   * 支持静态文本和动态模板变量
   */
  static getTooltipContent(fieldConfig, terminal) {
    if (!fieldConfig.tooltip) return null;

    // 如果tooltip配置是对象，支持动态内容
    if (typeof fieldConfig.tooltip === 'object') {
      const { template, dataPath } = fieldConfig.tooltip;

      if (template && dataPath) {
        // 从终端数据中获取tooltip的值
        const tooltipValue = this.getNestedValue(terminal, dataPath);
        if (tooltipValue !== null && tooltipValue !== undefined) {
          // 替换模板中的变量
          return this.replaceTemplate(template, {
            ...terminal,
            tooltipValue: tooltipValue
          });
        }
      }

      // 如果有静态文本，使用静态文本
      if (fieldConfig.tooltip.text) {
        return this.replaceTemplate(fieldConfig.tooltip.text, terminal);
      }
    }

    // 如果tooltip是字符串，支持模板变量替换
    if (typeof fieldConfig.tooltip === 'string') {
      return this.replaceTemplate(fieldConfig.tooltip, terminal);
    }

    return null;
  }

  /**
   * 替换模板变量
   */
  static replaceTemplate(template, data) {
    if (!template || typeof template !== 'string') return template;

    let result = template;

    // 替换常用的终端变量
    result = result
      .replace(/{terminalType}/g, data.terminalType || '')
      .replace(/{lineNO}/g, data.lineNO || '')
      .replace(/{merchantID}/g, data.merchantID || '')
      .replace(/{deviceNO}/g, data.deviceNO || '')
      .replace(/{serialNO}/g, data.serialNO || '')
      .replace(/{merchantName}/g, data.merchantName || '');

    // 替换特殊的tooltip变量
    if (data.tooltipValue !== undefined) {
      result = result.replace(/{tooltipValue}/g, String(data.tooltipValue));
    }

    // 支持任意字段的变量替换 {fieldName}
    const variablePattern = /{([^}]+)}/g;
    result = result.replace(variablePattern, (match, fieldName) => {
      const value = this.getNestedValue(data, fieldName);
      return value !== null && value !== undefined ? String(value) : match;
    });

    return result;
  }

  /**
   * 格式化日期
   */
  static formatDate(dateString) {
    if (!dateString) return '-';
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('zh-CN');
    } catch {
      return '-';
    }
  }

  /**
   * 格式化日期时间
   */
  static formatDateTime(dateString) {
    if (!dateString) return '-';
    try {
      const date = new Date(dateString);
      return date.toLocaleString('zh-CN');
    } catch {
      return '-';
    }
  }

  /**
   * 普通文本渲染器
   */
  static text(value, fieldConfig, terminal) {
    if (value === null || value === undefined) return '-';

    // 如果值是对象，尝试转换为JSON字符串或返回默认值
    if (typeof value === 'object') {
      try {
        const jsonStr = JSON.stringify(value);
        // 如果是空对象，返回 '-'
        if (jsonStr === '{}' || jsonStr === '[]') return '-';
        // 如果JSON字符串太长，截断显示
        if (jsonStr.length > 50) {
          return (
            <Tooltip title={`点击复制完整内容: ${jsonStr}`}>
              <Typography
                variant="body2"
                sx={{
                  cursor: 'pointer',
                  maxWidth: 150,
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap'
                }}
                onClick={() => {
                  navigator.clipboard.writeText(jsonStr);
                  // 这里需要通过props传递snackbar函数，暂时使用console
                  console.log('已复制到剪贴板');
                }}
              >
                {jsonStr.substring(0, 47)}...
              </Typography>
            </Tooltip>
          );
        }
        return jsonStr;
      } catch (e) {
        console.warn('Failed to stringify object value:', value, e);
        return '-';
      }
    }

    const displayValue = typeof value === 'string' ? value : String(value);

    // 检查是否有动态tooltip配置
    const tooltipContent = this.getTooltipContent(fieldConfig, terminal);

    // 处理长文本，支持点击复制
    if (typeof value === 'string' && value.length > 20) {
      const content = (
        <Typography
          variant="body2"
          sx={{
            cursor: 'pointer',
            maxWidth: 150,
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap'
          }}
          onClick={() => {
            navigator.clipboard.writeText(value);
            console.log('已复制到剪贴板');
          }}
        >
          {value}
        </Typography>
      );

      // 如果有tooltip配置，使用配置的tooltip，否则使用默认的"点击复制"
      const finalTooltip = (tooltipContent && tooltipContent.trim()) ? tooltipContent : "点击复制";

      return (
        <Tooltip title={finalTooltip} arrow>
          {content}
        </Tooltip>
      );
    }

    // 短文本处理
    if (tooltipContent && tooltipContent.trim()) {
      return (
        <Tooltip title={tooltipContent} arrow>
          <span>{displayValue}</span>
        </Tooltip>
      );
    }

    return displayValue;
  }

  /**
   * 日期渲染器
   */
  static date(value, fieldConfig, terminal) {
    return this.formatDate(value);
  }

  /**
   * 日期时间渲染器
   */
  static datetime(value, fieldConfig, terminal) {
    return this.formatDateTime(value);
  }

  /**
   * 状态渲染器
   */
  static status(statusObj, fieldConfig, terminal) {
    if (!statusObj) {
      return <Chip label="未知" color="default" size="small" />;
    }

    // 判断是否在线（5分钟内活跃且状态为激活）
    const isOnline = statusObj.activeStatus === 1 &&
      statusObj.lastActiveTime &&
      (new Date() - new Date(statusObj.lastActiveTime)) < 5 * 60 * 1000;

    if (isOnline) {
      return <Chip label="在线" color="success" size="small" />;
    } else {
      // 计算离线时长
      const offlineTime = statusObj.lastActiveTime ?
        Math.floor((new Date() - new Date(statusObj.lastActiveTime)) / (1000 * 60)) : 0;

      let label = '离线';
      if (offlineTime > 0) {
        if (offlineTime < 60) {
          label = `离线${offlineTime}分钟`;
        } else if (offlineTime < 1440) {
          label = `离线${Math.floor(offlineTime / 60)}小时`;
        } else {
          label = `离线${Math.floor(offlineTime / 1440)}天`;
        }
      }

      return <Chip label={label} color="error" size="small" />;
    }
  }

  /**
   * 离线时长渲染器
   */
  static offlineDuration(lastActiveTime, fieldConfig, terminal) {
    if (!terminal.status) return '-';

    // 判断是否在线
    const isOnline = terminal.status.activeStatus === 1 &&
      terminal.status.lastActiveTime &&
      (new Date() - new Date(terminal.status.lastActiveTime)) < 5 * 60 * 1000;

    if (isOnline) {
      return '-'; // 在线时不显示离线时长
    }

    // 使用formatOfflineDuration函数
    const { formatOfflineDuration } = require('../../utils/dateUtils');
    return formatOfflineDuration(terminal.status.lastActiveTime);
  }

  /**
   * 文件版本渲染器
   */
  static fileVersion(metadata, fieldConfig, terminal) {
    if (!metadata || !fieldConfig.config?.versionKeys) {
      return '-';
    }

    let currentVersion = '-';
    let versionKey = '-';

    // 根据配置的versionKeys查找版本
    for (const keyTemplate of fieldConfig.config.versionKeys) {
      const actualKey = this.replaceTemplate(keyTemplate, terminal);

      if (metadata[actualKey]?.current) {
        currentVersion = metadata[actualKey].current;
        versionKey = actualKey;
        break;
      }
    }

    // 如果没有找到版本，显示 "-"
    if (currentVersion === '-') {
      return '-';
    }

    const content = (
      <Box>
        <Typography variant="body2" fontWeight="bold" sx={{ lineHeight: 1.2 }}>
          {currentVersion}
        </Typography>
        {versionKey !== '-' && (
          <Typography variant="caption" color="textSecondary" sx={{ lineHeight: 1 }}>
            {versionKey}
          </Typography>
        )}
      </Box>
    );

    // 检查是否有动态tooltip配置
    const tooltipContent = this.getTooltipContent(fieldConfig, terminal);
    if (tooltipContent && tooltipContent.trim()) {
      return (
        <Tooltip title={tooltipContent} arrow>
          <span>{content}</span>
        </Tooltip>
      );
    }

    return content;
  }

  /**
   * 百分比渲染器
   */
  static percentage(value, fieldConfig, terminal) {
    const numValue = parseFloat(value);
    if (isNaN(numValue)) return '-';
    
    let color = 'default';
    if (numValue > 60) color = 'success';
    else if (numValue > 30) color = 'warning';
    else color = 'error';
    
    const chip = <Chip label={`${numValue}%`} color={color} size="small" />;

    const tooltipContent = this.getTooltipContent(fieldConfig, terminal);
    if (tooltipContent) {
      return <Tooltip title={tooltipContent} arrow>{chip}</Tooltip>;
    }

    return chip;
  }

  /**
   * 信号强度渲染器
   */
  static signal(value, fieldConfig, terminal) {
    const numValue = parseInt(value);
    if (isNaN(numValue)) return '-';
    
    let color = 'default';
    let icon = <Signal0Icon fontSize="small" />;
    
    if (numValue > -60) {
      color = 'success';
      icon = <SignalCellular4Bar fontSize="small" />;
    } else if (numValue > -70) {
      color = 'success';
      icon = <Signal3Icon fontSize="small" />;
    } else if (numValue > -80) {
      color = 'warning';
      icon = <Signal2Icon fontSize="small" />;
    } else if (numValue > -90) {
      color = 'warning';
      icon = <Signal1Icon fontSize="small" />;
    } else {
      color = 'error';
      icon = <Signal0Icon fontSize="small" />;
    }
    
    const content = (
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
        {icon}
        <Typography variant="caption">
          {numValue}dBm
        </Typography>
      </Box>
    );

    const tooltipContent = this.getTooltipContent(fieldConfig, terminal);
    if (tooltipContent) {
      return <Tooltip title={tooltipContent} arrow>{content}</Tooltip>;
    }

    return content;
  }

  /**
   * 温度渲染器
   */
  static temperature(value, fieldConfig, terminal) {
    const numValue = parseFloat(value);
    if (isNaN(numValue)) return '-';
    
    let color = 'default';
    if (numValue < 50) color = 'success';
    else if (numValue < 70) color = 'warning';
    else color = 'error';
    
    const chip = <Chip label={`${numValue}°C`} color={color} size="small" />;

    const tooltipContent = this.getTooltipContent(fieldConfig, terminal);
    if (tooltipContent && tooltipContent.trim()) {
      return <Tooltip title={tooltipContent} arrow>{chip}</Tooltip>;
    }

    return chip;
  }

  /**
   * 网络类型渲染器
   */
  static networkType(value, fieldConfig, terminal) {
    if (!value) return '-';

    const networkTypes = {
      'wifi': { label: 'WiFi', color: 'primary', icon: <WifiIcon fontSize="small" /> },
      '4g': { label: '4G', color: 'success', icon: <NetworkIcon fontSize="small" /> },
      '5g': { label: '5G', color: 'success', icon: <NetworkIcon fontSize="small" /> },
      '3g': { label: '3G', color: 'warning', icon: <NetworkIcon fontSize="small" /> },
      '2g': { label: '2G', color: 'error', icon: <NetworkIcon fontSize="small" /> },
      'bluetooth': { label: '蓝牙', color: 'info', icon: <BluetoothIcon fontSize="small" /> }
    };

    const networkInfo = networkTypes[value.toLowerCase()] ||
      { label: value, color: 'default', icon: <NetworkIcon fontSize="small" /> };

    const content = (
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
        {networkInfo.icon}
        <Typography variant="caption">
          {networkInfo.label}
        </Typography>
      </Box>
    );

    const tooltipContent = this.getTooltipContent(fieldConfig, terminal);
    if (tooltipContent && tooltipContent.trim()) {
      return <Tooltip title={tooltipContent} arrow>{content}</Tooltip>;
    }

    return content;
  }

  /**
   * 键值对渲染器 - 用于显示对象的键和值
   */
  static keyValue(value, fieldConfig, terminal) {
    if (!value || typeof value !== 'object') return '-';

    // 如果配置了特定的键，只显示该键的值
    if (fieldConfig.config?.key) {
      const specificValue = value[fieldConfig.config.key];
      if (specificValue === undefined || specificValue === null) return '-';

      const content = (
        <Box>
          <Typography variant="body2" fontWeight="bold" sx={{ lineHeight: 1.2 }}>
            {String(specificValue)}
          </Typography>
          {fieldConfig.config.showKey && (
            <Typography variant="caption" color="textSecondary" sx={{ lineHeight: 1 }}>
              {fieldConfig.config.key}
            </Typography>
          )}
        </Box>
      );

      const tooltipContent = this.getTooltipContent(fieldConfig, terminal);
      if (tooltipContent) {
        return <Tooltip title={tooltipContent} arrow><span>{content}</span></Tooltip>;
      }

      return content;
    }

    // 如果没有指定键，显示所有键值对（限制显示数量）
    const entries = Object.entries(value).slice(0, 3); // 最多显示3个
    if (entries.length === 0) return '-';

    const content = (
      <Box>
        {entries.map(([key, val], index) => (
          <Box key={key} sx={{ mb: index < entries.length - 1 ? 0.5 : 0 }}>
            <Typography variant="body2" fontWeight="bold" sx={{ lineHeight: 1.2 }}>
              {String(val)}
            </Typography>
            <Typography variant="caption" color="textSecondary" sx={{ lineHeight: 1 }}>
              {key}
            </Typography>
          </Box>
        ))}
        {Object.keys(value).length > 3 && (
          <Typography variant="caption" color="textSecondary">
            +{Object.keys(value).length - 3} 更多...
          </Typography>
        )}
      </Box>
    );

    const tooltipContent = this.getTooltipContent(fieldConfig, terminal);
    if (tooltipContent) {
      return <Tooltip title={tooltipContent} arrow><span>{content}</span></Tooltip>;
    }

    return content;
  }

  /**
   * 主渲染方法
   */
  static render(terminal, fieldConfig) {
    try {
      const value = this.getNestedValue(terminal, fieldConfig.dataPath);
      const renderer = this[fieldConfig.type] || this.text;
      
      return renderer.call(this, value, fieldConfig, terminal);
    } catch (error) {
      console.error(`渲染字段 ${fieldConfig.key} 时出错:`, error);
      return '-';
    }
  }
}
