/* 全局样式 - 玻璃拟态+微立体感设计 */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  height: 100%;
  font-size: 16px;
}

body {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: linear-gradient(135deg, #0F172A 0%, #1E293B 100%);
  overflow-x: hidden;
}

#root {
  height: 100%;
  min-height: 100vh;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* 主题切换动画 */
[data-theme="light"] {
  --bg-primary: #F5F3FF;
  --bg-secondary: rgba(255, 255, 255, 0.85);
  --text-primary: #1F2937;
  --text-secondary: #4B5563;
}

[data-theme="dark"] {
  --bg-primary: #0F172A;
  --bg-secondary: rgba(30, 41, 59, 0.85);
  --text-primary: #FFFFFF;
  --text-secondary: rgba(255, 255, 255, 0.7);
}

/* 玻璃拟态效果 */
.glass-morphism {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
  border-radius: 12px;
  box-shadow: 
    0px 8px 32px rgba(0, 0, 0, 0.1),
    0px 2px 8px rgba(0, 0, 0, 0.05),
    inset 0px 0px 0px 1px rgba(255, 255, 255, 0.2);
}

/* 微立体感按钮效果 */
.micro-3d-button {
  position: relative;
  transform: translateY(0);
  transition: all 0.2s ease-in-out;
}

.micro-3d-button:hover {
  transform: translateY(-2px);
  box-shadow: 
    0px 8px 25px rgba(126, 34, 206, 0.3),
    0px 4px 12px rgba(0, 0, 0, 0.15);
}

.micro-3d-button:active {
  transform: translateY(0);
  box-shadow: 
    0px 2px 8px rgba(126, 34, 206, 0.2),
    0px 1px 4px rgba(0, 0, 0, 0.1);
}

/* 加载动画 */
@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

.loading-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* 渐入动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.6s ease-out;
}

/* 响应式设计 */
@media (max-width: 768px) {
  html {
    font-size: 14px;
  }
  
  .glass-morphism {
    border-radius: 8px;
  }
}

/* 打印样式 */
@media print {
  body {
    background: white !important;
  }
  
  .glass-morphism {
    background: white !important;
    border: 1px solid #ccc !important;
    box-shadow: none !important;
    backdrop-filter: none !important;
  }
}
