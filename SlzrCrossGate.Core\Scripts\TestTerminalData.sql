-- 测试终端数据和属性配置
-- 这个脚本用于创建测试数据来演示终端属性友好名称显示功能

-- 1. 确保有测试商户
INSERT IGNORE INTO Merchants (MerchantID, Name, IsActive, CreateTime) 
VALUES ('TEST001', '测试商户001', 1, NOW());

-- 2. 创建测试终端
INSERT IGNORE INTO Terminals (ID, MerchantID, MachineID, DeviceNO, LineNO, TerminalType, CreateTime, IsDeleted)
VALUES ('TEST_TERMINAL_001', 'TEST001', 'MACHINE001', 'DEV001', 'LINE001', 'POS', NOW(), 0);

-- 3. 创建测试终端状态（包含属性信息）
INSERT INTO TerminalStatus (ID, LastActiveTime, ActiveStatus, LoginInTime, LoginOffTime, Token, ConnectionProtocol, EndPoint, FileVersions, Properties)
VALUES ('TEST_TERMINAL_001', NOW(), 1, NOW(), NOW(), 'test_token', 'TCP', '192.168.1.100:8822', 
        '{}',
        '{"DeviceModel":"POS-2000","FirmwareVersion":"1.2.3","NetworkStatus":"正常","BatteryLevel":"85%","Temperature":"35°C","LastMaintenance":"2024-12-20 10:30:00","TransactionCount":"156","WorkMode":"营业模式"}')
ON DUPLICATE KEY UPDATE
    Properties = '{"DeviceModel":"POS-2000","FirmwareVersion":"1.2.3","NetworkStatus":"正常","BatteryLevel":"85%","Temperature":"35°C","LastMaintenance":"2024-12-20 10:30:00","TransactionCount":"156","WorkMode":"营业模式"}',
    LastActiveTime = NOW();

-- 4. 为测试商户创建终端属性字典配置
INSERT INTO MerchantDictionaries (MerchantID, DictionaryType, DictionaryCode, DictionaryLabel, DictionaryValue, SortOrder, IsActive, Description, CreateTime, Creator)
VALUES 
-- 设备信息相关
('TEST001', 'TerminalProperty', 'DeviceModel', '设备型号', '', 1, 1, '终端设备的型号信息', NOW(), 'System'),
('TEST001', 'TerminalProperty', 'DeviceSerial', '设备序列号', '', 2, 1, '终端设备的序列号', NOW(), 'System'),

-- 软件版本相关
('TEST001', 'TerminalProperty', 'FirmwareVersion', '固件版本', '', 10, 1, '终端设备的固件版本号', NOW(), 'System'),
('TEST001', 'TerminalProperty', 'AppVersion', '应用版本', '', 11, 1, '终端应用程序版本号', NOW(), 'System'),

-- 网络连接相关
('TEST001', 'TerminalProperty', 'NetworkStatus', '网络状态', '', 20, 1, '终端网络连接状态', NOW(), 'System'),
('TEST001', 'TerminalProperty', 'IPAddress', 'IP地址', '', 21, 1, '终端设备的IP地址', NOW(), 'System'),

-- 硬件状态相关
('TEST001', 'TerminalProperty', 'BatteryLevel', '电池电量', '', 30, 1, '终端设备电池电量百分比', NOW(), 'System'),
('TEST001', 'TerminalProperty', 'Temperature', '设备温度', '', 31, 1, '终端设备当前温度', NOW(), 'System'),

-- 维护相关
('TEST001', 'TerminalProperty', 'LastMaintenance', '最后维护时间', '', 40, 1, '终端设备最后一次维护的时间', NOW(), 'System'),
('TEST001', 'TerminalProperty', 'MaintenanceStatus', '维护状态', '', 41, 1, '终端设备当前的维护状态', NOW(), 'System'),

-- 业务相关
('TEST001', 'TerminalProperty', 'TransactionCount', '交易笔数', '', 50, 1, '终端设备今日交易笔数', NOW(), 'System'),
('TEST001', 'TerminalProperty', 'WorkMode', '工作模式', '', 52, 1, '终端设备当前工作模式', NOW(), 'System')

ON DUPLICATE KEY UPDATE
    DictionaryLabel = VALUES(DictionaryLabel),
    Description = VALUES(Description),
    UpdateTime = NOW();

-- 5. 显示创建的测试数据
SELECT '=== 测试商户信息 ===' as Info;
SELECT MerchantID, Name, IsActive FROM Merchants WHERE MerchantID = 'TEST001';

SELECT '=== 测试终端信息 ===' as Info;
SELECT ID, MerchantID, MachineID, DeviceNO, TerminalType FROM Terminals WHERE ID = 'TEST_TERMINAL_001';

SELECT '=== 测试终端状态 ===' as Info;
SELECT ID, ActiveStatus, ConnectionProtocol, EndPoint, Properties FROM TerminalStatus WHERE ID = 'TEST_TERMINAL_001';

SELECT '=== 测试字典配置 ===' as Info;
SELECT DictionaryCode, DictionaryLabel, Description 
FROM MerchantDictionaries 
WHERE MerchantID = 'TEST001' AND DictionaryType = 'TerminalProperty' 
ORDER BY SortOrder;
