import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Paper,
  Typography,
  Grid,
  Button,
  TextField,
  TablePagination,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
  MenuItem,
  Chip,
  Card,
  CardContent,
  FormControl,
  InputLabel,
  Select,
  Stack,
  FormControlLabel,
  Switch
} from '@mui/material';
import ResponsiveTable, {
  ResponsiveTableHead,
  ResponsiveTableBody,
  ResponsiveTableRow,
  ResponsiveTableCell
} from '../../components/ResponsiveTable';
import {
  Refresh as RefreshIcon,
  Search as SearchIcon,
  Clear as ClearIcon,
  Visibility as VisibilityIcon,
  Send as SendIcon,
  Delete as DeleteIcon,
  List as ListIcon,
  Message as MessageIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { formatDateTime, formatDateForAPI } from '../../utils/dateUtils';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { zhCN } from '@mui/x-date-pickers/locales';
import { zhCN as dateFnsZhCN } from 'date-fns/locale';
import { messageAPI, merchantAPI } from '../../services/api';
import MerchantAutocomplete from '../../components/MerchantAutocomplete';
import { useSnackbar } from 'notistack';
import { PERMISSIONS } from '../../constants/permissions';
import { useAuth } from '../../contexts/AuthContext';
import { useFeaturePermission } from '../../hooks/useFeaturePermission';
import { FeatureGuard } from '../../components/FeatureGuard';
import { parseErrorMessage } from '../../utils/errorHandler';

const MessageList = () => {
  const navigate = useNavigate();
  const { enqueueSnackbar } = useSnackbar();
  const { hasPermission } = useFeaturePermission();
  const [messages, setMessages] = useState([]);
  const [messageTypes, setMessageTypes] = useState([]);
  const [stats, setStats] = useState({ totalCount: 0, readCount: 0, unreadCount: 0, dailyStats: [] });
  const [usePreciseTime, setUsePreciseTime] = useState(false); // 是否使用精确时间选择
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);

  // 筛选条件
  const [filters, setFilters] = useState({
    merchantId: '',
    msgTypeId: '',
    machineId: '', // 出厂序列号
    deviceNo: '', // 设备编号
    isRead: '',
    startDate: null,
    endDate: null
  });

  // 选中的商户对象（用于MerchantAutocomplete）
  const [selectedMerchant, setSelectedMerchant] = useState(null);

  // 消息详情对话框
  const [openDetailDialog, setOpenDetailDialog] = useState(false);
  const [selectedMessage, setSelectedMessage] = useState(null);
  // 消息删除对话框
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);

  // 防重复请求
  const loadMessagesRef = useRef();
  const hasLoadedRef = useRef(false);

  // 加载消息列表
  const loadMessages = async (isInitialLoad = false, forceLoad = false) => {
    try {
      setLoading(true);

      // 构建查询参数
      const params = {
        page: page + 1,
        pageSize: rowsPerPage,
        ...Object.fromEntries(
          Object.entries(filters)
            .filter(([key, value]) => value !== '' && value !== null)
            .map(([key, value]) => {
              if (key === 'startDate') {
                return [key, value ? formatDateForAPI(value, true) : undefined];
              } else if (key === 'endDate') {
                return [key, value ? formatDateForAPI(value, false) : undefined];
              }
              return [key, value];
            })
        )
      };

      // 移除undefined的参数
      Object.keys(params).forEach(key => {
        if (params[key] === undefined) {
          delete params[key];
        }
      });

      // 避免重复请求：检查参数是否真的发生了变化（除非强制加载）
      const paramsString = JSON.stringify(params);
      if (!isInitialLoad && !forceLoad && loadMessagesRef.current === paramsString) {
        console.log('MessageList: 参数未变化，跳过重复请求');
        setLoading(false);
        return;
      }
      loadMessagesRef.current = paramsString;

      console.log('MessageList: 执行数据请求', params);
      const response = await messageAPI.getMessages(params);
      setMessages(response.items);
      setTotalCount(response.totalCount);
    } catch (error) {
      console.error('Error loading messages:', error);
    } finally {
      setLoading(false);
    }
  };

  // 加载消息类型列表
  const loadMessageTypes = async (merchantId = '') => {
    try {
      // 只有在提供了商户ID时才加载消息类型
      if (merchantId) {
        const params = { merchantId };
        const response = await messageAPI.getMessageTypes(params);
        setMessageTypes(response.items);
      } else {
        // 当没有提供商户ID时，清空消息类型列表
        setMessageTypes([]);
      }
    } catch (error) {
      console.error('Error loading message types:', error);
    }
  };



  // 加载消息统计
  const loadMessageStats = async () => {
    try {
      const response = await messageAPI.getMessageStats();
      setStats(response);
    } catch (error) {
      console.error('Error loading message stats:', error);
    }
  };

  // 统一的数据加载逻辑
  useEffect(() => {
    // 防止重复加载
    if (hasLoadedRef.current) {
      console.log('MessageList: 已加载过，跳过重复请求');
      return;
    }

    console.log('MessageList: 执行首次加载');
    hasLoadedRef.current = true;
    loadMessages(true, false); // 标记为初始加载，非强制加载
    loadMessageStats();
  }, []);

  // 当参数变化时重新加载
  useEffect(() => {
    if (hasLoadedRef.current) { // 只有在首次加载完成后才响应参数变化
      console.log('MessageList: 参数变化，重新加载');
      loadMessages(false, false); // 非初始加载，非强制加载
    }
  }, [page, rowsPerPage]);

  // 处理筛选条件变更
  const handleFilterChange = (event) => {
    const { name, value } = event.target;
    setFilters(prev => ({ ...prev, [name]: value }));
  };

  // 处理日期筛选变更
  const handleDateChange = (name, date) => {
    setFilters(prev => ({ ...prev, [name]: date }));
  };

  // 应用筛选
  const applyFilters = () => {
    setPage(0);
    loadMessages(false, true); // 强制执行搜索
  };

  // 清除筛选
  const clearFilters = () => {
    setFilters({
      merchantId: '',
      msgTypeId: '',
      machineId: '', // 出厂序列号
      deviceNo: '', // 设备编号
      isRead: '',
      startDate: null,
      endDate: null
    });
    setSelectedMerchant(null); // 重置商户下拉框选择状态
    setMessageTypes([]); // 直接清空消息类型列表，而不是调用loadMessageTypes()
    setPage(0);
    loadMessages(false, true); // 强制执行搜索
  };

  // 处理分页变更
  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // 打开消息详情对话框
  const openMessageDetail = (message) => {
    setSelectedMessage(message);
    setOpenDetailDialog(true);
  };

  // 打开消删除确认对话框
  const openDeleteConfirmDialog = (message) => {
    setSelectedMessage(message);
    setOpenDeleteDialog(true);
  };

  // 跳转到发送消息页面
  const goToSendMessage = () => {
    navigate('/app/messages/send');
  };

  const deleteMessage = async () => {
    if (!selectedMessage) return;

    setDeleteLoading(true);
    try {
      await messageAPI.deleteMessage(selectedMessage.id);
      setOpenDeleteDialog(false);
      enqueueSnackbar('消息删除成功', { variant: 'success' });
      loadMessages(false, true); // 强制刷新列表
    } catch (error) {
      console.error('Error deleting message:', error);
      const errorMessage = parseErrorMessage(error, '删除失败');
      enqueueSnackbar(errorMessage, { variant: 'error' });
    } finally {
      setDeleteLoading(false);
    }
  };


  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        消息管理
      </Typography>

      {/* 统计卡片 */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" color="textSecondary">
                总消息数
              </Typography>
              <Typography variant="h3">
                {stats.totalCount}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" color="textSecondary">
                已读消息
              </Typography>
              <Typography variant="h3" color="success.main">
                {stats.readCount}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" color="textSecondary">
                未读消息
              </Typography>
              <Typography variant="h3" color="error.main">
                {stats.unreadCount}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* 筛选条件 */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={6} md={2}>
            <MerchantAutocomplete
              value={selectedMerchant}
              onChange={(event, newValue) => {
                setSelectedMerchant(newValue);
                const merchantId = newValue ? newValue.merchantID : '';
                setFilters(prev => ({
                  ...prev,
                  merchantId: merchantId,
                  msgTypeId: '' // 重置消息类型选择
                }));
                // 重新加载所选商户的消息类型
                loadMessageTypes(merchantId);
              }}
              size="small"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <FormControl fullWidth size="small">
              <InputLabel>消息类型</InputLabel>
              <Select
                name="msgTypeId"
                value={filters.msgTypeId}
                onChange={handleFilterChange}
                label="消息类型"
              >
                <MenuItem value="">全部</MenuItem>
                {messageTypes.map((type) => (
                  <MenuItem key={`${type.code}-${type.merchantId}`} value={type.code}>
                    {type.code} - {type.name || '未命名'}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <TextField
              fullWidth
              label="出厂序列号"
              name="machineId"
              value={filters.machineId}
              onChange={handleFilterChange}
              size="small"
              placeholder="请输入出厂序列号"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <TextField
              fullWidth
              label="设备编号"
              name="deviceNo"
              value={filters.deviceNo}
              onChange={handleFilterChange}
              size="small"
              placeholder="请输入设备编号"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <FormControl fullWidth size="small">
              <InputLabel>状态</InputLabel>
              <Select
                name="isRead"
                value={filters.isRead}
                onChange={handleFilterChange}
                label="状态"
              >
                <MenuItem value="">全部</MenuItem>
                <MenuItem value="true">已读</MenuItem>
                <MenuItem value="false">未读</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          {/* 精确时间切换开关 */}
          <Grid item xs={12}>
            <FormControlLabel
              control={
                <Switch
                  checked={usePreciseTime}
                  onChange={(e) => setUsePreciseTime(e.target.checked)}
                  size="small"
                />
              }
              label="精确时间选择（包含小时分钟）"
            />
          </Grid>

          <Grid item xs={12} sm={6} md={2}>
            <LocalizationProvider
              dateAdapter={AdapterDateFns}
              adapterLocale={dateFnsZhCN}
              localeText={zhCN.components.MuiLocalizationProvider.defaultProps.localeText}
            >
              {usePreciseTime ? (
                <DateTimePicker
                  label="开始时间"
                  value={filters.startDate}
                  onChange={(date) => handleDateChange('startDate', date)}
                  format="yyyy/MM/dd HH:mm"
                  ampm={false} // 使用24小时制
                  slotProps={{
                    textField: {
                      fullWidth: true,
                      size: 'small'
                    }
                  }}
                />
              ) : (
                <DatePicker
                  label="开始日期"
                  value={filters.startDate}
                  onChange={(date) => handleDateChange('startDate', date)}
                  format="yyyy/MM/dd"
                  slotProps={{
                    textField: {
                      fullWidth: true,
                      size: 'small'
                    }
                  }}
                />
              )}
            </LocalizationProvider>
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <LocalizationProvider
              dateAdapter={AdapterDateFns}
              adapterLocale={dateFnsZhCN}
              localeText={zhCN.components.MuiLocalizationProvider.defaultProps.localeText}
            >
              {usePreciseTime ? (
                <DateTimePicker
                  label="结束时间"
                  value={filters.endDate}
                  onChange={(date) => handleDateChange('endDate', date)}
                  format="yyyy/MM/dd HH:mm"
                  ampm={false} // 使用24小时制
                  slotProps={{
                    textField: {
                      fullWidth: true,
                      size: 'small'
                    }
                  }}
                />
              ) : (
                <DatePicker
                  label="结束日期"
                  value={filters.endDate}
                  onChange={(date) => handleDateChange('endDate', date)}
                  format="yyyy/MM/dd"
                  slotProps={{
                    textField: {
                      fullWidth: true,
                      size: 'small'
                    }
                  }}
                />
              )}
            </LocalizationProvider>
          </Grid>
          <Grid item xs={12} sm={6} md={1}>
            <Button
              fullWidth
              variant="contained"
              color="primary"
              startIcon={<SearchIcon />}
              onClick={applyFilters}
            >
              搜索
            </Button>
          </Grid>
          <Grid item xs={12} sm={6} md={1}>
            <Button
              fullWidth
              variant="outlined"
              startIcon={<ClearIcon />}
              onClick={clearFilters}
            >
              清除
            </Button>
          </Grid>
          <Grid item xs={12} sm={6} md={1}>
            <Button
              fullWidth
              variant="outlined"
              color="primary"
              startIcon={<RefreshIcon />}
              onClick={loadMessages}
            >
              刷新
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {/* 操作按钮 - 单独放置在标题下方 */}
      <Box sx={{
        display: 'flex',
        justifyContent: 'flex-end',
        gap: 2,
        mb: 3
      }}>
        <Button
          variant="outlined"
          color="secondary" // 由 "info" 修改为 "secondary"
          startIcon={<ListIcon />}
          onClick={() => navigate('/app/messages-types')}
        >
          消息类型管理
        </Button>
        <FeatureGuard featureKey={PERMISSIONS.MESSAGE.SEND}>
          <Button
            variant="contained"
            color="secondary"
            startIcon={<MessageIcon />}
            onClick={goToSendMessage}
          >
            消息发布
          </Button>
        </FeatureGuard>
      </Box>

      {/* 消息列表 */}
      <Paper>
        <ResponsiveTable minWidth={1000} stickyActions={true}>
          <ResponsiveTableHead>
            <ResponsiveTableRow>
              <ResponsiveTableCell>ID</ResponsiveTableCell>
              <ResponsiveTableCell hideOn={['xs']}>商户</ResponsiveTableCell>
              <ResponsiveTableCell hideOn={['xs', 'sm']}>终端ID</ResponsiveTableCell>
              <ResponsiveTableCell hideOn={['xs', 'sm']}>设备编号</ResponsiveTableCell>
              <ResponsiveTableCell>消息类型</ResponsiveTableCell>
              <ResponsiveTableCell>内容</ResponsiveTableCell>
              <ResponsiveTableCell>状态</ResponsiveTableCell>
              <ResponsiveTableCell hideOn={['xs']}>发送时间</ResponsiveTableCell>
              <ResponsiveTableCell hideOn={['xs']}>读取时间</ResponsiveTableCell>
              <ResponsiveTableCell hideOn={['xs', 'sm']}>操作人</ResponsiveTableCell>
              <ResponsiveTableCell sticky={true} minWidth={80}>操作</ResponsiveTableCell>
            </ResponsiveTableRow>
          </ResponsiveTableHead>
          <ResponsiveTableBody>
            {loading ? (
              <ResponsiveTableRow>
                <ResponsiveTableCell colSpan={11} align="center">
                  <CircularProgress size={24} />
                </ResponsiveTableCell>
              </ResponsiveTableRow>
            ) : messages.length === 0 ? (
              <ResponsiveTableRow>
                <ResponsiveTableCell colSpan={11} align="center">
                  没有找到消息
                </ResponsiveTableCell>
              </ResponsiveTableRow>
            ) : (
              messages.map((message) => (
                <ResponsiveTableRow key={message.id}>
                  <ResponsiveTableCell>
                    <Box>
                      <Typography variant="body2" fontWeight="bold">
                        {message.id}
                      </Typography>
                      {/* 在小屏幕上显示商户信息 */}
                      <Typography variant="caption" color="textSecondary" sx={{ display: { xs: 'block', sm: 'none' } }}>
                        {message.merchantName}
                      </Typography>
                    </Box>
                  </ResponsiveTableCell>
                  <ResponsiveTableCell hideOn={['xs']}>
                    <Tooltip title={message.merchantID || ''}>
                      <span>{message.merchantName || message.merchantID}</span>
                    </Tooltip>
                  </ResponsiveTableCell>
                  <ResponsiveTableCell hideOn={['xs', 'sm']}>{message.terminalID}</ResponsiveTableCell>
                  <ResponsiveTableCell hideOn={['xs', 'sm']}>{message.terminalDeviceNO || '-'}</ResponsiveTableCell>
                  <ResponsiveTableCell>
                    <Box>
                      <Typography variant="body2">
                        {message.msgTypeName || message.msgTypeID || '-'}
                      </Typography>
                      {/* 在小屏幕上显示终端信息 */}
                      <Typography variant="caption" color="textSecondary" sx={{ display: { xs: 'block', md: 'none' } }}>
                        终端: {message.terminalDeviceNO || message.terminalID}
                      </Typography>
                    </Box>
                  </ResponsiveTableCell>
                  <ResponsiveTableCell>
                    <Box>
                      <Typography variant="body2">
                        {message.content ? (
                          message.content.length > 30
                            ? `${message.content.substring(0, 30)}...`
                            : message.content
                        ) : '-'}
                      </Typography>
                      {/* 在小屏幕上显示时间和操作人 */}
                      <Typography variant="caption" color="textSecondary" sx={{ display: { xs: 'block', sm: 'none' } }}>
                        {formatDateTime(message.createTime)} | {message.operator || '系统'}
                      </Typography>
                    </Box>
                  </ResponsiveTableCell>
                  <ResponsiveTableCell>
                    <Chip
                      label={message.isRead ? "已读" : "未读"}
                      color={message.isRead ? "success" : "error"}
                      size="small"
                    />
                  </ResponsiveTableCell>
                  <ResponsiveTableCell hideOn={['xs']}>{formatDateTime(message.createTime)}</ResponsiveTableCell>
                  <ResponsiveTableCell hideOn={['xs']}>{formatDateTime(message.readTime)}</ResponsiveTableCell>
                  <ResponsiveTableCell hideOn={['xs', 'sm']}>{message.operator || '-'}</ResponsiveTableCell>
                  <ResponsiveTableCell sticky={true}>
                    <Tooltip title="查看详情">
                      <IconButton
                        size="small"
                        color="primary"
                        onClick={() => openMessageDetail(message)}
                      >
                        <VisibilityIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                    {hasPermission(PERMISSIONS.MESSAGE.DELETE) && (
                      <Tooltip title="删除">
                        <IconButton
                          size="small"
                          color="error"
                          onClick={() => openDeleteConfirmDialog(message)}
                        >
                          <DeleteIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    )}
                  </ResponsiveTableCell>
                </ResponsiveTableRow>
              ))
            )}
          </ResponsiveTableBody>
        </ResponsiveTable>
        <TablePagination
          rowsPerPageOptions={[5, 10, 25, 50]}
          component="div"
          count={totalCount}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
          labelRowsPerPage="每页行数:"
          labelDisplayedRows={({ from, to, count }) => `${from}-${to} 共 ${count}`}
        />
      </Paper>

      {/* 消息详情对话框 */}
      <Dialog open={openDetailDialog} onClose={() => setOpenDetailDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>消息详情</DialogTitle>
        <DialogContent>
          {selectedMessage && (
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="textSecondary">消息ID</Typography>
                <Typography variant="body1">{selectedMessage.id}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="textSecondary">商户ID</Typography>
                <Typography variant="body1">{selectedMessage.merchantID}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="textSecondary">终端ID</Typography>
                <Typography variant="body1">{selectedMessage.terminalID}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="textSecondary">终端设备号</Typography>
                <Typography variant="body1">{selectedMessage.terminalDeviceNO || '-'}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="textSecondary">消息类型</Typography>
                <Typography variant="body1">{selectedMessage.msgTypeName || selectedMessage.msgTypeID || '-'}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="textSecondary">状态</Typography>
                <Box sx={{ mt: 0.5 }}>
                  <Chip
                    label={selectedMessage.isRead ? "已读" : "未读"}
                    color={selectedMessage.isRead ? "success" : "error"}
                    size="small"
                  />
                </Box>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="textSecondary">发送时间</Typography>
                <Typography variant="body1">{formatDateTime(selectedMessage.createTime)}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="textSecondary">操作人</Typography>
                <Typography variant="body1">{selectedMessage.operator || '-'}</Typography>
              </Grid>
              <Grid item xs={12}>
                <Typography variant="subtitle2" color="textSecondary">消息内容</Typography>
                <Paper variant="outlined" sx={{ p: 2, mt: 1 }}>
                  <Typography variant="body1">{selectedMessage.content || '-'}</Typography>
                </Paper>
              </Grid>
              {selectedMessage.isRead && selectedMessage.readTime && (
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="textSecondary">读取时间</Typography>
                  <Typography variant="body1">{formatDateTime(selectedMessage.readTime)}</Typography>
                </Grid>
              )}
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDetailDialog(false)}>关闭</Button>
        </DialogActions>
      </Dialog>

      {/* 删除确认对话框 */}
      <Dialog open={openDeleteDialog} onClose={() => setOpenDeleteDialog(false)}>
        <DialogTitle>确认删除</DialogTitle>
        <DialogContent>
          {selectedMessage && ( 
          <Typography>
            确定要删除消息(ID:{selectedMessage.id}) 吗？此操作不可撤销。
          </Typography>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDeleteDialog(false)}>取消</Button>
          <Button
            onClick={deleteMessage}
            variant="contained"
            color="error"
            disabled={deleteLoading}
          >
            {deleteLoading ? <CircularProgress size={24} /> : '删除'}
          </Button>
        </DialogActions>
      </Dialog>

    </Box>
  );
};

export default MessageList;
