using Microsoft.Extensions.Logging;
using SlzrCrossGate.Common;
using SlzrCrossGate.Core.Models;
using SlzrCrossGate.Core.Service;
using SlzrCrossGate.Core.Services.BusinessServices;
using SlzrCrossGate.Tcp.Protocol;
using System.Text;

namespace SlzrCrossGate.Tcp.Handler
{
    [MessageType(Iso8583MessageType.MsgRequest)]
    public class FetchMsgMessageHandler : IIso8583MessageHandler
    {
        private readonly ILogger<FetchMsgMessageHandler> _logger;
        private readonly Iso8583Schema _schema;
        private readonly MsgBoxService _msgBoxService;
        private readonly TerminalManager _terminalManager;

        public FetchMsgMessageHandler(ILogger<FetchMsgMessageHandler> logger, Iso8583Schema schema, MsgBoxService msgBoxService, TerminalManager terminalManager)
        {
            _logger = logger;
            _schema = schema;
            _msgBoxService = msgBoxService;
            _terminalManager = terminalManager;
        }

        public async Task<Iso8583Message> HandleMessageAsync(TcpConnectionContext context, Iso8583Message message)
        {
            var msg = await _msgBoxService.GetFirstUnreadMessagesAsync(message.TerimalID, message.MerchantID);

            var response = new Iso8583Message(_schema, Iso8583MessageType.MsgResponse);
            //response.SetField(3, "805001");

            if (msg == null) {
                response.SetField(39, "0010");
                response.Error("0010", "No message found");
                
                //清空未读消息数量
                _terminalManager.ClearUnreadMessageCount(message.TerimalID);
                _logger.LogInformation("No message found for terminal {TerminalID}, MerchantID={MerchantID},clear unread message count", message.TerimalID, message.MerchantID);
                return response;
            } 


            var body = string.Empty;
            if (msg.CodeType == MessageCodeType.ASCII)
            {
                body = DataConvert.BytesToHex(Encoding.Default.GetBytes(msg.Content));
            }
            else {
                body = msg.Content;
            }
            var msgStr = msg.MsgTypeID + msg.ID.ToString("X2").PadLeft(8, '0') + body;
            //加上长度 BCD格式
            msgStr = (msgStr.Length / 2).ToString().PadLeft(4, '0') + msgStr;


            response.SetField(51, msgStr);
            response.Ok();

            if (msg != null) await _msgBoxService.MarkMessageAsReadAsync(message.TerimalID, msg.ID);

            return response;
        }


    }
}
