import React, { useState, useEffect, useRef } from 'react';
import {
  Container,
  Box,
  Typography,
  Paper,
  Button,
  Chip,
  CircularProgress,
  Grid,
  Card,
  CardContent,
  FormControlLabel,
  Switch,
  Alert,
  Divider,
  Backdrop,
  Collapse,
  IconButton,
  FormGroup,
  TextField,
  InputAdornment,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions
} from '@mui/material';
import {
  Save as SaveIcon,
  Refresh as RefreshIcon,
  Settings as SettingsIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Search as SearchIcon,
  FilterList as FilterIcon
} from '@mui/icons-material';
import { useSnackbar } from 'notistack';
import { permissionAPI } from '../../services/api';
import { NoPermissionPage } from '../../components/FeatureGuard';
import { useFeaturePermission } from '../../hooks/useFeaturePermission';
import { PERMISSIONS } from '../../constants/permissions';
import { getIconComponent } from '../../utils/iconMapping';

// 获取分类图标 - 与菜单系统保持一致
const getCategoryIcon = (category) => {
  // 分类到菜单图标的映射（基于MenuService.cs中的配置）
  const categoryToIconMap = {
    // 终端管理相关
    '终端管理': 'SmartphoneIcon',
    '终端事件': 'SmartphoneIcon',
    '终端记录': 'SmartphoneIcon',
    '终端日志记录': 'ActivityIcon',

    // 文件管理相关 - 对应"内容管理"菜单组
    '文件类型': 'FileTextIcon',
    '文件版本': 'ArchiveIcon',
    '发布记录': 'SendIcon',
    '文件管理': 'FolderIcon',

    // 消息管理相关
    '消息类型': 'TagIcon',
    '消息管理': 'MessageSquareIcon',

    // 业务配置相关
    '线路参数': 'CreditCardIcon',
    '银联密钥': 'DatabaseIcon',
    '票价折扣方案': 'PercentIcon',

    // 商户管理相关
    '商户管理': 'ShoppingBagIcon',

    // 系统管理相关
    '用户管理': 'UsersIcon',
    '角色管理': 'LockIcon',
    '功能权限管理': 'ShieldIcon',
    '系统设置': 'SettingsIcon',
    '安全管理': 'ShieldIcon'
  };

  const iconName = categoryToIconMap[category] || 'SettingsIcon';
  const IconComponent = getIconComponent(iconName);

  return IconComponent ? <IconComponent size={20} /> : <SettingsIcon />;
};

// 获取分类颜色
const getCategoryColor = (category) => {
  const colorMap = {
    '用户管理': 'primary',
    '商户管理': 'secondary',
    '终端管理': 'success',
    '文件管理': 'warning',
    '系统设置': 'info',
    '安全管理': 'error'
  };
  return colorMap[category] || 'default';
};

const FeaturePermissionManagement = () => {
  const { enqueueSnackbar } = useSnackbar();

  // 临时简化权限检查，避免无限循环
  const canManagePermissions = true; // 临时设为 true 进行测试

  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [featureConfigs, setFeatureConfigs] = useState([]);
  const [groupedConfigs, setGroupedConfigs] = useState({});
  const [changes, setChanges] = useState({});
  const [initialized, setInitialized] = useState(false);

  // 防重复请求
  const hasLoadedRef = useRef(false);
  const [expandedConfigs, setExpandedConfigs] = useState({});
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');

  // 确认对话框状态
  const [confirmDialog, setConfirmDialog] = useState({
    open: false,
    type: '', // 'rolePermission' 或 'featureToggle'
    featureKey: '',
    roleName: '', // 仅用于角色权限类型
    field: '', // 仅用于功能开关类型
    newValue: null,
    title: '',
    message: ''
  });

  // 筛选配置数据
  const filteredGroupedConfigs = React.useMemo(() => {
    if (!searchTerm && selectedCategory === 'all') {
      return groupedConfigs;
    }

    const filtered = {};
    Object.entries(groupedConfigs).forEach(([category, configs]) => {
      // 分类筛选
      if (selectedCategory !== 'all' && category !== selectedCategory) {
        return;
      }

      // 搜索筛选
      const filteredConfigs = configs.filter(config =>
        config.featureName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        config.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        config.featureKey.toLowerCase().includes(searchTerm.toLowerCase())
      );

      if (filteredConfigs.length > 0) {
        filtered[category] = filteredConfigs;
      }
    });

    return filtered;
  }, [groupedConfigs, searchTerm, selectedCategory]);

  // 获取功能权限配置数据 - 使用简单的异步函数，不使用 useCallback
  const fetchFeatureConfigs = async () => {
    if (loading) return; // 防止重复调用

    try {
      setLoading(true);
      console.log('开始获取功能权限配置数据...');

      const response = await permissionAPI.getFeatureConfigs();
      console.log('API Response:', response);

      // 检查响应格式
      if (response && response.data && Array.isArray(response.data)) {
        console.log('设置功能配置数据:', response.data);
        console.log('第一个配置项的结构:', response.data[0]);
        setFeatureConfigs(response.data);
        setChanges({});
      } else if (Array.isArray(response)) {
        // 如果直接返回数组
        console.log('直接返回数组格式，设置功能配置数据:', response);
        console.log('第一个配置项的结构:', response[0]);
        setFeatureConfigs(response);
        setChanges({});
      } else {
        console.warn('API响应数据格式不正确:', response);
        setFeatureConfigs([]);
      }
    } catch (error) {
      console.error('获取功能权限配置失败:', error);
      enqueueSnackbar('获取功能权限配置失败', { variant: 'error' });
      setFeatureConfigs([]);
    } finally {
      setLoading(false);
    }
  };

  // 按分类分组数据，保持数据库中的排序
  const groupConfigsByCategory = (configs) => {
    if (!Array.isArray(configs) || configs.length === 0) {
      return {};
    }

    // 先按照后端返回的顺序（已经按Category和SortOrder排序）进行分组
    const grouped = configs.reduce((acc, config) => {
      const category = config.category || '其他';
      if (!acc[category]) {
        acc[category] = [];
      }
      acc[category].push(config);
      return acc;
    }, {});

    // 确保每个分组内的配置按SortOrder排序（后端已排序，但保险起见）
    Object.keys(grouped).forEach(category => {
      grouped[category].sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0));
    });

    console.log('按分类分组的配置（保持数据库排序）:', grouped);
    return grouped;
  };

  // 初始化数据 - 只在组件挂载时执行一次
  useEffect(() => {
    // 防止重复加载
    if (hasLoadedRef.current) {
      console.log('FeaturePermissionManagement: 已加载过，跳过重复请求');
      return;
    }

    if (canManagePermissions && !initialized) {
      console.log('FeaturePermissionManagement: 执行首次加载');
      hasLoadedRef.current = true;
      setInitialized(true);
      fetchFeatureConfigs();
    }
  }, [canManagePermissions, initialized]);

  // 当 featureConfigs 变化时重新分组
  useEffect(() => {
    const grouped = groupConfigsByCategory(featureConfigs);
    setGroupedConfigs(grouped);
  }, [featureConfigs]);

  // 处理权限开关变化
  const handlePermissionChange = (configId, field, value) => {
    console.log('权限变化:', { configId, field, value });
    console.log('当前 changes:', changes);

    // 特殊保护：功能权限管理的"启用功能"开关
    if (configId === 'feature_permission.save_changes' && field === 'isGloballyEnabled' && value === false) {
      setConfirmDialog({
        open: true,
        type: 'featureToggle',
        featureKey: configId,
        field: field,
        newValue: value,
        title: '⚠️ 极度危险操作确认',
        message: '您即将关闭功能权限管理功能！一旦关闭，将无法通过界面重新开启，这是一个极度危险的操作，确定要继续吗？'
      });
      return;
    }

    setChanges(prev => {
      const newChanges = {
        ...prev,
        [configId]: {
          ...prev[configId],
          [field]: value
        }
      };
      console.log('新的 changes:', newChanges);
      return newChanges;
    });
  };

  // 获取角色权限的当前值
  const getRolePermissionValue = (configKey, roleName) => {
    // 首先检查是否有变更
    const configChanges = changes[configKey];
    if (configChanges && configChanges.rolePermissions && configChanges.rolePermissions.hasOwnProperty(roleName)) {
      return configChanges.rolePermissions[roleName];
    }

    // 如果没有变更，返回原始值
    const originalConfig = featureConfigs.find(c => c.featureKey === configKey);
    const originalPermissions = originalConfig?.rolePermissions || {};
    return originalPermissions[roleName];
  };

  // 更新角色权限（带安全保护）
  const updateRolePermission = (configKey, roleName, value) => {
    console.log(`更新角色权限: [${configKey}][${roleName}] = ${value}`);

    // 特殊保护：功能权限管理的SystemAdmin权限
    if (configKey === 'feature_permission.save_changes' && roleName === 'SystemAdmin' && value !== true) {
      setConfirmDialog({
        open: true,
        type: 'rolePermission',
        featureKey: configKey,
        roleName: roleName,
        newValue: value,
        title: '⚠️ 危险操作确认',
        message: '您即将取消系统管理员的功能权限管理权限！这将导致您无法再修改任何权限设置。确定要继续吗？'
      });
      return;
    }

    // 执行权限更新
    performRolePermissionUpdate(configKey, roleName, value);
  };

  // 实际执行权限更新的函数
  const performRolePermissionUpdate = (configKey, roleName, value) => {
    setChanges(prevChanges => {
      const currentConfig = prevChanges[configKey] || {};
      const currentRolePermissions = currentConfig.rolePermissions || {};

      const newChanges = {
        ...prevChanges,
        [configKey]: {
          ...currentConfig,
          rolePermissions: {
            ...currentRolePermissions,
            [roleName]: value
          }
        }
      };

      console.log('更新后的完整changes:', newChanges);
      return newChanges;
    });
  };

  // 确认对话框处理
  const handleConfirmDialogClose = (confirmed) => {
    if (confirmed) {
      if (confirmDialog.type === 'rolePermission') {
        // 角色权限更新
        performRolePermissionUpdate(
          confirmDialog.featureKey,
          confirmDialog.roleName,
          confirmDialog.newValue
        );
      } else if (confirmDialog.type === 'featureToggle') {
        // 功能开关更新
        setChanges(prev => {
          const newChanges = {
            ...prev,
            [confirmDialog.featureKey]: {
              ...prev[confirmDialog.featureKey],
              [confirmDialog.field]: confirmDialog.newValue
            }
          };
          return newChanges;
        });
      }
    }

    // 关闭对话框
    setConfirmDialog({
      open: false,
      type: '',
      featureKey: '',
      roleName: '',
      field: '',
      newValue: null,
      title: '',
      message: ''
    });
  };

  // 切换配置项的展开状态
  const toggleConfigExpanded = (configId) => {
    setExpandedConfigs(prev => ({
      ...prev,
      [configId]: !prev[configId]
    }));
  };

  // 保存更改
  const handleSave = async () => {
    try {
      setSaving(true);

      // 构建符合后端期望的数据结构
      const updates = Object.entries(changes).map(([featureKey, changeData]) => {
        // 找到原始配置
        const originalConfig = featureConfigs.find(config => config.featureKey === featureKey);

        // 处理权限控制逻辑
        let isGloballyEnabled = changeData.isGloballyEnabled ?? originalConfig?.isGloballyEnabled ?? false;

        // 修复角色权限合并逻辑
        let rolePermissions = {};

        // 先获取原始权限
        if (originalConfig?.rolePermissions) {
          rolePermissions = { ...originalConfig.rolePermissions };
        }

        // 然后应用变更的权限
        if (changeData.rolePermissions) {
          rolePermissions = { ...rolePermissions, ...changeData.rolePermissions };
        }

        // 如果用户禁用了功能，清空所有角色权限
        if (changeData.hasOwnProperty('isGloballyEnabled') && !changeData.isGloballyEnabled) {
          rolePermissions = {};
        }

        // 如果用户设置了requiresPermission为false，清空所有角色权限（表示不需要权限控制）
        if (changeData.hasOwnProperty('requiresPermission') && !changeData.requiresPermission) {
          rolePermissions = {};
        }

        console.log(`构建更新数据 [${featureKey}]:`, {
          originalRolePermissions: originalConfig?.rolePermissions,
          changeDataRolePermissions: changeData.rolePermissions,
          finalRolePermissions: rolePermissions
        });

        return {
          featureKey: featureKey,
          isGloballyEnabled: isGloballyEnabled,
          rolePermissions: rolePermissions
        };
      });

      if (updates.length === 0) {
        enqueueSnackbar('没有需要保存的更改', { variant: 'info' });
        return;
      }

      console.log('发送到后端的更新数据:', updates);
      await permissionAPI.batchUpdatePermissions(updates);
      enqueueSnackbar('功能权限配置保存成功', { variant: 'success' });

      setChanges({});
      await fetchFeatureConfigs();
    } catch (error) {
      console.error('保存功能权限配置失败:', error);
      enqueueSnackbar('保存功能权限配置失败', { variant: 'error' });
    } finally {
      setSaving(false);
    }
  };

  // 刷新配置
  const handleRefresh = () => {
    setChanges({});
    fetchFeatureConfigs();
  };

  // 刷新权限缓存
  const handleRefreshCache = async () => {
    try {
      await permissionAPI.refreshCache();
      enqueueSnackbar('权限缓存已刷新', { variant: 'success' });
    } catch (error) {
      console.error('刷新权限缓存失败:', error);
      enqueueSnackbar('刷新权限缓存失败', { variant: 'error' });
    }
  };

  if (!canManagePermissions) {
    return <NoPermissionPage />;
  }

  if (loading) {
    return (
      <Backdrop open={true} sx={{ color: '#fff', zIndex: (theme) => theme.zIndex.drawer + 1 }}>
        <CircularProgress color="inherit" />
      </Backdrop>
    );
  }

  return (
    <Container maxWidth={false}>
      <Box sx={{ pt: 3, pb: 10 }}> {/* 增加底部padding为固定按钮留空间 */}
        {/* 页面标题 */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h4" component="h1" sx={{ fontWeight: 'bold' }}>
            功能权限管理
          </Typography>
          <Box sx={{ display: 'flex', gap: 2 }}>
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={handleRefresh}
              disabled={saving}
            >
              刷新配置
            </Button>
            <Button
              variant="outlined"
              onClick={handleRefreshCache}
              startIcon={<RefreshIcon />}
              disabled={saving}
            >
              刷新缓存
            </Button>
          </Box>
        </Box>

        {/* 搜索和筛选 */}
        <Paper sx={{ p: 2, mb: 3 }}>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                placeholder="搜索功能权限..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
                size="small"
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <FormControl fullWidth size="small">
                <InputLabel>分类筛选</InputLabel>
                <Select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  label="分类筛选"
                  startAdornment={<FilterIcon sx={{ mr: 1, color: 'text.secondary' }} />}
                >
                  <MenuItem value="all">全部分类</MenuItem>
                  {Object.entries(groupedConfigs)
                    .sort(([, configsA], [, configsB]) => {
                      // 按照每个分组中第一个配置的sortOrder排序分组
                      const sortOrderA = configsA[0]?.sortOrder || 0;
                      const sortOrderB = configsB[0]?.sortOrder || 0;
                      return sortOrderA - sortOrderB;
                    })
                    .map(([category]) => (
                      <MenuItem key={category} value={category}>
                        {getCategoryIcon(category)} {category}
                      </MenuItem>
                    ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={2}>
              <Typography variant="body2" color="text.secondary">
                共 {Object.values(filteredGroupedConfigs).reduce((sum, configs) => sum + configs.length, 0)} 项
              </Typography>
            </Grid>
          </Grid>
        </Paper>

        {/* 变更提示 */}
        {Object.keys(changes).length > 0 && (
          <Alert severity="info" sx={{ mb: 3 }}>
            您有 {Object.keys(changes).length} 项未保存的更改
          </Alert>
        )}

        {/* 主要内容 */}
        {Object.keys(filteredGroupedConfigs).length === 0 ? (
          <Paper sx={{ p: 4, textAlign: 'center' }}>
            <SettingsIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h6" color="text.secondary" gutterBottom>
              暂无功能权限配置数据
            </Typography>
            <Typography variant="body2" color="text.secondary">
              系统中还没有配置任何功能权限，请联系管理员进行配置
            </Typography>
          </Paper>
        ) : (
          <Grid container spacing={3}>
            {Object.entries(filteredGroupedConfigs)
              .sort(([, configsA], [, configsB]) => {
                // 按照每个分组中第一个配置的sortOrder排序分组
                const sortOrderA = configsA[0]?.sortOrder || 0;
                const sortOrderB = configsB[0]?.sortOrder || 0;
                return sortOrderA - sortOrderB;
              })
              .map(([category, configs]) => (
              <Grid item xs={12} key={category}>
                <Card>
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      {getCategoryIcon(category)}
                      <Typography variant="h6" sx={{ ml: 1, fontWeight: 'bold' }}>
                        {category}
                      </Typography>
                      <Chip
                        label={`${configs.length} 项`}
                        size="small"
                        color={getCategoryColor(category)}
                        sx={{ ml: 2 }}
                      />
                    </Box>
                    <Divider sx={{ mb: 2 }} />

                    {/* 响应式网格布局 */}
                    <Grid container spacing={2}>
                      {configs.map((config) => (
                        <Grid
                          item
                          xs={12}      // 手机：1列
                          sm={6}       // 平板：2列
                          md={4}       // 桌面：3列
                          lg={3}       // 大屏：4列
                          key={config.featureKey}
                        >
                        <Paper
                          variant="outlined"
                          sx={{
                            p: 2,
                            height: '100%', // 确保同一行的卡片高度一致
                            display: 'flex',
                            flexDirection: 'column',
                            border: changes[config.featureKey] ? '2px solid' : '1px solid',
                            borderColor: changes[config.featureKey] ? 'warning.main' : 'divider',
                            '&:hover': {
                              boxShadow: 2
                            }
                          }}
                        >
                            <Typography
                              variant="subtitle2"
                              fontWeight="bold"
                              gutterBottom
                              sx={{
                                lineHeight: 1.3,
                                minHeight: '3rem', // 调整高度以适应标准字体大小
                                display: 'flex',
                                alignItems: 'center'
                              }}
                            >
                              {config.featureName}
                            </Typography>
                            <Typography
                              variant="body2"
                              color="text.secondary"
                              sx={{
                                mb: 2,
                                lineHeight: 1.4,
                                minHeight: '3.2rem', // 调整高度以适应标准字体大小
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                display: '-webkit-box',
                                WebkitLineClamp: 2,
                                WebkitBoxOrient: 'vertical'
                              }}
                            >
                              {config.description}
                            </Typography>

                            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5, flex: 1 }}>
                              <FormControlLabel
                                control={
                                  <Switch
                                    checked={changes[config.featureKey]?.isGloballyEnabled ?? config.isGloballyEnabled}
                                    onChange={(e) => handlePermissionChange(config.featureKey, 'isGloballyEnabled', e.target.checked)}
                                    color="primary"
                                    size="small"
                                  />
                                }
                                label="启用功能"
                                sx={{ mb: 0.5 }}
                              />

                              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                                <FormControlLabel
                                  control={
                                    <Switch
                                      checked={changes[config.featureKey]?.requiresPermission ?? (config.rolePermissions && Object.keys(config.rolePermissions).length > 0)}
                                      onChange={(e) => handlePermissionChange(config.featureKey, 'requiresPermission', e.target.checked)}
                                      color="secondary"
                                      size="small"
                                      disabled={!(changes[config.featureKey]?.isGloballyEnabled ?? config.isGloballyEnabled)}
                                    />
                                  }
                                  label="需要权限"
                                />

                                {(changes[config.featureKey]?.requiresPermission ?? (config.rolePermissions && Object.keys(config.rolePermissions).length > 0)) && (
                                  <IconButton
                                    size="small"
                                    onClick={() => toggleConfigExpanded(config.featureKey)}
                                    sx={{ ml: 0.5, p: 0.5 }}
                                  >
                                    {expandedConfigs[config.featureKey] ? <ExpandLessIcon fontSize="small" /> : <ExpandMoreIcon fontSize="small" />}
                                  </IconButton>
                                )}
                              </Box>

                              {/* 角色权限配置 */}
                              <Collapse in={expandedConfigs[config.featureKey] && (changes[config.featureKey]?.requiresPermission ?? (config.rolePermissions && Object.keys(config.rolePermissions).length > 0))}>
                                <Box sx={{ mt: 1.5, p: 1.5, bgcolor: 'background.default', borderRadius: 1 }}>
                                  <Typography
                                    variant="caption"
                                    color="text.secondary"
                                    sx={{
                                      mb: 1,
                                      display: 'block'
                                    }}
                                  >
                                    角色权限设置（开启表示允许访问，关闭表示禁止访问）
                                  </Typography>
                                  <FormGroup sx={{ gap: 0.5 }}>
                                    {['SystemAdmin', 'MerchantAdmin', 'User'].map((roleName) => {
                                      // 使用专门的函数获取角色权限状态
                                      const isEnabled = getRolePermissionValue(config.featureKey, roleName);

                                      return (
                                        <FormControlLabel
                                          key={roleName}
                                          control={
                                            <Switch
                                              checked={isEnabled === true}
                                              onChange={() => {
                                                // 简化的二状态切换逻辑：允许 ↔ 禁止
                                                const currentValue = getRolePermissionValue(config.featureKey, roleName);
                                                const newValue = currentValue === true ? null : true;

                                                // 更新状态
                                                updateRolePermission(config.featureKey, roleName, newValue);
                                              }}
                                              size="small"
                                            />
                                          }
                                          label={
                                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, width: '100%' }}>
                                              <Typography
                                                variant="body2"
                                                sx={{
                                                  flex: 1,
                                                  minWidth: 0
                                                }}
                                              >
                                                {roleName}
                                              </Typography>
                                              {isEnabled === true ? (
                                                <Chip
                                                  label="允许"
                                                  size="small"
                                                  color="success"
                                                />
                                              ) : (
                                                <Chip
                                                  label="禁止"
                                                  size="small"
                                                  variant="outlined"
                                                  color="default"
                                                />
                                              )}
                                            </Box>
                                          }
                                        />
                                      );
                                    })}
                                  </FormGroup>
                                </Box>
                              </Collapse>
                            </Box>
                          </Paper>
                        </Grid>
                      ))}
                    </Grid>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        )}
      </Box>

      {/* 固定的保存按钮 */}
      {Object.keys(changes).length > 0 && (
        <Box
          sx={{
            position: 'fixed',
            bottom: 20,
            right: 20,
            zIndex: 1000,
            display: 'flex',
            gap: 2,
            alignItems: 'center'
          }}
        >
          <Alert
            severity="info"
            sx={{
              display: 'flex',
              alignItems: 'center',
              '& .MuiAlert-message': { py: 0 }
            }}
          >
            {Object.keys(changes).length} 项未保存的更改
          </Alert>
          <Button
            variant="contained"
            startIcon={<SaveIcon />}
            onClick={handleSave}
            disabled={saving}
            size="large"
            sx={{
              boxShadow: 3,
              '&:hover': {
                boxShadow: 6
              }
            }}
          >
            {saving ? '保存中...' : '保存更改'}
          </Button>
        </Box>
      )}

      {/* 确认对话框 */}
      <Dialog
        open={confirmDialog.open}
        onClose={() => handleConfirmDialogClose(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle sx={{ color: 'error.main' }}>
          {confirmDialog.title}
        </DialogTitle>
        <DialogContent>
          <DialogContentText>
            {confirmDialog.message}
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => handleConfirmDialogClose(false)}
            color="primary"
          >
            取消
          </Button>
          <Button
            onClick={() => handleConfirmDialogClose(true)}
            color="error"
            variant="contained"
          >
            确认继续
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default FeaturePermissionManagement;