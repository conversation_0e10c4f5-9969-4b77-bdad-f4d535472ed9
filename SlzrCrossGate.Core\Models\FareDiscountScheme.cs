using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace SlzrCrossGate.Core.Models
{
    /// <summary>
    /// 票价折扣方案表，用于统一管理票价参数配置方案
    /// </summary>
    public class FareDiscountScheme : ITenantEntity
    {
        /// <summary>
        /// 自增ID（主键）
        /// </summary>
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int ID { get; set; }

        /// <summary>
        /// 商户ID
        /// </summary>
        [Required]
        [MaxLength(8)]
        public required string MerchantID { get; set; }

        /// <summary>
        /// 方案名称
        /// </summary>
        [Required]
        [MaxLength(100)]
        public required string SchemeName { get; set; }

        /// <summary>
        /// 方案编码，用于程序识别
        /// </summary>
        [Required]
        [MaxLength(50)]
        public required string SchemeCode { get; set; }

        /// <summary>
        /// 方案描述
        /// </summary>
        [MaxLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// 线路额外参数JSON字符串，包含如差额换乘时间、定额换乘时间、是否支持换乘等参数
        /// </summary>
        [Column(TypeName = "text")]
        public string? ExtraParamsJson { get; set; }

        /// <summary>
        /// 卡类参数信息JSON字符串，包含各卡类的折扣、播报语音等参数
        /// </summary>
        [Column(TypeName = "text")]
        public string? CardDiscountInfoJson { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdateTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 创建者
        /// </summary>
        [MaxLength(50)]
        public string? Creator { get; set; }

        /// <summary>
        /// 更新者
        /// </summary>
        [MaxLength(50)]
        public string? Updater { get; set; }

        /// <summary>
        /// 使用次数统计（每次创建版本时使用该方案会增加此计数）
        /// </summary>
        public int UsageCount { get; set; } = 0;

        /// <summary>
        /// 最后使用时间
        /// </summary>
        public DateTime? LastUsedTime { get; set; }

        /// <summary>
        /// 当前最新版本号，4位16进制数，从0001开始
        /// </summary>
        [MaxLength(4)]
        public string? CurrentVersion { get; set; }

        /// <summary>
        /// 当前文件参数，用于生成文件时的para字段，默认为方案编号
        /// </summary>
        [MaxLength(50)]
        public string CurrentFilePara { get; set; } = string.Empty;

        /// <summary>
        /// 当前版本对应的文件版本ID，关联FileVer表
        /// </summary>
        public int? CurrentFileVerID { get; set; }

        /// <summary>
        /// 商户关系引用
        /// </summary>
        [ForeignKey("MerchantID")]
        public Merchant? Merchant { get; set; }
    }
}
