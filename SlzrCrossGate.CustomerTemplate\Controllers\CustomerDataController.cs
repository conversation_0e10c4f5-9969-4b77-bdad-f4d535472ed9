using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SlzrCrossGate.CustomerTemplate.Models;
using SlzrCrossGate.CustomerTemplate.Services;
using System.Security.Claims;

namespace SlzrCrossGate.CustomerTemplate.Controllers
{
    /// <summary>
    /// 客户数据控制器 - 处理客户专属数据操作
    /// </summary>
    [ApiController]
    [Route("api/customa/[controller]")]
    [Authorize]
    public class CustomerDataController : ControllerBase
    {
        private readonly CustomerDataService _customerDataService;
        private readonly AuthService _authService;
        private readonly ILogger<CustomerDataController> _logger;

        public CustomerDataController(
            CustomerDataService customerDataService,
            AuthService authService,
            ILogger<CustomerDataController> logger)
        {
            _customerDataService = customerDataService;
            _authService = authService;
            _logger = logger;
        }

        /// <summary>
        /// 获取客户数据列表
        /// </summary>
        /// <param name="dataType">数据类型筛选</param>
        /// <param name="page">页码</param>
        /// <param name="pageSize">页大小</param>
        /// <returns>客户数据列表</returns>
        [HttpGet]
        public async Task<ActionResult<CustomerDataListResponse>> GetDataList(
            [FromQuery] string? dataType = null,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 20)
        {
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized("用户身份验证失败");
                }

                var allData = await _customerDataService.GetDataListAsync(userId, dataType);
                
                // 分页处理
                var totalCount = allData.Count;
                var pagedData = allData
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToList();

                var response = new CustomerDataListResponse
                {
                    Data = pagedData,
                    TotalCount = totalCount,
                    Page = page,
                    PageSize = pageSize,
                    TotalPages = (int)Math.Ceiling((double)totalCount / pageSize)
                };

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取客户数据列表时发生错误");
                return StatusCode(500, "获取数据失败");
            }
        }

        /// <summary>
        /// 获取单个客户数据
        /// </summary>
        /// <param name="id">数据ID</param>
        /// <returns>客户数据详情</returns>
        [HttpGet("{id}")]
        public async Task<ActionResult<CustomerData>> GetData(int id)
        {
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized("用户身份验证失败");
                }

                var allData = await _customerDataService.GetDataListAsync(userId);
                var data = allData.FirstOrDefault(d => d.Id == id);

                if (data == null)
                {
                    return NotFound("数据不存在");
                }

                return Ok(data);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取客户数据详情时发生错误: {Id}", id);
                return StatusCode(500, "获取数据失败");
            }
        }

        /// <summary>
        /// 创建客户数据
        /// </summary>
        /// <param name="request">创建请求</param>
        /// <returns>创建结果</returns>
        [HttpPost]
        public async Task<ActionResult<CustomerData>> CreateData([FromBody] CreateCustomerDataRequest request)
        {
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized("用户身份验证失败");
                }

                var data = new CustomerData
                {
                    DataType = request.DataType,
                    DataValue = request.DataValue,
                    MerchantId = "", // 将在服务中设置
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                };

                var success = await _customerDataService.SaveDataAsync(userId, data);
                if (!success)
                {
                    return BadRequest("创建数据失败");
                }

                return CreatedAtAction(nameof(GetData), new { id = data.Id }, data);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建客户数据时发生错误");
                return StatusCode(500, "创建数据失败");
            }
        }

        /// <summary>
        /// 更新客户数据
        /// </summary>
        /// <param name="id">数据ID</param>
        /// <param name="request">更新请求</param>
        /// <returns>更新结果</returns>
        [HttpPut("{id}")]
        public async Task<ActionResult<CustomerData>> UpdateData(int id, [FromBody] UpdateCustomerDataRequest request)
        {
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized("用户身份验证失败");
                }

                var data = new CustomerData
                {
                    Id = id,
                    DataType = request.DataType,
                    DataValue = request.DataValue,
                    MerchantId = "", // 将在服务中设置
                    UpdatedAt = DateTime.Now
                };

                var success = await _customerDataService.SaveDataAsync(userId, data);
                if (!success)
                {
                    return BadRequest("更新数据失败");
                }

                return Ok(data);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新客户数据时发生错误: {Id}", id);
                return StatusCode(500, "更新数据失败");
            }
        }

        /// <summary>
        /// 删除客户数据
        /// </summary>
        /// <param name="id">数据ID</param>
        /// <returns>删除结果</returns>
        [HttpDelete("{id}")]
        public async Task<ActionResult> DeleteData(int id)
        {
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized("用户身份验证失败");
                }

                var success = await _customerDataService.DeleteDataAsync(userId, id);
                if (!success)
                {
                    return BadRequest("删除数据失败");
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除客户数据时发生错误: {Id}", id);
                return StatusCode(500, "删除数据失败");
            }
        }

        /// <summary>
        /// 获取数据统计
        /// </summary>
        /// <returns>数据统计信息</returns>
        [HttpGet("statistics")]
        public async Task<ActionResult<DataStatisticsResponse>> GetStatistics()
        {
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized("用户身份验证失败");
                }

                var user = await _authService.ValidateUserAsync(userId);
                if (user == null)
                {
                    return Unauthorized("用户验证失败");
                }

                var allData = await _customerDataService.GetDataListAsync(userId);
                
                var statistics = new DataStatisticsResponse
                {
                    TotalRecords = allData.Count,
                    DataTypeStats = allData
                        .GroupBy(d => d.DataType)
                        .Select(g => new DataTypeStatistic
                        {
                            DataType = g.Key,
                            Count = g.Count(),
                            LastUpdated = g.Max(d => d.UpdatedAt)
                        })
                        .ToList(),
                    RecentRecords = allData
                        .OrderByDescending(d => d.UpdatedAt)
                        .Take(5)
                        .ToList()
                };

                return Ok(statistics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取数据统计时发生错误");
                return StatusCode(500, "获取统计数据失败");
            }
        }
    }

    #region Request/Response DTOs

    /// <summary>
    /// 客户数据列表响应
    /// </summary>
    public class CustomerDataListResponse
    {
        public List<CustomerData> Data { get; set; } = new();
        public int TotalCount { get; set; }
        public int Page { get; set; }
        public int PageSize { get; set; }
        public int TotalPages { get; set; }
    }

    /// <summary>
    /// 创建客户数据请求
    /// </summary>
    public class CreateCustomerDataRequest
    {
        public string DataType { get; set; } = string.Empty;
        public string? DataValue { get; set; }
    }

    /// <summary>
    /// 更新客户数据请求
    /// </summary>
    public class UpdateCustomerDataRequest
    {
        public string DataType { get; set; } = string.Empty;
        public string? DataValue { get; set; }
    }

    /// <summary>
    /// 数据统计响应
    /// </summary>
    public class DataStatisticsResponse
    {
        public int TotalRecords { get; set; }
        public List<DataTypeStatistic> DataTypeStats { get; set; } = new();
        public List<CustomerData> RecentRecords { get; set; } = new();
    }

    /// <summary>
    /// 数据类型统计
    /// </summary>
    public class DataTypeStatistic
    {
        public string DataType { get; set; } = string.Empty;
        public int Count { get; set; }
        public DateTime LastUpdated { get; set; }
    }

    #endregion
}
