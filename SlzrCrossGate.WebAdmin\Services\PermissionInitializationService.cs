using Microsoft.EntityFrameworkCore;
using SlzrCrossGate.Core.Database;
using SlzrCrossGate.Core.Services;

namespace SlzrCrossGate.WebAdmin.Services
{
    /// <summary>
    /// 权限初始化服务
    /// 在应用启动时初始化默认的功能权限配置
    /// </summary>
    public class PermissionInitializationService : IHostedService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<PermissionInitializationService> _logger;

        public PermissionInitializationService(
            IServiceProvider serviceProvider,
            ILogger<PermissionInitializationService> logger)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
        }

        public async Task StartAsync(CancellationToken cancellationToken)
        {
            try
            {
                using var scope = _serviceProvider.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<TcpDbContext>();
                var permissionService = scope.ServiceProvider.GetRequiredService<IFeaturePermissionService>();

                // 确保数据库已创建
                await context.Database.EnsureCreatedAsync(cancellationToken);

                // 初始化默认功能权限配置
                await permissionService.InitializeDefaultConfigsAsync();

                _logger.LogInformation("Permission initialization completed successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to initialize permissions");
                // 不抛出异常，避免影响应用启动
            }
        }

        public Task StopAsync(CancellationToken cancellationToken)
        {
            return Task.CompletedTask;
        }
    }
}
