import React, { useState, useEffect } from 'react';
import {
    Box,
    Paper,
    Typography,
    Button,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    TablePagination,
    Chip,
    IconButton,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    Alert,
    Snackbar,
    CircularProgress,
    Tooltip
} from '@mui/material';
import {
    ArrowBack as ArrowBackIcon,
    Visibility as VisibilityIcon,
    Download as DownloadIcon,
    Refresh as RefreshIcon
} from '@mui/icons-material';
import { useNavigate, useParams } from 'react-router-dom';
import { fareDiscountSchemeAPI } from '../../services/api';

const FareDiscountSchemeVersionsView = () => {
    const navigate = useNavigate();
    const { id } = useParams();
    const [versions, setVersions] = useState([]);
    const [schemeInfo, setSchemeInfo] = useState(null);
    const [loading, setLoading] = useState(false);
    const [page, setPage] = useState(0);
    const [rowsPerPage, setRowsPerPage] = useState(10);
    const [totalCount, setTotalCount] = useState(0);
    const [previewDialogOpen, setPreviewDialogOpen] = useState(false);
    const [previewContent, setPreviewContent] = useState('');
    const [previewLoading, setPreviewLoading] = useState(false);
    const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });

    // 加载版本列表
    const loadVersions = async () => {
        setLoading(true);
        try {
            const params = {
                page: page + 1,
                pageSize: rowsPerPage
            };

            const response = await fareDiscountSchemeAPI.getVersions(id, params);
            setVersions(response.items || []);
            setTotalCount(response.totalCount || 0);
        } catch (error) {
            console.error('加载版本历史失败:', error);
            showSnackbar('加载版本历史失败', 'error');
        } finally {
            setLoading(false);
        }
    };

    // 加载方案基本信息
    const loadSchemeInfo = async () => {
        try {
            const response = await fareDiscountSchemeAPI.getScheme(id);
            setSchemeInfo(response);
        } catch (error) {
            console.error('加载方案信息失败:', error);
            showSnackbar('加载方案信息失败', 'error');
        }
    };

    useEffect(() => {
        loadSchemeInfo();
        loadVersions();
    }, [id, page, rowsPerPage]);

    const showSnackbar = (message, severity = 'success') => {
        setSnackbar({ open: true, message, severity });
    };

    const handleBack = () => {
        navigate('/app/fare-discount-schemes');
    };

    const handleRefresh = () => {
        loadVersions();
    };

    // 预览版本文件
    const handlePreview = async (versionId) => {
        setPreviewLoading(true);
        setPreviewDialogOpen(true);
        try {
            const response = await fareDiscountSchemeAPI.previewVersion(versionId);
            setPreviewContent(JSON.stringify(response.fileContent, null, 2));
        } catch (error) {
            console.error('预览文件失败:', error);
            showSnackbar('预览文件失败', 'error');
            setPreviewContent('预览失败');
        } finally {
            setPreviewLoading(false);
        }
    };

    // 下载版本文件
    const handleDownload = async (version) => {
        try {
            console.log('开始下载版本文件:', version);

            // 使用后端的下载API，参考文件版本管理的实现
            const blob = await fareDiscountSchemeAPI.downloadVersion(version.id);

            console.log('下载响应blob:', blob);
            console.log('Blob类型:', blob.type);
            console.log('Blob大小:', blob.size);

            // 检查blob是否有效
            if (!blob || blob.size === 0) {
                throw new Error('下载的文件为空');
            }

            // 创建blob URL并触发下载
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `PZB_${version.filePara}_${version.version}.json`;
            link.style.display = 'none';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);

            console.log(`下载文件成功: 版本ID=${version.id}, 文件名=PZB_${version.filePara}_${version.version}.json`);
            //showSnackbar('文件下载成功');
        } catch (error) {
            console.error('下载文件失败:', error);
            showSnackbar(error.message || '下载文件失败', 'error');
        }
    };

    const formatDateTime = (dateString) => {
        if (!dateString) return '-';
        return new Date(dateString).toLocaleString('zh-CN');
    };

    const getStatusChip = (version) => {
        if (version.isPublished) {
            return <Chip label="已发布" color="success" size="small" />;
        } else if (version.status === 'Submitted') {
            return <Chip label="已提交" color="primary" size="small" />;
        } else {
            return <Chip label="草稿" color="default" size="small" />;
        }
    };

    return (
        <Box sx={{ p: 3 }}>
            <Paper sx={{ p: 3 }}>
                {/* 页面标题 */}
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                    <Box>
                        <Typography variant="h5" component="h1">
                            版本历史
                        </Typography>
                        {schemeInfo && (
                            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                                方案：{schemeInfo.schemeName} ({schemeInfo.schemeCode})
                            </Typography>
                        )}
                    </Box>
                    <Box sx={{ display: 'flex', gap: 2 }}>
                        <Button
                            variant="outlined"
                            startIcon={<RefreshIcon />}
                            onClick={handleRefresh}
                            disabled={loading}
                        >
                            刷新
                        </Button>
                        <Button
                            variant="outlined"
                            startIcon={<ArrowBackIcon />}
                            onClick={handleBack}
                        >
                            返回列表
                        </Button>
                    </Box>
                </Box>

                {/* 版本列表 */}
                <TableContainer>
                    <Table>
                        <TableHead>
                            <TableRow>
                                <TableCell>版本号</TableCell>
                                <TableCell>文件参数</TableCell>
                                <TableCell>备注信息</TableCell>
                                <TableCell>状态</TableCell>
                                <TableCell>提交时间</TableCell>
                                <TableCell>提交者</TableCell>
                                <TableCell>操作</TableCell>
                            </TableRow>
                        </TableHead>
                        <TableBody>
                            {loading ? (
                                <TableRow>
                                    <TableCell colSpan={7} align="center">
                                        <CircularProgress />
                                    </TableCell>
                                </TableRow>
                            ) : versions.length === 0 ? (
                                <TableRow>
                                    <TableCell colSpan={7} align="center">
                                        <Typography color="text.secondary">
                                            暂无版本历史
                                        </Typography>
                                    </TableCell>
                                </TableRow>
                            ) : (
                                versions.map((version) => (
                                    <TableRow key={version.id} hover>
                                        <TableCell>
                                            <Typography variant="body2" fontWeight="medium">
                                                {version.version}
                                            </Typography>
                                        </TableCell>
                                        <TableCell>
                                            <Typography variant="body2">
                                                {version.filePara}
                                            </Typography>
                                        </TableCell>
                                        <TableCell>
                                            <Typography
                                                variant="body2"
                                                color="text.secondary"
                                                sx={{
                                                    maxWidth: 200,
                                                    overflow: 'hidden',
                                                    textOverflow: 'ellipsis',
                                                    whiteSpace: 'nowrap'
                                                }}
                                                title={version.remarks || ''}
                                            >
                                                {version.remarks || '-'}
                                            </Typography>
                                        </TableCell>
                                        <TableCell>
                                            {getStatusChip(version)}
                                        </TableCell>
                                        <TableCell>
                                            <Typography variant="body2">
                                                {formatDateTime(version.submitTime)}
                                            </Typography>
                                        </TableCell>
                                        <TableCell>
                                            <Typography variant="body2">
                                                {version.submitter || '-'}
                                            </Typography>
                                        </TableCell>
                                        <TableCell>
                                            <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
                                                <Tooltip title="预览文件">
                                                    <IconButton
                                                        size="small"
                                                        color="info"
                                                        onClick={() => handlePreview(version.id)}
                                                    >
                                                        <VisibilityIcon fontSize="small" />
                                                    </IconButton>
                                                </Tooltip>
                                                <Tooltip title="下载文件">
                                                    <IconButton
                                                        size="small"
                                                        color="primary"
                                                        onClick={() => handleDownload(version)}
                                                    >
                                                        <DownloadIcon fontSize="small" />
                                                    </IconButton>
                                                </Tooltip>
                                            </Box>
                                        </TableCell>
                                    </TableRow>
                                ))
                            )}
                        </TableBody>
                    </Table>
                </TableContainer>

                {/* 分页 */}
                <TablePagination
                    component="div"
                    count={totalCount}
                    page={page}
                    onPageChange={(event, newPage) => setPage(newPage)}
                    rowsPerPage={rowsPerPage}
                    onRowsPerPageChange={(event) => {
                        setRowsPerPage(parseInt(event.target.value, 10));
                        setPage(0);
                    }}
                    labelRowsPerPage="每页行数:"
                    labelDisplayedRows={({ from, to, count }) => `${from}-${to} 共 ${count} 条`}
                />
            </Paper>

            {/* 预览对话框 */}
            <Dialog open={previewDialogOpen} onClose={() => setPreviewDialogOpen(false)} maxWidth="md" fullWidth>
                <DialogTitle>预览文件内容</DialogTitle>
                <DialogContent>
                    {previewLoading ? (
                        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                            <CircularProgress />
                        </Box>
                    ) : (
                        <Box
                            component="pre"
                            sx={{
                                backgroundColor: '#f5f5f5',
                                p: 2,
                                borderRadius: 1,
                                overflow: 'auto',
                                maxHeight: '60vh',
                                fontSize: '0.875rem',
                                fontFamily: 'monospace'
                            }}
                        >
                            {previewContent}
                        </Box>
                    )}
                </DialogContent>
                <DialogActions>
                    <Button onClick={() => setPreviewDialogOpen(false)}>关闭</Button>
                </DialogActions>
            </Dialog>

            {/* 消息提示 */}
            <Snackbar
                open={snackbar.open}
                autoHideDuration={6000}
                onClose={() => setSnackbar({ ...snackbar, open: false })}
            >
                <Alert
                    onClose={() => setSnackbar({ ...snackbar, open: false })}
                    severity={snackbar.severity}
                    sx={{ width: '100%' }}
                >
                    {snackbar.message}
                </Alert>
            </Snackbar>
        </Box>
    );
};

export default FareDiscountSchemeVersionsView;
