# 2025-08-07 添加预约发布管理菜单
INSERT INTO MenuItems (
    MenuGroupId,
    ItemKey,
    Title,
    Href,
    IconName,
    SortOrder,
    IsEnabled,
    VisibleToSystemAdmin,
    VisibleToMerchantAdmin,
    VisibleToUser,
    CreatedAt,
    UpdatedAt
) 
SELECT
    mg.Id as MenuGroupId,
    'scheduled-publish' as Item<PERSON>ey,
    '预约发布' as Title,
    '/app/files/scheduled-publish' as Href,
    'Clock' as IconName,
    COALESCE(MAX(mi.SortOrder), 0) + 1 as SortOrder,
    1 as IsEnabled,
    1 as VisibleToSystemAdmin,
    1 as VisibleToMerchantAdmin,
    0 as VisibleToUser,
    NOW() as CreatedAt,
    NOW() as UpdatedAt
FROM MenuGroups mg
LEFT JOIN MenuItems mi ON mg.Id = mi.MenuGroupId
WHERE mg.GroupKey = 'content'
GROUP BY mg.Id;


# 2025-07-28 添加按钮权限配置页面
 
INSERT INTO MenuItems (
    MenuGroupId,
    ItemKey,
    Title,
    Href,
    IconName,
    SortOrder,
    IsEnabled,
    VisibleToSystemAdmin,
    VisibleToMerchantAdmin,
    VisibleToUser,
    CreatedAt,
    UpdatedAt
) 
SELECT
    mg.Id as MenuGroupId,
    'feature-permissions' as ItemKey,
    '功能权限管理' as Title,
    '/app/feature-permissions' as Href,
    'SecurityIcon' as IconName,
    COALESCE(MAX(mi.SortOrder), 0) + 1 as SortOrder,
    1 as IsEnabled,
    1 as VisibleToSystemAdmin,
    1 as VisibleToMerchantAdmin,
    0 as VisibleToUser,
    NOW() as CreatedAt,
    NOW() as UpdatedAt
FROM MenuGroups mg
LEFT JOIN MenuItems mi ON mg.Id = mi.MenuGroupId
WHERE mg.GroupKey = 'system'
GROUP BY mg.Id;


# 2025-07-11 增加终端文件上传管理菜单项

-- 添加终端文件上传管理菜单项到终端管理分组
INSERT INTO MenuItems (
    MenuGroupId,
    ItemKey,
    Title,
    Href,
    IconName,
    SortOrder,
    IsEnabled,
    VisibleToSystemAdmin,
    VisibleToMerchantAdmin,
    VisibleToUser,
    CreatedAt,
    UpdatedAt
)
SELECT
    mg.Id as MenuGroupId,
    'terminal-file-uploads' as ItemKey,
    '文件上传管理' as Title,
    '/app/terminal-file-uploads' as Href,
    'Upload' as IconName,
    COALESCE(MAX(mi.SortOrder), 0) + 1 as SortOrder,
    1 as IsEnabled,
    1 as VisibleToSystemAdmin,
    1 as VisibleToMerchantAdmin,
    0 as VisibleToUser,
    NOW() as CreatedAt,
    NOW() as UpdatedAt
FROM MenuGroups mg
LEFT JOIN MenuItems mi ON mg.Id = mi.MenuGroupId
WHERE mg.GroupKey = 'terminal'
GROUP BY mg.Id;

# 2025-07-31 增加终端日志记录菜单项

-- 添加终端日志记录菜单项到终端管理分组
INSERT INTO MenuItems (
    MenuGroupId,
    ItemKey,
    Title,
    Href,
    IconName,
    SortOrder,
    IsEnabled,
    VisibleToSystemAdmin,
    VisibleToMerchantAdmin,
    VisibleToUser,
    CreatedAt,
    UpdatedAt
)
SELECT
    mg.Id as MenuGroupId,
    'terminal-logs' as ItemKey,
    '终端日志记录' as Title,
    '/app/terminal-logs' as Href,
    'ActivityIcon' as IconName,
    COALESCE(MAX(mi.SortOrder), 0) + 1 as SortOrder,
    1 as IsEnabled,
    1 as VisibleToSystemAdmin,
    1 as VisibleToMerchantAdmin,
    0 as VisibleToUser,
    NOW() as CreatedAt,
    NOW() as UpdatedAt
FROM MenuGroups mg
LEFT JOIN MenuItems mi ON mg.Id = mi.MenuGroupId
WHERE mg.GroupKey = 'terminal'
GROUP BY mg.Id;



# 2025-07-09 增加票价折扣方案菜单项

-- 添加票价折扣方案菜单项到业务配置分组
INSERT INTO MenuItems (
    MenuGroupId,
    ItemKey,
    Title,
    Href,
    IconName,
    SortOrder,
    IsEnabled,
    VisibleToSystemAdmin,
    VisibleToMerchantAdmin,
    VisibleToUser,
    CreatedAt,
    UpdatedAt
)
SELECT
    mg.Id as MenuGroupId,
    'fare-discount-schemes' as ItemKey,
    '票价折扣方案' as Title,
    '/app/fare-discount-schemes' as Href,
    'PercentIcon' as IconName,
    COALESCE(MAX(mi.SortOrder), 0) + 1 as SortOrder,
    1 as IsEnabled,
    1 as VisibleToSystemAdmin,
    1 as VisibleToMerchantAdmin,
    0 as VisibleToUser,
    NOW() as CreatedAt,
    NOW() as UpdatedAt
FROM MenuGroups mg
LEFT JOIN MenuItems mi ON mg.Id = mi.MenuGroupId
WHERE mg.GroupKey = 'business'
GROUP BY mg.Id;

# 2025-07-07 增加车辆管理菜单项

-- 一次性添加车辆管理菜单项
INSERT INTO MenuItems (
    MenuGroupId,
    ItemKey,
    Title,
    Href,
    IconName,
    SortOrder,
    IsEnabled,
    VisibleToSystemAdmin,
    VisibleToMerchantAdmin,
    VisibleToUser,
    CreatedAt,
    UpdatedAt
) 
SELECT 
    mg.Id as MenuGroupId,
    'vehicles' as ItemKey,
    '车辆管理' as Title,
    '/app/vehicles' as Href,
    'CarIcon' as IconName,
    COALESCE(MAX(mi.SortOrder), 0) + 1 as SortOrder,
    1 as IsEnabled,
    1 as VisibleToSystemAdmin,
    1 as VisibleToMerchantAdmin,
    0 as VisibleToUser,
    NOW() as CreatedAt,
    NOW() as UpdatedAt
FROM MenuGroups mg
LEFT JOIN MenuItems mi ON mg.Id = mi.MenuGroupId
WHERE mg.GroupKey = 'business'
GROUP BY mg.Id;


-- 添加票价折扣方案菜单项到业务配置分组
INSERT INTO MenuItems (
    MenuGroupId,
    ItemKey,
    Title,
    Href,
    IconName,
    SortOrder,
    IsEnabled,
    VisibleToSystemAdmin,
    VisibleToMerchantAdmin,
    VisibleToUser,
    CreatedAt,
    UpdatedAt
) 
SELECT 
    mg.Id as MenuGroupId,
    'fare-discount-schemes' as ItemKey,
    '票价折扣方案' as Title,
    '/app/fare-discount-schemes' as Href,
    'PercentIcon' as IconName,
    COALESCE(MAX(mi.SortOrder), 0) + 1 as SortOrder,
    1 as IsEnabled,
    1 as VisibleToSystemAdmin,
    1 as VisibleToMerchantAdmin,
    0 as VisibleToUser,
    NOW() as CreatedAt,
    NOW() as UpdatedAt
FROM MenuGroups mg
LEFT JOIN MenuItems mi ON mg.Id = mi.MenuGroupId
WHERE mg.GroupKey = 'business'
GROUP BY mg.Id;