// 图标映射文件 - 将字符串图标名称映射到实际的图标组件
import {
  Activity as ActivityIcon,
  BarChart as BarChartIcon,
  Smartphone as SmartphoneIcon,
  List as ListIcon,
  Cpu as CpuIcon,
  Monitor as MonitorIcon,
  Folder as FolderIcon,
  FileText as FileTextIcon,
  Archive as ArchiveIcon,
  Send as SendIcon,
  Tag as TagIcon,
  MessageSquare as MessageSquareIcon,
  Sliders as SlidersIcon,
  CreditCard as CreditCardIcon,
  Database as DatabaseIcon,
  Book as BookIcon,
  Shield as ShieldIcon,
  Users as UsersIcon,
  Lock as LockIcon,
  ShoppingBag as ShoppingBagIcon,
  Settings as SettingsIcon,
  LogIn as LogInIcon,
  Key as KeyIcon,
  Menu as MenuIcon,
  Truck as CarIcon,
  Percent as PercentIcon,
  Upload as CloudUploadIcon,
  Search as SearchIcon,
  Package as ExtensionIcon,
  Clock as ClockIcon
} from 'react-feather';

/**
 * 图标映射表
 * 将字符串图标名称映射到对应的React Feather图标组件
 */
export const iconMapping = {
  // 基础图标
  ActivityIcon,
  BarChartIcon,
  SmartphoneIcon,
  ListIcon,
  CpuIcon,
  MonitorIcon,
  FolderIcon,
  FileTextIcon,
  ArchiveIcon,
  SendIcon,
  TagIcon,
  MessageSquareIcon,
  SlidersIcon,
  CreditCardIcon,
  DatabaseIcon,
  BookIcon,
  ShieldIcon,
  UsersIcon,
  LockIcon,
  ShoppingBagIcon,
  SettingsIcon,
  LogInIcon,
  KeyIcon,
  MenuIcon,
  CarIcon,
  PercentIcon,
  CloudUploadIcon,
  SearchIcon,
  ExtensionIcon,
  ClockIcon,

  // 别名映射（兼容性）
  'Activity': ActivityIcon,
  'BarChart': BarChartIcon,
  'Smartphone': SmartphoneIcon,
  'List': ListIcon,
  'Cpu': CpuIcon,
  'Monitor': MonitorIcon,
  'Folder': FolderIcon,
  'FileText': FileTextIcon,
  'Archive': ArchiveIcon,
  'Send': SendIcon,
  'Tag': TagIcon,
  'MessageSquare': MessageSquareIcon,
  'Sliders': SlidersIcon,
  'CreditCard': CreditCardIcon,
  'Database': DatabaseIcon,
  'Book': BookIcon,
  'Shield': ShieldIcon,
  'Users': UsersIcon,
  'Lock': LockIcon,
  'ShoppingBag': ShoppingBagIcon,
  'Settings': SettingsIcon,
  'LogIn': LogInIcon,
  'Key': KeyIcon,
  'Menu': MenuIcon,
  'Car': CarIcon,
  'Truck': CarIcon,
  'Percent': PercentIcon,
  'CloudUpload': CloudUploadIcon,
  'Upload': CloudUploadIcon,
  'Search': SearchIcon,
  'Extension': ExtensionIcon,
  'Package': ExtensionIcon,
  'Clock': ClockIcon,
  'Schedule': ClockIcon,
  'ScheduleIcon': ClockIcon,

  // 权限管理相关图标
  'SecurityIcon': ShieldIcon,
  'Security': ShieldIcon
};

/**
 * 根据图标名称获取对应的图标组件
 * @param {string} iconName - 图标名称
 * @returns {React.Component|null} 图标组件或null
 */
export const getIconComponent = (iconName) => {
  if (!iconName) return null;

  // 尝试直接匹配
  let IconComponent = iconMapping[iconName];

  // 如果没有找到，尝试移除Icon后缀
  if (!IconComponent && iconName.endsWith('Icon')) {
    const nameWithoutIcon = iconName.slice(0, -4);
    IconComponent = iconMapping[nameWithoutIcon];
  }

  // 如果还没有找到，尝试添加Icon后缀
  if (!IconComponent && !iconName.endsWith('Icon')) {
    IconComponent = iconMapping[iconName + 'Icon'];
  }

  return IconComponent || null;
};

/**
 * 获取所有可用的图标名称列表
 * @returns {string[]} 图标名称数组
 */
export const getAvailableIconNames = () => {
  return Object.keys(iconMapping).filter(name => name.endsWith('Icon'));
};

export default iconMapping;
