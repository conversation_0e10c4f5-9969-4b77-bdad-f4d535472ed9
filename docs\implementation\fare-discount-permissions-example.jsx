// 票价折扣方案页面权限控制改造示例
import React, { useMemo } from 'react';
import { 
  Box, 
  IconButton, 
  Tooltip,
  Button
} from '@mui/material';
import {
  Edit as EditIcon,
  Publish as PublishIcon,
  History as HistoryIcon,
  FileCopy as CopyIcon,
  Delete as DeleteIcon,
  Add as AddIcon
} from '@mui/icons-material';
import { FeatureGuard } from '../components/FeatureGuard';
import { useFeaturePermission } from '../hooks/useFeaturePermission';
import { PERMISSIONS } from '../constants/permissions';

// 票价折扣方案操作按钮组件
const FareDiscountSchemeActionButtons = ({ 
  scheme, 
  onEdit, 
  onSubmitVersion, 
  onViewVersions, 
  onCopy, 
  onDelete 
}) => {
  const { user } = useAuth();
  const { checkMultiple } = useFeaturePermission();
  
  // 批量检查权限
  const permissions = useMemo(() => 
    checkMultiple([
      PERMISSIONS.FARE_DISCOUNT_SCHEME.EDIT,
      PERMISSIONS.FARE_DISCOUNT_SCHEME.SUBMIT_VERSION,
      PERMISSIONS.FARE_DISCOUNT_SCHEME.VIEW_VERSIONS,
      PERMISSIONS.FARE_DISCOUNT_SCHEME.COPY,
      PERMISSIONS.FARE_DISCOUNT_SCHEME.DELETE
    ]), 
    [checkMultiple]
  );

  // 业务逻辑判断
  const canEdit = permissions[PERMISSIONS.FARE_DISCOUNT_SCHEME.EDIT] && 
    (user.roles.includes('SystemAdmin') || scheme.merchantId === user.merchantId);
    
  const canSubmitVersion = permissions[PERMISSIONS.FARE_DISCOUNT_SCHEME.SUBMIT_VERSION] && 
    canEdit && scheme.status === 'draft';
    
  const canDelete = permissions[PERMISSIONS.FARE_DISCOUNT_SCHEME.DELETE] && 
    canEdit && !scheme.hasPublishedVersions;

  return (
    <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
      {/* 编辑按钮 */}
      <FeatureGuard 
        featureKey={PERMISSIONS.FARE_DISCOUNT_SCHEME.EDIT}
        additionalCheck={canEdit}
        fallback={
          <Tooltip title="无编辑权限">
            <span>
              <IconButton size="small" disabled>
                <EditIcon fontSize="small" />
              </IconButton>
            </span>
          </Tooltip>
        }
      >
        <Tooltip title="编辑">
          <IconButton
            size="small"
            color="primary"
            onClick={() => onEdit(scheme)}
          >
            <EditIcon fontSize="small" />
          </IconButton>
        </Tooltip>
      </FeatureGuard>

      {/* 提交版本按钮 */}
      <FeatureGuard 
        featureKey={PERMISSIONS.FARE_DISCOUNT_SCHEME.SUBMIT_VERSION}
        additionalCheck={canSubmitVersion}
        fallback={
          <Tooltip title={
            !permissions[PERMISSIONS.FARE_DISCOUNT_SCHEME.SUBMIT_VERSION] 
              ? "无提交版本权限"
              : scheme.status !== 'draft' 
                ? "只有草稿状态的方案可以提交版本"
                : "无编辑权限"
          }>
            <span>
              <IconButton size="small" disabled>
                <PublishIcon fontSize="small" />
              </IconButton>
            </span>
          </Tooltip>
        }
      >
        <Tooltip title="提交版本">
          <IconButton
            size="small"
            color="success"
            onClick={() => onSubmitVersion(scheme)}
          >
            <PublishIcon fontSize="small" />
          </IconButton>
        </Tooltip>
      </FeatureGuard>

      {/* 版本历史按钮 */}
      <FeatureGuard 
        featureKey={PERMISSIONS.FARE_DISCOUNT_SCHEME.VIEW_VERSIONS}
        additionalCheck={scheme.currentVersion > 0}
        fallback={
          scheme.currentVersion === 0 ? (
            <Tooltip title="该方案还没有版本历史">
              <span>
                <IconButton size="small" disabled>
                  <HistoryIcon fontSize="small" />
                </IconButton>
              </span>
            </Tooltip>
          ) : (
            <Tooltip title="无查看版本历史权限">
              <span>
                <IconButton size="small" disabled>
                  <HistoryIcon fontSize="small" />
                </IconButton>
              </span>
            </Tooltip>
          )
        }
      >
        <Tooltip title="版本历史">
          <IconButton
            size="small"
            color="info"
            onClick={() => onViewVersions(scheme)}
          >
            <HistoryIcon fontSize="small" />
          </IconButton>
        </Tooltip>
      </FeatureGuard>

      {/* 复制按钮 */}
      <FeatureGuard 
        featureKey={PERMISSIONS.FARE_DISCOUNT_SCHEME.COPY}
        additionalCheck={permissions[PERMISSIONS.FARE_DISCOUNT_SCHEME.CREATE]}
      >
        <Tooltip title="复制方案">
          <IconButton
            size="small"
            color="secondary"
            onClick={() => onCopy(scheme)}
          >
            <CopyIcon fontSize="small" />
          </IconButton>
        </Tooltip>
      </FeatureGuard>

      {/* 删除按钮 */}
      <FeatureGuard 
        featureKey={PERMISSIONS.FARE_DISCOUNT_SCHEME.DELETE}
        additionalCheck={canDelete}
        fallback={
          <Tooltip title={
            !permissions[PERMISSIONS.FARE_DISCOUNT_SCHEME.DELETE] 
              ? "无删除权限"
              : scheme.hasPublishedVersions 
                ? "已发布版本的方案无法删除"
                : "无编辑权限"
          }>
            <span>
              <IconButton size="small" disabled>
                <DeleteIcon fontSize="small" />
              </IconButton>
            </span>
          </Tooltip>
        }
      >
        <Tooltip title="删除">
          <IconButton
            size="small"
            color="error"
            onClick={() => onDelete(scheme)}
          >
            <DeleteIcon fontSize="small" />
          </IconButton>
        </Tooltip>
      </FeatureGuard>
    </Box>
  );
};

// 创建方案按钮
const CreateFareDiscountSchemeButton = ({ onClick }) => {
  return (
    <FeatureGuard featureKey={PERMISSIONS.FARE_DISCOUNT_SCHEME.CREATE}>
      <Button
        variant="contained"
        color="primary"
        startIcon={<AddIcon />}
        onClick={onClick}
      >
        创建折扣方案
      </Button>
    </FeatureGuard>
  );
};

// 批量操作按钮
const BatchActionButtons = ({ selectedSchemes, onBatchDelete }) => {
  const { hasPermission } = useFeaturePermission();
  const { user } = useAuth();
  
  const canBatchDelete = hasPermission(PERMISSIONS.FARE_DISCOUNT_SCHEME.DELETE) &&
    selectedSchemes.every(scheme => 
      !scheme.hasPublishedVersions && 
      (user.roles.includes('SystemAdmin') || scheme.merchantId === user.merchantId)
    );

  if (selectedSchemes.length === 0) return null;

  return (
    <Box sx={{ mb: 2, display: 'flex', gap: 1 }}>
      <FeatureGuard 
        featureKey={PERMISSIONS.FARE_DISCOUNT_SCHEME.DELETE}
        additionalCheck={canBatchDelete}
        fallback={
          <Tooltip title="选中的方案中包含已发布版本或无权限删除的方案">
            <span>
              <Button disabled startIcon={<DeleteIcon />}>
                批量删除 ({selectedSchemes.length})
              </Button>
            </span>
          </Tooltip>
        }
      >
        <Button 
          color="error" 
          startIcon={<DeleteIcon />}
          onClick={() => onBatchDelete(selectedSchemes)}
        >
          批量删除 ({selectedSchemes.length})
        </Button>
      </FeatureGuard>
    </Box>
  );
};

// 主页面组件
// 重要：不使用页面级权限检查，只对按钮功能进行权限控制
const FareDiscountSchemesPage = () => {
  // 不要使用页面级权限检查！
  // 页面本身对所有用户开放，只对具体功能按钮进行权限控制

  return (
    <Container>
      {/* 页面标题和创建按钮 */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
        <Typography variant="h4">票价折扣方案管理</Typography>
        <CreateFareDiscountSchemeButton onClick={handleCreate} />
      </Box>

      {/* 批量操作按钮 */}
      <BatchActionButtons 
        selectedSchemes={selectedSchemes}
        onBatchDelete={handleBatchDelete}
      />

      {/* 表格内容 */}
      <TableContainer>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell padding="checkbox">
                <Checkbox />
              </TableCell>
              <TableCell>方案名称</TableCell>
              <TableCell>方案编码</TableCell>
              <TableCell>商户</TableCell>
              <TableCell>当前版本</TableCell>
              <TableCell>文件参数</TableCell>
              <TableCell>状态</TableCell>
              <TableCell>操作</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {schemes.map((scheme) => (
              <TableRow key={scheme.id}>
                <TableCell padding="checkbox">
                  <Checkbox />
                </TableCell>
                <TableCell>{scheme.schemeName}</TableCell>
                <TableCell>{scheme.schemeCode}</TableCell>
                <TableCell>{scheme.merchantName}</TableCell>
                <TableCell>{scheme.currentVersion || '-'}</TableCell>
                <TableCell>{scheme.currentFilePara || '-'}</TableCell>
                <TableCell>
                  <Chip 
                    label={scheme.status} 
                    color={scheme.status === 'published' ? 'success' : 'default'}
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  <FareDiscountSchemeActionButtons
                    scheme={scheme}
                    onEdit={handleEdit}
                    onSubmitVersion={handleSubmitVersion}
                    onViewVersions={handleViewVersions}
                    onCopy={handleCopy}
                    onDelete={handleDelete}
                  />
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Container>
  );
};

export { 
  FareDiscountSchemeActionButtons,
  CreateFareDiscountSchemeButton,
  BatchActionButtons,
  FareDiscountSchemesPage 
};
