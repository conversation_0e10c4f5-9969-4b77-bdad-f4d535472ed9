import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import {
  Box,
  Paper,
  Typography,
  Grid,
  Card,
  CardContent,
  Button,
  TextField,
  MenuItem,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Chip,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  Alert,
  Snackbar,
  CircularProgress,
  TableSortLabel,
  Menu,
  FormControlLabel,
  Checkbox,
  Divider
} from '@mui/material';
import { FeatureGuard } from '../../components/FeatureGuard';
// 移除了不再使用的权限相关导入
import { PERMISSIONS } from '../../constants/permissions';
import ResponsiveTable, {
  ResponsiveTableHead,
  ResponsiveTableBody,
  ResponsiveTableRow,
  ResponsiveTableCell
} from '../../components/ResponsiveTable';
import {
  Refresh as RefreshIcon,
  Search as SearchIcon,
  Clear as ClearIcon,
  Visibility as VisibilityIcon,
  Message as MessageIcon,
  Publish as PublishIcon,
  History as HistoryIcon,
  GetApp as GetAppIcon,
  Settings as SettingsIcon,
  ViewColumn as ViewColumnIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { terminalAPI, messageAPI, fileAPI } from '../../services/api'; // 使用API服务代替直接的axios
import { formatDateTime,formatDate, isWithinMinutes, formatOfflineDuration } from '../../utils/dateUtils'; // 使用统一的时间处理工具
import MerchantAutocomplete from '../../components/MerchantAutocomplete'; // 导入商户下拉框组件
import { parseErrorMessage } from '../../utils/errorHandler';
import { FieldRenderers } from '../../components/FieldRenderers'; // 导入字段渲染器
import { useFieldConfig } from '../../hooks/useFieldConfig';

// 终端操作按钮组件
const TerminalActionButtons = ({ terminal, onViewDetails, onSendMessage, onPublishFile, onViewEvents }) => {
  // 添加调试信息和缓存清理
  console.log('TerminalActionButtons渲染，权限常量:', {
    SEND_MESSAGE: PERMISSIONS.TERMINAL.SEND_MESSAGE,
    PUBLISH_FILE: PERMISSIONS.TERMINAL.PUBLISH_FILE
  });

  // 清理权限缓存（临时调试用）
  React.useEffect(() => {
    // 清理localStorage中的权限缓存
    const keys = Object.keys(localStorage);
    keys.forEach(key => {
      if (key.includes('feature_permissions_')) {
        console.log('清理权限缓存:', key);
        localStorage.removeItem(key);
      }
    });
  }, []);

  return (
    <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
      {/* 查看详情 - 所有用户都可以查看，不需要功能权限控制 */}
      <Tooltip title="查看详情">
        <IconButton
          size="small"
          color="primary"
          onClick={onViewDetails}
        >
          <VisibilityIcon fontSize="small" />
        </IconButton>
      </Tooltip>

      {/* 发送消息 - 需要功能权限 */}
      <FeatureGuard featureKey={PERMISSIONS.TERMINAL.SEND_MESSAGE} showDebugInfo={true}>
        <Tooltip title="发送消息">
          <IconButton
            size="small"
            color="secondary"
            onClick={onSendMessage}
          >
            <MessageIcon fontSize="small" />
          </IconButton>
        </Tooltip>
      </FeatureGuard>

      {/* 发布文件 - 需要功能权限 */}
      <FeatureGuard featureKey={PERMISSIONS.TERMINAL.PUBLISH_FILE}>
        <Tooltip title="发布文件">
          <IconButton
            size="small"
            color="success"
            onClick={onPublishFile}
          >
            <PublishIcon fontSize="small" />
          </IconButton>
        </Tooltip>
      </FeatureGuard>

      {/* 查看事件 - 所有用户都可以查看，不需要功能权限控制 */}
      <Tooltip title="查看事件">
        <IconButton
          size="small"
          color="info"
          onClick={onViewEvents}
        >
          <HistoryIcon fontSize="small" />
        </IconButton>
      </Tooltip>
    </Box>
  );
};

// 可排序的表头单元格组件
const SortableTableCell = ({ children, sortKey, orderBy, order, onRequestSort, ...props }) => {
  const handleSort = () => {
    if (sortKey) {
      onRequestSort(sortKey);
    }
  };

  return (
    <ResponsiveTableCell {...props}>
      {sortKey ? (
        <TableSortLabel
          active={orderBy === sortKey}
          direction={orderBy === sortKey ? order : 'asc'}
          onClick={handleSort}
          sx={{
            '& .MuiTableSortLabel-icon': {
              fontSize: '1rem'
            }
          }}
        >
          {children}
        </TableSortLabel>
      ) : (
        children
      )}
    </ResponsiveTableCell>
  );
};

const TerminalList = () => {
  const navigate = useNavigate();

  // 字段配置管理 - 必须在所有Hook调用完成后再进行条件返回
  const { fieldConfig } = useFieldConfig();

  // 注意：页面访问权限通过菜单权限控制，这里不再进行页面级别权限检查
  const [terminals, setTerminals] = useState([]);
  const [stats, setStats] = useState({
    totalCount: 0,
    activeCount: 0,
    inactiveCount: 0,
    registeredLineCount: 0,
    unregisteredLineCount: 0
  });
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);

  // 筛选条件
  const [filters, setFilters] = useState({
    merchantId: '',
    lineNo: '',
    deviceNo: '',
    machineId: '',
    terminalType: '',
    activeStatus: '',
    licensePlate: ''
  });

  // 选中的商户
  const [selectedMerchant, setSelectedMerchant] = useState(null);

  // 防重复请求
  const loadTerminalsRef = useRef();
  const hasLoadedRef = useRef(false);

  // 排序状态
  const [orderBy, setOrderBy] = useState('');
  const [order, setOrder] = useState('asc');

  // 列管理状态
  const [columnSettings, setColumnSettings] = useState(() => {
    const saved = localStorage.getItem('terminalList_columnSettings');
    return saved ? JSON.parse(saved) : {};
  });
  const [columnMenuAnchor, setColumnMenuAnchor] = useState(null);



  // 消息提示
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success' // 'success', 'error', 'warning', 'info'
  });

  // 获取启用的字段列表
  const enabledFields = useMemo(() => {
    if (!fieldConfig) return [];

    const allFields = [];
    Object.values(fieldConfig.fieldCategories || {}).forEach(category => {
      Object.values(category.fields || {}).forEach(field => {
        // 字段必须在配置文件中启用，并且在列设置中可见
        const isEnabledInConfig = field.enabled;
        const isVisibleInSettings = columnSettings[field.key]?.visible !== false; // 默认可见

        if (isEnabledInConfig && isVisibleInSettings) {
          // 合并字段配置和列设置
          const mergedField = {
            ...field,
            // 如果列设置中有宽度，使用列设置的宽度，否则使用字段配置的宽度
            width: columnSettings[field.key]?.width || field.width
          };
          allFields.push(mergedField);
        }
      });
    });

    // 按order排序
    return allFields.sort((a, b) => (a.order || 999) - (b.order || 999));
  }, [fieldConfig, columnSettings]);

  // 字段渲染函数
  const renderFieldValue = useCallback((field, terminal) => {
    if (!field || !terminal) return '-';

    // 获取字段值
    const getValue = (path, data) => {
      return path.split('.').reduce((obj, key) => {
        if (obj && typeof obj === 'object') {
          return obj[key];
        }
        return undefined;
      }, data);
    };

    let value = getValue(field.dataPath, terminal);

    // 处理模板变量
    if (typeof value === 'string' && value.includes('{')) {
      value = value.replace(/\{(\w+)\}/g, (match, key) => {
        return getValue(key, terminal) || match;
      });
    }

    // 根据字段类型渲染
    switch (field.type) {
      case 'date':
        return value ? formatDate(value) : '-';

      case 'datetime':
        return value ? formatDateTime(value) : '-';

      case 'status':
        if (field.key === 'status') {
          const status = terminal.status;
          if (!status) return <Chip label="未知" size="small" color="default" />;

          const isActive = status.activeStatus === 1 && isWithinMinutes(status.lastActiveTime, 5);
          if (isActive) {
            return <Chip label="在线" size="small" color="success" />;
          } else {
            // 显示离线时长
            const offlineDuration = formatOfflineDuration(status.lastActiveTime);
            return <Chip label={offlineDuration} size="small" color="error" />;
          }
        }
        return value ? <Chip label={String(value)} size="small" /> : '-';

      case 'offlineDuration':
        if (!terminal.status) return '-';

        // 判断是否在线
        const isOnline = terminal.status.activeStatus === 1 && isWithinMinutes(terminal.status.lastActiveTime, 5);

        if (isOnline) {
          return '-'; // 在线时不显示离线时长
        }

        // 显示离线时长
        return formatOfflineDuration(terminal.status.lastActiveTime);

      case 'fileVersion':
        if (field.config?.versionKeys && terminal.status?.fileVersionMetadata) {
          let currentVersion = '-';
          let versionKey = '-';

          // 根据配置的versionKeys查找版本
          for (const keyTemplate of field.config.versionKeys) {
            const processedKey = keyTemplate.replace(/\{(\w+)\}/g, (match, varName) => {
              return getValue(varName, terminal) || match;
            });

            const versionData = terminal.status.fileVersionMetadata[processedKey];
            if (versionData?.current) {
              currentVersion = versionData.current;
              versionKey = processedKey;
              break;
            }
          }

          // 如果没有找到版本，显示 "-"
          if (currentVersion === '-') {
            return '-';
          }

          // 返回版本号在上，文件类型在下的格式
          return (
            <Box>
              <Typography variant="body2" fontWeight="bold" sx={{ lineHeight: 1.2 }}>
                {currentVersion}
              </Typography>
              {versionKey !== '-' && (
                <Typography variant="caption" color="textSecondary" sx={{ lineHeight: 1 }}>
                  {versionKey}
                </Typography>
              )}
            </Box>
          );
        }
        return value ? String(value) : '-';

      case 'percentage':
        return FieldRenderers.percentage(value, field, terminal);

      case 'signal':
        if (typeof value === 'number') {
          const level = value >= 80 ? 'success' : value >= 50 ? 'warning' : 'error';
          return <Chip label={`${value}%`} size="small" color={level} />;
        }
        return value ? String(value) : '-';

      case 'temperature':
        if (typeof value === 'number') {
          const level = value >= 70 ? 'error' : value >= 50 ? 'warning' : 'success';
          return <Chip label={`${value}°C`} size="small" color={level} />;
        }
        return value ? String(value) : '-';

      case 'networkType':
        if (value) {
          const typeMap = {
            'wifi': { label: 'WiFi', color: 'primary' },
            '4g': { label: '4G', color: 'success' },
            '5g': { label: '5G', color: 'info' },
            'ethernet': { label: '以太网', color: 'default' }
          };
          const valueStr = String(value);
          const type = typeMap[valueStr.toLowerCase()] || { label: valueStr, color: 'default' };
          return <Chip label={type.label} size="small" color={type.color} />;
        }
        return '-';

      case 'text':
      default:
        // 使用FieldRenderers处理text类型，支持tooltip配置
        return FieldRenderers.text(value, field, terminal);
    }
  }, [formatDate, formatDateTime, isWithinMinutes, setSnackbar]);

  // 消息发送对话框
  const [messageDialog, setMessageDialog] = useState(false);
  const [selectedTerminals, setSelectedTerminals] = useState([]);
  const [messageType, setMessageType] = useState('');
  const [messageContent, setMessageContent] = useState('');
  const [messageTypes, setMessageTypes] = useState([]);
  const [selectedMessageType, setSelectedMessageType] = useState(null); // 选中的消息类型详细信息
  const [currentTerminalMerchantForMessage, setCurrentTerminalMerchantForMessage] = useState(null);

  // 文件发布对话框
  const [fileDialog, setFileDialog] = useState(false);
  const [selectedFileVersion, setSelectedFileVersion] = useState(null);
  const [fileVersions, setFileVersions] = useState([]);
  const [publishLoading, setPublishLoading] = useState(false);
  const [fileVersionsLoading, setFileVersionsLoading] = useState(false);

  // 文件版本分页和筛选
  const [fileVersionPage, setFileVersionPage] = useState(0);
  const [fileVersionRowsPerPage, setFileVersionRowsPerPage] = useState(5);
  const [fileVersionTotalCount, setFileVersionTotalCount] = useState(0);
  const [fileVersionFilters, setFileVersionFilters] = useState({
    fileTypeId: '',
    filePara: '',
    ver: ''
  });
  const [fileTypes, setFileTypes] = useState([]);
  const [currentTerminalMerchant, setCurrentTerminalMerchant] = useState(null);



  // 显示消息提示
  const showMessage = (message, severity = 'success') => {
    setSnackbar({
      open: true,
      message,
      severity
    });
  };

  // 关闭消息提示
  const handleCloseSnackbar = () => {
    setSnackbar(prev => ({ ...prev, open: false }));
  };

  // 处理未注册线路卡片点击
  const handleUnregisteredLinesClick = () => {
    navigate('/app/unregistered-lines');
  };

  // 处理已注册线路卡片点击
  const handleRegisteredLinesClick = () => {
    navigate('/app/fare-params');
  };

  // 处理文件版本筛选条件变更
  const handleFileVersionFilterChange = (event) => {
    const { name, value } = event.target;
    setFileVersionFilters(prev => ({ ...prev, [name]: value }));
  };

  // 应用文件版本筛选
  const applyFileVersionFilters = () => {
    setFileVersionPage(0);
    loadFileVersionsForPublish();
  };

  // 清除文件版本筛选
  const clearFileVersionFilters = () => {
    setFileVersionFilters({
      fileTypeId: '',
      filePara: '',
      ver: ''
    });
    setFileVersionPage(0);
    loadFileVersionsForPublish();
  };

  // 处理文件版本分页变更
  const handleFileVersionPageChange = (_, newPage) => {
    setFileVersionPage(newPage);
  };

  const handleFileVersionRowsPerPageChange = (event) => {
    setFileVersionRowsPerPage(parseInt(event.target.value, 10));
    setFileVersionPage(0);
  };

  // 选择文件版本
  const handleSelectFileVersion = (version) => {
    setSelectedFileVersion(version);
  };

  // 加载终端列表
  const loadTerminals = async (isInitialLoad = false, forceLoad = false) => {
    try {
      setLoading(true);

      // 构建查询参数
      const params = {
        page: page + 1,
        pageSize: rowsPerPage,
        ...Object.fromEntries(
          Object.entries(filters).filter(([_, value]) => value !== '')
        )
      };

      // 如果选择了商户，使用商户ID
      if (selectedMerchant) {
        params.merchantId = selectedMerchant.merchantID;
      }

      // 移除undefined的参数
      Object.keys(params).forEach(key => {
        if (params[key] === undefined) {
          delete params[key];
        }
      });

      // 避免重复请求：检查参数是否真的发生了变化（除非强制加载）
      const paramsString = JSON.stringify(params);
      if (!isInitialLoad && !forceLoad && loadTerminalsRef.current === paramsString) {
        console.log('TerminalList: 参数未变化，跳过重复请求');
        setLoading(false);
        return;
      }
      loadTerminalsRef.current = paramsString;

      console.log('TerminalList: 执行数据请求', params);
      // 并行加载终端列表和统计数据
      const [terminalsResponse, statsResponse] = await Promise.all([
        terminalAPI.getTerminals(params),
        terminalAPI.getTerminalStats({
          merchantId: selectedMerchant ? selectedMerchant.merchantID : undefined
        })
      ]);

      setTerminals(terminalsResponse.items);
      setTotalCount(terminalsResponse.totalCount);
      setStats(statsResponse);
    } catch (error) {
      console.error('Error loading terminals:', error);
      const errorMessage = parseErrorMessage(error, '加载终端列表失败');
      showMessage(errorMessage, 'error');
    } finally {
      setLoading(false);
    }
  };

  // 加载消息类型（根据商户ID）
  const loadMessageTypes = async (merchantId) => {
    if (!merchantId) return;

    try {
      const response = await messageAPI.getAllMessageTypes(merchantId);
      setMessageTypes(response || []);
    } catch (error) {
      console.error('Error loading message types:', error);
      showMessage('加载消息类型失败', 'error');
    }
  };

  // 加载文件版本（用于发布对话框）
  const loadFileVersionsForPublish = async () => {
    if (!currentTerminalMerchant) return;

    setFileVersionsLoading(true);
    try {
      const params = {
        merchantId: currentTerminalMerchant.merchantID,
        page: fileVersionPage + 1,
        pageSize: fileVersionRowsPerPage,
        ...Object.fromEntries(
          Object.entries(fileVersionFilters).filter(([_, value]) => value !== '')
        )
      };

      const response = await fileAPI.getFileVersions(params);
      setFileVersions(response.items || []);
      setFileVersionTotalCount(response.totalCount || 0);
    } catch (error) {
      console.error('Error loading file versions:', error);
      showMessage('加载文件版本失败', 'error');
    } finally {
      setFileVersionsLoading(false);
    }
  };

  // 加载文件类型（用于筛选）
  const loadFileTypesForPublish = async () => {
    if (!currentTerminalMerchant) return;

    try {
      const response = await fileAPI.getAllFileTypes();
      const merchantFileTypes = response.items?.filter(
        type => type.merchantID === currentTerminalMerchant.merchantID
      ) || [];
      setFileTypes(merchantFileTypes);
    } catch (error) {
      console.error('Error loading file types:', error);
    }
  };

  // 统一的数据加载逻辑
  useEffect(() => {
    // 防止重复加载
    if (hasLoadedRef.current) {
      console.log('TerminalList: 已加载过，跳过重复请求');
      return;
    }

    console.log('TerminalList: 执行首次加载');
    hasLoadedRef.current = true;
    loadTerminals(true, false); // 标记为初始加载，非强制加载
  }, []);

  // 当参数变化时重新加载
  useEffect(() => {
    if (hasLoadedRef.current) { // 只有在首次加载完成后才响应参数变化
      console.log('TerminalList: 参数变化，重新加载');
      loadTerminals(false, false); // 非初始加载，非强制加载
    }
  }, [page, rowsPerPage, filters, selectedMerchant]);

  // 监听文件版本相关状态变化
  useEffect(() => {
    if (currentTerminalMerchant && fileDialog) {
      loadFileVersionsForPublish();
      loadFileTypesForPublish();
    }
  }, [currentTerminalMerchant, fileDialog, fileVersionPage, fileVersionRowsPerPage]);

  // 处理筛选条件变更
  const handleFilterChange = (event) => {
    const { name, value } = event.target;
    setFilters(prev => ({ ...prev, [name]: value }));
  };

  // 应用筛选
  const applyFilters = () => {
    setPage(0);
    loadTerminals(false, true); // 强制执行搜索
  };

  // 清除筛选
  const clearFilters = () => {
    setFilters({
      merchantId: '',
      lineNo: '',
      deviceNo: '',
      machineId: '',
      terminalType: '',
      activeStatus: '',
      licensePlate: ''
    });
    setSelectedMerchant(null);
    setPage(0);
    loadTerminals(false, true); // 强制执行搜索
  };

  // 处理分页变更
  const handleChangePage = (_, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // 处理消息类型选择
  const handleMessageTypeChange = (typeCode) => {
    setMessageType(typeCode);
    // 查找选中的消息类型详细信息
    const selectedType = messageTypes.find(type => type.code === typeCode);
    setSelectedMessageType(selectedType);

    // 如果有示例消息，可以预填充到内容框中
    if (selectedType && selectedType.exampleMessage) {
      setMessageContent(selectedType.exampleMessage);
    } else {
      setMessageContent('');
    }
  };

  // 打开消息对话框
  const openMessageDialog = (terminal) => {
    setSelectedTerminals([terminal]);
    setCurrentTerminalMerchantForMessage({ merchantID: terminal.merchantID, name: terminal.merchantName });
    setMessageType('');
    setMessageContent('');
    setSelectedMessageType(null);
    setMessageDialog(true);
    // 加载该商户的消息类型
    loadMessageTypes(terminal.merchantID);
  };

  // 发送消息
  const sendMessage = async () => {
    try {
      await terminalAPI.sendMessage(
        selectedTerminals.map(t => t.id),
        messageType,
        messageContent,
        currentTerminalMerchantForMessage.merchantID
      );

      // 发送成功
      showMessage('消息发送成功！', 'success');
      setMessageDialog(false);
      setMessageType('');
      setMessageContent('');

      // 刷新终端列表
      //loadTerminals();
    } catch (error) {
      console.error('Error sending message:', error);

      const errorMessage = parseErrorMessage(error, '消息发送失败');
      showMessage(errorMessage, 'error');
    }
  };

  // 打开文件发布对话框
  const openFileDialog = (terminal) => {
    setSelectedTerminals([terminal]);
    setCurrentTerminalMerchant({ merchantID: terminal.merchantID, name: terminal.merchantName });
    setSelectedFileVersion(null);
    // 重置筛选和分页
    setFileVersionFilters({
      fileTypeId: '',
      filePara: '',
      ver: ''
    });
    setFileVersionPage(0);
    setFileDialog(true);
  };

  // 发布文件
  const publishFile = async () => {
    if (!selectedFileVersion) {
      showMessage('请选择要发布的文件版本', 'warning');
      return;
    }

    setPublishLoading(true);
    try {
      await terminalAPI.publishFile(
        selectedTerminals.map(t => t.id),
        selectedFileVersion.id
      );

      // 发布成功
      showMessage(`文件版本 ${selectedFileVersion.fileFullType}-${selectedFileVersion.ver} 发布成功！`, 'success');
      setFileDialog(false);
      setSelectedFileVersion(null);

      // 刷新终端列表
      //loadTerminals();
    } catch (error) {
      console.error('Error publishing file:', error);

      const errorMessage = parseErrorMessage(error, '文件发布失败');
      showMessage(errorMessage, 'error');
    } finally {
      setPublishLoading(false);
    }
  };

  // 导出终端列表
  const exportTerminals = async () => {
    try {
      // 构建请求数据
      const requestData = {
        // 筛选条件
        merchantId: selectedMerchant?.merchantID || null,
        lineNo: filters.lineNo || null,
        deviceNo: filters.deviceNo || null,
        machineId: filters.machineId || null,
        terminalType: filters.terminalType || null,
        activeStatus: filters.activeStatus || null,
        licensePlate: filters.licensePlate || null,

        // 字段配置
        fields: enabledFields.map(field => ({
          key: field.key,
          displayName: field.displayName,
          dataPath: field.dataPath,
          type: field.type,
          config: field.config
        }))
      };

      //showMessage('正在导出终端列表...', 'info');

      // 调用API导出
      const blob = await terminalAPI.exportTerminals(requestData);

      // 创建下载链接
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;

      // 生成文件名
      const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
      link.download = `terminals_${timestamp}.csv`;

      // 触发下载
      document.body.appendChild(link);
      link.click();

      // 清理
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      //showMessage('终端列表导出成功！', 'success');
    } catch (error) {
      console.error('Error exporting terminals:', error);
      const errorMessage = parseErrorMessage(error, '导出终端列表失败');
      showMessage(errorMessage, 'error');
    }
  };



  // 格式化文件大小
  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 处理排序
  const handleRequestSort = (property) => {
    const isAsc = orderBy === property && order === 'asc';
    setOrder(isAsc ? 'desc' : 'asc');
    setOrderBy(property);

    // 重新获取数据（如果后端支持排序）
    // 或者在前端排序
    const sortedTerminals = [...terminals].sort((a, b) => {
      let aValue = getNestedValue(a, property);
      let bValue = getNestedValue(b, property);

      // 处理不同数据类型
      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (aValue < bValue) {
        return isAsc ? 1 : -1;
      }
      if (aValue > bValue) {
        return isAsc ? -1 : 1;
      }
      return 0;
    });

    setTerminals(sortedTerminals);
  };

  // 获取嵌套属性值
  const getNestedValue = (obj, path) => {
    return path.split('.').reduce((current, key) => current?.[key], obj) || '';
  };

  // 处理列设置变更
  const handleColumnSettingChange = (columnKey, setting, value) => {
    const newSettings = {
      ...columnSettings,
      [columnKey]: {
        ...columnSettings[columnKey],
        [setting]: value
      }
    };
    setColumnSettings(newSettings);
    localStorage.setItem('terminalList_columnSettings', JSON.stringify(newSettings));
  };

  // 重置列设置
  const resetColumnSettings = () => {
    const defaultSettings = {};

    // 基于字段配置生成默认设置
    if (fieldConfig) {
      Object.values(fieldConfig.fieldCategories || {}).forEach(category => {
        Object.values(category.fields || {}).forEach(field => {
          if (field.enabled) {
            defaultSettings[field.key] = {
              visible: true,
              width: field.width || 120
            };
          }
        });
      });
    }

    setColumnSettings(defaultSettings);
    localStorage.setItem('terminalList_columnSettings', JSON.stringify(defaultSettings));
  };

  // 获取所有可配置的字段（用于列设置菜单）
  const configurableFields = useMemo(() => {
    if (!fieldConfig) return [];

    const allFields = [];
    Object.values(fieldConfig.fieldCategories || {}).forEach(category => {
      Object.values(category.fields || {}).forEach(field => {
        if (field.enabled) {
          allFields.push(field);
        }
      });
    });

    // 按order排序
    return allFields.sort((a, b) => (a.order || 999) - (b.order || 999));
  }, [fieldConfig]);

  // 获取列的显示名称
  const getColumnDisplayName = (field) => {
    return field.displayName || field.key;
  };

  // 页面访问权限通过菜单权限控制，不再进行页面级别权限检查

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        终端管理
      </Typography>

      {/* 统计卡片 */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={2.4}>
          <Card>
            <CardContent>
              <Typography variant="h6" color="textSecondary">
                总终端数
              </Typography>
              <Typography variant="h3">
                {stats.totalCount}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2.4}>
          <Card>
            <CardContent>
              <Typography variant="h6" color="textSecondary">
                在线设备
              </Typography>
              <Typography variant="h3" color="success.main">
                {stats.activeCount}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2.4}>
          <Card>
            <CardContent>
              <Typography variant="h6" color="textSecondary">
                离线设备
              </Typography>
              <Typography variant="h3" color="error.main">
                {stats.inactiveCount}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2.4}>
          <Card>
            <CardContent>
              <Typography variant="h6" color="textSecondary">
                正常线路数
              </Typography>
              <Typography variant="h3" color="primary.main">
                {stats.registeredLineCount}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2.4}>
          <Card
            sx={{
              cursor: 'pointer',
              '&:hover': {
                backgroundColor: 'action.hover',
                transform: 'translateY(-2px)',
                transition: 'all 0.2s ease-in-out'
              }
            }}
            onClick={handleUnregisteredLinesClick}
          >
            <CardContent>
              <Typography variant="h6" color="textSecondary">
                未注册线路
              </Typography>
              <Typography variant="h3" color="warning.main">
                {stats.unregisteredLineCount}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* 筛选条件 */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={6} md={2}>
            <MerchantAutocomplete
              label="商户"
              value={selectedMerchant}
              onChange={(_, newValue) => setSelectedMerchant(newValue)}
              fullWidth
              size="small"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <TextField
              fullWidth
              label="线路编号"
              name="lineNo"
              value={filters.lineNo}
              onChange={handleFilterChange}
              size="small"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <TextField
              fullWidth
              label="设备编号"
              name="deviceNo"
              value={filters.deviceNo}
              onChange={handleFilterChange}
              size="small"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <TextField
              fullWidth
              label="出厂序列号"
              name="machineId"
              value={filters.machineId}
              onChange={handleFilterChange}
              size="small"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <TextField
              fullWidth
              label="终端类型"
              name="terminalType"
              value={filters.terminalType}
              onChange={handleFilterChange}
              size="small"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <TextField
              fullWidth
              select
              label="状态"
              name="activeStatus"
              value={filters.activeStatus}
              onChange={handleFilterChange}
              size="small"
            >
              <MenuItem value="">全部</MenuItem>
              <MenuItem value="1">在线</MenuItem>
              <MenuItem value="2">离线</MenuItem>
            </TextField>
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <TextField
              fullWidth
              label="车牌号"
              name="licensePlate"
              value={filters.licensePlate}
              onChange={handleFilterChange}
              size="small"
              placeholder="输入车牌号"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={1}>
            <Button
              fullWidth
              variant="contained"
              color="primary"
              startIcon={<SearchIcon />}
              onClick={applyFilters}
            >
              搜索
            </Button>
          </Grid>
          <Grid item xs={12} sm={6} md={1}>
            <Button
              fullWidth
              variant="outlined"
              startIcon={<ClearIcon />}
              onClick={clearFilters}
            >
              清除
            </Button>
          </Grid>
          <Grid item xs={12} sm={6} md={1}>
            <Button
              fullWidth
              variant="outlined"
              color="primary"
              startIcon={<RefreshIcon />}
              onClick={loadTerminals}
            >
              刷新
            </Button>
          </Grid>
          <Grid item xs={12} sm={6} md={1}>
            <Button
              fullWidth
              variant="outlined"
              color="secondary"
              startIcon={<GetAppIcon />}
              onClick={exportTerminals}
            >
              导出
            </Button>
          </Grid>
          <Grid item xs={12} sm={6} md={1}>
            <Button
              fullWidth
              variant="outlined"
              startIcon={<ViewColumnIcon />}
              onClick={(e) => setColumnMenuAnchor(e.currentTarget)}
            >
              列设置
            </Button>
          </Grid>

        </Grid>
      </Paper>

      {/* 终端列表 */}
      <Paper>
        <ResponsiveTable minWidth={1200} stickyActions={true}>
          <ResponsiveTableHead>
            <ResponsiveTableRow>
              {/* 固定的终端ID列 */}
              <SortableTableCell
                sortKey="id"
                orderBy={orderBy}
                order={order}
                onRequestSort={handleRequestSort}
              >
                终端ID
              </SortableTableCell>

              {/* 动态字段列 */}
              {enabledFields.map((field) => {
                const CellComponent = field.sortable ? SortableTableCell : ResponsiveTableCell;
                const cellProps = {
                  ...(field.hideOn && { hideOn: field.hideOn }),
                  ...(field.width && { minWidth: field.width }),
                  ...(field.sortable && {
                    sortKey: field.sortKey || field.dataPath,
                    orderBy,
                    order,
                    onRequestSort: handleRequestSort
                  })
                };

                return (
                  <CellComponent key={field.key} {...cellProps}>
                    {field.displayName}
                  </CellComponent>
                );
              })}

              {/* 固定的操作列 */}
              <ResponsiveTableCell sticky={true} minWidth={160}>操作</ResponsiveTableCell>
            </ResponsiveTableRow>
          </ResponsiveTableHead>
          <ResponsiveTableBody>
            {loading ? (
              <ResponsiveTableRow>
                <ResponsiveTableCell colSpan={enabledFields.length + 2} align="center">
                  加载中...
                </ResponsiveTableCell>
              </ResponsiveTableRow>
            ) : terminals.length === 0 ? (
              <ResponsiveTableRow>
                <ResponsiveTableCell colSpan={enabledFields.length + 2} align="center">
                  没有找到终端
                </ResponsiveTableCell>
              </ResponsiveTableRow>
            ) : (
              terminals.map((terminal) => (
                <ResponsiveTableRow key={terminal.id}>
                  {/* 固定的终端ID列 */}
                  <ResponsiveTableCell>
                    <Box>
                      <Typography variant="body2" fontWeight="bold">
                        {terminal.id}
                      </Typography>
                      {/* 在小屏幕上显示商户信息 */}
                      <Typography variant="caption" color="textSecondary" sx={{ display: { xs: 'block', sm: 'none' } }}>
                        {terminal.merchantName}
                      </Typography>
                    </Box>
                  </ResponsiveTableCell>

                  {/* 动态字段列 */}
                  {enabledFields.map((field) => {
                    const cellProps = {
                      ...(field.hideOn && { hideOn: field.hideOn }),
                      ...(field.width && { minWidth: field.width })
                    };

                    return (
                      <ResponsiveTableCell key={field.key} {...cellProps}>
                        {renderFieldValue(field, terminal)}
                      </ResponsiveTableCell>
                    );
                  })}
                  <ResponsiveTableCell sticky={true}>
                    <TerminalActionButtons
                      terminal={terminal}
                      onViewDetails={() => navigate(`/app/terminals/${terminal.id}`)}
                      onSendMessage={() => openMessageDialog(terminal)}
                      onPublishFile={() => openFileDialog(terminal)}
                      onViewEvents={() => navigate(`/app/terminals/${terminal.id}/events`)}
                    />
                  </ResponsiveTableCell>
                </ResponsiveTableRow>
              ))
            )}
          </ResponsiveTableBody>
        </ResponsiveTable>
        <TablePagination
          rowsPerPageOptions={[5, 10, 25, 50]}
          component="div"
          count={totalCount}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
          labelRowsPerPage="每页行数:"
          labelDisplayedRows={({ from, to, count }) => `${from}-${to} 共 ${count}`}
        />
      </Paper>

      {/* 消息发送对话框 */}
      <Dialog open={messageDialog} onClose={() => setMessageDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          发送消息
          <Typography variant="subtitle2" color="textSecondary">
            终端: {selectedTerminals.map(t => `${t.id}(${t.deviceNO})`).join(', ')} |
            商户: {currentTerminalMerchantForMessage?.name}
          </Typography>
        </DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <FormControl fullWidth sx={{ mt: 2 }}>
              <InputLabel>消息类型</InputLabel>
              <Select
                value={messageType}
                onChange={(e) => handleMessageTypeChange(e.target.value)}
                label="消息类型"
              >
                {messageTypes.map((type) => (
                  <MenuItem key={type.code} value={type.code}>
                    {type.code} - {type.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            {/* 消息类型详细信息 */}
            {selectedMessageType && (
              <Paper sx={{ p: 2, mt: 2, backgroundColor: 'primary.light', color: 'primary.contrastText' }}>
                <Typography variant="subtitle1" gutterBottom>
                  消息类型信息
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="body2">
                      <strong>编码:</strong> {selectedMessageType.code}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="body2">
                      <strong>编码类型:</strong> {selectedMessageType.codeType === 1 ? 'ASCII' : 'HEX'}
                    </Typography>
                  </Grid>
                  {selectedMessageType.remark && (
                    <Grid item xs={12}>
                      <Typography variant="body2">
                        <strong>备注:</strong> {selectedMessageType.remark}
                      </Typography>
                    </Grid>
                  )}
                  {selectedMessageType.exampleMessage && (
                    <Grid item xs={12}>
                      <Typography variant="body2">
                        <strong>示例:</strong> {selectedMessageType.exampleMessage}
                      </Typography>
                    </Grid>
                  )}
                </Grid>
              </Paper>
            )}

            <TextField
              fullWidth
              multiline
              rows={4}
              label="消息内容"
              value={messageContent}
              onChange={(e) => setMessageContent(e.target.value)}
              sx={{ mt: 2 }}
              helperText={selectedMessageType ? "请根据上方的消息类型信息和示例输入消息内容" : "请先选择消息类型"}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setMessageDialog(false)}>取消</Button>
          <Button
            onClick={sendMessage}
            variant="contained"
            color="primary"
            disabled={!messageType || !messageContent}
          >
            发送
          </Button>
        </DialogActions>
      </Dialog>

      {/* 文件发布对话框 */}
      <Dialog open={fileDialog} onClose={() => setFileDialog(false)} maxWidth="lg" fullWidth>
        <DialogTitle>
          发布文件到终端
          <Typography variant="subtitle2" color="textSecondary">
            终端: {selectedTerminals.map(t => `${t.id}(${t.deviceNO})`).join(', ')} |
            商户: {currentTerminalMerchant?.name}
          </Typography>
        </DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            {/* 筛选条件 */}
            <Paper sx={{ p: 2, mb: 2 }}>
              <Typography variant="subtitle1" gutterBottom>筛选条件</Typography>
              <Grid container spacing={2} alignItems="center">
                <Grid item xs={12} sm={3}>
                  <TextField
                    fullWidth
                    select
                    label="文件类型"
                    name="fileTypeId"
                    value={fileVersionFilters.fileTypeId}
                    onChange={handleFileVersionFilterChange}
                    size="small"
                  >
                    <MenuItem value="">全部</MenuItem>
                    {fileTypes.map((type) => (
                      <MenuItem key={type.code} value={type.code}>
                        {type.code} - {type.name}
                      </MenuItem>
                    ))}
                  </TextField>
                </Grid>
                <Grid item xs={12} sm={3}>
                  <TextField
                    fullWidth
                    label="文件参数"
                    name="filePara"
                    value={fileVersionFilters.filePara}
                    onChange={handleFileVersionFilterChange}
                    size="small"
                  />
                </Grid>
                <Grid item xs={12} sm={3}>
                  <TextField
                    fullWidth
                    label="版本号"
                    name="ver"
                    value={fileVersionFilters.ver}
                    onChange={handleFileVersionFilterChange}
                    size="small"
                  />
                </Grid>
                <Grid item xs={12} sm={3}>
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <Button
                      variant="contained"
                      size="small"
                      startIcon={<SearchIcon />}
                      onClick={applyFileVersionFilters}
                    >
                      搜索
                    </Button>
                    <Button
                      variant="outlined"
                      size="small"
                      startIcon={<ClearIcon />}
                      onClick={clearFileVersionFilters}
                    >
                      清除
                    </Button>
                  </Box>
                </Grid>
              </Grid>
            </Paper>

            {/* 文件版本列表 */}
            <Paper>
              <TableContainer sx={{ maxHeight: 400 }}>
                <Table stickyHeader>
                  <TableHead>
                    <TableRow>
                      <TableCell padding="checkbox">选择</TableCell>
                      <TableCell>文件类型</TableCell>
                      <TableCell>文件参数</TableCell>
                      <TableCell>版本号</TableCell>
                      <TableCell>文件大小</TableCell>
                      <TableCell>CRC校验</TableCell>
                      <TableCell>创建时间</TableCell>
                      <TableCell>操作人</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {fileVersionsLoading ? (
                      <TableRow>
                        <TableCell colSpan={8} align="center">
                          <CircularProgress size={24} />
                        </TableCell>
                      </TableRow>
                    ) : fileVersions.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={8} align="center">
                          没有找到文件版本
                        </TableCell>
                      </TableRow>
                    ) : (
                      fileVersions.map((version) => (
                        <TableRow
                          key={version.id}
                          hover
                          selected={selectedFileVersion?.id === version.id}
                          onClick={() => handleSelectFileVersion(version)}
                          sx={{ cursor: 'pointer' }}
                        >
                          <TableCell padding="checkbox">
                            <input
                              type="radio"
                              checked={selectedFileVersion?.id === version.id}
                              onChange={() => handleSelectFileVersion(version)}
                            />
                          </TableCell>
                          <TableCell>{version.fileTypeName}({version.fileTypeID})</TableCell>
                          <TableCell>{version.filePara || '-'}</TableCell>
                          <TableCell>{version.ver}</TableCell>
                          <TableCell>{formatFileSize(version.fileSize)}</TableCell>
                          <TableCell>{version.crc}</TableCell>
                          <TableCell>{formatDateTime(version.createTime)}</TableCell>
                          <TableCell>{version.operator || '-'}</TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </TableContainer>
              <TablePagination
                rowsPerPageOptions={[5, 10, 25]}
                component="div"
                count={fileVersionTotalCount}
                rowsPerPage={fileVersionRowsPerPage}
                page={fileVersionPage}
                onPageChange={handleFileVersionPageChange}
                onRowsPerPageChange={handleFileVersionRowsPerPageChange}
                labelRowsPerPage="每页行数:"
                labelDisplayedRows={({ from, to, count }) => `${from}-${to} 共 ${count}`}
              />
            </Paper>

            {/* 选中的文件版本信息 */}
            {selectedFileVersion && (
              <Paper sx={{ p: 2, mt: 2, backgroundColor: 'primary.light', color: 'primary.contrastText' }}>
                <Typography variant="subtitle1" gutterBottom>
                  已选择文件版本
                </Typography>
                <Typography variant="body2">
                  {selectedFileVersion.fileTypeName}({selectedFileVersion.fileTypeID}) -
                  参数: {selectedFileVersion.filePara || '无'} -
                  版本: {selectedFileVersion.ver} -
                  大小: {formatFileSize(selectedFileVersion.fileSize)}
                </Typography>
              </Paper>
            )}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setFileDialog(false)} disabled={publishLoading}>
            取消
          </Button>
          <Button
            onClick={publishFile}
            variant="contained"
            color="primary"
            disabled={!selectedFileVersion || publishLoading}
            startIcon={publishLoading ? <CircularProgress size={20} /> : null}
          >
            {publishLoading ? '发布中...' : '发布'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* 列管理菜单 */}
      <Menu
        anchorEl={columnMenuAnchor}
        open={Boolean(columnMenuAnchor)}
        onClose={() => setColumnMenuAnchor(null)}
        slotProps={{
          paper: {
            sx: { width: 280, maxHeight: 400 }
          }
        }}
      >
        <Box sx={{ p: 2 }}>
          <Typography variant="subtitle1" gutterBottom>
            列显示设置
          </Typography>
          <Divider sx={{ mb: 2 }} />

          {configurableFields.map((field) => {
            const isVisible = columnSettings[field.key]?.visible !== false; // 默认可见
            return (
              <FormControlLabel
                key={field.key}
                control={
                  <Checkbox
                    checked={isVisible}
                    onChange={(e) => handleColumnSettingChange(field.key, 'visible', e.target.checked)}
                    size="small"
                  />
                }
                label={getColumnDisplayName(field)}
                sx={{ display: 'block', mb: 1 }}
              />
            );
          })}

          <Divider sx={{ my: 2 }} />
          <Button
            fullWidth
            variant="outlined"
            size="small"
            onClick={resetColumnSettings}
          >
            重置为默认
          </Button>
        </Box>
      </Menu>

      {/* 消息提示 */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>


    </Box>
  );
};

export default TerminalList;
