# 多数据库迁移使用指南

## 📋 概述

本项目支持MySQL和SQL Server两种数据库，通过独立的迁移程序集实现完全自动化的多数据库支持。每种数据库都有专门的迁移项目，确保迁移文件的完全隔离和自动化生成。

## 🏗️ 项目结构

```
SlzrCrossGate/
├── SlzrCrossGate.Core/                    # 核心业务逻辑和DbContext
├── SlzrCrossGate.WebAdmin/                # Web管理界面
├── SlzrCrossGate.ApiService/              # API服务
├── SlzrCrossGate.Migrations.MySql/        # MySQL专用迁移项目
│   └── Migrations/                        # MySQL迁移文件
└── SlzrCrossGate.Migrations.SqlServer/    # SQL Server专用迁移项目
    └── Migrations/                        # SQL Server迁移文件
```

## ⚙️ 配置说明

### 数据库提供程序配置

在 `appsettings.json` 中配置数据库类型：

```json
{
  "DatabaseProvider": "MySql",  // 或 "SqlServer"
  "ConnectionStrings": {
    "DefaultConnection": "your_connection_string_here"
  }
}
```

### 自动迁移程序集选择

系统会根据 `DatabaseProvider` 配置自动选择对应的迁移程序集：
- **MySQL**: `SlzrCrossGate.Migrations.MySql`
- **SQL Server**: `SlzrCrossGate.Migrations.SqlServer`

## 🚀 日常开发流程

### 1. MySQL环境开发（推荐的主要开发环境）

#### 配置环境
```json
// appsettings.json
{
  "DatabaseProvider": "MySql",
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;Database=TcpserverTms;User=root;Password=your_password;"
  }
}
```

#### 生成新迁移
```bash
# 添加新功能后生成迁移
dotnet ef migrations add AddNewFeature --project SlzrCrossGate.Migrations.MySql --startup-project SlzrCrossGate.WebAdmin

# 查看待应用的迁移
dotnet ef migrations list --project SlzrCrossGate.Migrations.MySql --startup-project SlzrCrossGate.WebAdmin
```

#### 应用迁移
```bash
# 应用所有待应用的迁移
dotnet ef database update --project SlzrCrossGate.Migrations.MySql --startup-project SlzrCrossGate.WebAdmin

# 应用到指定迁移
dotnet ef database update 20250820120000_AddNewFeature --project SlzrCrossGate.Migrations.MySql --startup-project SlzrCrossGate.WebAdmin
```

### 2. SQL Server环境开发

#### 配置环境
```json
// appsettings.json
{
  "DatabaseProvider": "SqlServer",
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;Database=TcpserverTms;User Id=sa;Password=your_password;TrustServerCertificate=true;"
  }
}
```

#### 生成新迁移
```bash
# 生成SQL Server迁移
dotnet ef migrations add AddNewFeature --project SlzrCrossGate.Migrations.SqlServer --startup-project SlzrCrossGate.WebAdmin

# 查看待应用的迁移
dotnet ef migrations list --project SlzrCrossGate.Migrations.SqlServer --startup-project SlzrCrossGate.WebAdmin
```

#### 应用迁移
```bash
# 应用所有待应用的迁移
dotnet ef database update --project SlzrCrossGate.Migrations.SqlServer --startup-project SlzrCrossGate.WebAdmin
```

## 🔄 跨数据库同步流程

### 场景：从MySQL同步到SQL Server

1. **在MySQL环境完成开发**
   ```bash
   # MySQL环境下开发和测试
   dotnet ef migrations add AddNewFeature --project SlzrCrossGate.Migrations.MySql --startup-project SlzrCrossGate.WebAdmin
   dotnet ef database update --project SlzrCrossGate.Migrations.MySql --startup-project SlzrCrossGate.WebAdmin
   ```

2. **切换到SQL Server环境**
   ```json
   // 修改 appsettings.json
   {
     "DatabaseProvider": "SqlServer",
     "ConnectionStrings": {
       "DefaultConnection": "Server=localhost;Database=TcpserverTms;User Id=sa;Password=your_password;TrustServerCertificate=true;"
     }
   }
   ```

3. **为SQL Server生成对应迁移**
   ```bash
   # 生成SQL Server版本的迁移（EF会自动生成SQL Server语法）
   dotnet ef migrations add AddNewFeature --project SlzrCrossGate.Migrations.SqlServer --startup-project SlzrCrossGate.WebAdmin
   dotnet ef database update --project SlzrCrossGate.Migrations.SqlServer --startup-project SlzrCrossGate.WebAdmin
   ```

## 🛠️ 常用命令参考

### 迁移管理命令

```bash
# 查看迁移历史
dotnet ef migrations list --project SlzrCrossGate.Migrations.MySql --startup-project SlzrCrossGate.WebAdmin

# 生成迁移脚本（不直接应用到数据库）
dotnet ef migrations script --project SlzrCrossGate.Migrations.MySql --startup-project SlzrCrossGate.WebAdmin

# 回滚到指定迁移
dotnet ef database update 20250815120000_PreviousMigration --project SlzrCrossGate.Migrations.MySql --startup-project SlzrCrossGate.WebAdmin

# 移除最后一个迁移（仅在未应用到数据库时）
dotnet ef migrations remove --project SlzrCrossGate.Migrations.MySql --startup-project SlzrCrossGate.WebAdmin
```

### 数据库管理命令

```bash
# 删除数据库
dotnet ef database drop --project SlzrCrossGate.Migrations.MySql --startup-project SlzrCrossGate.WebAdmin

# 查看数据库连接信息
dotnet ef dbcontext info --project SlzrCrossGate.Migrations.MySql --startup-project SlzrCrossGate.WebAdmin
```

## ⚠️ 重要注意事项

### 1. 生产环境安全
- **MySQL生产环境**: 现有的MySQL生产环境完全不受影响，继续使用原有的迁移文件
- **迁移隔离**: 两个数据库的迁移历史完全独立，不会相互影响
- **配置隔离**: 通过`DatabaseProvider`配置确保环境隔离

### 2. 开发最佳实践
- **主要开发环境**: 建议以MySQL为主要开发环境
- **定期同步**: 建议每个版本发布前同步一次SQL Server
- **测试验证**: 在两个数据库环境都要进行充分测试
- **命名规范**: 迁移名称保持一致，便于跟踪对应关系

### 3. 迁移文件管理
- **不要手动修改**: 迁移文件由EF Core自动生成，不需要手动修改
- **版本控制**: 所有迁移文件都应该提交到版本控制系统
- **备份策略**: 重要的数据库变更前建议先备份数据库

## 🔍 故障排除

### 常见问题

1. **迁移项目找不到**
   ```bash
   # 确保项目已添加到解决方案
   dotnet sln add SlzrCrossGate.Migrations.MySql/SlzrCrossGate.Migrations.MySql.csproj
   dotnet sln add SlzrCrossGate.Migrations.SqlServer/SlzrCrossGate.Migrations.SqlServer.csproj
   ```

2. **连接字符串错误**
   ```bash
   # 检查配置文件中的连接字符串和DatabaseProvider设置
   # 确保数据库服务正在运行
   ```

3. **迁移冲突**
   ```bash
   # 查看当前迁移状态
   dotnet ef migrations list --project SlzrCrossGate.Migrations.MySql --startup-project SlzrCrossGate.WebAdmin
   
   # 如果需要，回滚到安全的迁移点
   dotnet ef database update SafeMigrationName --project SlzrCrossGate.Migrations.MySql --startup-project SlzrCrossGate.WebAdmin
   ```

## 📝 版本记录

- **v1.0**: 初始多数据库支持实现
- **当前版本**: 支持MySQL和SQL Server的完全自动化迁移

---

**维护者**: 开发团队  
**最后更新**: 2025-08-15  
**文档版本**: 1.0
