# 功能按钮控制系统设计文档（优化版）

功能权限明细

终端管理：发送消息，发布文件
文件类型：新建，编辑，删除
文件版本：上传，发布，删除
发布记录：删除
消息类型：新建，删除
线路参数：新建，编辑，删除，查看版本
线路参数版本：新建版本，复制创建，复制到其它线路，编辑，提交
银联密钥：新增密钥，批量导入，编辑，解绑，删除，导出数据
商户字典：新建，编辑，删除
票价折扣方案：新建，编辑，删除，提交版本
系统设置中字段管理：打开字段配置管理器
菜单管理：初始化默认菜单，新增分组，菜单项分组管理中的编辑和删除，菜单项管理中的添加菜单项目、编辑和删除
商户管理：新增，编辑，停用，删除
用户管理：添加，编辑，锁定，删除
角色管理：新增，编辑，删除
功能权限管理：保存更改


在初始化角色权限时：
将所有权限都给予系统管理员
将终端管理、文件类型、文件版本、发布记录、消息类型、线路参数、线路参数版本、银联密钥、票价折扣方案、用户管理 这些页面中的功能权限都给予商户管理员
普通用户不给任何功能权限


## 1. 整体架构设计

```
┌─────────────────────────────────────────────────────────────────────┐
│                      功能按钮控制系统架构                              │
├─────────────────────────────────────────────────────────────────────┤
│                                                                     │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐  │
│  │   权限管理界面   │    │   前端权限控制   │    │   后端权限验证   │  │
│  │  (管理界面)     │    │  (Hook/组件)    │    │  (API拦截器)    │  │
│  └─────────────────┘    └─────────────────┘    └─────────────────┘  │
│           │                       │                       │        │
│           ▼                       ▼                       ▼        │
│  ┌─────────────────────────────────────────────────────────────────┐│
│  │                        数据层                                   ││
│  │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ ││
│  │  │  FeatureConfig  │  │ RoleFeature     │  │ PermissionCache │ ││
│  │  │   (功能配置)     │  │ Permission      │  │   (权限缓存)     │ ││
│  │  │                 │  │ (角色权限)       │  │                 │ ││
│  │  └─────────────────┘  └─────────────────┘  └─────────────────┘ ││
│  └─────────────────────────────────────────────────────────────────┘│
│                              │                                     │
│                              ▼                                     │
│  ┌─────────────────────────────────────────────────────────────────┐│
│  │                      应用层                                     ││
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────┐ ││
│  │  │FeatureGuard │  │hasPermission│  │canPerform   │  │权限常量 │ ││
│  │  │   (组件)    │  │   (Hook)    │  │   (Hook)    │  │管理     │ ││
│  │  └─────────────┘  └─────────────┘  └─────────────┘  └─────────┘ ││
│  └─────────────────────────────────────────────────────────────────┘│
└─────────────────────────────────────────────────────────────────────┘
```

## 2. 数据模型设计

### 2.1 功能配置表 (FeatureConfig)

```csharp
/// <summary>
/// 功能配置表 - 定义系统中所有可控制的功能
/// </summary>
[Table("FeatureConfigs")]
public class FeatureConfig
{
    [Key]
    public int Id { get; set; }

    /// <summary>
    /// 功能标识符 (如: merchant.delete)
    /// </summary>
    [Required]
    [StringLength(100)]
    [Index(IsUnique = true)]
    public required string FeatureKey { get; set; }

    /// <summary>
    /// 功能名称
    /// </summary>
    [Required]
    [StringLength(100)]
    public required string FeatureName { get; set; }

    /// <summary>
    /// 功能描述
    /// </summary>
    [StringLength(500)]
    public string? Description { get; set; }

    /// <summary>
    /// 功能分组 (如: 商户管理、用户管理)
    /// </summary>
    [Required]
    [StringLength(50)]
    public required string Category { get; set; }

    /// <summary>
    /// 风险等级 (Low/Medium/High)
    /// </summary>
    [Required]
    [StringLength(20)]
    public required string RiskLevel { get; set; }

    /// <summary>
    /// 全局是否启用
    /// </summary>
    public bool IsGloballyEnabled { get; set; } = true;

    /// <summary>
    /// 是否为系统内置功能（不可删除）
    /// </summary>
    public bool IsSystemBuiltIn { get; set; } = false;

    /// <summary>
    /// 排序顺序
    /// </summary>
    public int SortOrder { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 创建者
    /// </summary>
    [StringLength(50)]
    public string? CreatedBy { get; set; }

    /// <summary>
    /// 更新者
    /// </summary>
    [StringLength(50)]
    public string? UpdatedBy { get; set; }

    /// <summary>
    /// 角色权限配置
    /// </summary>
    public virtual ICollection<RoleFeaturePermission> RolePermissions { get; set; } = new List<RoleFeaturePermission>();
}
```

### 2.2 角色功能权限表 (RoleFeaturePermission)

```csharp
/// <summary>
/// 角色功能权限表 - 定义特定角色对特定功能的权限覆盖
/// </summary>
[Table("RoleFeaturePermissions")]
public class RoleFeaturePermission
{
    [Key]
    public int Id { get; set; }

    /// <summary>
    /// 角色名称
    /// </summary>
    [Required]
    [StringLength(50)]
    public required string RoleName { get; set; }

    /// <summary>
    /// 功能标识符
    /// </summary>
    [Required]
    [StringLength(100)]
    public required string FeatureKey { get; set; }

    /// <summary>
    /// 是否启用 (null表示使用全局设置)
    /// </summary>
    public bool? IsEnabled { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 创建者
    /// </summary>
    [StringLength(50)]
    public string? CreatedBy { get; set; }

    /// <summary>
    /// 更新者
    /// </summary>
    [StringLength(50)]
    public string? UpdatedBy { get; set; }

    /// <summary>
    /// 关联的功能配置
    /// </summary>
    [ForeignKey("FeatureKey")]
    public virtual FeatureConfig? FeatureConfig { get; set; }

    /// <summary>
    /// 复合索引：角色+功能唯一
    /// </summary>
    [Index(nameof(RoleName), nameof(FeatureKey), IsUnique = true)]
    public class RoleFeatureIndex { }
}
```

### 2.3 权限计算逻辑

```
权限计算流程:
1. 获取功能的全局开关状态 (FeatureConfig.IsGloballyEnabled)
2. 获取用户的所有角色
3. 查询角色权限覆盖 (RoleFeaturePermission)
4. 应用权限计算规则

权限计算规则:
- 如果全局禁用，则所有角色都无权限
- 如果全局启用，检查角色权限覆盖：
  * 如果角色有明确的权限设置，使用角色设置
  * 如果角色没有设置，使用全局设置
  * 如果用户有多个角色，取最高权限（OR逻辑）

示例:
- 功能: merchant.delete, 全局启用: true
- SystemAdmin角色: 明确设置为 true
- MerchantAdmin角色: 明确设置为 false
- User角色: 无设置 (使用全局设置 true)
- 结果: SystemAdmin=true, MerchantAdmin=false, User=true
```

### 2.4 权限常量管理

```javascript
/**
 * 权限常量定义 - 统一管理所有功能权限标识符
 * 避免硬编码字符串，便于重构和维护
 */
export const PERMISSIONS = {
  // 商户管理
  MERCHANT: {
    CREATE: 'merchant.create',
    EDIT: 'merchant.edit',
    DELETE: 'merchant.delete',
    TOGGLE_ACTIVE: 'merchant.toggle_active',
    VIEW_DETAILS: 'merchant.view_details',
    EXPORT: 'merchant.export'
  },

  // 用户管理
  USER: {
    CREATE: 'user.create',
    EDIT: 'user.edit',
    DELETE: 'user.delete',
    LOCK: 'user.lock',
    RESET_PASSWORD: 'user.reset_password',
    RESET_TWO_FACTOR: 'user.reset_two_factor',
    VIEW_DETAILS: 'user.view_details'
  },

  // 角色管理
  ROLE: {
    CREATE: 'role.create',
    EDIT: 'role.edit',
    DELETE: 'role.delete',
    ASSIGN: 'role.assign'
  },

  // 系统功能
  SYSTEM: {
    BACKUP: 'system.backup',
    LOGS: 'system.logs',
    SETTINGS: 'system.settings',
    FEATURE_CONTROL: 'system.feature_control'
  },

  // 文件管理
  FILE: {
    UPLOAD: 'file.upload',
    DELETE: 'file.delete',
    PUBLISH: 'file.publish',
    VERSION_MANAGE: 'file.version_manage'
  },

  // 终端管理
  TERMINAL: {
    CREATE: 'terminal.create',
    EDIT: 'terminal.edit',
    DELETE: 'terminal.delete',
    VIEW_EVENTS: 'terminal.view_events',
    VIEW_RECORDS: 'terminal.view_records'
  }
};

/**
 * 获取所有权限标识符
 */
export const getAllPermissionKeys = () => {
  const keys = [];
  const traverse = (obj) => {
    Object.values(obj).forEach(value => {
      if (typeof value === 'string') {
        keys.push(value);
      } else if (typeof value === 'object') {
        traverse(value);
      }
    });
  };
  traverse(PERMISSIONS);
  return keys;
};
```

## 3. 后端权限验证设计

### 3.1 权限验证特性 (Attribute)

```csharp
/// <summary>
/// 功能权限验证特性
/// 用于API方法的权限验证
/// </summary>
[AttributeUsage(AttributeTargets.Method | AttributeTargets.Class)]
public class RequireFeaturePermissionAttribute : Attribute, IAuthorizationFilter
{
    private readonly string _featureKey;
    private readonly bool _requireAll;

    public RequireFeaturePermissionAttribute(string featureKey, bool requireAll = false)
    {
        _featureKey = featureKey;
        _requireAll = requireAll;
    }

    public void OnAuthorization(AuthorizationFilterContext context)
    {
        var permissionService = context.HttpContext.RequestServices
            .GetRequiredService<IFeaturePermissionService>();

        var user = context.HttpContext.User;
        if (!user.Identity?.IsAuthenticated == true)
        {
            context.Result = new UnauthorizedResult();
            return;
        }

        var hasPermission = permissionService.HasPermissionAsync(user, _featureKey).Result;
        if (!hasPermission)
        {
            context.Result = new ForbidResult();
        }
    }
}
```

### 3.2 权限服务接口

```csharp
/// <summary>
/// 功能权限服务接口
/// </summary>
public interface IFeaturePermissionService
{
    /// <summary>
    /// 检查用户是否有指定功能权限
    /// </summary>
    Task<bool> HasPermissionAsync(ClaimsPrincipal user, string featureKey);

    /// <summary>
    /// 批量检查用户权限
    /// </summary>
    Task<Dictionary<string, bool>> CheckMultiplePermissionsAsync(ClaimsPrincipal user, IEnumerable<string> featureKeys);

    /// <summary>
    /// 获取用户所有权限
    /// </summary>
    Task<Dictionary<string, bool>> GetUserPermissionsAsync(ClaimsPrincipal user);

    /// <summary>
    /// 刷新权限缓存
    /// </summary>
    Task RefreshPermissionCacheAsync(string? userId = null);

    /// <summary>
    /// 获取功能配置列表
    /// </summary>
    Task<List<FeatureConfigDto>> GetFeatureConfigsAsync();

    /// <summary>
    /// 更新功能配置
    /// </summary>
    Task UpdateFeatureConfigAsync(string featureKey, bool isEnabled);

    /// <summary>
    /// 更新角色权限
    /// </summary>
    Task UpdateRolePermissionAsync(string roleName, string featureKey, bool? isEnabled);
}
```

## 4. 前端权限控制设计

### 4.1 权限缓存策略

```javascript
/**
 * 权限缓存管理
 * 使用多层缓存策略提高性能
 */
class PermissionCache {
  constructor() {
    this.memoryCache = new Map(); // 内存缓存
    this.localStorageKey = 'feature_permissions';
    this.cacheExpiry = 5 * 60 * 1000; // 5分钟过期
  }

  // 获取缓存的权限
  get(userId) {
    // 1. 先检查内存缓存
    const memoryData = this.memoryCache.get(userId);
    if (memoryData && !this.isExpired(memoryData.timestamp)) {
      return memoryData.permissions;
    }

    // 2. 检查localStorage缓存
    try {
      const stored = localStorage.getItem(`${this.localStorageKey}_${userId}`);
      if (stored) {
        const data = JSON.parse(stored);
        if (!this.isExpired(data.timestamp)) {
          // 恢复到内存缓存
          this.memoryCache.set(userId, data);
          return data.permissions;
        }
      }
    } catch (error) {
      console.warn('Failed to read permission cache from localStorage:', error);
    }

    return null;
  }

  // 设置权限缓存
  set(userId, permissions) {
    const data = {
      permissions,
      timestamp: Date.now()
    };

    // 设置内存缓存
    this.memoryCache.set(userId, data);

    // 设置localStorage缓存
    try {
      localStorage.setItem(`${this.localStorageKey}_${userId}`, JSON.stringify(data));
    } catch (error) {
      console.warn('Failed to save permission cache to localStorage:', error);
    }
  }

  // 清除缓存
  clear(userId) {
    if (userId) {
      this.memoryCache.delete(userId);
      localStorage.removeItem(`${this.localStorageKey}_${userId}`);
    } else {
      this.memoryCache.clear();
      // 清除所有权限相关的localStorage
      Object.keys(localStorage).forEach(key => {
        if (key.startsWith(this.localStorageKey)) {
          localStorage.removeItem(key);
        }
      });
    }
  }

  // 检查是否过期
  isExpired(timestamp) {
    return Date.now() - timestamp > this.cacheExpiry;
  }
}

const permissionCache = new PermissionCache();
```

### 4.2 useFeaturePermission Hook (优化版)

```javascript
/**
 * 功能权限控制 Hook (优化版)
 *
 * 特性:
 * 1. 多层缓存策略
 * 2. 批量权限预加载
 * 3. 权限变更实时通知
 * 4. 错误降级处理
 */
export const useFeaturePermission = () => {
  const { user } = useAuth();
  const [permissions, setPermissions] = useState(new Map());
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // 权限加载
  const loadPermissions = useCallback(async () => {
    if (!user?.id) return;

    try {
      setLoading(true);
      setError(null);

      // 1. 尝试从缓存获取
      const cached = permissionCache.get(user.id);
      if (cached) {
        setPermissions(new Map(Object.entries(cached)));
        setLoading(false);
        return;
      }

      // 2. 从后端获取
      const response = await api.get('/api/permissions/user-permissions');
      const permissionMap = new Map(Object.entries(response.data));

      // 3. 更新状态和缓存
      setPermissions(permissionMap);
      permissionCache.set(user.id, response.data);

    } catch (err) {
      console.error('Failed to load permissions:', err);
      setError(err);

      // 降级到默认权限
      const defaultPermissions = getDefaultPermissions(user.roles);
      setPermissions(new Map(Object.entries(defaultPermissions)));

    } finally {
      setLoading(false);
    }
  }, [user?.id, user?.roles]);

  // 权限检查方法
  const hasPermission = useCallback((featureKey) => {
    return permissions.get(featureKey) === true;
  }, [permissions]);

  const canPerform = useCallback((featureKey, additionalCheck = true) => {
    return hasPermission(featureKey) && additionalCheck;
  }, [hasPermission]);

  const checkMultiple = useCallback((featureKeys) => {
    return featureKeys.reduce((acc, key) => {
      acc[key] = hasPermission(key);
      return acc;
    }, {});
  }, [hasPermission]);

  // 刷新权限
  const refreshPermissions = useCallback(() => {
    if (user?.id) {
      permissionCache.clear(user.id);
      loadPermissions();
    }
  }, [user?.id, loadPermissions]);

  // 初始加载
  useEffect(() => {
    loadPermissions();
  }, [loadPermissions]);

  // 监听权限变更事件
  useEffect(() => {
    const handlePermissionChange = () => {
      refreshPermissions();
    };

    // WebSocket事件监听
    if (window.socket) {
      window.socket.on('permission_changed', handlePermissionChange);
    }

    // 定时刷新 (备用方案)
    const interval = setInterval(refreshPermissions, 5 * 60 * 1000); // 5分钟

    return () => {
      if (window.socket) {
        window.socket.off('permission_changed', handlePermissionChange);
      }
      clearInterval(interval);
    };
  }, [refreshPermissions]);

  return {
    permissions: Object.fromEntries(permissions), // 转换为普通对象
    loading,
    error,
    hasPermission,
    canPerform,
    checkMultiple,
    refreshPermissions
  };
};

/**
 * 获取默认权限配置 (降级方案)
 */
const getDefaultPermissions = (roles = []) => {
  const defaultPerms = {};

  if (roles.includes('SystemAdmin')) {
    // 系统管理员默认拥有所有权限
    getAllPermissionKeys().forEach(key => {
      defaultPerms[key] = true;
    });
  } else if (roles.includes('MerchantAdmin')) {
    // 商户管理员默认权限
    Object.values(PERMISSIONS.MERCHANT).forEach(key => {
      if (key !== PERMISSIONS.MERCHANT.DELETE) {
        defaultPerms[key] = true;
      }
    });
    Object.values(PERMISSIONS.USER).forEach(key => {
      if (key !== PERMISSIONS.USER.DELETE) {
        defaultPerms[key] = true;
      }
    });
  } else {
    // 普通用户默认只有查看权限
    defaultPerms[PERMISSIONS.MERCHANT.VIEW_DETAILS] = true;
    defaultPerms[PERMISSIONS.USER.VIEW_DETAILS] = true;
  }

  return defaultPerms;
};
```

### 4.3 FeatureGuard 组件设计 (优化版)

```jsx
/**
 * 功能权限保护组件 (优化版)
 *
 * 特性:
 * 1. 支持权限常量
 * 2. 性能优化
 * 3. 错误边界处理
 * 4. 开发模式调试
 */
export const FeatureGuard = memo(({
  featureKey,           // 必需: 功能标识符 (支持PERMISSIONS常量)
  children,             // 必需: 有权限时显示的内容
  fallback = null,      // 可选: 无权限时显示的内容
  additionalCheck = true, // 可选: 额外的检查条件
  showDebugInfo = false // 可选: 开发模式显示调试信息
}) => {
  const { canPerform, loading, error } = useFeaturePermission();

  // 开发模式调试信息
  if (process.env.NODE_ENV === 'development' && showDebugInfo) {
    console.log(`FeatureGuard [${featureKey}]:`, {
      hasPermission: canPerform(featureKey, true),
      additionalCheck,
      finalResult: canPerform(featureKey, additionalCheck)
    });
  }

  // 加载中状态
  if (loading) {
    return fallback || <Skeleton variant="rectangular" width={100} height={36} />;
  }

  // 错误状态
  if (error) {
    console.error(`FeatureGuard error for ${featureKey}:`, error);
    return fallback;
  }

  // 权限检查
  if (!canPerform(featureKey, additionalCheck)) {
    return fallback;
  }

  return children;
});

FeatureGuard.displayName = 'FeatureGuard';

/**
 * 使用示例 (使用权限常量):
 */
import { PERMISSIONS } from '../constants/permissions';

// 1. 简单使用 - 无权限时隐藏
<FeatureGuard featureKey={PERMISSIONS.MERCHANT.DELETE}>
  <Button color="error">删除商户</Button>
</FeatureGuard>

// 2. 带降级显示 - 无权限时显示禁用状态
<FeatureGuard
  featureKey={PERMISSIONS.MERCHANT.EDIT}
  fallback={<Button disabled>编辑商户</Button>}
>
  <Button>编辑商户</Button>
</FeatureGuard>

// 3. 带额外条件 - 权限 + 业务条件
<FeatureGuard
  featureKey={PERMISSIONS.MERCHANT.DELETE}
  additionalCheck={merchant?.status !== 'active' && !merchant?.hasActiveUsers}
>
  <Button color="error">删除商户</Button>
</FeatureGuard>

// 4. 开发模式调试
<FeatureGuard
  featureKey={PERMISSIONS.USER.DELETE}
  showDebugInfo={true}
  additionalCheck={user.id !== currentUser.id}
>
  <IconButton color="error">
    <DeleteIcon />
  </IconButton>
</FeatureGuard>
```

### 4.4 权限相关的自定义Hook

**注意：本项目不使用页面级权限检查，只对具体按钮功能进行权限控制**

```javascript
// 注意：usePagePermission 已废弃，不要使用页面级权限检查
// 本项目只对按钮级功能进行权限控制

/**
 * 批量按钮权限Hook
 * 用于表格或列表中的批量权限检查
 */
export const useBatchPermissions = (items, getPermissionKey) => {
  const { hasPermission } = useFeaturePermission();

  return useMemo(() => {
    return items.map(item => ({
      ...item,
      permissions: {
        canEdit: hasPermission(getPermissionKey(item, 'edit')),
        canDelete: hasPermission(getPermissionKey(item, 'delete')),
        canView: hasPermission(getPermissionKey(item, 'view'))
      }
    }));
  }, [items, hasPermission, getPermissionKey]);
};

/**
 * 条件权限Hook
 * 结合业务逻辑的复杂权限判断
 */
export const useConditionalPermission = (featureKey, conditions = {}) => {
  const { hasPermission } = useFeaturePermission();
  const { user } = useAuth();

  return useMemo(() => {
    // 基础权限检查
    if (!hasPermission(featureKey)) {
      return { allowed: false, reason: 'insufficient_permission' };
    }

    // 业务条件检查
    for (const [conditionName, conditionFn] of Object.entries(conditions)) {
      if (typeof conditionFn === 'function' && !conditionFn(user)) {
        return { allowed: false, reason: conditionName };
      }
    }

    return { allowed: true, reason: null };
  }, [hasPermission, featureKey, conditions, user]);
};
```

## 5. 权限管理界面设计

### 5.1 功能配置数据结构

```javascript
/**
 * 功能配置分组结构
 * 从后端API获取，支持动态配置
 */
const featureGroups = {
  '商户管理': {
    icon: 'Business',
    description: '商户相关的增删改查操作',
    color: 'primary',
    features: [
      {
        key: PERMISSIONS.MERCHANT.CREATE,
        name: '创建商户',
        description: '允许创建新的商户账户',
        riskLevel: 'Low',
        isGloballyEnabled: true,
        rolePermissions: {
          'SystemAdmin': true,
          'MerchantAdmin': false,
          'User': false
        }
      },
      {
        key: PERMISSIONS.MERCHANT.DELETE,
        name: '删除商户',
        description: '允许删除商户账户（高风险操作）',
        riskLevel: 'High',
        isGloballyEnabled: true,
        rolePermissions: {
          'SystemAdmin': true,
          'MerchantAdmin': false,
          'User': false
        }
      }
      // ... 更多功能
    ]
  },

  '用户管理': {
    icon: 'Person',
    description: '用户账户相关操作',
    color: 'secondary',
    features: [
      // ... 用户管理功能
    ]
  },

  '系统功能': {
    icon: 'Security',
    description: '系统级别的高级功能',
    color: 'warning',
    features: [
      // ... 系统功能
    ]
  }
};
```

### 5.2 管理界面组件设计

```jsx
/**
 * 功能权限管理主页面
 */
const FeaturePermissionManagement = () => {
  const [featureConfigs, setFeatureConfigs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [hasChanges, setHasChanges] = useState(false);
  const [selectedTab, setSelectedTab] = useState(0);

  // 获取功能配置
  const loadFeatureConfigs = async () => {
    try {
      setLoading(true);
      const response = await api.get('/api/permissions/feature-configs');
      setFeatureConfigs(response.data);
    } catch (error) {
      console.error('Failed to load feature configs:', error);
    } finally {
      setLoading(false);
    }
  };

  // 保存配置
  const saveChanges = async () => {
    try {
      await api.post('/api/permissions/batch-update', {
        featureConfigs: featureConfigs
      });

      setHasChanges(false);
      enqueueSnackbar('权限配置已保存', { variant: 'success' });

      // 通知其他用户权限已变更
      socket.emit('permission_changed');

    } catch (error) {
      console.error('Failed to save changes:', error);
      enqueueSnackbar('保存失败: ' + error.message, { variant: 'error' });
    }
  };

  return (
    <Container maxWidth="lg">
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" gutterBottom>
          功能权限管理
        </Typography>
        <Typography variant="body1" color="text.secondary">
          管理系统中各项功能的启用状态和角色权限
        </Typography>
      </Box>

      {/* 操作栏 */}
      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Box>
          {hasChanges && (
            <Chip
              label="有未保存的更改"
              color="warning"
              icon={<WarningIcon />}
            />
          )}
        </Box>

        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant="outlined"
            onClick={() => window.location.reload()}
            disabled={!hasChanges}
          >
            重置更改
          </Button>
          <Button
            variant="contained"
            onClick={saveChanges}
            disabled={!hasChanges}
            startIcon={<SaveIcon />}
          >
            保存配置
          </Button>
        </Box>
      </Box>

      {/* 功能分组标签页 */}
      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={selectedTab}
          onChange={(e, newValue) => setSelectedTab(newValue)}
          variant="scrollable"
          scrollButtons="auto"
        >
          {Object.keys(featureGroups).map((groupName, index) => (
            <Tab
              key={groupName}
              label={groupName}
              icon={<Icon>{featureGroups[groupName].icon}</Icon>}
            />
          ))}
        </Tabs>
      </Paper>

      {/* 功能配置内容 */}
      {Object.entries(featureGroups).map(([groupName, group], index) => (
        <TabPanel key={groupName} value={selectedTab} index={index}>
          <FeatureGroupPanel
            group={group}
            features={featureConfigs.filter(f => f.category === groupName)}
            onFeatureChange={(featureKey, field, value) => {
              setFeatureConfigs(prev =>
                prev.map(f =>
                  f.featureKey === featureKey
                    ? { ...f, [field]: value }
                    : f
                )
              );
              setHasChanges(true);
            }}
          />
        </TabPanel>
      ))}
    </Container>
  );
};

/**
 * 功能分组面板组件
 */
const FeatureGroupPanel = ({ group, features, onFeatureChange }) => {
  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        {group.description}
      </Typography>

      <Grid container spacing={2}>
        {features.map(feature => (
          <Grid item xs={12} key={feature.featureKey}>
            <FeatureConfigCard
              feature={feature}
              onChange={onFeatureChange}
            />
          </Grid>
        ))}
      </Grid>
    </Box>
  );
};

/**
 * 功能配置卡片组件
 */
const FeatureConfigCard = ({ feature, onChange }) => {
  const [showRoleConfig, setShowRoleConfig] = useState(false);

  const handleGlobalToggle = (enabled) => {
    if (!enabled && feature.riskLevel === 'High') {
      // 高风险操作需要确认
      confirmDialog({
        title: '确认禁用功能',
        content: `您确定要禁用"${feature.name}"功能吗？这是一个高风险操作。`,
        onConfirm: () => onChange(feature.featureKey, 'isGloballyEnabled', enabled)
      });
    } else {
      onChange(feature.featureKey, 'isGloballyEnabled', enabled);
    }
  };

  return (
    <Card variant="outlined">
      <CardContent>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
          <Box sx={{ flex: 1 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
              <Typography variant="h6">
                {feature.name}
              </Typography>
              <RiskLevelChip level={feature.riskLevel} />
            </Box>

            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              {feature.description}
            </Typography>

            <Typography variant="caption" color="text.secondary">
              功能标识: {feature.featureKey}
            </Typography>
          </Box>

          <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-end', gap: 1 }}>
            {/* 全局开关 */}
            <FormControlLabel
              control={
                <Switch
                  checked={feature.isGloballyEnabled}
                  onChange={(e) => handleGlobalToggle(e.target.checked)}
                  color="primary"
                />
              }
              label="全局启用"
              labelPlacement="start"
            />

            {/* 角色配置按钮 */}
            <Button
              size="small"
              variant="outlined"
              onClick={() => setShowRoleConfig(!showRoleConfig)}
              startIcon={<SettingsIcon />}
            >
              角色配置
            </Button>
          </Box>
        </Box>

        {/* 角色权限配置 */}
        <Collapse in={showRoleConfig}>
          <Divider sx={{ my: 2 }} />
          <RolePermissionConfig
            feature={feature}
            onChange={onChange}
          />
        </Collapse>
      </CardContent>
    </Card>
  );
};
```

### 5.3 角色权限配置组件

```jsx
/**
 * 角色权限配置组件
 */
const RolePermissionConfig = ({ feature, onChange }) => {
  const roles = ['SystemAdmin', 'MerchantAdmin', 'User'];

  const handleRolePermissionChange = (roleName, enabled) => {
    const newRolePermissions = {
      ...feature.rolePermissions,
      [roleName]: enabled
    };
    onChange(feature.featureKey, 'rolePermissions', newRolePermissions);
  };

  return (
    <Box>
      <Typography variant="subtitle2" gutterBottom>
        角色权限设置
      </Typography>
      <Typography variant="caption" color="text.secondary" sx={{ mb: 2, display: 'block' }}>
        角色权限会覆盖全局设置。留空表示使用全局设置。
      </Typography>

      <Grid container spacing={2}>
        {roles.map(roleName => (
          <Grid item xs={12} sm={4} key={roleName}>
            <Paper variant="outlined" sx={{ p: 2 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Box>
                  <Typography variant="subtitle2">
                    {getRoleDisplayName(roleName)}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    {getRoleDescription(roleName)}
                  </Typography>
                </Box>

                <FormControl size="small">
                  <Select
                    value={feature.rolePermissions[roleName] ?? 'inherit'}
                    onChange={(e) => {
                      const value = e.target.value;
                      handleRolePermissionChange(
                        roleName,
                        value === 'inherit' ? null : value === 'true'
                      );
                    }}
                  >
                    <MenuItem value="inherit">
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <InheritIcon fontSize="small" />
                        继承全局
                      </Box>
                    </MenuItem>
                    <MenuItem value="true">
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <CheckIcon fontSize="small" color="success" />
                        允许
                      </Box>
                    </MenuItem>
                    <MenuItem value="false">
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <CloseIcon fontSize="small" color="error" />
                        禁止
                      </Box>
                    </MenuItem>
                  </Select>
                </FormControl>
              </Box>
            </Paper>
          </Grid>
        ))}
      </Grid>
    </Box>
  );
};

/**
 * 风险等级标签组件
 */
const RiskLevelChip = ({ level }) => {
  const config = {
    'Low': { color: 'success', icon: <InfoIcon /> },
    'Medium': { color: 'warning', icon: <WarningIcon /> },
    'High': { color: 'error', icon: <DangerousIcon /> }
  };

  const { color, icon } = config[level] || config['Low'];

  return (
    <Chip
      size="small"
      label={level}
      color={color}
      icon={icon}
      variant="outlined"
    />
  );
};

/**
 * 工具函数
 */
const getRoleDisplayName = (roleName) => {
  const names = {
    'SystemAdmin': '系统管理员',
    'MerchantAdmin': '商户管理员',
    'User': '普通用户'
  };
  return names[roleName] || roleName;
};

const getRoleDescription = (roleName) => {
  const descriptions = {
    'SystemAdmin': '拥有系统最高权限',
    'MerchantAdmin': '管理特定商户',
    'User': '基础用户权限'
  };
  return descriptions[roleName] || '';
};
```

## 6. 使用场景和示例

### 6.1 按钮级权限控制 (正确做法)

**重要：本项目不使用页面级权限检查，只对具体按钮功能进行权限控制**

```jsx
// 商户管理页面 (只对按钮进行权限控制)
const MerchantManagementPage = () => {
  const { user } = useAuth();

  // 不要使用页面级权限检查！
  // 页面本身对所有用户开放，只对具体功能按钮进行权限控制

  return (
    <Container>
      {/* 页面标题和操作栏 */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
        <Typography variant="h4">商户管理</Typography>

        {/* 创建按钮 - 只对按钮进行权限控制 */}
        <FeatureGuard featureKey={PERMISSIONS.MERCHANT.CREATE}>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleCreate}
          >
            创建商户
          </Button>
        </FeatureGuard>
      </Box>

      {/* 商户列表 - 列表本身对所有用户可见，但操作按钮有权限控制 */}
      <MerchantDataTable />
    </Container>
  );
};

/**
 * 商户数据表格组件
 */
const MerchantDataTable = () => {
  const [merchants, setMerchants] = useState([]);
  const { user } = useAuth();

  // 使用批量权限Hook
  const merchantsWithPermissions = useBatchPermissions(
    merchants,
    (merchant, action) => {
      // 根据商户和操作类型返回权限标识符
      switch (action) {
        case 'edit':
          return PERMISSIONS.MERCHANT.EDIT;
        case 'delete':
          return PERMISSIONS.MERCHANT.DELETE;
        case 'view':
          return PERMISSIONS.MERCHANT.VIEW_DETAILS;
        default:
          return null;
      }
    }
  );

  const columns = [
    { field: 'name', headerName: '商户名称', flex: 1 },
    { field: 'code', headerName: '商户编码', width: 150 },
    { field: 'status', headerName: '状态', width: 100 },
    {
      field: 'actions',
      headerName: '操作',
      width: 200,
      renderCell: (params) => {
        const merchant = params.row;
        const permissions = merchant.permissions;

        return (
          <Box sx={{ display: 'flex', gap: 0.5 }}>
            {/* 查看详情 */}
            {permissions.canView && (
              <Tooltip title="查看详情">
                <IconButton
                  size="small"
                  onClick={() => handleView(merchant.id)}
                >
                  <VisibilityIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            )}

            {/* 编辑 */}
            <FeatureGuard
              featureKey={PERMISSIONS.MERCHANT.EDIT}
              additionalCheck={
                user.roles.includes('SystemAdmin') ||
                merchant.createdBy === user.id
              }
              fallback={
                <Tooltip title="无编辑权限">
                  <span>
                    <IconButton size="small" disabled>
                      <EditIcon fontSize="small" />
                    </IconButton>
                  </span>
                </Tooltip>
              }
            >
              <Tooltip title="编辑">
                <IconButton
                  size="small"
                  color="primary"
                  onClick={() => handleEdit(merchant.id)}
                >
                  <EditIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </FeatureGuard>

            {/* 删除 */}
            <FeatureGuard
              featureKey={PERMISSIONS.MERCHANT.DELETE}
              additionalCheck={
                merchant.status !== 'active' &&
                !merchant.hasActiveUsers &&
                merchant.id !== user.merchantId // 不能删除自己的商户
              }
            >
              <Tooltip title="删除">
                <IconButton
                  size="small"
                  color="error"
                  onClick={() => handleDelete(merchant.id)}
                >
                  <DeleteIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </FeatureGuard>
          </Box>
        );
      }
    }
  ];

  return (
    <DataGrid
      rows={merchantsWithPermissions}
      columns={columns}
      pageSize={10}
      rowsPerPageOptions={[10, 25, 50]}
      disableSelectionOnClick
      autoHeight
    />
  );
};
```

### 6.2 条件权限控制 (优化版)

```jsx
// 用户详情页面 - 复杂权限控制示例
const UserDetailPage = ({ userId }) => {
  const { user: currentUser } = useAuth();
  const [targetUser, setTargetUser] = useState(null);

  // 使用条件权限Hook
  const deletePermission = useConditionalPermission(PERMISSIONS.USER.DELETE, {
    not_self: (user) => targetUser?.id !== user.id,
    is_system_admin: (user) => user.roles.includes('SystemAdmin'),
    target_not_system_admin: () => !targetUser?.roles.includes('SystemAdmin') || currentUser.roles.includes('SystemAdmin')
  });

  const editPermission = useConditionalPermission(PERMISSIONS.USER.EDIT, {
    same_merchant_or_admin: (user) =>
      user.roles.includes('SystemAdmin') ||
      (user.roles.includes('MerchantAdmin') && targetUser?.merchantId === user.merchantId)
  });

  const resetPasswordPermission = useConditionalPermission(PERMISSIONS.USER.RESET_PASSWORD, {
    user_active: () => targetUser?.status === 'active',
    long_time_no_login: () => {
      if (!targetUser?.lastLoginTime) return true;
      return dayjs().diff(targetUser.lastLoginTime, 'day') > 30;
    }
  });

  if (!targetUser) {
    return <LoadingScreen />;
  }

  return (
    <Container>
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" gutterBottom>
          用户详情 - {targetUser.realName}
        </Typography>
      </Box>

      {/* 用户信息卡片 */}
      <UserInfoCard user={targetUser} />

      {/* 操作按钮区域 */}
      <Paper sx={{ p: 3, mt: 3 }}>
        <Typography variant="h6" gutterBottom>
          用户操作
        </Typography>

        <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
          {/* 编辑用户 */}
          <FeatureGuard
            featureKey={PERMISSIONS.USER.EDIT}
            additionalCheck={editPermission.allowed}
            fallback={
              <Tooltip title={getPermissionDeniedMessage(editPermission.reason)}>
                <span>
                  <Button disabled startIcon={<EditIcon />}>
                    编辑用户
                  </Button>
                </span>
              </Tooltip>
            }
          >
            <Button
              variant="contained"
              startIcon={<EditIcon />}
              onClick={() => navigate(`/app/users/${userId}/edit`)}
            >
              编辑用户
            </Button>
          </FeatureGuard>

          {/* 重置密码 */}
          <FeatureGuard
            featureKey={PERMISSIONS.USER.RESET_PASSWORD}
            additionalCheck={resetPasswordPermission.allowed}
            fallback={
              <Tooltip title={getPermissionDeniedMessage(resetPasswordPermission.reason)}>
                <span>
                  <Button disabled startIcon={<RefreshIcon />}>
                    重置密码
                  </Button>
                </span>
              </Tooltip>
            }
          >
            <Button
              color="warning"
              startIcon={<RefreshIcon />}
              onClick={handleResetPassword}
            >
              重置密码
            </Button>
          </FeatureGuard>

          {/* 锁定/解锁用户 */}
          <FeatureGuard featureKey={PERMISSIONS.USER.LOCK}>
            <Button
              color={targetUser.isLocked ? "success" : "warning"}
              startIcon={targetUser.isLocked ? <LockOpenIcon /> : <LockIcon />}
              onClick={handleToggleLock}
            >
              {targetUser.isLocked ? '解锁用户' : '锁定用户'}
            </Button>
          </FeatureGuard>

          {/* 删除用户 */}
          <FeatureGuard
            featureKey={PERMISSIONS.USER.DELETE}
            additionalCheck={deletePermission.allowed}
            fallback={
              <Tooltip title={getPermissionDeniedMessage(deletePermission.reason)}>
                <span>
                  <Button disabled color="error" startIcon={<DeleteIcon />}>
                    删除用户
                  </Button>
                </span>
              </Tooltip>
            }
          >
            <Button
              color="error"
              startIcon={<DeleteIcon />}
              onClick={handleDeleteUser}
            >
              删除用户
            </Button>
          </FeatureGuard>
        </Box>

        {/* 权限说明 */}
        <Box sx={{ mt: 2 }}>
          <Typography variant="caption" color="text.secondary">
            * 操作权限基于您的角色和系统配置确定
          </Typography>
        </Box>
      </Paper>
    </Container>
  );
};

/**
 * 权限拒绝原因消息映射
 */
const getPermissionDeniedMessage = (reason) => {
  const messages = {
    'insufficient_permission': '您没有执行此操作的权限',
    'not_self': '不能对自己执行此操作',
    'is_system_admin': '需要系统管理员权限',
    'target_not_system_admin': '不能对系统管理员执行此操作',
    'same_merchant_or_admin': '只能操作同商户的用户',
    'user_active': '用户必须处于活跃状态',
    'long_time_no_login': '用户最近有登录记录'
  };
  return messages[reason] || '操作被拒绝';
};
```

## 7. 技术实现要点

### 7.1 后端权限服务实现

```csharp
/// <summary>
/// 功能权限服务实现
/// </summary>
public class FeaturePermissionService : IFeaturePermissionService
{
    private readonly TcpDbContext _context;
    private readonly IMemoryCache _cache;
    private readonly ILogger<FeaturePermissionService> _logger;
    private readonly UserManager<ApplicationUser> _userManager;

    public FeaturePermissionService(
        TcpDbContext context,
        IMemoryCache cache,
        ILogger<FeaturePermissionService> logger,
        UserManager<ApplicationUser> userManager)
    {
        _context = context;
        _cache = cache;
        _logger = logger;
        _userManager = userManager;
    }

    public async Task<bool> HasPermissionAsync(ClaimsPrincipal user, string featureKey)
    {
        try
        {
            // 1. 获取用户角色
            var roles = user.Claims
                .Where(c => c.Type == ClaimTypes.Role)
                .Select(c => c.Value)
                .ToList();

            if (!roles.Any())
            {
                return false;
            }

            // 2. 获取功能配置
            var featureConfig = await GetFeatureConfigAsync(featureKey);
            if (featureConfig == null)
            {
                _logger.LogWarning("Feature config not found: {FeatureKey}", featureKey);
                return false;
            }

            // 3. 检查全局开关
            if (!featureConfig.IsGloballyEnabled)
            {
                return false;
            }

            // 4. 检查角色权限覆盖
            var rolePermissions = await _context.RoleFeaturePermissions
                .Where(rp => rp.FeatureKey == featureKey && roles.Contains(rp.RoleName))
                .ToListAsync();

            // 5. 应用权限计算逻辑
            foreach (var role in roles)
            {
                var rolePermission = rolePermissions.FirstOrDefault(rp => rp.RoleName == role);
                if (rolePermission?.IsEnabled == true)
                {
                    return true; // 任一角色明确允许则允许
                }
            }

            // 6. 权限计算逻辑修正：
            // 当功能启用权限控制时，必须有明确的角色权限才能访问
            // 如果没有任何角色权限记录，说明所有角色都被拒绝访问
            if (!rolePermissions.Any())
            {
                return false; // 没有配置角色权限时拒绝访问
            }

            // 如果有角色权限记录但都是拒绝的，也拒绝访问
            var hasExplicitDeny = rolePermissions.Any(rp => rp.IsEnabled == false);
            var hasExplicitAllow = rolePermissions.Any(rp => rp.IsEnabled == true);

            return hasExplicitAllow && !hasExplicitDeny;

        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking permission for feature: {FeatureKey}", featureKey);
            return false; // 出错时拒绝访问
        }
    }

    public async Task<Dictionary<string, bool>> GetUserPermissionsAsync(ClaimsPrincipal user)
    {
        var cacheKey = $"user_permissions_{user.Identity?.Name}";

        if (_cache.TryGetValue(cacheKey, out Dictionary<string, bool>? cached))
        {
            return cached!;
        }

        var permissions = new Dictionary<string, bool>();

        // 获取所有功能配置
        var allFeatures = await _context.FeatureConfigs.ToListAsync();

        // 批量检查权限
        foreach (var feature in allFeatures)
        {
            permissions[feature.FeatureKey] = await HasPermissionAsync(user, feature.FeatureKey);
        }

        // 缓存5分钟
        _cache.Set(cacheKey, permissions, TimeSpan.FromMinutes(5));

        return permissions;
    }

    public async Task RefreshPermissionCacheAsync(string? userId = null)
    {
        if (userId != null)
        {
            _cache.Remove($"user_permissions_{userId}");
        }
        else
        {
            // 清除所有权限缓存
            if (_cache is MemoryCache memoryCache)
            {
                var field = typeof(MemoryCache).GetField("_coherentState",
                    BindingFlags.NonPublic | BindingFlags.Instance);
                if (field?.GetValue(memoryCache) is IDictionary coherentState)
                {
                    var keysToRemove = coherentState.Keys
                        .OfType<string>()
                        .Where(key => key.StartsWith("user_permissions_"))
                        .ToList();

                    foreach (var key in keysToRemove)
                    {
                        _cache.Remove(key);
                    }
                }
            }
        }
    }

    private async Task<FeatureConfig?> GetFeatureConfigAsync(string featureKey)
    {
        var cacheKey = $"feature_config_{featureKey}";

        if (_cache.TryGetValue(cacheKey, out FeatureConfig? cached))
        {
            return cached;
        }

        var config = await _context.FeatureConfigs
            .FirstOrDefaultAsync(fc => fc.FeatureKey == featureKey);

        if (config != null)
        {
            _cache.Set(cacheKey, config, TimeSpan.FromHours(1));
        }

        return config;
    }
}
```

### 7.2 API控制器实现

```csharp
/// <summary>
/// 权限管理API控制器
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class PermissionsController : ControllerBase
{
    private readonly IFeaturePermissionService _permissionService;
    private readonly TcpDbContext _context;
    private readonly ILogger<PermissionsController> _logger;

    public PermissionsController(
        IFeaturePermissionService permissionService,
        TcpDbContext context,
        ILogger<PermissionsController> logger)
    {
        _permissionService = permissionService;
        _context = context;
        _logger = logger;
    }

    /// <summary>
    /// 获取当前用户的所有权限
    /// </summary>
    [HttpGet("user-permissions")]
    public async Task<ActionResult<Dictionary<string, bool>>> GetUserPermissions()
    {
        try
        {
            var permissions = await _permissionService.GetUserPermissionsAsync(User);
            return Ok(permissions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get user permissions");
            return StatusCode(500, new { message = "获取权限失败" });
        }
    }

    /// <summary>
    /// 检查特定功能权限
    /// </summary>
    [HttpGet("check/{featureKey}")]
    public async Task<ActionResult<bool>> CheckPermission(string featureKey)
    {
        try
        {
            var hasPermission = await _permissionService.HasPermissionAsync(User, featureKey);
            return Ok(hasPermission);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to check permission for {FeatureKey}", featureKey);
            return StatusCode(500, new { message = "权限检查失败" });
        }
    }

    /// <summary>
    /// 获取功能配置列表 (仅系统管理员)
    /// </summary>
    [HttpGet("feature-configs")]
    [RequireFeaturePermission("system.feature_control")]
    public async Task<ActionResult<List<FeatureConfigDto>>> GetFeatureConfigs()
    {
        try
        {
            var configs = await _context.FeatureConfigs
                .Include(fc => fc.RolePermissions)
                .OrderBy(fc => fc.Category)
                .ThenBy(fc => fc.SortOrder)
                .ToListAsync();

            var dtos = configs.Select(fc => new FeatureConfigDto
            {
                FeatureKey = fc.FeatureKey,
                FeatureName = fc.FeatureName,
                Description = fc.Description,
                Category = fc.Category,
                RiskLevel = fc.RiskLevel,
                IsGloballyEnabled = fc.IsGloballyEnabled,
                RolePermissions = fc.RolePermissions.ToDictionary(
                    rp => rp.RoleName,
                    rp => rp.IsEnabled
                )
            }).ToList();

            return Ok(dtos);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get feature configs");
            return StatusCode(500, new { message = "获取功能配置失败" });
        }
    }

    /// <summary>
    /// 批量更新功能配置 (仅系统管理员)
    /// </summary>
    [HttpPost("batch-update")]
    [RequireFeaturePermission("system.feature_control")]
    public async Task<IActionResult> BatchUpdateConfigs([FromBody] BatchUpdateRequest request)
    {
        try
        {
            using var transaction = await _context.Database.BeginTransactionAsync();

            foreach (var update in request.Updates)
            {
                var config = await _context.FeatureConfigs
                    .FirstOrDefaultAsync(fc => fc.FeatureKey == update.FeatureKey);

                if (config != null)
                {
                    config.IsGloballyEnabled = update.IsGloballyEnabled;
                    config.UpdatedAt = DateTime.Now;
                    config.UpdatedBy = User.Identity?.Name;

                    // 更新角色权限
                    var existingRolePermissions = await _context.RoleFeaturePermissions
                        .Where(rp => rp.FeatureKey == update.FeatureKey)
                        .ToListAsync();

                    _context.RoleFeaturePermissions.RemoveRange(existingRolePermissions);

                    foreach (var rolePermission in update.RolePermissions)
                    {
                        if (rolePermission.Value.HasValue)
                        {
                            _context.RoleFeaturePermissions.Add(new RoleFeaturePermission
                            {
                                RoleName = rolePermission.Key,
                                FeatureKey = update.FeatureKey,
                                IsEnabled = rolePermission.Value,
                                CreatedAt = DateTime.Now,
                                CreatedBy = User.Identity?.Name
                            });
                        }
                    }
                }
            }

            await _context.SaveChangesAsync();
            await transaction.CommitAsync();

            // 清除权限缓存
            await _permissionService.RefreshPermissionCacheAsync();

            return Ok(new { message = "配置更新成功" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update feature configs");
            return StatusCode(500, new { message = "配置更新失败" });
        }
    }
}
```

### 7.3 数据传输对象 (DTOs)

```csharp
/// <summary>
/// 功能配置DTO
/// </summary>
public class FeatureConfigDto
{
    public string FeatureKey { get; set; } = string.Empty;
    public string FeatureName { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string Category { get; set; } = string.Empty;
    public string RiskLevel { get; set; } = string.Empty;
    public bool IsGloballyEnabled { get; set; }
    public Dictionary<string, bool?> RolePermissions { get; set; } = new();
}

/// <summary>
/// 批量更新请求DTO
/// </summary>
public class BatchUpdateRequest
{
    public List<FeatureConfigUpdateDto> Updates { get; set; } = new();
}

public class FeatureConfigUpdateDto
{
    public string FeatureKey { get; set; } = string.Empty;
    public bool IsGloballyEnabled { get; set; }
    public Dictionary<string, bool?> RolePermissions { get; set; } = new();
}
```

## 8. 部署和维护

### 8.1 数据库迁移脚本

```sql
-- 创建功能配置表
CREATE TABLE FeatureConfigs (
    Id INT PRIMARY KEY AUTO_INCREMENT,
    FeatureKey VARCHAR(100) NOT NULL UNIQUE,
    FeatureName VARCHAR(100) NOT NULL,
    Description VARCHAR(500),
    Category VARCHAR(50) NOT NULL,
    RiskLevel VARCHAR(20) NOT NULL DEFAULT 'Low',
    IsGloballyEnabled BOOLEAN NOT NULL DEFAULT TRUE,
    IsSystemBuiltIn BOOLEAN NOT NULL DEFAULT FALSE,
    SortOrder INT NOT NULL DEFAULT 0,
    CreatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    CreatedBy VARCHAR(50),
    UpdatedBy VARCHAR(50),

    INDEX idx_category (Category),
    INDEX idx_feature_key (FeatureKey)
);

-- 创建角色功能权限表
CREATE TABLE RoleFeaturePermissions (
    Id INT PRIMARY KEY AUTO_INCREMENT,
    RoleName VARCHAR(50) NOT NULL,
    FeatureKey VARCHAR(100) NOT NULL,
    IsEnabled BOOLEAN,
    CreatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    CreatedBy VARCHAR(50),
    UpdatedBy VARCHAR(50),

    UNIQUE KEY uk_role_feature (RoleName, FeatureKey),
    FOREIGN KEY (FeatureKey) REFERENCES FeatureConfigs(FeatureKey) ON DELETE CASCADE,
    INDEX idx_role_name (RoleName),
    INDEX idx_feature_key (FeatureKey)
);

-- 插入默认功能配置
INSERT INTO FeatureConfigs (FeatureKey, FeatureName, Description, Category, RiskLevel, IsSystemBuiltIn) VALUES
-- 商户管理
('merchant.create', '创建商户', '允许创建新的商户账户', '商户管理', 'Low', TRUE),
('merchant.edit', '编辑商户', '允许修改商户基本信息', '商户管理', 'Medium', TRUE),
('merchant.delete', '删除商户', '允许删除商户账户（高风险操作）', '商户管理', 'High', TRUE),
('merchant.toggle_active', '启用/停用商户', '允许激活或停用商户账户', '商户管理', 'Medium', TRUE),
('merchant.view_details', '查看商户详情', '允许查看商户详细信息', '商户管理', 'Low', TRUE),
('merchant.export', '导出商户数据', '允许导出商户列表数据', '商户管理', 'Medium', TRUE),

-- 用户管理
('user.create', '创建用户', '允许创建新的用户账户', '用户管理', 'Low', TRUE),
('user.edit', '编辑用户', '允许修改用户基本信息', '用户管理', 'Medium', TRUE),
('user.delete', '删除用户', '允许删除用户账户', '用户管理', 'High', TRUE),
('user.lock', '锁定/解锁用户', '允许锁定或解锁用户账户', '用户管理', 'Medium', TRUE),
('user.reset_password', '重置密码', '允许重置用户密码', '用户管理', 'Medium', TRUE),
('user.reset_two_factor', '重置双因素认证', '允许重置用户的双因素认证', '用户管理', 'Medium', TRUE),
('user.view_details', '查看用户详情', '允许查看用户详细信息', '用户管理', 'Low', TRUE),

-- 角色管理
('role.create', '创建角色', '允许创建新的系统角色', '角色管理', 'High', TRUE),
('role.edit', '编辑角色', '允许修改角色信息', '角色管理', 'High', TRUE),
('role.delete', '删除角色', '允许删除系统角色', '角色管理', 'High', TRUE),
('role.assign', '分配角色', '允许为用户分配角色', '角色管理', 'Medium', TRUE),

-- 系统功能
('system.backup', '系统备份', '允许执行系统数据备份', '系统功能', 'Low', TRUE),
('system.logs', '系统日志', '允许查看系统操作日志', '系统功能', 'Low', TRUE),
('system.settings', '系统设置', '允许修改系统配置', '系统功能', 'High', TRUE),
('system.feature_control', '功能控制', '允许管理功能权限配置', '系统功能', 'High', TRUE),

-- 文件管理
('file.upload', '文件上传', '允许上传文件', '文件管理', 'Low', TRUE),
('file.delete', '文件删除', '允许删除文件', '文件管理', 'Medium', TRUE),
('file.publish', '文件发布', '允许发布文件到终端', '文件管理', 'Medium', TRUE),
('file.version_manage', '版本管理', '允许管理文件版本', '文件管理', 'Medium', TRUE),

-- 终端管理
('terminal.create', '创建终端', '允许创建新的终端', '终端管理', 'Low', TRUE),
('terminal.edit', '编辑终端', '允许修改终端信息', '终端管理', 'Medium', TRUE),
('terminal.delete', '删除终端', '允许删除终端', '终端管理', 'High', TRUE),
('terminal.view_events', '查看终端事件', '允许查看终端事件日志', '终端管理', 'Low', TRUE),
('terminal.view_records', '查看终端记录', '允许查看终端操作记录', '终端管理', 'Low', TRUE);

-- 插入默认角色权限
INSERT INTO RoleFeaturePermissions (RoleName, FeatureKey, IsEnabled) VALUES
-- SystemAdmin 拥有所有权限
('SystemAdmin', 'merchant.delete', TRUE),
('SystemAdmin', 'user.delete', TRUE),
('SystemAdmin', 'role.create', TRUE),
('SystemAdmin', 'role.edit', TRUE),
('SystemAdmin', 'role.delete', TRUE),
('SystemAdmin', 'system.settings', TRUE),
('SystemAdmin', 'system.feature_control', TRUE),
('SystemAdmin', 'terminal.delete', TRUE),

-- MerchantAdmin 受限权限
('MerchantAdmin', 'merchant.delete', FALSE),
('MerchantAdmin', 'user.delete', FALSE),
('MerchantAdmin', 'role.create', FALSE),
('MerchantAdmin', 'role.edit', FALSE),
('MerchantAdmin', 'role.delete', FALSE),
('MerchantAdmin', 'system.settings', FALSE),
('MerchantAdmin', 'system.feature_control', FALSE),
('MerchantAdmin', 'terminal.delete', FALSE),

-- User 最小权限
('User', 'merchant.create', FALSE),
('User', 'merchant.edit', FALSE),
('User', 'merchant.delete', FALSE),
('User', 'user.create', FALSE),
('User', 'user.edit', FALSE),
('User', 'user.delete', FALSE),
('User', 'user.lock', FALSE),
('User', 'user.reset_password', FALSE),
('User', 'role.create', FALSE),
('User', 'role.edit', FALSE),
('User', 'role.delete', FALSE),
('User', 'role.assign', FALSE),
('User', 'system.backup', FALSE),
('User', 'system.settings', FALSE),
('User', 'system.feature_control', FALSE),
('User', 'file.delete', FALSE),
('User', 'file.publish', FALSE),
('User', 'terminal.create', FALSE),
('User', 'terminal.edit', FALSE),
('User', 'terminal.delete', FALSE);
```

### 8.2 权限审计和日志

```csharp
/// <summary>
/// 权限变更审计服务
/// </summary>
public class PermissionAuditService
{
    private readonly AuditLogService _auditLogService;
    private readonly ILogger<PermissionAuditService> _logger;

    public PermissionAuditService(
        AuditLogService auditLogService,
        ILogger<PermissionAuditService> logger)
    {
        _auditLogService = auditLogService;
        _logger = logger;
    }

    public async Task LogPermissionChangeAsync(
        string userId,
        string featureKey,
        bool? oldValue,
        bool? newValue,
        string changeType = "FEATURE_CONFIG_CHANGE")
    {
        try
        {
            await _auditLogService.LogAsync(new AuditLog
            {
                UserId = userId,
                Action = changeType,
                EntityType = "FeaturePermission",
                EntityId = featureKey,
                Details = JsonSerializer.Serialize(new
                {
                    featureKey,
                    oldValue,
                    newValue,
                    timestamp = DateTime.UtcNow
                }),
                IpAddress = GetCurrentIpAddress(),
                UserAgent = GetCurrentUserAgent(),
                CreatedAt = DateTime.UtcNow
            });

            _logger.LogInformation(
                "Permission change logged: User={UserId}, Feature={FeatureKey}, Old={OldValue}, New={NewValue}",
                userId, featureKey, oldValue, newValue);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to log permission change");
        }
    }

    public async Task LogPermissionCheckAsync(
        string userId,
        string featureKey,
        bool hasPermission,
        string? denyReason = null)
    {
        try
        {
            await _auditLogService.LogAsync(new AuditLog
            {
                UserId = userId,
                Action = "PERMISSION_CHECK",
                EntityType = "FeaturePermission",
                EntityId = featureKey,
                Details = JsonSerializer.Serialize(new
                {
                    featureKey,
                    hasPermission,
                    denyReason,
                    timestamp = DateTime.UtcNow
                }),
                IpAddress = GetCurrentIpAddress(),
                UserAgent = GetCurrentUserAgent(),
                CreatedAt = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to log permission check");
        }
    }

    private string? GetCurrentIpAddress()
    {
        // 实现获取当前请求IP地址的逻辑
        return null;
    }

    private string? GetCurrentUserAgent()
    {
        // 实现获取当前请求User-Agent的逻辑
        return null;
    }
}
```

## 9. 实施计划

### 9.1 开发阶段

#### 阶段一：数据模型和后端基础 (1-2周)
1. **数据库设计**
   - 创建 FeatureConfig 和 RoleFeaturePermission 表
   - 执行数据库迁移脚本
   - 插入默认功能配置数据

2. **后端服务实现**
   - 实现 IFeaturePermissionService 接口
   - 创建权限验证特性 RequireFeaturePermissionAttribute
   - 实现权限管理API控制器

3. **权限常量定义**
   - 创建前后端共享的权限常量
   - 建立权限标识符命名规范

#### 阶段二：前端权限控制框架 (1-2周)
1. **核心Hook实现**
   - 实现 useFeaturePermission Hook
   - 添加权限缓存策略
   - 实现错误降级处理

2. **权限组件开发**
   - 创建 FeatureGuard 组件
   - 实现辅助Hook (useBatchPermissions等，注意：不使用usePagePermission)
   - 添加开发模式调试功能

3. **权限服务集成**
   - 集成到 AuthContext
   - 实现权限预加载
   - 添加权限变更通知机制

#### 阶段三：管理界面开发 (2-3周)
1. **权限管理页面**
   - 创建功能配置管理界面
   - 实现分组展示和搜索功能
   - 添加批量操作功能

2. **角色权限配置**
   - 实现角色权限覆盖设置
   - 添加权限继承可视化
   - 实现风险提示和确认机制

3. **审计和日志**
   - 集成权限变更日志
   - 实现权限使用统计
   - 添加权限报告功能

#### 阶段四：现有页面集成 (2-3周)
1. **核心页面改造**
   - 用户管理页面权限控制
   - 商户管理页面权限控制
   - 系统设置页面权限控制

2. **业务页面集成**
   - 文件管理页面权限控制
   - 终端管理页面权限控制
   - 其他业务页面权限控制

3. **权限优化**
   - 性能优化和缓存调优
   - 用户体验优化
   - 错误处理完善

### 9.2 测试计划

#### 单元测试
```javascript
// 权限Hook测试示例
describe('useFeaturePermission', () => {
  test('should return correct permissions for SystemAdmin', async () => {
    const { result } = renderHook(() => useFeaturePermission(), {
      wrapper: createAuthWrapper({ roles: ['SystemAdmin'] })
    });

    await waitFor(() => {
      expect(result.current.hasPermission(PERMISSIONS.USER.DELETE)).toBe(true);
      expect(result.current.hasPermission(PERMISSIONS.MERCHANT.DELETE)).toBe(true);
    });
  });

  test('should handle permission cache correctly', async () => {
    // 测试缓存逻辑
  });

  test('should fallback to default permissions on error', async () => {
    // 测试错误降级
  });
});
```

#### 集成测试
```csharp
// 后端权限服务测试示例
[Test]
public async Task HasPermissionAsync_SystemAdmin_ShouldReturnTrue()
{
    // Arrange
    var user = CreateUserWithRoles("SystemAdmin");
    var featureKey = "user.delete";

    // Act
    var result = await _permissionService.HasPermissionAsync(user, featureKey);

    // Assert
    Assert.IsTrue(result);
}

[Test]
public async Task HasPermissionAsync_GloballyDisabled_ShouldReturnFalse()
{
    // 测试全局禁用功能
}
```

#### 端到端测试
```javascript
// E2E测试示例
describe('Permission Control E2E', () => {
  test('SystemAdmin can access all features', async () => {
    await loginAs('SystemAdmin');
    await page.goto('/app/users');

    // 验证所有按钮都可见且可用
    expect(await page.locator('[data-testid="create-user-btn"]').isVisible()).toBe(true);
    expect(await page.locator('[data-testid="delete-user-btn"]').isVisible()).toBe(true);
  });

  test('MerchantAdmin has limited access', async () => {
    await loginAs('MerchantAdmin');
    await page.goto('/app/users');

    // 验证删除按钮不可见或禁用
    expect(await page.locator('[data-testid="delete-user-btn"]').isVisible()).toBe(false);
  });
});
```

### 9.3 部署策略

#### 灰度发布计划
1. **阶段1：内部测试** (1周)
   - 开发团队内部测试
   - 修复发现的问题
   - 性能基准测试

2. **阶段2：小范围试用** (1周)
   - 选择1-2个客户进行试用
   - 收集用户反馈
   - 优化用户体验

3. **阶段3：逐步推广** (2周)
   - 扩大到更多客户
   - 监控系统性能
   - 持续优化

4. **阶段4：全面上线** (1周)
   - 所有客户启用新功能
   - 移除旧的权限控制代码
   - 文档更新

#### 回滚准备
```javascript
// 功能开关配置
const FEATURE_FLAGS = {
  NEW_PERMISSION_SYSTEM: process.env.ENABLE_NEW_PERMISSIONS === 'true',
  LEGACY_PERMISSION_FALLBACK: process.env.ENABLE_LEGACY_FALLBACK === 'true'
};

// 权限检查兼容层
const checkPermission = (featureKey) => {
  if (FEATURE_FLAGS.NEW_PERMISSION_SYSTEM) {
    return newPermissionService.hasPermission(featureKey);
  } else {
    return legacyPermissionService.hasPermission(featureKey);
  }
};
```

### 9.4 监控和维护

#### 性能监控
```javascript
// 权限检查性能监控
const performanceMonitor = {
  trackPermissionCheck: (featureKey, duration) => {
    analytics.track('permission_check', {
      featureKey,
      duration,
      timestamp: Date.now()
    });
  },

  trackCacheHit: (featureKey, hitType) => {
    analytics.track('permission_cache', {
      featureKey,
      hitType, // 'memory', 'localStorage', 'miss'
      timestamp: Date.now()
    });
  }
};
```

#### 错误监控
```csharp
// 后端错误监控
public class PermissionErrorHandler
{
    public void HandlePermissionError(Exception ex, string featureKey, string userId)
    {
        _logger.LogError(ex,
            "Permission check failed: Feature={FeatureKey}, User={UserId}",
            featureKey, userId);

        // 发送告警
        _alertService.SendAlert(new Alert
        {
            Type = "PermissionError",
            Message = $"Permission system error for feature {featureKey}",
            Severity = AlertSeverity.High
        });
    }
}
```

## 10. 总结

### 10.1 设计优势

这套优化后的功能按钮控制系统具有以下优势：

1. **🏗️ 架构优势**
   - **分层清晰**：数据层、服务层、应用层职责分明
   - **前后端分离**：前端权限控制 + 后端权限验证双重保障
   - **可扩展性强**：易于添加新功能和新角色

2. **🔒 安全性优势**
   - **多层验证**：前端UX优化 + 后端安全验证
   - **权限审计**：完整的权限变更和使用日志
   - **错误降级**：权限服务异常时的安全降级策略

3. **⚡ 性能优势**
   - **多层缓存**：内存缓存 + localStorage缓存
   - **批量检查**：减少API调用次数
   - **智能预加载**：用户登录时预加载权限

4. **🎯 易用性优势**
   - **统一常量**：避免硬编码字符串错误
   - **Hook + 组件**：适应不同使用场景
   - **开发友好**：调试模式和错误提示

5. **🔧 维护性优势**
   - **数据库设计**：规范化存储，易于查询和维护
   - **代码组织**：清晰的文件结构和命名规范
   - **文档完善**：详细的API文档和使用示例

### 10.2 与原方案对比

| 方面 | 原方案 | 优化方案 | 改进点 |
|------|--------|----------|--------|
| 数据存储 | Dictionary序列化 | 独立数据表 | 查询性能、并发安全 |
| 权限常量 | 硬编码字符串 | 统一常量管理 | 重构友好、错误减少 |
| 缓存策略 | 简单localStorage | 多层缓存 | 性能提升、智能失效 |
| 安全性 | 主要前端控制 | 前后端双重验证 | 安全性大幅提升 |
| 错误处理 | 基础错误处理 | 完善降级策略 | 系统稳定性提升 |
| 审计日志 | 基础日志 | 完整权限审计 | 合规性、可追溯性 |

### 10.3 适用场景

这套系统特别适合以下场景：

- **多租户SaaS系统**：不同客户需要不同的功能权限
- **企业内部系统**：需要细粒度的角色权限控制
- **合规要求高的系统**：需要完整的权限审计日志
- **功能复杂的系统**：有大量功能按钮需要权限控制

通过这套完整的功能按钮控制系统，可以实现对WebAdmin项目中所有功能的精细化、安全化、可维护化的权限管理，满足不同客户的个性化需求，同时保证系统的安全性和可扩展性。