# WebAdmin 项目文档目录

## 文档结构说明

本目录包含WebAdmin项目的所有技术文档，按功能模块分类组织：

### 📋 主要文档

- **[NOTES.md](../NOTES.md)** - 主备忘录文件（仅包含最新更新和文档索引）
- **[system-architecture.md](./system-architecture.md)** - 系统架构文档 ✅
- **[文档管理规范](./DOCUMENT_MANAGEMENT_GUIDELINES.md)** - 文档创建、组织和维护规范 ⚠️

### 📁 技术文档

#### 🏗️ 架构与技术栈
- **[frontend-development.md](./frontend-development.md)** - 前端技术栈、组件规范、开发配置 ✅
- **[backend-development.md](./backend-development.md)** - 后端技术栈、API设计、数据库配置 ✅

#### 📁 专项功能
- **[tcp-file-upload.md](./tcp-file-upload.md)** - TCP终端文件上传功能 ✅

### 📋 用户指南 (guides/)
- **[客户项目集成指南](./guides/CUSTOMER_PROJECT_INTEGRATION_GUIDE.md)** - 客户项目集成到主应用的完整指南 ✅
- **[外部链接菜单指南](./guides/EXTERNAL_LINK_MENU_GUIDE.md)** - 外部链接菜单配置和使用 ✅
- **[iframe目标选项指南](./guides/IFRAME_TARGET_OPTIONS_GUIDE.md)** - iframe打开方式配置 ✅

### 🏛️ 架构设计 (architecture/)
- **[客户项目结构](./architecture/CUSTOMER_PROJECT_STRUCTURE.md)** - 客户项目架构和设计模式 ✅
- **[外部系统路由实现](./architecture/EXTERNAL_SYSTEM_ROUTING_IMPLEMENTATION.md)** - 外部系统路由重构详情 ✅

### 🚀 部署文档 (deployment/)
- **[客户URL配置指南](./deployment/CUSTOMER_URL_CONFIGURATION_GUIDE.md)** - 客户项目URL配置 ✅
- **[微前端部署指南](./deployment/MICROFRONTEND_DEPLOYMENT_GUIDE.md)** - 微前端架构部署 ✅

### 🗄️ 数据库文档 (database/)
- **[数据库表结构说明](./database/DATABASE_SCHEMA_DOCUMENTATION.md)** - TcpDbContext完整表结构文档 ✅
- **[客户数据库手册](./database/CUSTOMER_MANUAL_DATABASE_GUIDE.md)** - 客户数据库管理指南 ✅
- **[数据库迁移策略](./database/DATABASE_MIGRATION_STRATEGY.md)** - 数据库迁移和版本管理 ✅

### 📝 待完善文档

以下文档将根据实际开发需要逐步创建：

#### 🎨 前端界面与体验
- **[frontend-ui-ux.md](./frontend-ui-ux.md)** - UI设计规范、用户体验优化 ⏳

#### 🔧 技术实现与优化
- **[technical-implementation.md](./technical-implementation.md)** - 数据库设计、性能优化、技术方案 ⏳

#### 🐛 问题解决记录
- **[troubleshooting.md](./troubleshooting.md)** - 常见问题、Bug修复记录 ⏳

#### 📊 API文档
- **[api-reference.md](./api-reference.md)** - API接口文档 ⏳

## 使用说明

1. **查找信息**：根据功能模块在对应文档中查找相关信息
2. **更新记录**：新的开发记录优先更新到主备忘录，定期整理到对应模块文档
3. **文档维护**：每个模块文档保持独立，便于查找和维护

## 文档更新原则

- 主备忘录只保留最近的重要更新（最多10条）
- 详细的技术实现记录到对应模块文档
- 保持文档结构清晰，便于快速定位信息
