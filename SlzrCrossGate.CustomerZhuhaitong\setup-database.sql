-- 珠海通客户项目数据库初始化脚本
-- 使用方法: mysql -u root -p < setup-database.sql

USE tcpserver;

-- 1. 创建只读用户（用于认证验证）
CREATE USER IF NOT EXISTS 'customer_readonly'@'%' IDENTIFIED BY 'readonly_password_2024!';

-- 2. 授予认证相关表的只读权限
GRANT SELECT ON tcpserver.Users TO 'customer_readonly'@'%';
GRANT SELECT ON tcpserver.UserRoles TO 'customer_readonly'@'%';
GRANT SELECT ON tcpserver.Roles TO 'customer_readonly'@'%';
GRANT SELECT ON tcpserver.Merchants TO 'customer_readonly'@'%';
GRANT SELECT ON tcpserver.MenuGroups TO 'customer_readonly'@'%';
GRANT SELECT ON tcpserver.MenuItems TO 'customer_readonly'@'%';

-- 3. 创建珠海通客户专用用户
CREATE USER IF NOT EXISTS 'customer_zhuhaitong'@'%' IDENTIFIED BY 'zhuhaitong_password_2024!';

-- 4. 授予珠海通客户专用表的完整权限
GRANT ALL PRIVILEGES ON tcpserver.Customer_Zhuhaitong_* TO 'customer_zhuhaitong'@'%';

-- 5. 刷新权限
FLUSH PRIVILEGES;

-- 6. 创建珠海通客户专用表
-- 查询记录表
CREATE TABLE IF NOT EXISTS Customer_Zhuhaitong_QueryRecords (
    Id INT AUTO_INCREMENT PRIMARY KEY,
    MerchantId VARCHAR(8) NOT NULL COMMENT '商户ID',
    QueryType VARCHAR(50) NOT NULL COMMENT '查询类型',
    QueryParams TEXT COMMENT '查询参数',
    QueryResult LONGTEXT COMMENT '查询结果',
    CreatedBy VARCHAR(50) COMMENT '创建人',
    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    UpdatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_zhuhaitong_query_merchant (MerchantId),
    INDEX idx_zhuhaitong_query_type (QueryType),
    INDEX idx_zhuhaitong_query_created (CreatedAt)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='珠海通查询记录表';

-- 查询配置表
CREATE TABLE IF NOT EXISTS Customer_Zhuhaitong_QueryConfigs (
    Id INT AUTO_INCREMENT PRIMARY KEY,
    MerchantId VARCHAR(8) NOT NULL COMMENT '商户ID',
    ConfigKey VARCHAR(100) NOT NULL COMMENT '配置键',
    ConfigValue TEXT COMMENT '配置值',
    Description VARCHAR(500) COMMENT '描述',
    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    UpdatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_zhuhaitong_config_merchant_key (MerchantId, ConfigKey)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='珠海通查询配置表';

-- 7. 插入一些示例配置
INSERT INTO Customer_Zhuhaitong_QueryConfigs (MerchantId, ConfigKey, ConfigValue, Description) VALUES
('00000001', 'query.timeout', '30', '查询超时时间（秒）'),
('00000001', 'query.max_records', '1000', '单次查询最大记录数'),
('00000001', 'query.cache_enabled', 'true', '是否启用查询缓存');

-- 8. 插入一些示例查询记录
INSERT INTO Customer_Zhuhaitong_QueryRecords (MerchantId, QueryType, QueryParams, QueryResult, CreatedBy) VALUES
('00000001', 'terminal_status', '{"limit": 10}', '{"success": true, "data": [{"terminalId": "T001", "status": "在线"}], "totalCount": 1}', 'admin'),
('00000001', 'transaction_summary', '{"date": "2024-01-15"}', '{"success": true, "data": {"totalTransactions": 100, "totalAmount": 10000}, "totalCount": 1}', 'admin');

-- 9. 在主系统中添加菜单项
-- 首先检查是否存在客户功能菜单组
INSERT IGNORE INTO MenuGroups (GroupKey, Title, IconName, SortOrder, IsEnabled, VisibleToSystemAdmin, VisibleToMerchantAdmin, VisibleToUser)
VALUES ('customer-features', '客户功能', 'StarIcon', 10, 1, 1, 1, 0);

-- 添加珠海通查询菜单项
INSERT IGNORE INTO MenuItems (
    MenuGroupId, ItemKey, Title, Href, IconName, SortOrder, 
    IsEnabled, VisibleToSystemAdmin, VisibleToMerchantAdmin, VisibleToUser, 
    IsExternal, Target
)
VALUES (
    (SELECT Id FROM MenuGroups WHERE GroupKey = 'customer-features'),
    'zhuhaitong-query',
    '珠海通查询',
    'http://localhost:5271/',
    'SearchIcon',
    1, 1, 1, 1, 0, 1, '_iframe'
);

-- 10. 显示创建的表和用户
SHOW TABLES LIKE 'Customer_Zhuhaitong_%';
SELECT User, Host FROM mysql.user WHERE User LIKE 'customer_%';
SHOW GRANTS FOR 'customer_readonly'@'%';
SHOW GRANTS FOR 'customer_zhuhaitong'@'%';
