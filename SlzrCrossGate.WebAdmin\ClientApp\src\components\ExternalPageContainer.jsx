import React, { useEffect, useRef, useState } from 'react';
import { Box, CircularProgress, Alert, IconButton, Typography } from '@mui/material';
import { X as CloseIcon, RefreshCw as RefreshIcon } from 'react-feather';
import { useAuth } from '../contexts/AuthContext';
import { useTheme } from '../contexts/ThemeContext';

const ExternalPageContainer = ({
  url,
  title,
  onClose,
  height = '100vh',
  supportAuth = true,  // 是否支持认证共享
  showHeader = true    // 是否显示标题栏
}) => {
  const iframeRef = useRef(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [authSupported, setAuthSupported] = useState(supportAuth);
  const { user, token } = useAuth();
  const { mode, theme } = useTheme();

  useEffect(() => {
    // 只有支持认证的iframe才监听消息
    if (!authSupported) {
      return;
    }

    // 监听来自iframe的消息
    const handleMessage = (event) => {
      // 验证消息来源
      if (!url.startsWith(event.origin)) {
        return;
      }

      switch (event.data.type) {
        case 'REQUEST_AUTH_INFO':
          // 向iframe发送认证信息
          sendAuthInfo();
          break;

        case 'REQUEST_TOKEN_REFRESH':
          // 处理token刷新请求
          handleTokenRefresh();
          break;

        case 'LOGOUT_REQUEST':
          // 处理登出请求
          handleLogout();
          break;

        case 'AUTH_EXPIRED':
          // 处理认证过期
          handleAuthExpired();
          break;

        case 'IFRAME_READY':
          // iframe加载完成
          setIsLoading(false);
          sendAuthInfo();
          break;

        case 'IFRAME_ERROR':
          // iframe加载错误
          setError(event.data.error || '页面加载失败');
          setIsLoading(false);
          break;

        default:
          break;
      }
    };

    window.addEventListener('message', handleMessage);

    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, [url, user, token, authSupported]);

  // 监听主题变化，实时同步到iframe
  useEffect(() => {
    if (authSupported && iframeRef.current) {
      sendThemeUpdate();
    }
  }, [mode, theme, authSupported]);

  const sendAuthInfo = () => {
    if (iframeRef.current && authSupported) {
      // 发送认证信息和主题信息
      iframeRef.current.contentWindow.postMessage({
        type: 'AUTH_INFO_RESPONSE',
        success: true,
        token: token,
        user: user,
        theme: {
          mode: mode,
          primaryColor: theme.palette.primary.main,
          backgroundColor: theme.palette.background.default,
          paperColor: theme.palette.background.paper,
          textColor: theme.palette.text.primary
        }
      }, '*');
    }
  };

  const sendThemeUpdate = () => {
    if (iframeRef.current && authSupported) {
      // 发送主题更新消息
      iframeRef.current.contentWindow.postMessage({
        type: 'THEME_UPDATE',
        theme: {
          mode: mode,
          primaryColor: theme.palette.primary.main,
          backgroundColor: theme.palette.background.default,
          paperColor: theme.palette.background.paper,
          textColor: theme.palette.text.primary
        }
      }, '*');
    }
  };

  const handleTokenRefresh = async () => {
    try {
      // 这里应该调用主应用的token刷新逻辑
      // 暂时返回当前token
      if (iframeRef.current) {
        iframeRef.current.contentWindow.postMessage({
          type: 'TOKEN_REFRESH_RESPONSE',
          success: true,
          token: token
        }, '*');
      }
    } catch (error) {
      if (iframeRef.current) {
        iframeRef.current.contentWindow.postMessage({
          type: 'TOKEN_REFRESH_RESPONSE',
          success: false,
          error: error.message
        }, '*');
      }
    }
  };

  const handleLogout = () => {
    // 通知主应用执行登出
    window.location.href = '/login';
  };

  const handleAuthExpired = () => {
    // 处理认证过期，重定向到登录页
    window.location.href = '/login';
  };

  const handleIframeLoad = () => {
    setIsLoading(false);
    setError(null);

    // 只有支持认证的iframe才发送认证信息
    if (authSupported) {
      setTimeout(() => {
        sendAuthInfo();
      }, 100);
    }
  };

  const handleIframeError = () => {
    setError('页面加载失败，请检查网络连接或联系管理员');
    setIsLoading(false);
  };

  const handleRefresh = () => {
    setIsLoading(true);
    setError(null);
    if (iframeRef.current) {
      iframeRef.current.src = iframeRef.current.src;
    }
  };

  return (
    <Box sx={{
      position: 'relative',
      height: height,
      display: 'flex',
      flexDirection: 'column'
    }}>
      {/* 标题栏 - 可选显示 */}
      {showHeader && (
        <Box sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          p: 2,
          borderBottom: 1,
          borderColor: 'divider',
          bgcolor: 'background.paper'
        }}>
          <Typography variant="h6" component="h1">
            {title}
          </Typography>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <IconButton
              size="small"
              onClick={handleRefresh}
              title="刷新页面"
            >
              <RefreshIcon size={18} />
            </IconButton>
            <IconButton
              size="small"
              onClick={onClose}
              title="关闭页面"
            >
              <CloseIcon size={18} />
            </IconButton>
          </Box>
        </Box>
      )}

      {/* 内容区域 */}
      <Box sx={{ 
        position: 'relative', 
        flex: 1,
        overflow: 'hidden'
      }}>
        {/* 加载指示器 */}
        {isLoading && (
          <Box sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            bgcolor: 'background.default',
            zIndex: 1000
          }}>
            <Box sx={{ textAlign: 'center' }}>
              <CircularProgress />
              <Typography variant="body2" sx={{ mt: 2 }}>
                正在加载页面...
              </Typography>
            </Box>
          </Box>
        )}

        {/* 错误提示 */}
        {error && (
          <Box sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            bgcolor: 'background.default',
            zIndex: 1000,
            p: 3
          }}>
            <Alert 
              severity="error" 
              action={
                <IconButton
                  color="inherit"
                  size="small"
                  onClick={handleRefresh}
                >
                  <RefreshIcon size={16} />
                </IconButton>
              }
            >
              {error}
            </Alert>
          </Box>
        )}

        {/* iframe */}
        <iframe
          ref={iframeRef}
          src={url}
          title={title}
          onLoad={handleIframeLoad}
          onError={handleIframeError}
          style={{
            width: '100%',
            height: '100%',
            border: 'none',
            display: error ? 'none' : 'block'
          }}
          sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-modals"
        />
      </Box>
    </Box>
  );
};

export default ExternalPageContainer;
