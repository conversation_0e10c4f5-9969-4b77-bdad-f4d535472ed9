using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using Microsoft.Extensions.Configuration;
using SlzrCrossGate.Core.Database;

namespace SlzrCrossGate.Migrations.MySql
{
    public class TcpDbContextFactory : IDesignTimeDbContextFactory<TcpDbContext>
    {
        public TcpDbContext CreateDbContext(string[] args)
        {
            var optionsBuilder = new DbContextOptionsBuilder<TcpDbContext>();

            // 使用MySQL测试连接字符串
            var connectionString = "Server=localhost;Database=TcpserverTms_Test;User=root;Password=******;CharSet=utf8mb4;";
            var serverVersion = ServerVersion.AutoDetect(connectionString);

            // 强制使用MySQL并指定迁移程序集
            optionsBuilder.UseMySql(connectionString, serverVersion, options =>
                options.MigrationsAssembly("SlzrCrossGate.Migrations.MySql"));

            // 创建一个简单的配置对象
            var configData = new Dictionary<string, string>
            {
                {"DatabaseProvider", "MySql"},
                {"ConnectionStrings:DefaultConnection", connectionString}
            };
            var configuration = new ConfigurationBuilder()
                .AddInMemoryCollection(configData)
                .Build();

            return new TcpDbContext(optionsBuilder.Options, configuration);
        }
    }
}
