using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace SlzrCrossGate.Core.Models
{
    /// <summary>
    /// 预约文件发布
    /// </summary>
    public class ScheduledFilePublish : ITenantEntity
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int ID { get; set; }

        /// <summary>
        /// 文件版本ID
        /// </summary>
        public int FileVersionID { get; set; }

        /// <summary>
        /// 商户ID
        /// </summary>
        [MaxLength(8)]
        public required string MerchantID { get; set; }

        /// <summary>
        /// 文件类型ID
        /// </summary>
        [StringLength(3)]
        public required string FileTypeID { get; set; }

        /// <summary>
        /// 文件参数
        /// </summary>
        [MaxLength(8)]
        public required string FilePara { get; set; }

        /// <summary>
        /// 完整文件类型 (FileTypeID + FilePara)
        /// </summary>
        [MaxLength(11)]
        [MinLength(3)]
        public required string FileFullType { get; set; }

        /// <summary>
        /// 版本号
        /// </summary>
        [StringLength(4)]
        [Column(TypeName = "char(4)")]
        public required string Ver { get; set; }

        /// <summary>
        /// 发布类型
        /// </summary>
        public PublishTypeOption PublishType { get; set; }

        /// <summary>
        /// 发布目标
        /// </summary>
        [MaxLength(100)]
        public required string PublishTarget { get; set; }

        /// <summary>
        /// 预约发布时间
        /// </summary>
        public DateTime ScheduledTime { get; set; }

        /// <summary>
        /// 预约状态
        /// </summary>
        public ScheduledPublishStatus Status { get; set; } = ScheduledPublishStatus.Pending;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 创建人
        /// </summary>
        [MaxLength(20)]
        public required string CreatedBy { get; set; }

        /// <summary>
        /// 实际执行时间
        /// </summary>
        public DateTime? ExecutedTime { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        [MaxLength(500)]
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 备注信息
        /// </summary>
        [MaxLength(200)]
        public string? Remarks { get; set; }
    }
}
