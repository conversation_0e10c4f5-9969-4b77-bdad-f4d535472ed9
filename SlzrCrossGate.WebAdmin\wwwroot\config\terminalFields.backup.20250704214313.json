{"version": "1.0.0", "lastUpdated": "2025-07-04T13:42:47.864Z", "description": "终端列表字段配置文件 - 支持动态添加和修改字段", "fieldCategories": {"basic": {"name": "基础信息", "description": "终端的基本属性字段", "fields": {"merchantName": {"key": "merchantName", "displayName": "商户", "dataPath": "merchantName", "sortable": true, "width": 150, "hideOn": ["xs"], "type": "text", "enabled": true, "order": 1, "tooltip": "终端所属商户名称"}, "machineID": {"key": "machineID", "displayName": "出厂序列号", "dataPath": "machineID", "sortable": true, "width": 120, "hideOn": ["xs", "sm"], "type": "text", "enabled": true, "order": 2}, "deviceNO": {"key": "deviceNO", "displayName": "设备编号", "dataPath": "deviceNO", "sortable": true, "width": 120, "hideOn": [], "type": "text", "enabled": true, "order": 3}, "lineNO": {"key": "lineNO", "displayName": "线路编号", "dataPath": "lineNO", "sortable": true, "width": 100, "hideOn": ["xs", "sm"], "type": "text", "enabled": true, "order": 4}, "terminalType": {"key": "terminalType", "displayName": "终端类型", "dataPath": "terminalType", "sortable": true, "width": 100, "hideOn": ["xs", "sm"], "type": "text", "enabled": true, "order": 5}, "createTime": {"key": "createTime", "displayName": "注册日期", "dataPath": "createTime", "sortable": true, "width": 120, "hideOn": ["xs", "sm"], "type": "date", "enabled": true, "order": 6}, "lastActiveTime": {"key": "lastActiveTime", "displayName": "最后活跃时间", "dataPath": "status.lastActiveTime", "sortable": true, "width": 180, "hideOn": ["xs"], "type": "datetime", "enabled": true, "order": 7}, "status": {"key": "status", "displayName": "状态", "dataPath": "status", "sortable": true, "width": 120, "hideOn": [], "type": "status", "enabled": true, "order": 8}}}, "fileVersions": {"name": "文件版本", "description": "终端上的各种文件版本信息", "fields": {"appVersion": {"key": "appVersion", "displayName": "APP版本", "dataPath": "status.fileVersionMetadata", "sortable": false, "width": 150, "hideOn": ["xs", "sm"], "type": "fileVersion", "enabled": true, "order": 9, "config": {"versionKeys": ["APK{terminalType}", "PRO{terminalType}"], "showVersionKey": true}}, "lineVersion": {"key": "lineVersion", "displayName": "票价版本", "dataPath": "status.fileVersionMetadata", "sortable": false, "width": 150, "hideOn": ["xs", "sm"], "type": "fileVersion", "enabled": true, "order": 10, "config": {"versionKeys": ["PZB{lineNO}", "PRI{lineNO}", "PRF{lineNO}", "PRG{lineNO}"], "showVersionKey": true}}, "blacklistVersion": {"key": "blacklistVersion", "displayName": "黑名单版本", "dataPath": "status.fileVersionMetadata.BLKBUS.current", "sortable": false, "width": 120, "hideOn": ["xs", "sm"], "type": "text", "enabled": true, "order": 11}}}, "properties": {"name": "设备属性", "description": "从propertyMetadata中提取的设备属性信息", "fields": {"batteryLevel": {"key": "batteryLevel", "displayName": "电池电量", "dataPath": "status.propertyMetadata.battery_level", "sortable": true, "width": 100, "hideOn": ["xs", "sm"], "type": "percentage", "enabled": true, "order": 12, "tooltip": "设备当前电池电量百分比"}, "signalStrength": {"key": "signalStrength", "displayName": "信号强度", "dataPath": "status.propertyMetadata.signal_strength", "sortable": true, "width": 100, "hideOn": ["xs", "sm"], "type": "signal", "enabled": false, "order": 13, "tooltip": "网络信号强度(dBm)"}, "temperature": {"key": "temperature", "displayName": "设备温度", "dataPath": "status.propertyMetadata.temperature", "sortable": true, "width": 100, "hideOn": ["xs", "sm"], "type": "temperature", "enabled": false, "order": 14, "tooltip": "设备内部温度(°C)"}, "networkType": {"key": "networkType", "displayName": "网络类型", "dataPath": "status.propertyMetadata.network_type", "sortable": true, "width": 100, "hideOn": ["xs", "sm"], "type": "networkType", "enabled": true, "order": 15, "tooltip": "当前使用的网络类型"}}}}, "renderTypes": {"text": {"description": "普通文本显示"}, "date": {"description": "日期格式显示"}, "datetime": {"description": "日期时间格式显示"}, "status": {"description": "状态芯片显示"}, "fileVersion": {"description": "文件版本信息显示，支持动态键名"}, "percentage": {"description": "百分比显示，带颜色状态"}, "signal": {"description": "信号强度显示，单位dBm"}, "temperature": {"description": "温度显示，单位°C"}, "networkType": {"description": "网络类型显示"}}, "defaultSettings": {"enabledFields": ["merchantName", "machineID", "deviceNO", "lineNO", "terminalType", "createTime", "lastActiveTime", "status", "appVersion", "lineVersion", "blacklistVersion"], "columnSettings": {"autoWidth": false, "stickyHeader": true, "stickyActions": true}}}