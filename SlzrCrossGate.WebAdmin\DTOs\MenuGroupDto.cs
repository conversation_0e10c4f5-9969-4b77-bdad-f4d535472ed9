using System.ComponentModel.DataAnnotations;

namespace SlzrCrossGate.WebAdmin.DTOs
{
    /// <summary>
    /// 菜单分组DTO
    /// </summary>
    public class MenuGroupDto
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "分组标识符不能为空")]
        [StringLength(50, ErrorMessage = "分组标识符长度不能超过50个字符")]
        public required string GroupKey { get; set; }

        [Required(ErrorMessage = "分组标题不能为空")]
        [StringLength(100, ErrorMessage = "分组标题长度不能超过100个字符")]
        public required string Title { get; set; }

        [StringLength(50, ErrorMessage = "图标名称长度不能超过50个字符")]
        public string? IconName { get; set; }

        public int SortOrder { get; set; }

        public bool IsEnabled { get; set; } = true;

        public bool VisibleToSystemAdmin { get; set; } = true;

        public bool VisibleToMerchantAdmin { get; set; } = true;

        public bool VisibleToUser { get; set; } = true;

        public bool IsExternal { get; set; } = false;

        [StringLength(20, ErrorMessage = "链接打开方式长度不能超过20个字符")]
        public string Target { get; set; } = "_self";

        public DateTime CreatedAt { get; set; }

        public DateTime UpdatedAt { get; set; }

        public List<MenuItemDto> MenuItems { get; set; } = new List<MenuItemDto>();
    }

    /// <summary>
    /// 菜单项DTO
    /// </summary>
    public class MenuItemDto
    {
        public int Id { get; set; }

        public int MenuGroupId { get; set; }

        [Required(ErrorMessage = "菜单项标识符不能为空")]
        [StringLength(50, ErrorMessage = "菜单项标识符长度不能超过50个字符")]
        public required string ItemKey { get; set; }

        [Required(ErrorMessage = "菜单标题不能为空")]
        [StringLength(100, ErrorMessage = "菜单标题长度不能超过100个字符")]
        public required string Title { get; set; }

        [Required(ErrorMessage = "路由路径不能为空")]
        [StringLength(200, ErrorMessage = "路由路径长度不能超过200个字符")]
        public required string Href { get; set; }

        [StringLength(50, ErrorMessage = "图标名称长度不能超过50个字符")]
        public string? IconName { get; set; }

        public int SortOrder { get; set; }

        public bool IsEnabled { get; set; } = true;

        public bool VisibleToSystemAdmin { get; set; } = true;

        public bool VisibleToMerchantAdmin { get; set; } = true;

        public bool VisibleToUser { get; set; } = true;

        public bool IsExternal { get; set; } = false;

        [StringLength(20, ErrorMessage = "链接打开方式长度不能超过20个字符")]
        public string Target { get; set; } = "_self";

        public DateTime CreatedAt { get; set; }

        public DateTime UpdatedAt { get; set; }
    }

    /// <summary>
    /// 创建菜单分组请求DTO
    /// </summary>
    public class CreateMenuGroupDto
    {
        [Required(ErrorMessage = "分组标识符不能为空")]
        [StringLength(50, ErrorMessage = "分组标识符长度不能超过50个字符")]
        public required string GroupKey { get; set; }

        [Required(ErrorMessage = "分组标题不能为空")]
        [StringLength(100, ErrorMessage = "分组标题长度不能超过100个字符")]
        public required string Title { get; set; }

        [StringLength(50, ErrorMessage = "图标名称长度不能超过50个字符")]
        public string? IconName { get; set; }

        public int SortOrder { get; set; }

        public bool IsEnabled { get; set; } = true;

        public bool VisibleToSystemAdmin { get; set; } = true;

        public bool VisibleToMerchantAdmin { get; set; } = true;

        public bool VisibleToUser { get; set; } = true;
    }

    /// <summary>
    /// 更新菜单分组请求DTO
    /// </summary>
    public class UpdateMenuGroupDto
    {
        [Required(ErrorMessage = "分组标题不能为空")]
        [StringLength(100, ErrorMessage = "分组标题长度不能超过100个字符")]
        public required string Title { get; set; }

        [StringLength(50, ErrorMessage = "图标名称长度不能超过50个字符")]
        public string? IconName { get; set; }

        public int SortOrder { get; set; }

        public bool IsEnabled { get; set; } = true;

        public bool VisibleToSystemAdmin { get; set; } = true;

        public bool VisibleToMerchantAdmin { get; set; } = true;

        public bool VisibleToUser { get; set; } = true;
    }

    /// <summary>
    /// 创建菜单项请求DTO
    /// </summary>
    public class CreateMenuItemDto
    {
        public int MenuGroupId { get; set; }

        [Required(ErrorMessage = "菜单项标识符不能为空")]
        [StringLength(50, ErrorMessage = "菜单项标识符长度不能超过50个字符")]
        public required string ItemKey { get; set; }

        [Required(ErrorMessage = "菜单标题不能为空")]
        [StringLength(100, ErrorMessage = "菜单标题长度不能超过100个字符")]
        public required string Title { get; set; }

        [Required(ErrorMessage = "路由路径不能为空")]
        [StringLength(200, ErrorMessage = "路由路径长度不能超过200个字符")]
        public required string Href { get; set; }

        [StringLength(50, ErrorMessage = "图标名称长度不能超过50个字符")]
        public string? IconName { get; set; }

        public int SortOrder { get; set; }

        public bool IsEnabled { get; set; } = true;

        public bool VisibleToSystemAdmin { get; set; } = true;

        public bool VisibleToMerchantAdmin { get; set; } = true;

        public bool VisibleToUser { get; set; } = true;

        public bool IsExternal { get; set; } = false;

        [StringLength(20, ErrorMessage = "链接打开方式长度不能超过20个字符")]
        public string Target { get; set; } = "_self";
    }

    /// <summary>
    /// 更新菜单项请求DTO
    /// </summary>
    public class UpdateMenuItemDto
    {
        [Required(ErrorMessage = "菜单标题不能为空")]
        [StringLength(100, ErrorMessage = "菜单标题长度不能超过100个字符")]
        public required string Title { get; set; }

        [Required(ErrorMessage = "路由路径不能为空")]
        [StringLength(200, ErrorMessage = "路由路径长度不能超过200个字符")]
        public required string Href { get; set; }

        [StringLength(50, ErrorMessage = "图标名称长度不能超过50个字符")]
        public string? IconName { get; set; }

        public int SortOrder { get; set; }

        public bool IsEnabled { get; set; } = true;

        public bool VisibleToSystemAdmin { get; set; } = true;

        public bool VisibleToMerchantAdmin { get; set; } = true;

        public bool VisibleToUser { get; set; } = true;

        public bool IsExternal { get; set; } = false;

        [StringLength(20, ErrorMessage = "链接打开方式长度不能超过20个字符")]
        public string Target { get; set; } = "_self";
    }
}
