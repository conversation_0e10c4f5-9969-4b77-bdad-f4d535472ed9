using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using SlzrCrossGate.Core.Database;
using SlzrCrossGate.Core.Models;
using SlzrCrossGate.Core.Service.FileStorage;
using System.Security.Cryptography;

namespace SlzrCrossGate.Core.Services
{
    /// <summary>
    /// 终端文件上传服务
    /// </summary>
    public class TerminalFileUploadService
    {
        private readonly TcpDbContext _dbContext;
        private readonly FileService _fileService;
        private readonly ILogger<TerminalFileUploadService> _logger;

        public TerminalFileUploadService(
            TcpDbContext dbContext,
            FileService fileService,
            ILogger<TerminalFileUploadService> logger)
        {
            _dbContext = dbContext;
            _fileService = fileService;
            _logger = logger;
        }

        /// <summary>
        /// 创建文件上传记录
        /// </summary>
        public async Task<TerminalFileUpload> CreateUploadAsync(
            string merchantId,
            string terminalId,
            string terminalType,
            string fileType,
            string fileName,
            long fileSize,
            string fileCrc,
            int clientChunkSize,
            int serverChunkSize = 4096)
        {
            var uploadId = Guid.NewGuid().ToString("N"); // 32字节十六进制字符串
            var tempFilePath = Path.Combine(Path.GetTempPath(), $"terminal_upload_{uploadId}.tmp");

            var upload = new TerminalFileUpload
            {
                ID = uploadId,
                MerchantID = merchantId,
                TerminalID = terminalId,
                TerminalType = terminalType,
                FileType = fileType,
                FileName = fileName,
                FileSize = fileSize,
                FileCRC = fileCrc,
                ReceivedLength = 0,
                ClientChunkSize = clientChunkSize,
                ServerChunkSize = serverChunkSize,
                Status = TerminalUploadStatus.Uploading,
                StartTime = DateTime.Now,
                UpdateTime = DateTime.Now,
                TempFilePath = tempFilePath,
                UploadedBy = $"Terminal_{terminalId}",
                LastActivityTime = DateTime.Now
            };

            _dbContext.TerminalFileUploads.Add(upload);
            await _dbContext.SaveChangesAsync();

            _logger.LogInformation("Created file upload record: {UploadId} for terminal {TerminalId}, file: {FileName}",
                uploadId, terminalId, fileName);

            return upload;
        }

        /// <summary>
        /// 获取上传记录
        /// </summary>
        public async Task<TerminalFileUpload?> GetUploadAsync(string uploadId)
        {
            return await _dbContext.TerminalFileUploads
                .FirstOrDefaultAsync(u => u.ID == uploadId);
        }

        /// <summary>
        /// 更新上传记录的最后活动时间
        /// </summary>
        public async Task UpdateLastActivityTimeAsync(string uploadId)
        {
            var upload = await GetUploadAsync(uploadId);
            if (upload != null)
            {
                upload.LastActivityTime = DateTime.Now;
                await _dbContext.SaveChangesAsync();
            }
        }

        /// <summary>
        /// 写入文件块
        /// </summary>
        public async Task<bool> WriteChunkAsync(string uploadId, uint offset, byte[] chunkData)
        {
            var upload = await GetUploadAsync(uploadId);
            if (upload == null || upload.Status != TerminalUploadStatus.Uploading)
            {
                _logger.LogWarning("Upload not found or not in uploading status: {UploadId}", uploadId);
                return false;
            }

            try
            {
                // 确保临时文件目录存在
                var tempDir = Path.GetDirectoryName(upload.TempFilePath);
                if (!string.IsNullOrEmpty(tempDir) && !Directory.Exists(tempDir))
                {
                    Directory.CreateDirectory(tempDir);
                }

                // 写入文件块
                using var fileStream = new FileStream(upload.TempFilePath!, FileMode.OpenOrCreate, FileAccess.Write);
                fileStream.Seek(offset, SeekOrigin.Begin);
                await fileStream.WriteAsync(chunkData);

                // 更新接收长度
                var newReceivedLength = Math.Max(upload.ReceivedLength, offset + chunkData.Length);
                upload.ReceivedLength = newReceivedLength;
                upload.UpdateTime = DateTime.Now;
                upload.LastActivityTime = DateTime.Now;

                await _dbContext.SaveChangesAsync();

                _logger.LogDebug("Written chunk for upload {UploadId}: offset={Offset}, size={Size}, total={Total}/{FileSize}",
                    uploadId, offset, chunkData.Length, newReceivedLength, upload.FileSize);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to write chunk for upload {UploadId}", uploadId);
                
                // 标记为失败
                upload.Status = TerminalUploadStatus.Failed;
                upload.ErrorMessage = ex.Message;
                upload.UpdateTime = DateTime.Now;
                await _dbContext.SaveChangesAsync();

                return false;
            }
        }

        /// <summary>
        /// 完成文件上传
        /// </summary>
        public async Task<bool> CompleteUploadAsync(string uploadId)
        {
            var upload = await GetUploadAsync(uploadId);
            if (upload == null)
            {
                _logger.LogWarning("Upload not found: {UploadId}", uploadId);
                return false;
            }

            try
            {
                // 验证文件完整性
                if (upload.ReceivedLength != upload.FileSize)
                {
                    _logger.LogWarning("File size mismatch for upload {UploadId}: received={Received}, expected={Expected}",
                        uploadId, upload.ReceivedLength, upload.FileSize);
                    
                    upload.Status = TerminalUploadStatus.Failed;
                    upload.ErrorMessage = "File size mismatch";
                    upload.UpdateTime = DateTime.Now;
                    await _dbContext.SaveChangesAsync();
                    return false;
                }

                // 验证CRC
                var actualCrc = await CalculateFileCrcAsync(upload.TempFilePath!);
                if (upload.FileCRC=="AABBAABB") { 
                    //不验证CRC 
                    upload.FileCRC = actualCrc;

                }
                else if (!string.Equals(actualCrc, upload.FileCRC, StringComparison.OrdinalIgnoreCase))
                {
                    _logger.LogWarning("CRC mismatch for upload {UploadId}: actual={Actual}, expected={Expected}",
                        uploadId, actualCrc, upload.FileCRC);
                    
                    upload.Status = TerminalUploadStatus.Failed;
                    upload.ErrorMessage = "CRC mismatch";
                    upload.UpdateTime = DateTime.Now;
                    await _dbContext.SaveChangesAsync();
                    return false;
                }

                // 保存到最终存储
                var finalFilePath = await _fileService.SaveTemporaryFile(
                    await File.ReadAllBytesAsync(upload.TempFilePath!),
                    upload.FileName,
                    upload.UploadedBy!);

                // 创建UploadFile记录
                var uploadFile = new UploadFile
                {
                    ID = Guid.NewGuid().ToString("N"),
                    FileName = upload.FileName,
                    FileSize = (int)upload.FileSize,
                    FilePath = finalFilePath,
                    UploadTime = DateTime.Now,
                    Crc = upload.FileCRC,
                    UploadedBy = upload.UploadedBy,
                    ContentType = GetContentType(upload.FileType),
                    LastModifiedTime = DateTime.Now
                };

                _dbContext.UploadFiles.Add(uploadFile);

                // 更新上传记录
                upload.Status = TerminalUploadStatus.Completed;
                upload.FinalUploadFileID = uploadFile.ID;
                upload.UpdateTime = DateTime.Now;

                await _dbContext.SaveChangesAsync();

                // 删除临时文件
                try
                {
                    if (File.Exists(upload.TempFilePath))
                    {
                        File.Delete(upload.TempFilePath);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to delete temporary file: {TempFilePath}", upload.TempFilePath);
                }

                _logger.LogInformation("Completed file upload: {UploadId}, final file: {FinalFileId}",
                    uploadId, uploadFile.ID);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to complete upload {UploadId}", uploadId);
                
                upload.Status = TerminalUploadStatus.Failed;
                upload.ErrorMessage = ex.Message;
                upload.UpdateTime = DateTime.Now;
                await _dbContext.SaveChangesAsync();

                return false;
            }
        }

        /// <summary>
        /// 取消上传
        /// </summary>
        public async Task<bool> CancelUploadAsync(string uploadId, string reason = "")
        {
            var upload = await GetUploadAsync(uploadId);
            if (upload == null)
            {
                return false;
            }

            upload.Status = TerminalUploadStatus.Cancelled;
            upload.ErrorMessage = reason;
            upload.UpdateTime = DateTime.Now;

            await _dbContext.SaveChangesAsync();

            // 删除临时文件
            try
            {
                if (!string.IsNullOrEmpty(upload.TempFilePath) && File.Exists(upload.TempFilePath))
                {
                    File.Delete(upload.TempFilePath);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to delete temporary file: {TempFilePath}", upload.TempFilePath);
            }

            _logger.LogInformation("Cancelled file upload: {UploadId}, reason: {Reason}", uploadId, reason);
            return true;
        }

        /// <summary>
        /// 清理过期的上传记录
        /// </summary>
        public async Task<int> CleanupExpiredUploadsAsync(TimeSpan maxAge)
        {
            var cutoffTime = DateTime.Now - maxAge;
            
            var expiredUploads = await _dbContext.TerminalFileUploads
                .Where(u => u.LastActivityTime < cutoffTime && 
                           (u.Status == TerminalUploadStatus.Uploading || u.Status == TerminalUploadStatus.Failed))
                .ToListAsync();

            var cleanedCount = 0;
            foreach (var upload in expiredUploads)
            {
                // 删除临时文件
                try
                {
                    if (!string.IsNullOrEmpty(upload.TempFilePath) && File.Exists(upload.TempFilePath))
                    {
                        File.Delete(upload.TempFilePath);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to delete temporary file: {TempFilePath}", upload.TempFilePath);
                }

                // 标记为取消
                upload.Status = TerminalUploadStatus.Cancelled;
                upload.ErrorMessage = "Expired and cleaned up";
                upload.UpdateTime = DateTime.Now;
                cleanedCount++;
            }

            if (cleanedCount > 0)
            {
                await _dbContext.SaveChangesAsync();
                _logger.LogInformation("Cleaned up {Count} expired upload records", cleanedCount);
            }

            return cleanedCount;
        }

        /// <summary>
        /// 计算文件CRC32
        /// </summary>
        private async Task<string> CalculateFileCrcAsync(string filePath)
        {
            using var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read);
            var crc = SlzrCrossGate.Common.CRC.calFileCRC(await File.ReadAllBytesAsync(filePath));
            return crc.ToString("X8");
        }

        /// <summary>
        /// 根据文件类型获取Content-Type
        /// </summary>
        private static string GetContentType(string fileType)
        {
            return fileType.ToUpper() switch
            {
                "LOG" => "text/plain",
                "DAT" => "application/octet-stream",
                "TXT" => "text/plain",
                "JSON" => "application/json",
                "XML" => "application/xml",
                _ => "application/octet-stream"
            };
        }
    }
}
