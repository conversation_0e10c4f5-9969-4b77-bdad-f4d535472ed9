# WebAdmin 详细设计说明书

## 文档信息

- **项目名称**: SlzrCrossGate WebAdmin 终端管理系统
- **文档版本**: v1.0
- **创建日期**: 2025-08-27
- **文档类型**: 详细设计说明书
- **适用范围**: 开发团队、测试团队、运维团队、项目管理

---

## 目录

### 1. [系统概述](#1-系统概述)
- 1.1 项目背景
- 1.2 系统目标
- 1.3 核心功能
- 1.4 技术特点
- 1.5 系统边界

### 2. [系统架构设计](#2-系统架构设计)
- 2.1 总体架构
- 2.2 技术栈选型
- 2.3 模块划分
- 2.4 部署架构
- 2.5 数据流设计

### 3. [数据库设计](#3-数据库设计)
- 3.1 数据库架构
- 3.2 核心表结构
- 3.3 关系设计
- 3.4 索引策略
- 3.5 数据迁移

### 4. [前端设计](#4-前端设计)
- 4.1 React架构设计
- 4.2 组件设计规范
- 4.3 路由设计
- 4.4 状态管理
- 4.5 UI设计规范

### 5. [后端设计](#5-后端设计)
- 5.1 .NET架构设计
- 5.2 API设计规范
- 5.3 服务层设计
- 5.4 中间件设计
- 5.5 权限系统设计

### 6. [功能模块设计](#6-功能模块设计)
- 6.1 用户管理模块
- 6.2 商户管理模块
- 6.3 终端管理模块
- 6.4 文件管理模块
- 6.5 消息管理模块
- 6.6 系统管理模块

### 7. [安全设计](#7-安全设计)
- 7.1 身份认证设计
- 7.2 权限控制设计
- 7.3 数据安全设计
- 7.4 通信安全设计
- 7.5 审计日志设计

### 8. [接口设计](#8-接口设计)
- 8.1 RESTful API设计
- 8.2 TCP通信协议
- 8.3 消息队列接口
- 8.4 文件服务接口
- 8.5 第三方集成接口

### 9. [部署运维设计](#9-部署运维设计)
- 9.1 Docker容器化部署
- 9.2 环境配置管理
- 9.3 监控日志系统
- 9.4 备份恢复策略
- 9.5 性能优化方案

### 10. [附录](#10-附录)
- 10.1 技术选型对比
- 10.2 性能测试报告
- 10.3 部署检查清单
- 10.4 常见问题解答

---

## 文档说明

本设计说明书基于WebAdmin项目的实际实现情况编写，所有内容都与项目代码保持一致。文档将分章节逐步完成，每个章节都包含详细的设计说明、实现细节和相关代码示例。

### 文档更新记录

| 版本 | 日期 | 更新内容 | 更新人 |
|------|------|----------|--------|
| v1.0 | 2025-08-27 | 创建文档框架和目录结构 | AI Assistant |

### 相关文档

- [系统架构设计文档](./system-architecture.md)
- [前端开发规范](./frontend-development.md)
- [后端开发规范](./backend-development.md)
- [数据库表结构说明](./database/DATABASE_SCHEMA_DOCUMENTATION.md)
- [UI样式规范](./frontend/ui-style-guide.md)

---

## 1. 系统概述

### 1.1 项目背景

WebAdmin是SlzrCrossGate终端管理系统的核心组件，专门为支付终端、公交刷卡终端等智能设备的集中管理而设计。系统面向多商户运营模式，为终端设备运营商、商户管理员和系统管理员提供统一的管理平台。

#### 1.1.1 业务场景
- **支付终端管理**: 支持银联POS机、移动支付终端等设备的远程管理
- **公交刷卡终端**: 管理公交车载刷卡设备、站台刷卡设备等交通支付终端
- **多商户运营**: 支持多个商户独立管理各自的终端设备和业务数据
- **集中运维**: 提供统一的设备监控、文件分发、消息通信等运维功能

#### 1.1.2 用户群体
- **系统管理员**: 平台运营方，负责整个系统的管理和维护
- **商户管理员**: 各商户的管理人员，管理本商户的终端设备和业务
- **运维人员**: 负责设备维护、故障处理、数据监控等技术运维工作

### 1.2 系统目标

#### 1.2.1 业务目标
- **提升管理效率**: 通过集中化管理平台，提升终端设备管理效率50%以上
- **降低运维成本**: 减少现场运维工作量，实现远程管理和自动化运维
- **保障业务连续性**: 确保终端设备7×24小时稳定运行，业务中断时间<0.1%
- **支持业务扩展**: 支持新商户快速接入，新设备类型灵活扩展

#### 1.2.2 技术目标
- **高可用性**: 系统可用性≥99.9%，支持故障自动恢复
- **高并发性**: 支持1000+终端设备同时在线，10000+并发用户访问
- **高安全性**: 多层安全防护，支持双因素认证和数据加密
- **高扩展性**: 模块化设计，支持水平扩展和微服务架构

### 1.3 核心功能

#### 1.3.1 功能架构图
```
WebAdmin 核心功能架构
├── 用户权限管理
│   ├── 用户注册/登录
│   ├── 角色权限控制
│   ├── 双因素认证(2FA)
│   ├── 微信扫码登录
│   └── 功能按钮权限控制
├── 商户管理
│   ├── 多商户支持
│   ├── 商户信息管理
│   ├── 数据隔离
│   └── 商户配置管理
├── 终端设备管理
│   ├── 设备注册与配置
│   ├── 实时状态监控
│   ├── 远程控制操作
│   ├── 事件记录与分析
│   ├── 日志记录与查询
│   └── 交易记录管理
├── 文件管理
│   ├── 文件上传与版本控制
│   ├── 文件类型管理
│   ├── 文件分发与发布
│   ├── 预约发布功能
│   └── 发布历史追踪
├── 消息通信
│   ├── 消息类型管理
│   ├── 消息发送与接收
│   ├── 消息历史记录
│   └── 消息状态跟踪
├── 业务数据管理
│   ├── 线路票价管理
│   ├── 票价折扣方案
│   ├── 银联密钥管理
│   ├── 车辆信息管理
│   └── 字典数据管理
└── 系统管理
    ├── 菜单权限管理
    ├── 操作日志审计
    ├── 系统配置管理
    ├── 数据备份恢复
    └── 性能监控分析
```

#### 1.3.2 主要功能模块

**用户权限管理模块**
- 支持基于角色的权限控制(RBAC)
- 实现功能级别的按钮权限控制
- 提供双因素认证和微信扫码登录
- 完整的用户生命周期管理

**终端设备管理模块**
- 支持多种终端设备类型(支付终端、公交刷卡终端等)
- 实时设备状态监控和告警
- 设备远程配置和控制
- 设备事件记录和日志分析

**文件管理模块**
- 文件版本控制和历史管理
- 支持预约发布和批量发布
- 文件分发状态跟踪
- 多种文件存储方式(本地/MinIO)

**消息通信模块**
- 支持多种消息类型
- 实时消息推送和接收
- 消息状态跟踪和历史查询
- 支持批量消息发送

### 1.4 技术特点

#### 1.4.1 架构特点
- **前后端分离**: React + .NET 8.0 现代化技术栈
- **微服务架构**: 模块化设计，支持独立部署和扩展
- **容器化部署**: 基于Docker的标准化部署方案
- **多数据库支持**: 支持MySQL和SQL Server数据库

#### 1.4.2 技术优势
- **高性能**: 异步编程、批量操作、多级缓存优化
- **高可用**: 负载均衡、故障转移、健康检查机制
- **安全可靠**: JWT认证、数据加密、权限控制、审计日志
- **易于维护**: 清晰的架构分层、完善的监控体系、自动化运维

#### 1.4.3 创新特性
- **智能权限系统**: 基于功能的细粒度权限控制
- **预约发布功能**: 支持定时自动发布文件到终端设备
- **实时数据处理**: 基于RabbitMQ的消息队列处理
- **多租户架构**: 完善的数据隔离和商户管理

### 1.5 系统边界

#### 1.5.1 系统范围
**包含功能**:
- Web管理界面和用户交互
- 终端设备管理和监控
- 文件分发和版本管理
- 消息通信和推送
- 用户权限和安全管理
- 业务数据管理和配置
- 系统监控和日志审计

**不包含功能**:
- 终端设备硬件制造
- 第三方支付接口对接
- 移动端APP开发
- 硬件设备驱动开发

#### 1.5.2 外部接口
- **TCP通信服务**: 与终端设备的底层通信协议
- **RabbitMQ消息队列**: 异步消息处理和事件驱动
- **MinIO文件存储**: 分布式文件存储服务
- **数据库服务**: MySQL/SQL Server数据持久化
- **微信API**: 微信扫码登录集成

#### 1.5.3 技术约束
- **运行环境**: Linux/Windows Server + Docker容器
- **数据库**: MySQL 8.0+ 或 SQL Server 2019+
- **浏览器**: Chrome 90+, Firefox 88+, Safari 14+
- **网络**: HTTPS协议，支持WebSocket长连接
- **性能**: 响应时间<2秒，并发用户数1000+

## 2. 系统架构设计

### 2.1 总体架构

WebAdmin采用**前后端分离**和**微服务架构**的设计模式，实现了高可用、高扩展、易维护的现代化系统架构。

#### 2.1.1 架构层次图

```
┌─────────────────────────────────────────────────────────────┐
│                        用户接入层                            │
├─────────────────────────────────────────────────────────────┤
│  Web浏览器 (Chrome/Firefox/Safari)  │  移动端浏览器          │
└─────────────────────────────────────────────────────────────┘
                              │
                         HTTPS/WSS
                              │
┌─────────────────────────────────────────────────────────────┐
│                        前端应用层                            │
├─────────────────────────────────────────────────────────────┤
│              React 18 + Material-UI 5 SPA                  │
│  ├── 用户界面组件    ├── 状态管理     ├── 路由管理           │
│  ├── API服务调用     ├── 权限控制     ├── 主题管理           │
└─────────────────────────────────────────────────────────────┘
                              │
                         RESTful API
                              │
┌─────────────────────────────────────────────────────────────┐
│                        网关代理层                            │
├─────────────────────────────────────────────────────────────┤
│              Nginx/IIS 反向代理 (Port: 80/443)              │
│  ├── SSL终止         ├── 负载均衡     ├── 静态资源           │
│  ├── 请求路由        ├── 压缩缓存     ├── 安全防护           │
└─────────────────────────────────────────────────────────────┘
                              │
                         HTTP/HTTPS
                              │
┌─────────────────────────────────────────────────────────────┐
│                        应用服务层                            │
├─────────────────────────────────────────────────────────────┤
│  WebAdmin Service        │  ApiService           │  TCP Service │
│  (Port: 80)             │  (Port: 8080)         │  (Port: 9999) │
│  ├── Web API            │  ├── 业务API          │  ├── 设备通信  │
│  ├── 用户认证            │  ├── 数据处理          │  ├── 协议解析  │
│  ├── 权限控制            │  ├── 文件管理          │  ├── 消息转发  │
│  └── 前端资源            │  └── 消息队列          │  └── 状态同步  │
└─────────────────────────────────────────────────────────────┘
                              │
                         内部通信
                              │
┌─────────────────────────────────────────────────────────────┐
│                        中间件服务层                          │
├─────────────────────────────────────────────────────────────┤
│  RabbitMQ消息队列    │  MinIO对象存储     │  Redis缓存        │
│  (Port: 5672)       │  (Port: 9000)      │  (Port: 6379)     │
│  ├── 异步消息        │  ├── 文件存储       │  ├── 会话缓存      │
│  ├── 事件驱动        │  ├── 版本管理       │  ├── 数据缓存      │
│  └── 任务队列        │  └── 分布式存储     │  └── 分布式锁      │
└─────────────────────────────────────────────────────────────┘
                              │
                         数据访问
                              │
┌─────────────────────────────────────────────────────────────┐
│                        数据持久层                            │
├─────────────────────────────────────────────────────────────┤
│              MySQL 8.0 / SQL Server 2019+                  │
│              主从集群 (Port: 3306/1433)                     │
│  ├── 业务数据库      ├── 读写分离       ├── 备份恢复         │
│  ├── 事务处理        ├── 索引优化       ├── 性能监控         │
└─────────────────────────────────────────────────────────────┘
```

#### 2.1.2 服务架构特点

**分层架构优势**:
- **职责分离**: 每层专注特定功能，降低耦合度
- **技术独立**: 各层可独立选择最适合的技术栈
- **扩展灵活**: 支持水平和垂直扩展
- **维护简单**: 清晰的边界便于问题定位和修复

**微服务设计**:
- **WebAdmin服务**: 专注Web界面和用户体验
- **ApiService服务**: 处理核心业务逻辑和数据操作
- **TCP服务**: 专门处理终端设备通信
- **独立部署**: 每个服务可独立开发、测试、部署

### 2.2 技术栈选型

#### 2.2.1 前端技术栈

**核心框架**:
```json
{
  "react": "^18.2.0",              // 现代化UI框架
  "react-dom": "^18.2.0",          // DOM渲染引擎
  "react-router-dom": "^6.22.1"    // 客户端路由
}
```

**UI组件库**:
```json
{
  "@mui/material": "^5.15.10",     // Material Design组件
  "@mui/icons-material": "^5.15.10", // 图标库
  "@mui/x-data-grid": "^6.19.4",   // 数据表格
  "@mui/x-date-pickers": "^6.20.2" // 日期选择器
}
```

**工具库**:
```json
{
  "axios": "^1.6.7",               // HTTP客户端
  "formik": "^2.4.5",              // 表单处理
  "yup": "^1.3.3",                 // 数据验证
  "notistack": "^3.0.1",           // 通知组件
  "jwt-decode": "^4.0.0",          // JWT解析
  "date-fns": "^2.29.3"            // 日期处理
}
```

**构建工具**:
```json
{
  "vite": "^5.1.0",                // 现代化构建工具
  "@vitejs/plugin-react": "^4.2.1" // React插件
}
```

#### 2.2.2 后端技术栈

**核心框架**:
- **.NET 8.0**: 最新的.NET平台，提供高性能和现代化开发体验
- **ASP.NET Core**: Web API框架，支持跨平台部署
- **Entity Framework Core**: ORM框架，支持多数据库

**认证授权**:
- **ASP.NET Core Identity**: 用户身份管理
- **JWT Bearer**: 无状态认证令牌
- **双因素认证**: OTP.NET库实现

**数据存储**:
- **MySQL 8.0**: 主要业务数据库
- **SQL Server 2019+**: 可选数据库支持
- **MinIO**: 分布式对象存储
- **Redis**: 缓存和会话存储

**消息队列**:
- **RabbitMQ**: 异步消息处理和事件驱动

#### 2.2.3 基础设施技术栈

**容器化**:
- **Docker**: 应用容器化
- **Docker Compose**: 多容器编排

**Web服务器**:
- **Nginx**: 反向代理和负载均衡
- **IIS**: Windows环境Web服务器

**监控日志**:
- **Serilog**: 结构化日志记录
- **健康检查**: ASP.NET Core内置健康检查

### 2.3 模块划分

#### 2.3.1 解决方案结构

```
SlzrCrossGate 解决方案
├── SlzrCrossGate.Common          # 公共工具模块
│   ├── CRC.cs                    # CRC校验算法
│   ├── DataConvert.cs            # 数据转换工具
│   └── Encrypts.cs               # 加密解密工具
├── SlzrCrossGate.Core            # 核心业务模块
│   ├── Models/                   # 数据模型
│   ├── Services/                 # 业务服务
│   ├── Database/                 # 数据访问
│   └── Extensions/               # 扩展方法
├── SlzrCrossGate.WebAdmin        # Web管理模块
│   ├── Controllers/              # Web API控制器
│   ├── Services/                 # Web业务服务
│   ├── DTOs/                     # 数据传输对象
│   ├── Middleware/               # 中间件
│   ├── ClientApp/                # React前端应用
│   └── Program.cs                # 服务入口
├── SlzrCrossGate.ApiService      # API服务模块
│   ├── Controllers/              # API控制器
│   ├── Services/                 # API服务
│   └── Program.cs                # API服务入口
├── SlzrCrossGate.Tcp             # TCP通信模块
│   ├── Protocols/                # 通信协议
│   ├── Handlers/                 # 消息处理器
│   └── Services/                 # TCP服务
└── SlzrCrossGate.ServiceDefaults # 服务默认配置
    ├── Extensions.cs             # 服务扩展
    └── HealthChecks/             # 健康检查
```

#### 2.3.2 前端模块结构

```
ClientApp/
├── public/                       # 静态资源
│   ├── config/                   # 配置文件
│   └── assets/                   # 静态资源
├── src/
│   ├── components/               # 通用组件
│   │   ├── common/               # 基础组件
│   │   ├── forms/                # 表单组件
│   │   └── layout/               # 布局组件
│   ├── pages/                    # 页面组件
│   │   ├── auth/                 # 认证页面
│   │   ├── dashboard/            # 仪表盘
│   │   ├── terminals/            # 终端管理
│   │   ├── files/                # 文件管理
│   │   └── settings/             # 系统设置
│   ├── services/                 # API服务
│   │   ├── api.js                # API客户端
│   │   └── auth.js               # 认证服务
│   ├── contexts/                 # React上下文
│   │   ├── AuthContext.jsx       # 认证上下文
│   │   ├── ThemeContext.jsx      # 主题上下文
│   │   └── MerchantContext.jsx   # 商户上下文
│   ├── hooks/                    # 自定义Hook
│   ├── utils/                    # 工具函数
│   ├── constants/                # 常量定义
│   └── routes.jsx                # 路由配置
├── package.json                  # 依赖配置
└── vite.config.js                # 构建配置
```

### 2.4 部署架构

#### 2.4.1 容器化部署架构

```
Docker容器部署架构
├── 前端容器 (webadmin-frontend)
│   ├── Nginx + React静态文件
│   ├── 端口: 80/443
│   └── 配置: nginx.conf
├── 后端容器 (webadmin-backend)
│   ├── .NET 8.0 Runtime
│   ├── 端口: 80 (内部)
│   └── 环境变量配置
├── 数据库容器 (mysql/sqlserver)
│   ├── MySQL 8.0 / SQL Server 2019
│   ├── 端口: 3306/1433
│   └── 数据卷挂载
├── 消息队列容器 (rabbitmq)
│   ├── RabbitMQ 3.12
│   ├── 端口: 5672, 15672
│   └── 管理界面
├── 对象存储容器 (minio)
│   ├── MinIO Latest
│   ├── 端口: 9000, 9001
│   └── 数据卷挂载
└── 缓存容器 (redis)
    ├── Redis 7.0
    ├── 端口: 6379
    └── 持久化配置
```

#### 2.4.2 多阶段构建流程

**前端构建阶段**:
```dockerfile
# 阶段1: Node.js构建环境
FROM node:18-alpine AS frontend-build
WORKDIR /app
COPY package*.json ./
RUN npm ci
COPY . .
RUN npm run build
```

**后端构建阶段**:
```dockerfile
# 阶段2: .NET SDK构建环境
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src
COPY *.csproj ./
RUN dotnet restore
COPY . .
RUN dotnet publish -c Release -o /app/publish
```

**运行时阶段**:
```dockerfile
# 阶段3: 运行时环境
FROM mcr.microsoft.com/dotnet/aspnet:8.0
WORKDIR /app
COPY --from=build /app/publish .
COPY --from=frontend-build /app/dist ./wwwroot
ENTRYPOINT ["dotnet", "SlzrCrossGate.WebAdmin.dll"]
```

### 2.5 数据流设计

#### 2.5.1 用户操作数据流

```
用户操作数据流:
用户浏览器 → Nginx反向代理 → WebAdmin服务 → 业务处理 → 数据库
     ↓              ↓              ↓           ↓         ↓
  React SPA → 静态资源服务 → Web API → 服务层 → EF Core
     ↓              ↓              ↓           ↓         ↓
  状态管理 → 缓存策略 → 权限验证 → 业务逻辑 → 数据持久化
```

#### 2.5.2 设备通信数据流

```
设备通信数据流:
终端设备 → TCP服务 → 消息队列 → WebAdmin服务 → 数据库
    ↓        ↓        ↓          ↓            ↓
  TCP协议 → 协议解析 → RabbitMQ → 事件处理 → 状态更新
    ↓        ↓        ↓          ↓            ↓
  二进制数据 → 结构化数据 → 异步消息 → 业务逻辑 → 实时同步
```

#### 2.5.3 文件分发数据流

```
文件分发数据流:
管理员上传 → WebAdmin → MinIO存储 → 版本管理 → 发布队列
     ↓         ↓         ↓          ↓         ↓
  文件选择 → API处理 → 对象存储 → 数据库记录 → 消息队列
     ↓         ↓         ↓          ↓         ↓
  前端界面 → 后端服务 → 文件服务 → 版本控制 → 异步分发
                                      ↓
                              终端设备下载
```

#### 2.5.4 实时监控数据流

```
实时监控数据流:
终端状态变化 → TCP服务 → 消息队列 → 状态处理服务 → 数据库更新
      ↓          ↓        ↓          ↓            ↓
   设备事件 → 事件解析 → RabbitMQ → 业务处理 → 状态同步
      ↓          ↓        ↓          ↓            ↓
   实时数据 → 结构化事件 → 异步处理 → 状态计算 → 前端推送
                                      ↓
                              WebSocket/SSE → 前端实时更新
```

#### 2.5.5 权限验证数据流

```
权限验证数据流:
用户请求 → JWT验证 → 权限检查 → 功能权限 → 数据权限
    ↓        ↓        ↓         ↓         ↓
  HTTP请求 → Token解析 → 角色验证 → 按钮权限 → 商户隔离
    ↓        ↓        ↓         ↓         ↓
  API调用 → 身份确认 → 权限服务 → 功能控制 → 数据过滤
```

## 3. 数据库设计

### 3.1 数据库架构

#### 3.1.1 多数据库支持架构

WebAdmin系统采用**多数据库支持架构**，通过Entity Framework Core实现对MySQL和SQL Server的完整支持。

```
数据库支持架构
├── 数据库抽象层
│   ├── TcpDbContext (EF Core DbContext)
│   ├── 实体模型 (Entity Models)
│   └── 配置映射 (Entity Configurations)
├── 数据库适配层
│   ├── MySQL适配器 (MySQL Provider)
│   ├── SQL Server适配器 (SqlServer Provider)
│   └── 动态配置选择 (Dynamic Configuration)
├── 迁移管理层
│   ├── MySQL迁移程序集 (SlzrCrossGate.Migrations.MySql)
│   ├── SQL Server迁移程序集 (SlzrCrossGate.Migrations.SqlServer)
│   └── 自动迁移服务 (Migration Service)
└── 连接管理层
    ├── 连接字符串管理
    ├── 连接池配置
    └── 健康检查集成
```

#### 3.1.2 数据库配置策略

**动态数据库选择**:
```csharp
protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
{
    var databaseProvider = _configuration.GetValue<string>("DatabaseProvider") ?? "MySql";
    var connectionString = _configuration.GetConnectionString("DefaultConnection");

    if (databaseProvider == "SqlServer")
    {
        optionsBuilder.UseSqlServer(connectionString, options =>
            options.MigrationsAssembly("SlzrCrossGate.Migrations.SqlServer"));
    }
    else // MySQL
    {
        var serverVersion = ServerVersion.AutoDetect(connectionString);
        optionsBuilder.UseMySql(connectionString, serverVersion, options =>
            options.MigrationsAssembly("SlzrCrossGate.Migrations.MySql"));
    }
}
```

**配置文件示例**:
```json
// MySQL配置
{
  "DatabaseProvider": "MySql",
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;Database=TcpserverTms;User=root;Password=******;Port=3306;CharSet=utf8mb4;"
  }
}

// SQL Server配置
{
  "DatabaseProvider": "SqlServer",
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost,1433;Database=slzrcrossgate;User Id=sa;Password=YourPassword123!;TrustServerCertificate=true;"
  }
}
```

### 3.2 核心表结构

#### 3.2.1 表分类架构

```
数据库表结构分类
├── 1. 身份认证表 (ASP.NET Core Identity)
│   ├── AspNetUsers - 用户表
│   ├── AspNetRoles - 角色表
│   ├── AspNetUserRoles - 用户角色关联表
│   ├── AspNetUserClaims - 用户声明表
│   ├── AspNetRoleClaims - 角色声明表
│   ├── AspNetUserLogins - 用户登录表
│   └── AspNetUserTokens - 用户令牌表
├── 2. 业务核心表
│   ├── Merchants - 商户表
│   ├── Terminals - 终端表
│   ├── TerminalStatus - 终端状态表
│   ├── TerminalEvents - 终端事件表
│   ├── TerminalLogs - 终端日志表
│   ├── FileVers - 文件版本表
│   ├── FileTypes - 文件类型表
│   ├── FilePublishs - 文件发布表
│   ├── FilePublishHistories - 文件发布历史表
│   ├── ScheduledFilePublishs - 预约文件发布表
│   ├── MsgTypes - 消息类型表
│   ├── MsgContents - 消息内容表
│   ├── MsgBoxes - 消息盒子表
│   └── ConsumeData - 消费数据表
├── 3. 业务扩展表
│   ├── UnionPayTerminalKeys - 银联终端密钥表
│   ├── LinePriceInfos - 线路票价信息表
│   ├── LinePriceInfoVersions - 线路票价信息版本表
│   ├── FareDiscountSchemes - 票价折扣方案表
│   ├── FareDiscountSchemeVersions - 票价折扣方案版本表
│   ├── VehicleInfos - 车辆信息表
│   ├── MerchantDictionaries - 商户字典表
│   ├── UploadFiles - 上传文件表
│   ├── TerminalFileUploads - 终端文件上传表
│   └── IncrementContents - 增量内容表
├── 4. 系统管理表
│   ├── SystemSettings - 系统设置表
│   ├── MenuGroups - 菜单组表
│   ├── MenuItems - 菜单项表
│   ├── FeatureConfigs - 功能配置表
│   └── RoleFeaturePermissions - 角色功能权限表
└── 5. 审计日志表
    ├── LoginLogs - 登录日志表
    ├── PasswordChangeLogs - 密码变更日志表
    └── OperationLogs - 操作日志表
```

#### 3.2.2 核心实体模型

**商户表 (Merchants)**:
```csharp
public class Merchant : ITenantEntity
{
    [Key]
    [StringLength(8)]
    public required string MerchantID { get; set; }

    [StringLength(100)]
    public string? Name { get; set; }

    [StringLength(200)]
    public string? Address { get; set; }

    [StringLength(50)]
    public string? Contact { get; set; }

    [StringLength(20)]
    public string? Phone { get; set; }

    public bool IsActive { get; set; } = true;

    public DateTime CreatedAt { get; set; } = DateTime.Now;

    // 导航属性
    public virtual ICollection<Terminal> Terminals { get; set; }
    public virtual ICollection<FileType> FileTypes { get; set; }
}
```

**终端表 (Terminals)**:
```csharp
public class Terminal : ITenantEntity
{
    [Key]
    public int Id { get; set; }

    [StringLength(8)]
    public required string MerchantID { get; set; }

    [StringLength(8)]
    public string? MachineID { get; set; }  // 出厂序列号

    [StringLength(8)]
    public string? MachineNO { get; set; }  // 设备编号

    [StringLength(4)]
    public string? LineNO { get; set; }     // 线路号

    [StringLength(2)]
    public string? GroupNO { get; set; }    // 组号

    [StringLength(20)]
    public string? LicensePlate { get; set; } // 车牌号

    public DeviceActiveStatus ActiveStatus { get; set; }

    public DateTime? LastOnlineTime { get; set; }

    // 外键关系
    public virtual Merchant Merchant { get; set; }
    public virtual ICollection<TerminalEvent> Events { get; set; }
    public virtual ICollection<TerminalLog> Logs { get; set; }
}
```

**文件版本表 (FileVers)**:
```csharp
public class FileVer : ITenantEntity
{
    [Key]
    public int Id { get; set; }

    [StringLength(8)]
    public required string MerchantID { get; set; }

    [StringLength(3)]
    public required string FileTypeCode { get; set; }

    [StringLength(10)]
    public required string Version { get; set; }

    [StringLength(255)]
    public required string FileName { get; set; }

    [StringLength(255)]
    public required string FilePath { get; set; }

    public long FileSize { get; set; }

    [StringLength(32)]
    public string? MD5Hash { get; set; }

    [StringLength(500)]
    public string? Remarks { get; set; }

    public DateTime CreatedAt { get; set; } = DateTime.Now;

    [StringLength(50)]
    public string? CreatedBy { get; set; }

    // 外键关系
    public virtual Merchant Merchant { get; set; }
    public virtual FileType FileType { get; set; }
}
```

### 3.3 关系设计

#### 3.3.1 实体关系图 (ERD)

```
实体关系设计
┌─────────────┐    1:N    ┌─────────────┐    1:N    ┌─────────────┐
│   Merchants │ ────────→ │  Terminals  │ ────────→ │TerminalEvents│
│             │           │             │           │             │
│ MerchantID  │           │ MerchantID  │           │ TerminalId  │
│ Name        │           │ MachineID   │           │ EventType   │
│ Address     │           │ MachineNO   │           │ Timestamp   │
│ IsActive    │           │ LineNO      │           │ Description │
└─────────────┘           │ ActiveStatus│           └─────────────┘
       │                  └─────────────┘
       │ 1:N                     │ 1:N
       ↓                         ↓
┌─────────────┐           ┌─────────────┐
│  FileTypes  │           │TerminalLogs │
│             │           │             │
│ MerchantID  │           │ MerchantID  │
│ Code        │           │ MachineID   │
│ Name        │           │ LogType     │
│ Description │           │ LogTime     │
└─────────────┘           └─────────────┘
       │ 1:N
       ↓
┌─────────────┐    1:N    ┌─────────────┐
│  FileVers   │ ────────→ │FilePublishs │
│             │           │             │
│ MerchantID  │           │ FileVerId   │
│ FileTypeCode│           │ PublishType │
│ Version     │           │ Status      │
│ FilePath    │           │ CreatedAt   │
└─────────────┘           └─────────────┘
```

#### 3.3.2 多租户数据隔离

**ITenantEntity接口**:
```csharp
public interface ITenantEntity
{
    string MerchantID { get; set; }
}
```

**数据隔离策略**:
- 所有业务表都实现ITenantEntity接口
- 通过MerchantID字段实现数据隔离
- 查询时自动添加商户过滤条件
- 支持系统管理员跨商户访问

### 3.4 索引策略

#### 3.4.1 主要索引设计

**性能关键索引**:
```sql
-- 终端表索引
CREATE INDEX IX_Terminals_MerchantID ON Terminals(MerchantID);
CREATE INDEX IX_Terminals_MachineID ON Terminals(MachineID);
CREATE INDEX IX_Terminals_ActiveStatus ON Terminals(ActiveStatus);
CREATE INDEX IX_Terminals_LastOnlineTime ON Terminals(LastOnlineTime);

-- 终端事件表索引
CREATE INDEX IX_TerminalEvents_TerminalId ON TerminalEvents(TerminalId);
CREATE INDEX IX_TerminalEvents_Timestamp ON TerminalEvents(Timestamp);
CREATE INDEX IX_TerminalEvents_EventType ON TerminalEvents(EventType);

-- 文件版本表索引
CREATE INDEX IX_FileVers_MerchantID_FileTypeCode ON FileVers(MerchantID, FileTypeCode);
CREATE INDEX IX_FileVers_Version ON FileVers(Version);
CREATE INDEX IX_FileVers_CreatedAt ON FileVers(CreatedAt);

-- 消费数据表索引
CREATE INDEX IX_ConsumeData_MerchantID ON ConsumeData(MerchantID);
CREATE INDEX IX_ConsumeData_MachineID ON ConsumeData(MachineID);
CREATE INDEX IX_ConsumeData_ReceiveTime ON ConsumeData(ReceiveTime);

-- 操作日志表索引
CREATE INDEX IX_OperationLogs_UserId ON OperationLogs(UserId);
CREATE INDEX IX_OperationLogs_Timestamp ON OperationLogs(Timestamp);
CREATE INDEX IX_OperationLogs_Module ON OperationLogs(Module);
```

#### 3.4.2 复合索引优化

**查询优化索引**:
```sql
-- 终端管理页面查询优化
CREATE INDEX IX_Terminals_MerchantID_ActiveStatus_LineNO
ON Terminals(MerchantID, ActiveStatus, LineNO);

-- 文件发布历史查询优化
CREATE INDEX IX_FilePublishHistories_MerchantID_CreatedAt
ON FilePublishHistories(MerchantID, CreatedAt DESC);

-- 消息查询优化
CREATE INDEX IX_MsgContents_MerchantID_IsRead_CreatedAt
ON MsgContents(MerchantID, IsRead, CreatedAt DESC);

-- 终端日志查询优化
CREATE INDEX IX_TerminalLogs_MerchantID_LogType_LogTime
ON TerminalLogs(MerchantID, LogType, LogTime DESC);
```

### 3.5 数据迁移

#### 3.5.1 迁移架构设计

**独立迁移程序集**:
```
迁移程序集架构
├── SlzrCrossGate.Migrations.MySql/
│   ├── Migrations/
│   │   ├── 20240101000000_InitialCreate.cs
│   │   ├── 20240102000000_AddTerminalManagement.cs
│   │   └── 20240103000000_AddFeaturePermissions.cs
│   └── MySqlMigrationsAssembly.cs
└── SlzrCrossGate.Migrations.SqlServer/
    ├── Migrations/
    │   ├── 20240101000000_InitialCreate.cs
    │   ├── 20240102000000_AddTerminalManagement.cs
    │   └── 20240103000000_AddFeaturePermissions.cs
    └── SqlServerMigrationsAssembly.cs
```

#### 3.5.2 自动迁移服务

**迁移服务配置**:
```json
{
  "EnableMigration": true,
  "Migration": {
    "CommandTimeout": 600,
    "CreateBackup": true,
    "BackupPath": "./backups",
    "ValidateIndexes": true,
    "AutoRecovery": true,
    "LogDetailedErrors": true
  }
}
```

**迁移执行流程**:
```
迁移执行流程
├── 1. 启动时检查
│   ├── 检查EnableMigration配置
│   ├── 获取数据库连接
│   └── 验证数据库可访问性
├── 2. 迁移准备
│   ├── 创建数据库备份(可选)
│   ├── 获取待执行迁移列表
│   └── 验证迁移文件完整性
├── 3. 执行迁移
│   ├── 按顺序执行迁移
│   ├── 记录迁移日志
│   └── 处理迁移异常
├── 4. 索引验证
│   ├── 检查索引完整性
│   ├── 自动修复缺失索引
│   └── 生成修复脚本
└── 5. 完成确认
    ├── 验证数据库结构
    ├── 更新迁移历史
    └── 记录成功日志
```

#### 3.5.3 数据库兼容性

**跨数据库兼容性设计**:
- 使用EF Core抽象层屏蔽数据库差异
- 避免使用数据库特定的SQL语法
- 统一的数据类型映射策略
- 自动生成适配不同数据库的迁移文件

**数据类型映射**:
```csharp
// 统一的数据类型定义
[StringLength(8)]           // VARCHAR(8) / NVARCHAR(8)
public string MerchantID { get; set; }

[Column(TypeName = "datetime(6)")]  // DATETIME(6) / DATETIME2(6)
public DateTime CreatedAt { get; set; }

[Column(TypeName = "longtext")]     // LONGTEXT / NVARCHAR(MAX)
public string? Description { get; set; }
```

## 4. 前端设计

### 4.1 React架构设计

#### 4.1.1 整体架构模式

WebAdmin前端采用**现代化React架构**，基于函数式组件和Hooks的设计模式，实现高性能、可维护的单页应用。

```
React架构层次
├── 应用入口层 (main.jsx)
│   ├── React.StrictMode
│   ├── BrowserRouter
│   ├── ThemeProvider
│   ├── SnackbarProvider
│   └── AuthProvider
├── 应用核心层 (App.jsx)
│   ├── 路由配置 (useRoutes)
│   ├── 主题管理 (MuiThemeProvider)
│   ├── 本地化支持 (LocalizationProvider)
│   ├── 商户上下文 (MerchantProvider)
│   └── 版本管理 (VersionManager)
├── 布局层 (Layouts)
│   ├── DashboardLayout - 主应用布局
│   ├── AuthLayout - 认证页面布局
│   └── 响应式设计支持
├── 页面层 (Pages)
│   ├── 业务页面组件
│   ├── 路由级权限控制
│   └── 页面状态管理
├── 组件层 (Components)
│   ├── 通用组件 (Common)
│   ├── 业务组件 (Business)
│   ├── 表单组件 (Forms)
│   └── 布局组件 (Layout)
└── 基础设施层
    ├── 上下文管理 (Contexts)
    ├── 自定义Hook (Hooks)
    ├── 服务层 (Services)
    ├── 工具函数 (Utils)
    └── 常量定义 (Constants)
```

#### 4.1.2 技术栈配置

**核心依赖**:
```json
{
  "react": "^18.2.0",
  "react-dom": "^18.2.0",
  "react-router-dom": "^6.22.1",
  "@mui/material": "^5.15.10",
  "@mui/icons-material": "^5.15.10",
  "@mui/x-data-grid": "^6.19.4",
  "@mui/x-date-pickers": "^6.20.2"
}
```

**工具库集成**:
```json
{
  "axios": "^1.6.7",           // HTTP客户端
  "formik": "^2.4.5",          // 表单处理
  "yup": "^1.3.3",             // 数据验证
  "notistack": "^3.0.1",       // 通知系统
  "jwt-decode": "^4.0.0",      // JWT解析
  "date-fns": "^2.29.3",       // 日期处理
  "react-feather": "^2.0.10"   // 图标库
}
```

#### 4.1.3 应用初始化流程

```javascript
// main.jsx - 应用入口点
ReactDOM.createRoot(document.getElementById('root')).render(
  <React.StrictMode>
    <BrowserRouter>
      <ThemeProvider>
        <SnackbarProvider maxSnack={3}>
          <AuthProvider>
            <App />
          </AuthProvider>
        </SnackbarProvider>
      </ThemeProvider>
    </BrowserRouter>
  </React.StrictMode>
);
```

**初始化顺序**:
1. **React.StrictMode**: 开发模式下的严格检查
2. **BrowserRouter**: 客户端路由管理
3. **ThemeProvider**: 主题系统初始化
4. **SnackbarProvider**: 全局通知系统
5. **AuthProvider**: 用户认证状态管理
6. **App**: 主应用组件加载

### 4.2 组件设计规范

#### 4.2.1 组件分类架构

```
组件分类体系
├── 布局组件 (Layout Components)
│   ├── DashboardLayout - 主应用布局
│   ├── AuthLayout - 认证页面布局
│   ├── DashboardNavbar - 顶部导航栏
│   ├── DashboardSidebar - 侧边栏
│   └── 响应式容器组件
├── 通用组件 (Common Components)
│   ├── FeatureGuard - 功能权限保护
│   ├── RoleGuard - 角色权限保护
│   ├── TwoFactorGuard - 双因素认证保护
│   ├── VersionManager - 版本管理
│   ├── LoadingSpinner - 加载指示器
│   └── ErrorBoundary - 错误边界
├── 表单组件 (Form Components)
│   ├── MerchantAutocomplete - 商户选择器
│   ├── DateTimePicker - 日期时间选择器
│   ├── FileUpload - 文件上传组件
│   ├── FormDialog - 表单对话框
│   └── 验证组件集合
├── 数据展示组件 (Display Components)
│   ├── DataGrid - 数据表格
│   ├── StatCard - 统计卡片
│   ├── StatusChip - 状态标签
│   ├── ProgressBar - 进度条
│   └── 图表组件集合
└── 业务组件 (Business Components)
    ├── 终端管理相关组件
    ├── 文件管理相关组件
    ├── 消息管理相关组件
    ├── 用户管理相关组件
    └── 系统设置相关组件
```

#### 4.2.2 组件设计原则

**单一职责原则**:
```javascript
// ✅ 好的设计 - 单一职责
const StatusChip = ({ status, variant = "default" }) => {
  const getStatusConfig = (status) => {
    const configs = {
      active: { label: '在线', color: 'success' },
      inactive: { label: '离线', color: 'error' },
      unknown: { label: '未知', color: 'default' }
    };
    return configs[status] || configs.unknown;
  };

  const config = getStatusConfig(status);
  return <Chip label={config.label} color={config.color} variant={variant} />;
};
```

**可复用性设计**:
```javascript
// ✅ 高复用性的权限保护组件
const FeatureGuard = memo(({
  featureKey,
  children,
  fallback = null,
  additionalCheck = true
}) => {
  const { canPerform, loading } = useFeaturePermission();

  if (loading) return <Skeleton variant="rectangular" width={100} height={36} />;
  if (!canPerform(featureKey, additionalCheck)) return fallback;

  return children;
});
```

### 4.3 路由设计

#### 4.3.1 路由架构设计

```javascript
// routes.jsx - 路由配置架构
const routes = [
  {
    path: 'app',
    element: (
      <TwoFactorGuard>
        <DashboardLayout />
      </TwoFactorGuard>
    ),
    children: [
      // 仪表盘路由
      { path: 'dashboard', element: <DashboardView /> },

      // 用户管理路由 (角色权限保护)
      {
        path: 'users',
        element: (
          <RoleGuard roles={['SystemAdmin', 'MerchantAdmin']}>
            <UserListView />
          </RoleGuard>
        )
      },

      // 系统管理路由 (系统管理员专用)
      {
        path: 'settings',
        element: (
          <RoleGuard roles={['SystemAdmin']}>
            <SystemManagement />
          </RoleGuard>
        )
      },

      // 外部系统路由 (动态路由)
      {
        path: 'external/:systemKey',
        element: (
          <TwoFactorGuard>
            <ExternalSystemPage />
          </TwoFactorGuard>
        )
      }
    ]
  },
  {
    path: '/',
    element: <AuthLayout />,
    children: [
      { path: 'login', element: <Login /> },
      { path: 'verify-code', element: <VerifyCode /> },
      { path: 'two-factor-setup', element: <TwoFactorSetupView /> },
      { path: 'wechat-login', element: <WechatLoginView /> }
    ]
  }
];
```

#### 4.3.2 权限路由保护

**多层权限保护机制**:
```javascript
// 1. 认证保护 - TwoFactorGuard
const TwoFactorGuard = ({ children }) => {
  const { isAuthenticated, needTwoFactor } = useAuth();

  if (!isAuthenticated) return <Navigate to="/login" />;
  if (needTwoFactor) return <Navigate to="/two-factor-verify" />;

  return children;
};

// 2. 角色保护 - RoleGuard
const RoleGuard = ({ roles, children }) => {
  const { user } = useAuth();

  const hasRequiredRole = roles.some(role =>
    user?.roles?.includes(role)
  );

  if (!hasRequiredRole) {
    return <Navigate to="/app/dashboard" />;
  }

  return children;
};

// 3. 功能保护 - FeatureGuard (组件级别)
const FeatureGuard = ({ featureKey, children, fallback = null }) => {
  const { canPerform } = useFeaturePermission();

  if (!canPerform(featureKey)) return fallback;
  return children;
};
```

### 4.4 状态管理

#### 4.4.1 Context-based状态管理架构

WebAdmin采用**React Context + useReducer**的状态管理模式，实现轻量级、高性能的状态管理。

```
状态管理架构
├── 全局状态上下文
│   ├── AuthContext - 用户认证状态
│   ├── ThemeContext - 主题配置状态
│   ├── MerchantContext - 商户选择状态
│   └── NotificationContext - 通知状态
├── 页面级状态
│   ├── useState - 组件本地状态
│   ├── useReducer - 复杂状态逻辑
│   └── 自定义Hook - 状态逻辑复用
├── 服务器状态
│   ├── API调用状态
│   ├── 缓存管理
│   └── 错误处理
└── 持久化状态
    ├── localStorage - 用户偏好
    ├── sessionStorage - 会话数据
    └── IndexedDB - 大量数据缓存
```

#### 4.4.2 认证状态管理 (AuthContext)

```javascript
// AuthContext.jsx - 认证状态管理
const initialState = {
  isAuthenticated: false,
  isInitialized: false,
  user: null,
  token: null,
  needTwoFactor: false,
  isTwoFactorEnabled: false,
  tempToken: null,
  requirePasswordChange: false
};

const authReducer = (state, action) => {
  switch (action.type) {
    case 'INITIALIZE':
      return {
        ...state,
        isAuthenticated: action.payload.isAuthenticated,
        isInitialized: true,
        user: action.payload.user,
        token: action.payload.token
      };

    case 'LOGIN_SUCCESS':
      return {
        ...state,
        isAuthenticated: true,
        user: action.payload.user,
        token: action.payload.token,
        needTwoFactor: false,
        tempToken: null
      };

    case 'REQUIRE_TWO_FACTOR':
      return {
        ...state,
        needTwoFactor: true,
        tempToken: action.payload.tempToken,
        user: action.payload.user
      };

    case 'LOGOUT':
      return {
        ...initialState,
        isInitialized: true
      };

    default:
      return state;
  }
};

export const AuthProvider = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // 认证相关方法
  const login = async (username, password) => { /* 登录逻辑 */ };
  const logout = () => { /* 登出逻辑 */ };
  const verifyTwoFactor = async (code) => { /* 双因素验证 */ };

  return (
    <AuthContext.Provider value={{ ...state, login, logout, verifyTwoFactor }}>
      {children}
    </AuthContext.Provider>
  );
};
```

#### 4.4.3 主题状态管理 (ThemeContext)

```javascript
// ThemeContext.jsx - 主题状态管理
export const ThemeProvider = ({ children }) => {
  const [mode, setMode] = useState(() => {
    const savedMode = localStorage.getItem('themeMode');
    if (savedMode) return savedMode;

    // 检测系统首选项
    if (window.matchMedia?.('(prefers-color-scheme: dark)').matches) {
      return 'dark';
    }
    return 'light';
  });

  const [themeConfig, setThemeConfig] = useState({
    mode: mode,
    primaryColor: '#7E22CE',
    backgroundColor: null,
    paperColor: null,
  });

  // 使用 useMemo 缓存主题对象
  const theme = useMemo(() => createAppTheme(themeConfig), [themeConfig]);

  const toggleTheme = () => {
    setMode((prevMode) => {
      const newMode = prevMode === 'light' ? 'dark' : 'light';
      localStorage.setItem('themeMode', newMode);
      setThemeConfig(prev => ({ ...prev, mode: newMode }));
      return newMode;
    });
  };

  // 支持微前端架构的主题同步
  useEffect(() => {
    const handleMessage = (event) => {
      if (event.data.type === 'THEME_UPDATE' && event.data.theme) {
        updateThemeConfig(event.data.theme);
      }
    };

    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, []);

  return (
    <ThemeContext.Provider value={{ theme, mode, toggleTheme, updateThemeConfig }}>
      {children}
    </ThemeContext.Provider>
  );
};
```

### 4.5 UI设计规范

#### 4.5.1 设计系统规范

**Glass Morphism + Micro-3D 设计风格**:
```javascript
// theme.js - 主题配置
const createAppTheme = (config) => {
  const { mode, primaryColor } = config;

  return createTheme({
    palette: {
      mode,
      primary: {
        main: primaryColor || '#7E22CE',
        light: alpha(primaryColor || '#7E22CE', 0.1),
        dark: darken(primaryColor || '#7E22CE', 0.2)
      },
      background: {
        default: mode === 'dark' ? '#0a0a0a' : '#f5f5f5',
        paper: mode === 'dark'
          ? 'rgba(255, 255, 255, 0.05)'
          : 'rgba(255, 255, 255, 0.8)'
      }
    },
    components: {
      MuiPaper: {
        styleOverrides: {
          root: {
            backdropFilter: 'blur(10px)',
            border: `1px solid ${alpha('#ffffff', 0.1)}`,
            boxShadow: mode === 'dark'
              ? '0 8px 32px rgba(0, 0, 0, 0.3)'
              : '0 8px 32px rgba(0, 0, 0, 0.1)'
          }
        }
      },
      MuiButton: {
        styleOverrides: {
          root: {
            borderRadius: 8,
            textTransform: 'none',
            fontWeight: 500,
            transition: 'all 0.2s ease-in-out',
            '&:hover': {
              transform: 'translateY(-1px)',
              boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'
            }
          }
        }
      }
    }
  });
};
```

#### 4.5.2 响应式设计规范

**断点系统**:
```javascript
const breakpoints = {
  xs: 0,      // 手机端
  sm: 600,    // 平板端
  md: 900,    // 小屏桌面
  lg: 1200,   // 大屏桌面
  xl: 1536    // 超大屏
};

// 响应式布局示例
const DashboardLayout = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  return (
    <Box sx={{ display: 'flex' }}>
      <DashboardSidebar
        variant={isMobile ? 'temporary' : 'permanent'}
        isCollapsed={!isMobile && isSidebarCollapsed}
      />
      <Box
        sx={{
          flexGrow: 1,
          p: { xs: 1, sm: 2, md: 3 },
          ml: { md: isSidebarCollapsed ? 8 : 28 }
        }}
      >
        <Outlet />
      </Box>
    </Box>
  );
};
```

#### 4.5.3 组件样式规范

**统一的样式系统**:
```javascript
// 状态标签样式
const StatusChip = ({ status }) => {
  const getStatusStyle = (status) => {
    const styles = {
      online: {
        backgroundColor: alpha('#4caf50', 0.1),
        color: '#4caf50',
        border: '1px solid rgba(76, 175, 80, 0.3)'
      },
      offline: {
        backgroundColor: alpha('#f44336', 0.1),
        color: '#f44336',
        border: '1px solid rgba(244, 67, 54, 0.3)'
      },
      unknown: {
        backgroundColor: alpha('#9e9e9e', 0.1),
        color: '#9e9e9e',
        border: '1px solid rgba(158, 158, 158, 0.3)'
      }
    };
    return styles[status] || styles.unknown;
  };

  return (
    <Chip
      label={getStatusLabel(status)}
      sx={{
        ...getStatusStyle(status),
        borderRadius: 1,
        fontWeight: 500,
        fontSize: '0.75rem'
      }}
    />
  );
};

// 数据表格样式
const StyledDataGrid = styled(DataGrid)(({ theme }) => ({
  border: 'none',
  '& .MuiDataGrid-main': {
    borderRadius: theme.spacing(1),
    overflow: 'hidden'
  },
  '& .MuiDataGrid-cell': {
    borderBottom: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
    '&:focus': {
      outline: 'none'
    }
  },
  '& .MuiDataGrid-row': {
    '&:hover': {
      backgroundColor: alpha(theme.palette.primary.main, 0.04)
    }
  },
  '& .MuiDataGrid-columnHeaders': {
    backgroundColor: alpha(theme.palette.background.paper, 0.8),
    borderBottom: `2px solid ${theme.palette.divider}`
  }
}));
```

#### 4.5.4 动画和交互规范

**统一的动画系统**:
```javascript
// 页面切换动画
const PageTransition = ({ children }) => {
  return (
    <Fade in timeout={300}>
      <Box
        sx={{
          animation: 'slideInUp 0.3s ease-out',
          '@keyframes slideInUp': {
            from: {
              transform: 'translateY(20px)',
              opacity: 0
            },
            to: {
              transform: 'translateY(0)',
              opacity: 1
            }
          }
        }}
      >
        {children}
      </Box>
    </Fade>
  );
};

// 加载状态动画
const LoadingButton = ({ loading, children, ...props }) => {
  return (
    <Button
      {...props}
      disabled={loading}
      startIcon={loading ? <CircularProgress size={20} /> : props.startIcon}
      sx={{
        transition: 'all 0.2s ease-in-out',
        '&:hover:not(:disabled)': {
          transform: 'translateY(-1px)',
          boxShadow: 3
        }
      }}
    >
      {children}
    </Button>
  );
};
```

## 5. 后端设计

### 5.1 .NET架构设计

#### 5.1.1 整体架构模式

WebAdmin后端采用**分层架构**和**依赖注入**的设计模式，基于.NET 8.0和ASP.NET Core Identity构建现代化的Web API服务。

```
.NET后端架构层次
├── 表现层 (Presentation Layer)
│   ├── Controllers - API控制器
│   ├── DTOs - 数据传输对象
│   ├── Filters - 过滤器
│   └── Middleware - 中间件
├── 业务逻辑层 (Business Logic Layer)
│   ├── Services - 业务服务
│   ├── Validators - 数据验证
│   ├── Managers - 业务管理器
│   └── Handlers - 事件处理器
├── 数据访问层 (Data Access Layer)
│   ├── DbContext - 数据库上下文
│   ├── Repositories - 仓储模式
│   ├── Entities - 实体模型
│   └── Configurations - 实体配置
├── 基础设施层 (Infrastructure Layer)
│   ├── Authentication - 身份认证
│   ├── Authorization - 权限授权
│   ├── Logging - 日志记录
│   ├── Caching - 缓存管理
│   └── External Services - 外部服务
└── 跨切面关注点 (Cross-Cutting Concerns)
    ├── Exception Handling - 异常处理
    ├── Audit Logging - 审计日志
    ├── Performance Monitoring - 性能监控
    └── Health Checks - 健康检查
```

#### 5.1.2 Program.cs核心配置

**服务注册和配置**:
```csharp
// Program.cs - 实际配置
var builder = WebApplication.CreateBuilder(args);

// 注册中文编码支持（GB2312、GBK等）
Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);

// 设置应用程序时区为中国标准时间
TimeZoneInfo.ClearCachedData();
var chinaTimeZone = TimeZoneInfo.FindSystemTimeZoneById("China Standard Time");

// 配置日志输出时间
builder.Logging.AddSimpleConsole(options =>
{
    options.TimestampFormat = "[MM-dd HH:mm:ss] ";
    options.SingleLine = true;
});

// 注册过滤器服务
builder.Services.AddScoped<ActionLoggingFilter>();

// 配置控制器和过滤器
builder.Services.AddControllers(options =>
{
    // 添加全局操作日志过滤器
    options.Filters.Add<ActionLoggingFilter>();
})
.AddJsonOptions(options =>
{
    // 使用camelCase命名策略
    options.JsonSerializerOptions.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
    // 枚举使用数字格式，不使用JsonStringEnumConverter
});
```

**身份认证配置**:
```csharp
// ASP.NET Core Identity配置
builder.Services.AddIdentity<ApplicationUser, ApplicationRole>(options =>
{
    // 密码策略
    options.Password.RequireDigit = true;
    options.Password.RequireLowercase = true;
    options.Password.RequireNonAlphanumeric = false;
    options.Password.RequireUppercase = false;
    options.Password.RequiredLength = 6;
    options.Password.RequiredUniqueChars = 1;

    // 锁定策略
    options.Lockout.DefaultLockoutTimeSpan = TimeSpan.FromMinutes(5);
    options.Lockout.MaxFailedAccessAttempts = 5;
    options.Lockout.AllowedForNewUsers = true;

    // 用户策略
    options.User.AllowedUserNameCharacters = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0******789-._@+";
    options.User.RequireUniqueEmail = false;
})
.AddEntityFrameworkStores<TcpDbContext>()
.AddDefaultTokenProviders();

// JWT认证配置
builder.Services.AddAuthentication(options =>
{
    options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
})
.AddJwtBearer(options =>
{
    options.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuer = true,
        ValidateAudience = true,
        ValidateLifetime = true,
        ValidateIssuerSigningKey = true,
        ValidIssuer = builder.Configuration["Jwt:Issuer"],
        ValidAudience = builder.Configuration["Jwt:Audience"],
        IssuerSigningKey = new SymmetricSecurityKey(
            Encoding.UTF8.GetBytes(builder.Configuration["Jwt:Key"]!))
    };
});
```

**业务服务注册**:
```csharp
// 核心业务服务
builder.Services.AddScoped<TwoFactorAuthService>();
builder.Services.AddScoped<WechatAuthService>();
builder.Services.AddScoped<SystemSettingsService>();
builder.Services.AddScoped<UserService>();
builder.Services.AddScoped<AuditLogService>();
builder.Services.AddScoped<MenuService>();
builder.Services.AddSingleton<SessionActivityService>();

// 功能权限服务
builder.Services.AddScoped<IFeaturePermissionService, FeaturePermissionService>();

// 权限初始化服务
builder.Services.AddHostedService<PermissionInitializationService>();

// 后台服务
builder.Services.AddHostedService<SyncDataService>();
builder.Services.AddHostedService<ScheduledPublishService>();
builder.Services.AddHostedService<TerminalLogProcessingHostedService>();

// 终端日志服务
builder.Services.AddScoped<TerminalLogParserService>();
builder.Services.AddScoped<TerminalLogPublishService>();
```

### 5.2 API设计规范

#### 5.2.1 控制器设计模式

**标准控制器结构**:
```csharp
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class TerminalsController : ControllerBase
{
    private readonly TcpDbContext _dbContext;
    private readonly ILogger<TerminalsController> _logger;
    private readonly UserService _userService;

    public TerminalsController(
        TcpDbContext dbContext,
        ILogger<TerminalsController> logger,
        UserService userService)
    {
        _dbContext = dbContext;
        _logger = logger;
        _userService = userService;
    }

    // GET: api/Terminals
    [HttpGet]
    public async Task<ActionResult<PaginatedResult<TerminalDto>>> GetTerminals(
        [FromQuery] string? merchantId = null,
        [FromQuery] string? lineNo = null,
        [FromQuery] string? deviceNo = null,
        [FromQuery] string? machineId = null,
        [FromQuery] DeviceActiveStatus? activeStatus = null,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 10)
    {
        // 获取当前用户的商户ID
        var currentUserMerchantId = await _userService.GetUserMerchantIdAsync(User);
        var isSystemAdmin = User.IsInRole("SystemAdmin");

        // 权限控制：非系统管理员只能查看自己商户的数据
        if (!isSystemAdmin)
        {
            merchantId = currentUserMerchantId;
        }

        // 业务逻辑实现...
    }
}
```

#### 5.2.2 权限控制模式

**角色级权限控制**:
```csharp
// 1. 控制器级别权限
[Authorize(Roles = "SystemAdmin,MerchantAdmin")]
public class UsersController : ControllerBase { }

// 2. 方法级别权限
[HttpGet("PlatformStats")]
[Authorize(Roles = "SystemAdmin")]
public async Task<ActionResult<PlatformDashboardDto>> GetPlatformStats() { }

// 3. 动态权限检查
public async Task<ActionResult> SomeAction()
{
    var currentUser = await _userManager.GetUserAsync(User);
    var isSystemAdmin = await _userManager.IsInRoleAsync(currentUser, "SystemAdmin");

    if (!isSystemAdmin && currentUser.MerchantID != requestedMerchantId)
    {
        return Forbid();
    }

    // 业务逻辑...
}
```

**功能级权限控制**:
```csharp
// FeaturePermissionService - 实际实现
public async Task<bool> HasPermissionAsync(ClaimsPrincipal user, string featureKey)
{
    // 1. 获取用户角色
    var roles = user.Claims
        .Where(c => c.Type == ClaimTypes.Role)
        .Select(c => c.Value)
        .ToList();

    if (!roles.Any())
    {
        _logger.LogWarning("用户 {UserName} 没有任何角色", user.Identity?.Name);
        return false;
    }

    // 2. 获取功能配置
    var featureConfig = await GetFeatureConfigAsync(featureKey);
    if (featureConfig == null)
    {
        _logger.LogWarning("Feature config not found: {FeatureKey}", featureKey);
        return false;
    }

    // 3. 检查全局开关
    if (!featureConfig.IsGloballyEnabled)
    {
        return false;
    }

    // 4. 检查角色权限覆盖
    var rolePermissions = await _context.RoleFeaturePermissions
        .Where(rp => rp.FeatureKey == featureKey && roles.Contains(rp.RoleName))
        .ToListAsync();

    // 权限逻辑处理...
}
```

#### 5.2.3 响应格式规范

**统一响应格式**:
```csharp
// 成功响应
return Ok(new
{
    merchantID = merchant.MerchantID,
    name = merchant.Name,
    companyName = merchant.CompanyName,
    contactName = merchant.ContactPerson,
    contactPhone = merchant.ContactInfo,
    isActive = !merchant.IsDelete
});

// 错误响应
return BadRequest(new { message = "商户ID已存在" });

// 分页响应
return Ok(new PaginatedResult<TerminalDto>
{
    Items = terminalDtos,
    TotalCount = totalCount,
    Page = page,
    PageSize = pageSize,
    TotalPages = (int)Math.Ceiling((double)totalCount / pageSize)
});
```

### 5.3 服务层设计

#### 5.3.1 业务服务架构

**服务分类体系**:
```
业务服务分类
├── 核心认证服务
│   ├── TwoFactorAuthService - 双因素认证
│   ├── WechatAuthService - 微信认证
│   ├── UserService - 用户管理
│   └── AuditLogService - 审计日志
├── 业务管理服务
│   ├── SystemSettingsService - 系统设置
│   ├── MenuService - 菜单管理
│   ├── FeaturePermissionService - 功能权限
│   └── SessionActivityService - 会话管理
├── 数据处理服务
│   ├── TerminalLogParserService - 终端日志解析
│   ├── TerminalLogPublishService - 终端日志发布
│   └── SyncDataService - 数据同步
├── 后台服务 (Hosted Services)
│   ├── PermissionInitializationService - 权限初始化
│   ├── ScheduledPublishService - 预约发布
│   └── TerminalLogProcessingHostedService - 日志处理
└── 外部集成服务
    ├── RabbitMQ服务 - 消息队列
    ├── MinIO服务 - 文件存储
    └── HttpClient服务 - 外部API调用
```

#### 5.3.2 服务实现模式

**依赖注入配置**:
```csharp
// 服务生命周期管理
builder.Services.AddScoped<TwoFactorAuthService>();        // 请求范围
builder.Services.AddScoped<WechatAuthService>();           // 请求范围
builder.Services.AddScoped<SystemSettingsService>();      // 请求范围
builder.Services.AddScoped<UserService>();                // 请求范围
builder.Services.AddScoped<AuditLogService>();            // 请求范围
builder.Services.AddScoped<MenuService>();                // 请求范围
builder.Services.AddSingleton<SessionActivityService>();  // 单例模式

// 接口服务注册
builder.Services.AddScoped<IFeaturePermissionService, FeaturePermissionService>();

// 后台服务注册
builder.Services.AddHostedService<PermissionInitializationService>();
builder.Services.AddHostedService<SyncDataService>();
builder.Services.AddHostedService<ScheduledPublishService>();
```

**HttpClient配置**:
```csharp
// 外部API客户端配置
builder.Services.AddHttpClient("WechatApi", client =>
{
    client.BaseAddress = new Uri("https://api.weixin.qq.com/");
    client.DefaultRequestHeaders.Add("Accept", "application/json");
});
```

### 5.4 中间件设计

#### 5.4.1 中间件管道架构

**中间件执行顺序**:
```csharp
// app.Configure - 实际中间件管道
var app = builder.Build();

// 1. 全局异常处理中间件（必须在其他中间件之前）
app.UseMiddleware<GlobalExceptionHandlingMiddleware>();

// 2. 静态文件服务
app.UseDefaultFiles();
app.UseStaticFiles();

// 3. 路由中间件
app.UseRouting();

// 4. Session中间件
app.UseSession();

// 5. CORS中间件
if (app.Environment.IsDevelopment())
{
    app.UseCors(builder =>
    {
        builder.AllowAnyOrigin()
               .AllowAnyHeader()
               .AllowAnyMethod();
    });
}
else
{
    app.UseCors(builder =>
    {
        builder.WithOrigins("http://localhost:3000","http://localhost:5270")
               .AllowAnyHeader()
               .AllowAnyMethod()
               .AllowCredentials();
    });
}

// 6. 转发头部处理
app.UseForwardedHeaders(new ForwardedHeadersOptions
{
    ForwardedHeaders = ForwardedHeaders.XForwardedFor | ForwardedHeaders.XForwardedProto
});

// 7. 身份认证和授权
app.UseAuthentication();
app.UseAuthorization();

// 8. 审计日志中间件（必须在认证和授权之后）
app.UseAuditLog();

// 9. 控制器映射
app.MapControllers();
```

#### 5.4.2 全局异常处理中间件

**GlobalExceptionHandlingMiddleware实现**:
```csharp
public class GlobalExceptionHandlingMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<GlobalExceptionHandlingMiddleware> _logger;
    private readonly IWebHostEnvironment _environment;

    public GlobalExceptionHandlingMiddleware(
        RequestDelegate next,
        ILogger<GlobalExceptionHandlingMiddleware> logger,
        IWebHostEnvironment environment)
    {
        _next = next;
        _logger = logger;
        _environment = environment;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        try
        {
            await _next(context);
        }
        catch (Exception exception)
        {
            await HandleExceptionAsync(context, exception);
        }
    }

    private async Task HandleExceptionAsync(HttpContext context, Exception exception)
    {
        // 记录详细的异常信息
        var requestInfo = new
        {
            Method = context.Request.Method,
            Path = context.Request.Path.Value,
            QueryString = context.Request.QueryString.Value,
            Headers = context.Request.Headers.ToDictionary(h => h.Key, h => h.Value.ToString()),
            UserAgent = context.Request.Headers.UserAgent.ToString(),
            RemoteIpAddress = context.Connection.RemoteIpAddress?.ToString(),
            UserId = context.User?.Identity?.Name,
            TraceId = context.TraceIdentifier
        };

        _logger.LogError(exception,
            "未处理的异常发生。请求信息: {@RequestInfo}",
            requestInfo);

        // 设置响应
        context.Response.ContentType = "application/json";

        var response = new ProblemDetails();

        switch (exception)
        {
            case ArgumentException argEx:
                response.Status = (int)HttpStatusCode.BadRequest;
                response.Title = "参数错误";
                response.Detail = argEx.Message;
                break;

            case UnauthorizedAccessException:
                response.Status = (int)HttpStatusCode.Unauthorized;
                response.Title = "未授权访问";
                response.Detail = "您没有权限访问此资源";
                break;

            case KeyNotFoundException:
                response.Status = (int)HttpStatusCode.NotFound;
                response.Title = "资源未找到";
                response.Detail = "请求的资源不存在";
                break;

            case InvalidOperationException invOpEx:
                response.Status = (int)HttpStatusCode.BadRequest;
                response.Title = "操作无效";
                response.Detail = invOpEx.Message;
                break;

            case TimeoutException:
                response.Status = (int)HttpStatusCode.RequestTimeout;
                response.Title = "请求超时";
                response.Detail = "请求处理超时，请稍后重试";
                break;

            default:
                response.Status = (int)HttpStatusCode.InternalServerError;
                response.Title = "服务器内部错误";
                response.Detail = _environment.IsDevelopment()
                    ? exception.Message
                    : "服务器发生错误，请联系管理员";
                break;
        }

        // 添加追踪ID
        response.Extensions["traceId"] = context.TraceIdentifier;

        // 在开发环境下添加详细的异常信息
        if (_environment.IsDevelopment())
        {
            response.Extensions["exception"] = new
            {
                Type = exception.GetType().Name,
                Message = exception.Message,
                StackTrace = exception.StackTrace,
                InnerException = exception.InnerException?.Message
            };
        }

        context.Response.StatusCode = response.Status ?? (int)HttpStatusCode.InternalServerError;

        var jsonResponse = JsonSerializer.Serialize(response, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = true
        });

        await context.Response.WriteAsync(jsonResponse);
    }
}
```

### 5.5 权限系统设计

#### 5.5.1 多层权限架构

**权限控制层次**:
```
权限系统架构
├── 身份认证层 (Authentication)
│   ├── JWT Token验证
│   ├── 双因素认证(2FA)
│   ├── 微信扫码登录
│   └── 会话管理
├── 角色授权层 (Role-based Authorization)
│   ├── SystemAdmin - 系统管理员
│   ├── MerchantAdmin - 商户管理员
│   ├── Operator - 操作员
│   └── 自定义角色
├── 功能权限层 (Feature-based Permission)
│   ├── 功能配置表 (FeatureConfigs)
│   ├── 角色功能权限表 (RoleFeaturePermissions)
│   ├── 全局开关控制
│   └── 角色权限覆盖
├── 数据权限层 (Data-level Permission)
│   ├── 商户数据隔离 (ITenantEntity)
│   ├── 用户商户关联
│   ├── 跨商户访问控制
│   └── 数据过滤机制
└── 操作审计层 (Audit Trail)
    ├── 登录日志 (LoginLogs)
    ├── 密码变更日志 (PasswordChangeLogs)
    ├── 操作日志 (OperationLogs)
    └── 审计中间件
```

#### 5.5.2 功能权限实现

**权限缓存机制**:
```csharp
public async Task<Dictionary<string, bool>> GetUserPermissionsAsync(ClaimsPrincipal user)
{
    // 使用用户ID作为缓存key
    var userId = user.FindFirst(JwtRegisteredClaimNames.Sub)?.Value ??
                user.FindFirst(ClaimTypes.NameIdentifier)?.Value ??
                user.Identity?.Name ?? "unknown";
    var cacheKey = $"user_permissions_{userId}";

    if (_cache.TryGetValue(cacheKey, out Dictionary<string, bool>? cached))
    {
        return cached!;
    }

    var permissions = new Dictionary<string, bool>();

    // 获取所有功能配置
    var allFeatures = await _context.FeatureConfigs.ToListAsync();

    // 批量检查权限
    foreach (var feature in allFeatures)
    {
        permissions[feature.FeatureKey] = await HasPermissionAsync(user, feature.FeatureKey);
    }

    // 缓存权限结果（5分钟）
    _cache.Set(cacheKey, permissions, TimeSpan.FromMinutes(5));

    return permissions;
}
```

**数据隔离实现**:
```csharp
// ITenantEntity接口实现
public interface ITenantEntity
{
    string MerchantID { get; set; }
}

// 控制器中的数据隔离逻辑
public async Task<ActionResult> GetData([FromQuery] string? merchantId)
{
    var currentUser = await _userManager.GetUserAsync(User);
    var isSystemAdmin = await _userManager.IsInRoleAsync(currentUser, "SystemAdmin");

    // 如果不是系统管理员，只能查看自己商户的数据
    if (!isSystemAdmin && !string.IsNullOrEmpty(currentUser.MerchantID))
    {
        merchantId = currentUser.MerchantID;
    }

    // 数据查询时自动应用商户过滤
    var query = _context.SomeEntities.Where(e => e.MerchantID == merchantId);

    return Ok(await query.ToListAsync());
}
```

---

*注：本文档将按照任务计划逐章节完成，确保内容的准确性和完整性。*
