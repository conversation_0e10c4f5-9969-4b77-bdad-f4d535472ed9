using System.Security.Claims;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Identity;
using SlzrCrossGate.Core.Database;
using SlzrCrossGate.Core.Models;
using System.Reflection;
using System.IdentityModel.Tokens.Jwt;

namespace SlzrCrossGate.Core.Services
{
    /// <summary>
    /// 功能权限服务实现
    /// </summary>
    public class FeaturePermissionService : IFeaturePermissionService
    {
        private readonly TcpDbContext _context;
        private readonly IMemoryCache _cache;
        private readonly ILogger<FeaturePermissionService> _logger;
        private readonly UserManager<ApplicationUser> _userManager;

        public FeaturePermissionService(
            TcpDbContext context,
            IMemoryCache cache,
            ILogger<FeaturePermissionService> logger,
            UserManager<ApplicationUser> userManager)
        {
            _context = context;
            _cache = cache;
            _logger = logger;
            _userManager = userManager;
        }

        public async Task<bool> HasPermissionAsync(ClaimsPrincipal user, string featureKey)
        {
            try
            {
                // 1. 获取用户角色
                var roles = user.Claims
                    .Where(c => c.Type == ClaimTypes.Role)
                    .Select(c => c.Value)
                    .ToList();

                // 调试信息
                //_logger.LogInformation("权限检查调试 - 用户: {UserName}, 功能: {FeatureKey}, 角色: [{Roles}]",
                //    user.Identity?.Name, featureKey, string.Join(", ", roles));

                if (!roles.Any())
                {
                    _logger.LogWarning("用户 {UserName} 没有任何角色", user.Identity?.Name);
                    return false;
                }

                // 所有角色（包括SystemAdmin）都通过权限配置来控制，不再有特殊权限
                //_logger.LogInformation("用户 {UserName} 的功能 {FeatureKey} 需要检查权限配置", user.Identity?.Name, featureKey);

                // 2. 获取功能配置
                var featureConfig = await GetFeatureConfigAsync(featureKey);
                if (featureConfig == null)
                {
                    _logger.LogWarning("Feature config not found: {FeatureKey}", featureKey);
                    return false;
                }

                // 3. 检查全局开关
                //_logger.LogInformation("功能 {FeatureKey} 全局开关状态: {IsGloballyEnabled}", featureKey, featureConfig.IsGloballyEnabled);
                if (!featureConfig.IsGloballyEnabled)
                {
                //    _logger.LogInformation("功能 {FeatureKey} 全局开关已关闭，拒绝访问", featureKey);
                    return false;
                }

                // 4. 检查角色权限覆盖
                var rolePermissions = await _context.RoleFeaturePermissions
                    .Where(rp => rp.FeatureKey == featureKey && roles.Contains(rp.RoleName))
                    .ToListAsync();

                //_logger.LogInformation("找到 {Count} 条角色权限记录: {Permissions}",
                //    rolePermissions.Count,
                //    string.Join(", ", rolePermissions.Select(rp => $"{rp.RoleName}:{rp.IsEnabled}")));

                // 5. 最小权限原则：只有明确配置为允许(true)的角色才有权限
                // 检查用户的任一角色是否有明确的允许权限
                foreach (var role in roles)
                {
                    var rolePermission = rolePermissions.FirstOrDefault(rp => rp.RoleName == role);
                //    _logger.LogInformation("检查角色 {Role} 的权限配置: {Permission}", role, rolePermission?.IsEnabled);

                    if (rolePermission?.IsEnabled == true)
                    {
                //        _logger.LogInformation("角色 {Role} 有明确的允许权限，允许访问", role);
                        return true; // 找到明确允许的权限，立即返回true
                    }
                }

                // 6. 其他所有情况都拒绝访问：
                // - 没有配置任何角色权限
                // - 配置了权限但都不是true（null、false等）
                //_logger.LogInformation("功能 {FeatureKey} 未找到明确的允许权限，拒绝访问", featureKey);
                return false;

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking permission for feature: {FeatureKey}", featureKey);
                return false; // 出错时拒绝访问
            }
        }

        public async Task<Dictionary<string, bool>> CheckMultiplePermissionsAsync(ClaimsPrincipal user, IEnumerable<string> featureKeys)
        {
            var permissions = new Dictionary<string, bool>();
            
            foreach (var featureKey in featureKeys)
            {
                permissions[featureKey] = await HasPermissionAsync(user, featureKey);
            }
            
            return permissions;
        }

        public async Task<Dictionary<string, bool>> GetUserPermissionsAsync(ClaimsPrincipal user)
        {
            // 使用用户ID作为缓存key，确保与清理缓存时的key一致
            var userId = user.FindFirst(JwtRegisteredClaimNames.Sub)?.Value ??
                        user.FindFirst(ClaimTypes.NameIdentifier)?.Value ??
                        user.Identity?.Name ?? "unknown";
            var cacheKey = $"user_permissions_{userId}";

            if (_cache.TryGetValue(cacheKey, out Dictionary<string, bool>? cached))
            {
                return cached!;
            }

            var permissions = new Dictionary<string, bool>();

            // 获取所有功能配置
            var allFeatures = await _context.FeatureConfigs.ToListAsync();

            // 批量检查权限
            foreach (var feature in allFeatures)
            {
                permissions[feature.FeatureKey] = await HasPermissionAsync(user, feature.FeatureKey);
            }

            // 缓存5分钟
            _cache.Set(cacheKey, permissions, TimeSpan.FromMinutes(5));

            return permissions;
        }

        public async Task RefreshPermissionCacheAsync(string? userId = null)
        {
            if (userId != null)
            {
                // 清除指定用户的权限缓存
                _cache.Remove($"user_permissions_{userId}");
                _logger.LogInformation("Cleared permission cache for user: {UserId}", userId);
            }
            else
            {
                // 清除所有用户的权限缓存
                // 获取所有用户ID并清除其缓存
                var userIds = await _context.Users.Select(u => u.Id).ToListAsync();
                var clearedCount = 0;

                foreach (var uid in userIds)
                {
                    var cacheKey = $"user_permissions_{uid}";
                    _cache.Remove(cacheKey);
                    clearedCount++;
                }

                _logger.LogInformation("Cleared permission cache for {Count} users", clearedCount);
            }
        }

        public async Task<List<FeatureConfigDto>> GetFeatureConfigsAsync()
        {
            var configs = await _context.FeatureConfigs
                .Include(fc => fc.RolePermissions)
                .OrderBy(fc => fc.Category)
                .ThenBy(fc => fc.SortOrder)
                .ToListAsync();

            return configs.Select(fc => new FeatureConfigDto
            {
                FeatureKey = fc.FeatureKey,
                FeatureName = fc.FeatureName,
                Description = fc.Description,
                Category = fc.Category,
                RiskLevel = fc.RiskLevel,
                IsGloballyEnabled = fc.IsGloballyEnabled,
                IsSystemBuiltIn = fc.IsSystemBuiltIn,
                SortOrder = fc.SortOrder,
                RolePermissions = fc.RolePermissions.ToDictionary(
                    rp => rp.RoleName,
                    rp => rp.IsEnabled
                )
            }).ToList();
        }

        public async Task UpdateFeatureConfigAsync(string featureKey, bool isEnabled)
        {
            var config = await _context.FeatureConfigs
                .FirstOrDefaultAsync(fc => fc.FeatureKey == featureKey);

            if (config != null)
            {
                config.IsGloballyEnabled = isEnabled;
                config.UpdatedAt = DateTime.Now;
                
                await _context.SaveChangesAsync();
                await RefreshPermissionCacheAsync();
            }
        }

        public async Task UpdateRolePermissionAsync(string roleName, string featureKey, bool? isEnabled)
        {
            var existing = await _context.RoleFeaturePermissions
                .FirstOrDefaultAsync(rp => rp.RoleName == roleName && rp.FeatureKey == featureKey);

            if (isEnabled.HasValue)
            {
                if (existing != null)
                {
                    existing.IsEnabled = isEnabled;
                    existing.UpdatedAt = DateTime.Now;
                }
                else
                {
                    _context.RoleFeaturePermissions.Add(new RoleFeaturePermission
                    {
                        RoleName = roleName,
                        FeatureKey = featureKey,
                        IsEnabled = isEnabled,
                        CreatedAt = DateTime.Now
                    });
                }
            }
            else if (existing != null)
            {
                _context.RoleFeaturePermissions.Remove(existing);
            }

            await _context.SaveChangesAsync();
            await RefreshPermissionCacheAsync();
        }

        public async Task BatchUpdateConfigsAsync(List<FeatureConfigUpdateDto> updates)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();

            try
            {
                foreach (var update in updates)
                {
                    var config = await _context.FeatureConfigs
                        .FirstOrDefaultAsync(fc => fc.FeatureKey == update.FeatureKey);

                    if (config != null)
                    {
                        config.IsGloballyEnabled = update.IsGloballyEnabled;
                        config.UpdatedAt = DateTime.Now;

                        // 更新角色权限
                        var existingRolePermissions = await _context.RoleFeaturePermissions
                            .Where(rp => rp.FeatureKey == update.FeatureKey)
                            .ToListAsync();

                        _context.RoleFeaturePermissions.RemoveRange(existingRolePermissions);

                        foreach (var rolePermission in update.RolePermissions)
                        {
                            if (rolePermission.Value.HasValue)
                            {
                                _context.RoleFeaturePermissions.Add(new RoleFeaturePermission
                                {
                                    RoleName = rolePermission.Key,
                                    FeatureKey = update.FeatureKey,
                                    IsEnabled = rolePermission.Value,
                                    CreatedAt = DateTime.Now
                                });
                            }
                        }
                    }
                }

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                // 清除权限缓存
                await RefreshPermissionCacheAsync();
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        public async Task InitializeDefaultConfigsAsync()
        {
            // 检查功能配置是否已经初始化
            var existingConfigCount = await _context.FeatureConfigs.CountAsync();
            if (existingConfigCount == 0)
            {
                // 初始化默认功能配置数据
                var defaultConfigs = GetDefaultFeatureConfigs();
                _context.FeatureConfigs.AddRange(defaultConfigs);
                await _context.SaveChangesAsync();
                _logger.LogInformation("Default feature configurations initialized");
            }
            else
            {
                // 如果功能配置已存在，检查是否有缺失的配置并补充
                await EnsureRequiredFeatureConfigsExistAsync();
            }

            // 检查角色权限是否已经初始化（独立检查）
            await InitializeDefaultRolePermissionsAsync();

            _logger.LogInformation("Permission system initialization completed");
        }

        private async Task EnsureRequiredFeatureConfigsExistAsync()
        {
            // 获取所有默认配置
            var defaultConfigs = GetDefaultFeatureConfigs();

            // 获取现有配置的FeatureKey列表
            var existingKeys = await _context.FeatureConfigs
                .Select(fc => fc.FeatureKey)
                .ToListAsync();

            // 找出缺失的配置
            var missingConfigs = defaultConfigs
                .Where(config => !existingKeys.Contains(config.FeatureKey))
                .ToList();

            if (missingConfigs.Any())
            {
                _context.FeatureConfigs.AddRange(missingConfigs);
                await _context.SaveChangesAsync();
                _logger.LogInformation("Added {Count} missing feature configurations", missingConfigs.Count);
            }
        }

        private async Task InitializeDefaultRolePermissionsAsync()
        {
            var existingRolePermissions = await _context.RoleFeaturePermissions.CountAsync();
            if (existingRolePermissions > 0)
            {
                return;
            }

            // SystemAdmin 的权限（包含所有权限）
            var systemAdminPermissions = new[]
            {
                // 终端管理
                "terminal.send_message",
                "terminal.publish_file",

                // 文件类型
                "file_type.create",
                "file_type.edit",
                "file_type.delete",

                // 文件版本
                "file_version.upload",
                "file_version.publish",
                "file_version.delete",

                // 发布记录
                "publish_record.delete",

                // 消息类型
                "message_type.create",
                "message_type.edit",
                "message_type.delete",

                // 消息管理
                "message.send",
                "message.delete",

                // 线路参数
                "line_price.create",
                "line_price.edit",
                "line_price.delete",
                "line_price.view_versions",

                // 线路参数版本
                "line_price_version.create_version",
                "line_price_version.copy_create",
                "line_price_version.copy_to_other_line",
                "line_price_version.edit",
                "line_price_version.submit",

                // 银联密钥
                "unionpay_key.create",
                "unionpay_key.batch_import",
                "unionpay_key.edit",
                "unionpay_key.unbind",
                "unionpay_key.delete",
                "unionpay_key.export",

                // 商户字典
                "merchant_dictionary.create",
                "merchant_dictionary.edit",
                "merchant_dictionary.delete",

                // 票价折扣方案
                "fare_discount_scheme.create",
                "fare_discount_scheme.edit",
                "fare_discount_scheme.delete",
                "fare_discount_scheme.submit_version",

                // 系统设置中字段管理
                "system.field_management.open_config_manager",

                // 菜单管理
                "menu.init_default_menus",
                "menu.create_group",
                "menu.edit_group",
                "menu.delete_group",
                "menu.create_menu_item",
                "menu.edit_menu_item",
                "menu.delete_menu_item",

                // 商户管理
                "merchant.create",
                "merchant.edit",
                "merchant.toggle_active",
                "merchant.delete",

                // 用户管理
                "user.create",
                "user.edit",
                "user.lock",
                "user.delete",

                // 角色管理
                "role.create",
                "role.edit",
                "role.delete",

                // 功能权限管理
                "feature_permission.save_changes"
            };

            var rolePermissions = new List<RoleFeaturePermission>();

            // 为SystemAdmin添加高风险权限
            foreach (var permission in systemAdminPermissions)
            {
                rolePermissions.Add(new RoleFeaturePermission
                {
                    RoleName = "SystemAdmin",
                    FeatureKey = permission,
                    IsEnabled = true,
                    CreatedAt = DateTime.Now,
                    CreatedBy = "System"
                });
            }

            // 为MerchantAdmin添加明确权限设置（按照用户要求的权限列表）
            var merchantAdminPermissions = new[]
            {
                // 终端管理
                "terminal.send_message",
                "terminal.publish_file",

                // 文件类型
                "file_type.create",
                "file_type.edit",
                "file_type.delete",

                // 文件版本
                "file_version.upload",
                "file_version.publish",
                "file_version.delete",

                // 发布记录
                "publish_record.delete",

                // 消息类型
                "message_type.create",
                "message_type.edit",
                "message_type.delete",

                // 消息管理
                "message.send",
                "message.delete",

                // 线路参数
                "line_price.create",
                "line_price.edit",
                "line_price.delete",
                "line_price.view_versions",

                // 线路参数版本
                "line_price_version.create_version",
                "line_price_version.copy_create",
                "line_price_version.copy_to_other_line",
                "line_price_version.edit",
                "line_price_version.submit",

                // 银联密钥
                "unionpay_key.create",
                "unionpay_key.batch_import",
                "unionpay_key.edit",
                "unionpay_key.unbind",
                "unionpay_key.delete",
                "unionpay_key.export",

                // 票价折扣方案
                "fare_discount_scheme.create",
                "fare_discount_scheme.edit",
                "fare_discount_scheme.delete",
                "fare_discount_scheme.submit_version",

                // 用户管理
                "user.create",
                "user.edit",
                "user.lock",
                "user.delete"
            };

            foreach (var permission in merchantAdminPermissions)
            {
                rolePermissions.Add(new RoleFeaturePermission
                {
                    RoleName = "MerchantAdmin",
                    FeatureKey = permission,
                    IsEnabled = true,
                    CreatedAt = DateTime.Now,
                    CreatedBy = "System"
                });
            }

            _context.RoleFeaturePermissions.AddRange(rolePermissions);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Default role permissions initialized with {Count} records", rolePermissions.Count);
        }

        private List<FeatureConfig> GetDefaultFeatureConfigs()
        {
            var configs = new List<FeatureConfig>();
            var sortOrder = 1;

            // 终端管理
            configs.AddRange(new[]
            {
                new FeatureConfig { FeatureKey = "terminal.send_message", FeatureName = "发送消息", Description = "允许向终端发送消息", Category = "终端管理", RiskLevel = "Medium", IsSystemBuiltIn = true, SortOrder = sortOrder++ },
                new FeatureConfig { FeatureKey = "terminal.publish_file", FeatureName = "发布文件", Description = "允许向终端发布文件", Category = "终端管理", RiskLevel = "Medium", IsSystemBuiltIn = true, SortOrder = sortOrder++ }
            });

            // 文件类型
            configs.AddRange(new[]
            {
                new FeatureConfig { FeatureKey = "file_type.create", FeatureName = "新建", Description = "允许创建新的文件类型", Category = "文件类型", RiskLevel = "Low", IsSystemBuiltIn = true, SortOrder = sortOrder++ },
                new FeatureConfig { FeatureKey = "file_type.edit", FeatureName = "编辑", Description = "允许编辑文件类型信息", Category = "文件类型", RiskLevel = "Medium", IsSystemBuiltIn = true, SortOrder = sortOrder++ },
                new FeatureConfig { FeatureKey = "file_type.delete", FeatureName = "删除", Description = "允许删除文件类型", Category = "文件类型", RiskLevel = "High", IsSystemBuiltIn = true, SortOrder = sortOrder++ }
            });

            // 文件版本
            configs.AddRange(new[]
            {
                new FeatureConfig { FeatureKey = "file_version.upload", FeatureName = "上传", Description = "允许上传新的文件版本", Category = "文件版本", RiskLevel = "Medium", IsSystemBuiltIn = true, SortOrder = sortOrder++ },
                new FeatureConfig { FeatureKey = "file_version.publish", FeatureName = "发布", Description = "允许发布文件版本", Category = "文件版本", RiskLevel = "Medium", IsSystemBuiltIn = true, SortOrder = sortOrder++ },
                new FeatureConfig { FeatureKey = "file_version.delete", FeatureName = "删除", Description = "允许删除文件版本", Category = "文件版本", RiskLevel = "High", IsSystemBuiltIn = true, SortOrder = sortOrder++ }
            });

            // 发布记录
            configs.AddRange(new[]
            {
                new FeatureConfig { FeatureKey = "publish_record.delete", FeatureName = "删除", Description = "允许删除发布记录", Category = "发布记录", RiskLevel = "High", IsSystemBuiltIn = true, SortOrder = sortOrder++ }
            });

            // 消息类型
            configs.AddRange(new[]
            {
                new FeatureConfig { FeatureKey = "message_type.create", FeatureName = "新建", Description = "允许创建新的消息类型", Category = "消息类型", RiskLevel = "Low", IsSystemBuiltIn = true, SortOrder = sortOrder++ },
                new FeatureConfig { FeatureKey = "message_type.edit", FeatureName = "编辑", Description = "允许编辑消息类型", Category = "消息类型", RiskLevel = "Medium", IsSystemBuiltIn = true, SortOrder = sortOrder++ },
                new FeatureConfig { FeatureKey = "message_type.delete", FeatureName = "删除", Description = "允许删除消息类型", Category = "消息类型", RiskLevel = "High", IsSystemBuiltIn = true, SortOrder = sortOrder++ }
            });

            // 消息管理
            configs.AddRange(new[]
            {
                new FeatureConfig { FeatureKey = "message.send", FeatureName = "发布消息", Description = "允许发布消息到终端", Category = "消息管理", RiskLevel = "Medium", IsSystemBuiltIn = true, SortOrder = sortOrder++ },
                new FeatureConfig { FeatureKey = "message.delete", FeatureName = "删除", Description = "允许删除消息", Category = "消息管理", RiskLevel = "High", IsSystemBuiltIn = true, SortOrder = sortOrder++ }
            });

            // 线路参数
            configs.AddRange(new[]
            {
                new FeatureConfig { FeatureKey = "line_price.create", FeatureName = "新建", Description = "允许创建新的线路参数", Category = "线路参数", RiskLevel = "Low", IsSystemBuiltIn = true, SortOrder = sortOrder++ },
                new FeatureConfig { FeatureKey = "line_price.edit", FeatureName = "编辑", Description = "允许编辑线路参数", Category = "线路参数", RiskLevel = "Medium", IsSystemBuiltIn = true, SortOrder = sortOrder++ },
                new FeatureConfig { FeatureKey = "line_price.delete", FeatureName = "删除", Description = "允许删除线路参数", Category = "线路参数", RiskLevel = "High", IsSystemBuiltIn = true, SortOrder = sortOrder++ },
                new FeatureConfig { FeatureKey = "line_price.view_versions", FeatureName = "查看版本", Description = "允许查看线路参数版本历史", Category = "线路参数", RiskLevel = "Low", IsSystemBuiltIn = true, SortOrder = sortOrder++ }
            });

            // 线路参数版本
            configs.AddRange(new[]
            {
                new FeatureConfig { FeatureKey = "line_price_version.create_version", FeatureName = "新建版本", Description = "允许创建新的线路参数版本", Category = "线路参数版本", RiskLevel = "Medium", IsSystemBuiltIn = true, SortOrder = sortOrder++ },
                new FeatureConfig { FeatureKey = "line_price_version.copy_create", FeatureName = "复制创建", Description = "允许复制现有版本创建新版本", Category = "线路参数版本", RiskLevel = "Medium", IsSystemBuiltIn = true, SortOrder = sortOrder++ },
                new FeatureConfig { FeatureKey = "line_price_version.copy_to_other_line", FeatureName = "复制到其它线路", Description = "允许将版本复制到其他线路", Category = "线路参数版本", RiskLevel = "Medium", IsSystemBuiltIn = true, SortOrder = sortOrder++ },
                new FeatureConfig { FeatureKey = "line_price_version.edit", FeatureName = "编辑", Description = "允许编辑线路参数版本", Category = "线路参数版本", RiskLevel = "Medium", IsSystemBuiltIn = true, SortOrder = sortOrder++ },
                new FeatureConfig { FeatureKey = "line_price_version.submit", FeatureName = "提交", Description = "允许提交线路参数版本", Category = "线路参数版本", RiskLevel = "High", IsSystemBuiltIn = true, SortOrder = sortOrder++ }
            });

            // 银联密钥
            configs.AddRange(new[]
            {
                new FeatureConfig { FeatureKey = "unionpay_key.create", FeatureName = "新增密钥", Description = "允许新增银联密钥", Category = "银联密钥", RiskLevel = "High", IsSystemBuiltIn = true, SortOrder = sortOrder++ },
                new FeatureConfig { FeatureKey = "unionpay_key.batch_import", FeatureName = "批量导入", Description = "允许批量导入银联密钥", Category = "银联密钥", RiskLevel = "High", IsSystemBuiltIn = true, SortOrder = sortOrder++ },
                new FeatureConfig { FeatureKey = "unionpay_key.edit", FeatureName = "编辑", Description = "允许编辑银联密钥", Category = "银联密钥", RiskLevel = "High", IsSystemBuiltIn = true, SortOrder = sortOrder++ },
                new FeatureConfig { FeatureKey = "unionpay_key.unbind", FeatureName = "解绑", Description = "允许解绑银联密钥", Category = "银联密钥", RiskLevel = "High", IsSystemBuiltIn = true, SortOrder = sortOrder++ },
                new FeatureConfig { FeatureKey = "unionpay_key.delete", FeatureName = "删除", Description = "允许删除银联密钥", Category = "银联密钥", RiskLevel = "High", IsSystemBuiltIn = true, SortOrder = sortOrder++ },
                new FeatureConfig { FeatureKey = "unionpay_key.export", FeatureName = "导出数据", Description = "允许导出银联密钥数据", Category = "银联密钥", RiskLevel = "Medium", IsSystemBuiltIn = true, SortOrder = sortOrder++ }
            });

            // 商户字典
            configs.AddRange(new[]
            {
                new FeatureConfig { FeatureKey = "merchant_dictionary.create", FeatureName = "新建", Description = "允许创建商户字典项", Category = "商户字典", RiskLevel = "Low", IsSystemBuiltIn = true, SortOrder = sortOrder++ },
                new FeatureConfig { FeatureKey = "merchant_dictionary.edit", FeatureName = "编辑", Description = "允许编辑商户字典项", Category = "商户字典", RiskLevel = "Medium", IsSystemBuiltIn = true, SortOrder = sortOrder++ },
                new FeatureConfig { FeatureKey = "merchant_dictionary.delete", FeatureName = "删除", Description = "允许删除商户字典项", Category = "商户字典", RiskLevel = "High", IsSystemBuiltIn = true, SortOrder = sortOrder++ }
            });

            // 票价折扣方案
            configs.AddRange(new[]
            {
                new FeatureConfig { FeatureKey = "fare_discount_scheme.create", FeatureName = "新建", Description = "允许创建票价折扣方案", Category = "票价折扣方案", RiskLevel = "Low", IsSystemBuiltIn = true, SortOrder = sortOrder++ },
                new FeatureConfig { FeatureKey = "fare_discount_scheme.edit", FeatureName = "编辑", Description = "允许编辑票价折扣方案", Category = "票价折扣方案", RiskLevel = "Medium", IsSystemBuiltIn = true, SortOrder = sortOrder++ },
                new FeatureConfig { FeatureKey = "fare_discount_scheme.delete", FeatureName = "删除", Description = "允许删除票价折扣方案", Category = "票价折扣方案", RiskLevel = "High", IsSystemBuiltIn = true, SortOrder = sortOrder++ },
                new FeatureConfig { FeatureKey = "fare_discount_scheme.submit_version", FeatureName = "提交版本", Description = "允许提交票价折扣方案版本", Category = "票价折扣方案", RiskLevel = "High", IsSystemBuiltIn = true, SortOrder = sortOrder++ }
            });

            // 系统设置中字段管理
            configs.AddRange(new[]
            {
                new FeatureConfig { FeatureKey = "system.field_management.open_config_manager", FeatureName = "打开字段配置管理器", Description = "允许打开字段配置管理器", Category = "系统设置", RiskLevel = "High", IsSystemBuiltIn = true, SortOrder = sortOrder++ }
            });

            // 菜单管理
            configs.AddRange(new[]
            {
                new FeatureConfig { FeatureKey = "menu.init_default_menus", FeatureName = "初始化默认菜单", Description = "允许初始化默认菜单", Category = "菜单管理", RiskLevel = "High", IsSystemBuiltIn = true, SortOrder = sortOrder++ },
                new FeatureConfig { FeatureKey = "menu.create_group", FeatureName = "新增分组", Description = "允许新增菜单分组", Category = "菜单管理", RiskLevel = "Medium", IsSystemBuiltIn = true, SortOrder = sortOrder++ },
                new FeatureConfig { FeatureKey = "menu.edit_group", FeatureName = "编辑分组", Description = "允许编辑菜单分组", Category = "菜单管理", RiskLevel = "Medium", IsSystemBuiltIn = true, SortOrder = sortOrder++ },
                new FeatureConfig { FeatureKey = "menu.delete_group", FeatureName = "删除分组", Description = "允许删除菜单分组", Category = "菜单管理", RiskLevel = "High", IsSystemBuiltIn = true, SortOrder = sortOrder++ },
                new FeatureConfig { FeatureKey = "menu.create_menu_item", FeatureName = "添加菜单项目", Description = "允许添加菜单项目", Category = "菜单管理", RiskLevel = "Medium", IsSystemBuiltIn = true, SortOrder = sortOrder++ },
                new FeatureConfig { FeatureKey = "menu.edit_menu_item", FeatureName = "编辑菜单项目", Description = "允许编辑菜单项目", Category = "菜单管理", RiskLevel = "Medium", IsSystemBuiltIn = true, SortOrder = sortOrder++ },
                new FeatureConfig { FeatureKey = "menu.delete_menu_item", FeatureName = "删除菜单项目", Description = "允许删除菜单项目", Category = "菜单管理", RiskLevel = "High", IsSystemBuiltIn = true, SortOrder = sortOrder++ }
            });

            // 商户管理
            configs.AddRange(new[]
            {
                new FeatureConfig { FeatureKey = "merchant.create", FeatureName = "新增", Description = "允许新增商户", Category = "商户管理", RiskLevel = "Low", IsSystemBuiltIn = true, SortOrder = sortOrder++ },
                new FeatureConfig { FeatureKey = "merchant.edit", FeatureName = "编辑", Description = "允许编辑商户信息", Category = "商户管理", RiskLevel = "Medium", IsSystemBuiltIn = true, SortOrder = sortOrder++ },
                new FeatureConfig { FeatureKey = "merchant.toggle_active", FeatureName = "停用", Description = "允许停用/启用商户", Category = "商户管理", RiskLevel = "High", IsSystemBuiltIn = true, SortOrder = sortOrder++ },
                new FeatureConfig { FeatureKey = "merchant.delete", FeatureName = "删除", Description = "允许删除商户", Category = "商户管理", RiskLevel = "High", IsSystemBuiltIn = true, SortOrder = sortOrder++ }
            });

            // 用户管理
            configs.AddRange(new[]
            {
                new FeatureConfig { FeatureKey = "user.create", FeatureName = "添加", Description = "允许添加用户", Category = "用户管理", RiskLevel = "Low", IsSystemBuiltIn = true, SortOrder = sortOrder++ },
                new FeatureConfig { FeatureKey = "user.edit", FeatureName = "编辑", Description = "允许编辑用户信息", Category = "用户管理", RiskLevel = "Medium", IsSystemBuiltIn = true, SortOrder = sortOrder++ },
                new FeatureConfig { FeatureKey = "user.lock", FeatureName = "锁定", Description = "允许锁定/解锁用户", Category = "用户管理", RiskLevel = "High", IsSystemBuiltIn = true, SortOrder = sortOrder++ },
                new FeatureConfig { FeatureKey = "user.delete", FeatureName = "删除", Description = "允许删除用户", Category = "用户管理", RiskLevel = "High", IsSystemBuiltIn = true, SortOrder = sortOrder++ }
            });

            // 角色管理
            configs.AddRange(new[]
            {
                new FeatureConfig { FeatureKey = "role.create", FeatureName = "新增", Description = "允许新增角色", Category = "角色管理", RiskLevel = "High", IsSystemBuiltIn = true, SortOrder = sortOrder++ },
                new FeatureConfig { FeatureKey = "role.edit", FeatureName = "编辑", Description = "允许编辑角色", Category = "角色管理", RiskLevel = "High", IsSystemBuiltIn = true, SortOrder = sortOrder++ },
                new FeatureConfig { FeatureKey = "role.delete", FeatureName = "删除", Description = "允许删除角色", Category = "角色管理", RiskLevel = "High", IsSystemBuiltIn = true, SortOrder = sortOrder++ }
            });

            // 功能权限管理
            configs.AddRange(new[]
            {
                new FeatureConfig { FeatureKey = "feature_permission.save_changes", FeatureName = "保存更改", Description = "允许保存功能权限更改", Category = "功能权限管理", RiskLevel = "High", IsSystemBuiltIn = true, SortOrder = sortOrder++ }
            });

            return configs;
        }

        private async Task<FeatureConfig?> GetFeatureConfigAsync(string featureKey)
        {
            var cacheKey = $"feature_config_{featureKey}";
            
            if (_cache.TryGetValue(cacheKey, out FeatureConfig? cached))
            {
                return cached;
            }

            var config = await _context.FeatureConfigs
                .FirstOrDefaultAsync(fc => fc.FeatureKey == featureKey);
                
            if (config != null)
            {
                _cache.Set(cacheKey, config, TimeSpan.FromHours(1));
            }
            
            return config;
        }
    }
}
