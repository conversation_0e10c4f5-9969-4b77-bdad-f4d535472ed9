# iframe打开方式配置指南

## 🎯 支持的Target选项

### 1. `_self` - 当前窗口打开
```sql
UPDATE MenuItems SET Target = '_self' WHERE ItemKey = 'menu-item';
```
- **用途**: 内部页面导航
- **特点**: 在当前窗口中打开链接
- **适用**: 系统内部页面

### 2. `_blank` - 新窗口打开
```sql
UPDATE MenuItems SET Target = '_blank' WHERE ItemKey = 'menu-item';
```
- **用途**: 外部网站链接
- **特点**: 在新标签页中打开
- **适用**: 第三方网站、文档链接

### 3. `_iframe` - iframe内嵌（支持认证共享）
```sql
UPDATE MenuItems SET 
    IsExternal = 1,
    Target = '_iframe' 
WHERE ItemKey = 'menu-item';
```
- **用途**: 客户定制项目
- **特点**: 
  - ✅ iframe内嵌显示
  - ✅ 自动传递用户认证信息
  - ✅ 主题同步（亮色/暗色）
  - ✅ 统一用户体验
- **适用**: 按我们规范开发的客户项目

### 4. `_iframe_simple` - iframe内嵌（普通显示）
```sql
UPDATE MenuItems SET 
    IsExternal = 1,
    Target = '_iframe_simple' 
WHERE ItemKey = 'menu-item';
```
- **用途**: 第三方系统集成
- **特点**:
  - ✅ iframe内嵌显示
  - ❌ 不传递认证信息
  - ❌ 不同步主题
  - ✅ 简单集成第三方页面
- **适用**: 不支持我们认证规范的第三方系统

## 🔧 技术实现差异

### 认证共享对比

| 功能 | `_iframe` | `_iframe_simple` |
|------|-----------|------------------|
| iframe嵌入 | ✅ | ✅ |
| 认证信息传递 | ✅ | ❌ |
| 主题同步 | ✅ | ❌ |
| postMessage通信 | ✅ | ❌ |
| 统一用户体验 | ✅ | ❌ |

### 消息传递机制

#### `_iframe` 模式
```javascript
// 主应用发送给iframe
{
  type: 'AUTH_INFO_RESPONSE',
  success: true,
  token: 'jwt-token',
  user: { id: 1, name: 'user' },
  theme: {
    mode: 'light',
    primaryColor: '#7E22CE',
    backgroundColor: '#ffffff'
  }
}
```

#### `_iframe_simple` 模式
```javascript
// 不发送任何消息，纯粹的iframe嵌入
// 第三方页面独立运行，不与主应用交互
```

## 📋 使用场景

### 1. 客户定制项目 → `_iframe`
```sql
-- 珠海通查询系统
UPDATE MenuItems SET 
    IsExternal = 1,
    Target = '_iframe',
    Href = 'http://localhost:3001/'
WHERE ItemKey = 'zhuhaitong-query';
```

### 2. 第三方工具集成 → `_iframe_simple`
```sql
-- 集成第三方监控面板
INSERT INTO MenuItems (ItemKey, Title, Href, IsExternal, Target) 
VALUES ('monitoring-dashboard', '监控面板', 'https://grafana.example.com', 1, '_iframe_simple');
```

### 3. 外部文档链接 → `_blank`
```sql
-- API文档链接
INSERT INTO MenuItems (ItemKey, Title, Href, IsExternal, Target) 
VALUES ('api-docs', 'API文档', 'https://docs.example.com', 1, '_blank');
```

## 🛠️ 配置示例

### 示例1: 添加第三方监控系统
```sql
INSERT INTO MenuItems (
    MenuGroupId, 
    ItemKey, 
    Title, 
    Href, 
    IconName, 
    SortOrder, 
    IsEnabled, 
    IsExternal, 
    Target
) VALUES (
    11,  -- 系统管理组
    'grafana-monitoring',
    '系统监控',
    'https://grafana.yourdomain.com',
    'BarChartIcon',
    10,
    1,
    1,
    '_iframe_simple'  -- 普通iframe，不需要认证共享
);
```

### 示例2: 添加客户定制功能
```sql
INSERT INTO MenuItems (
    MenuGroupId, 
    ItemKey, 
    Title, 
    Href, 
    IconName, 
    SortOrder, 
    IsEnabled, 
    IsExternal, 
    Target
) VALUES (
    13,  -- 客户功能组
    'customer-analytics',
    '数据分析',
    'http://localhost:3002/',
    'TrendingUpIcon',
    2,
    1,
    1,
    '_iframe'  -- 支持认证共享的iframe
);
```

## 🔍 兼容性检测

### 检测第三方网站是否支持iframe
使用我们提供的检测工具：
```html
<!-- 打开 iframe-compatibility-checker.html -->
<!-- 输入目标URL进行测试 -->
```

### 常见限制
- **X-Frame-Options: DENY** → 无法嵌入
- **X-Frame-Options: SAMEORIGIN** → 只能同域嵌入
- **CSP frame-ancestors** → 受CSP策略限制

## 💡 最佳实践

### 1. 选择合适的Target
- **内部系统** → `_iframe`（支持认证）
- **第三方工具** → `_iframe_simple`（普通嵌入）
- **外部链接** → `_blank`（新窗口）

### 2. 用户体验考虑
- 为iframe页面提供加载指示器
- 添加错误处理和重试机制
- 提供返回主应用的便捷方式

### 3. 安全考虑
- 验证第三方网站的可信度
- 考虑数据隐私和安全政策
- 定期检查外部链接的有效性

## 🎉 总结

通过新增 `_iframe_simple` 选项，我们现在支持：

1. **完整集成** (`_iframe`): 客户定制项目，支持认证和主题同步
2. **简单嵌入** (`_iframe_simple`): 第三方系统，纯iframe显示
3. **灵活选择**: 根据实际需求选择合适的集成方式

这样既保持了与客户项目的深度集成，又支持了第三方系统的简单嵌入！
