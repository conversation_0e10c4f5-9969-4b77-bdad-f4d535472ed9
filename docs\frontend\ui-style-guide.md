# WebAdmin前端UI样式规范

## 📋 概述

本文档定义了WebAdmin项目的前端UI样式规范，确保所有页面保持一致的视觉风格和用户体验。所有新增页面和组件都必须遵循此规范。

## 🎨 设计原则

### 1. 一致性原则
- 所有页面使用相同的布局模式
- 统一的字体大小和颜色规范
- 一致的组件样式和交互方式

### 2. 响应式设计
- 支持手机、平板、桌面等多种屏幕尺寸
- 使用Material-UI的Grid系统进行布局
- 合理的断点设置和内容适配

### 3. 可访问性
- 使用标准的字体大小，确保可读性
- 合理的颜色对比度
- 清晰的视觉层次

## 📐 布局规范

### 1. 页面容器
```javascript
// ✅ 正确：适应屏幕宽度
<Container maxWidth={false}>
  <Box sx={{ pt: 3, pb: 3 }}>
    {/* 页面内容 */}
  </Box>
</Container>

// ❌ 错误：固定最大宽度
<Container maxWidth="xl">
```

**说明**：
- 使用 `maxWidth={false}` 让页面适应屏幕宽度
- 顶部padding `pt: 3`，底部padding `pb: 3`
- 如有固定按钮，底部padding增加到 `pb: 10`

### 2. 页面标题区域
```javascript
<Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
  <Typography variant="h4" component="h1" sx={{ fontWeight: 'bold' }}>
    页面标题
  </Typography>
  <Box sx={{ display: 'flex', gap: 1 }}>
    {/* 操作按钮 */}
  </Box>
</Box>
```

**规范**：
- 标题使用 `variant="h4"`，加粗显示
- 右侧操作按钮使用 `gap: 1` 间距
- 整个区域底部边距 `mb: 3`

### 3. 响应式网格布局
```javascript
// 卡片式布局
<Grid container spacing={2}>
  {items.map((item) => (
    <Grid 
      item 
      xs={12}      // 手机：1列
      sm={6}       // 平板：2列
      md={4}       // 桌面：3列
      lg={3}       // 大屏：4列
      key={item.id}
    >
      <Paper sx={{ p: 2, height: '100%' }}>
        {/* 卡片内容 */}
      </Paper>
    </Grid>
  ))}
</Grid>
```

**断点规范**：
- `xs={12}`: 手机端单列显示
- `sm={6}`: 平板端双列显示
- `md={4}`: 桌面端三列显示
- `lg={3}`: 大屏端四列显示

## 🔤 字体规范

### 1. 标准字体大小
```javascript
// 页面主标题
<Typography variant="h4" component="h1">主标题</Typography>

// 分组/卡片标题
<Typography variant="h6">分组标题</Typography>

// 子标题
<Typography variant="subtitle1">重要子标题</Typography>
<Typography variant="subtitle2">次要子标题</Typography>

// 正文内容
<Typography variant="body1">主要正文</Typography>
<Typography variant="body2">次要正文</Typography>

// 说明文字
<Typography variant="caption">说明文字</Typography>
```

### 2. 禁止的做法
```javascript
// ❌ 错误：手动设置字体大小
<Typography sx={{ fontSize: '0.8rem' }}>文字</Typography>

// ❌ 错误：不使用variant
<Typography>没有指定variant的文字</Typography>

// ✅ 正确：使用标准variant
<Typography variant="body2">标准正文</Typography>
```

### 3. 字体层次对应表
| 用途 | Variant | 默认大小 | 使用场景 |
|------|---------|----------|----------|
| 页面标题 | h4 | 2.125rem | 页面主标题 |
| 分组标题 | h6 | 1.25rem | 卡片/分组标题 |
| 重要子标题 | subtitle1 | 1rem | 重要的子标题 |
| 次要子标题 | subtitle2 | 0.875rem | 次要的子标题 |
| 主要正文 | body1 | 1rem | 主要内容文字 |
| 次要正文 | body2 | 0.875rem | 次要内容文字 |
| 说明文字 | caption | 0.75rem | 提示、说明文字 |

## 🎨 颜色规范

### 1. 主题色彩
```javascript
// 主色调
color="primary"     // #7E22CE (紫色)
color="secondary"   // 次要色

// 状态色彩
color="success"     // 成功/允许状态
color="error"       // 错误/危险状态
color="warning"     // 警告状态
color="info"        // 信息状态

// 文字色彩
color="text.primary"    // 主要文字
color="text.secondary"  // 次要文字
```

### 2. 状态标识
```javascript
// 成功状态
<Chip label="允许" size="small" color="success" />

// 禁止状态
<Chip label="禁止" size="small" variant="outlined" color="default" />

// 警告状态
<Chip label="警告" size="small" color="warning" />
```

## 🧩 组件规范

### 1. 卡片组件
```javascript
<Paper 
  variant="outlined"
  sx={{
    p: 2,
    height: '100%', // 确保同行卡片高度一致
    display: 'flex',
    flexDirection: 'column',
    '&:hover': {
      boxShadow: 2
    }
  }}
>
  {/* 卡片内容 */}
</Paper>
```

### 2. 表单控件
```javascript
// 开关控件
<FormControlLabel
  control={<Switch size="small" />}
  label="标签文字"
/>

// 按钮
<Button variant="contained" size="small">
  操作按钮
</Button>

// 图标按钮
<IconButton size="small">
  <EditIcon fontSize="small" />
</IconButton>
```

### 3. 搜索筛选区域
```javascript
<Paper sx={{ p: 2, mb: 3 }}>
  <Grid container spacing={2} alignItems="center">
    <Grid item xs={12} md={6}>
      <TextField
        fullWidth
        placeholder="搜索..."
        size="small"
      />
    </Grid>
    <Grid item xs={12} md={4}>
      <FormControl fullWidth size="small">
        <InputLabel>筛选条件</InputLabel>
        <Select>
          {/* 选项 */}
        </Select>
      </FormControl>
    </Grid>
  </Grid>
</Paper>
```

## 🔧 交互规范

### 1. 加载状态
```javascript
// 页面加载
{loading ? (
  <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
    <CircularProgress />
  </Box>
) : (
  // 页面内容
)}

// 按钮加载
<Button disabled={loading}>
  {loading ? <CircularProgress size={24} /> : '保存'}
</Button>
```

### 2. 空状态
```javascript
<Paper sx={{ p: 4, textAlign: 'center' }}>
  <SettingsIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
  <Typography variant="h6" color="text.secondary" gutterBottom>
    暂无数据
  </Typography>
  <Typography variant="body2" color="text.secondary">
    系统中还没有相关数据，请联系管理员进行配置
  </Typography>
</Paper>
```

### 3. 确认对话框
```javascript
<Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
  <DialogTitle sx={{ color: 'error.main' }}>
    ⚠️ 确认操作
  </DialogTitle>
  <DialogContent>
    <DialogContentText>
      确认要执行此操作吗？此操作不可撤销。
    </DialogContentText>
  </DialogContent>
  <DialogActions>
    <Button onClick={onClose}>取消</Button>
    <Button onClick={onConfirm} color="error" variant="contained">
      确认
    </Button>
  </DialogActions>
</Dialog>
```

## 📱 响应式规范

### 1. 断点定义
```javascript
// Material-UI断点
xs: 0px      // 手机
sm: 600px    // 平板
md: 960px    // 桌面
lg: 1280px   // 大屏
xl: 1920px   // 超大屏
```

### 2. 响应式布局示例
```javascript
// 搜索区域响应式
<Grid container spacing={2}>
  <Grid item xs={12} md={6}>
    <TextField fullWidth />
  </Grid>
  <Grid item xs={12} md={4}>
    <Select fullWidth />
  </Grid>
  <Grid item xs={12} md={2}>
    <Button fullWidth>搜索</Button>
  </Grid>
</Grid>
```

## 🎯 图标规范

### 1. 图标系统
```javascript
// 使用统一的图标映射
import { getIconComponent } from '../utils/iconMapping';

const IconComponent = getIconComponent('SmartphoneIcon');
return IconComponent ? <IconComponent size={20} /> : <DefaultIcon />;
```

### 2. 图标大小
```javascript
// 标准图标大小
fontSize="small"    // 小图标 (20px)
fontSize="medium"   // 中等图标 (24px) 
fontSize="large"    // 大图标 (35px)

// 自定义大小
<Icon sx={{ fontSize: 64 }} />  // 特殊场景
```

## ✅ 检查清单

### 新页面开发检查项
- [ ] 使用 `<Container maxWidth={false}>` 容器
- [ ] 页面标题使用 `variant="h4"`
- [ ] 所有文字都指定了正确的 `variant`
- [ ] 没有手动设置 `fontSize`
- [ ] 响应式网格布局正确设置断点
- [ ] 卡片使用 `height: '100%'` 保持高度一致
- [ ] 图标使用统一的图标映射系统
- [ ] 颜色使用主题定义的标准色彩
- [ ] 交互状态（加载、空状态）处理完整
- [ ] 在不同屏幕尺寸下测试正常

### 代码审查要点
- [ ] 样式代码符合规范
- [ ] 没有硬编码的颜色值
- [ ] 响应式布局合理
- [ ] 组件复用性良好
- [ ] 可访问性考虑充分

## 📚 参考资源

- [Material-UI Typography](https://mui.com/material-ui/react-typography/)
- [Material-UI Grid System](https://mui.com/material-ui/react-grid/)
- [Material-UI Theming](https://mui.com/material-ui/customization/theming/)
- [WebAdmin图标映射系统](../../SlzrCrossGate.WebAdmin/ClientApp/src/utils/iconMapping.js)

---

**重要提醒**：此规范是强制性的，所有新增页面和组件都必须严格遵循。如需修改规范，请先讨论并更新此文档。

**版本**：v1.0  
**最后更新**：2025-07-30  
**维护者**：前端开发团队
