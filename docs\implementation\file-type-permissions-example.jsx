// 文件类型管理页面权限控制改造示例
import React from 'react';
import { 
  Box, 
  Button, 
  IconButton, 
  Tooltip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon
} from '@mui/icons-material';
import { FeatureGuard } from '../components/FeatureGuard';
import { useFeaturePermission } from '../hooks/useFeaturePermission';
import { PERMISSIONS } from '../constants/permissions';

// 文件类型操作按钮组件
const FileTypeActionButtons = ({ fileType, onEdit, onDelete }) => {
  const { user } = useAuth();
  
  // 业务逻辑判断
  const canEdit = user.roles.includes('SystemAdmin') || 
    fileType.merchantID === user.merchantId;
    
  const canDelete = user.roles.includes('SystemAdmin') || 
    (fileType.merchantID === user.merchantId && !fileType.hasVersions);

  return (
    <Box sx={{ display: 'flex', gap: 0.5 }}>
      {/* 编辑按钮 */}
      <FeatureGuard 
        featureKey={PERMISSIONS.FILE_TYPE.EDIT}
        additionalCheck={canEdit}
        fallback={
          <Tooltip title="无编辑权限">
            <span>
              <IconButton size="small" disabled>
                <EditIcon fontSize="small" />
              </IconButton>
            </span>
          </Tooltip>
        }
      >
        <Tooltip title="编辑">
          <IconButton
            size="small"
            color="primary"
            onClick={() => onEdit(fileType)}
          >
            <EditIcon fontSize="small" />
          </IconButton>
        </Tooltip>
      </FeatureGuard>

      {/* 删除按钮 */}
      <FeatureGuard 
        featureKey={PERMISSIONS.FILE_TYPE.DELETE}
        additionalCheck={canDelete}
        fallback={
          <Tooltip title={
            !canDelete && fileType.hasVersions 
              ? "该文件类型已有版本，无法删除"
              : "无删除权限"
          }>
            <span>
              <IconButton size="small" disabled>
                <DeleteIcon fontSize="small" />
              </IconButton>
            </span>
          </Tooltip>
        }
      >
        <Tooltip title="删除">
          <IconButton
            size="small"
            color="error"
            onClick={() => onDelete(fileType)}
          >
            <DeleteIcon fontSize="small" />
          </IconButton>
        </Tooltip>
      </FeatureGuard>
    </Box>
  );
};

// 创建按钮组件
const CreateFileTypeButton = ({ onClick }) => {
  return (
    <FeatureGuard featureKey={PERMISSIONS.FILE_TYPE.CREATE}>
      <Button
        variant="contained"
        color="primary"
        startIcon={<AddIcon />}
        onClick={onClick}
      >
        创建文件类型
      </Button>
    </FeatureGuard>
  );
};

// 文件类型编辑对话框
const FileTypeEditDialog = ({ 
  open, 
  onClose, 
  fileType, 
  onSave, 
  mode = 'create' 
}) => {
  const requiredPermission = mode === 'create' 
    ? PERMISSIONS.FILE_TYPE.CREATE 
    : PERMISSIONS.FILE_TYPE.EDIT;

  return (
    <FeatureGuard 
      featureKey={requiredPermission}
      fallback={
        <Dialog open={open} onClose={onClose}>
          <DialogTitle>权限不足</DialogTitle>
          <DialogContent>
            您没有{mode === 'create' ? '创建' : '编辑'}文件类型的权限
          </DialogContent>
          <DialogActions>
            <Button onClick={onClose}>关闭</Button>
          </DialogActions>
        </Dialog>
      }
    >
      <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
        <DialogTitle>
          {mode === 'create' ? '创建文件类型' : '编辑文件类型'}
        </DialogTitle>
        <DialogContent>
          {/* 表单内容 */}
        </DialogContent>
        <DialogActions>
          <Button onClick={onClose}>取消</Button>
          <Button
            onClick={onSave}
            variant="contained"
            color="primary"
          >
            保存
          </Button>
        </DialogActions>
      </Dialog>
    </FeatureGuard>
  );
};

// 删除确认对话框
const FileTypeDeleteDialog = ({ 
  open, 
  onClose, 
  fileType, 
  onConfirm 
}) => {
  return (
    <FeatureGuard 
      featureKey={PERMISSIONS.FILE_TYPE.DELETE}
      fallback={
        <Dialog open={open} onClose={onClose}>
          <DialogTitle>权限不足</DialogTitle>
          <DialogContent>
            您没有删除文件类型的权限
          </DialogContent>
          <DialogActions>
            <Button onClick={onClose}>关闭</Button>
          </DialogActions>
        </Dialog>
      }
    >
      <Dialog open={open} onClose={onClose}>
        <DialogTitle>确认删除</DialogTitle>
        <DialogContent>
          确定要删除文件类型 "{fileType?.code}" 吗？此操作不可撤销。
        </DialogContent>
        <DialogActions>
          <Button onClick={onClose}>取消</Button>
          <Button
            onClick={onConfirm}
            variant="contained"
            color="error"
          >
            删除
          </Button>
        </DialogActions>
      </Dialog>
    </FeatureGuard>
  );
};

// 主页面组件
// 重要：不使用页面级权限检查，只对按钮功能进行权限控制
const FileTypeListPage = () => {
  // 不要使用页面级权限检查！
  // 页面本身对所有用户开放，只对具体功能按钮进行权限控制

  return (
    <Container>
      {/* 操作按钮区域 */}
      <Box sx={{ mb: 2 }}>
        <CreateFileTypeButton onClick={handleCreate} />
      </Box>

      {/* 表格内容 */}
      <TableContainer>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>商户</TableCell>
              <TableCell>类型代码</TableCell>
              <TableCell>类型名称</TableCell>
              <TableCell>备注</TableCell>
              <TableCell>操作</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {fileTypes.map((fileType) => (
              <TableRow key={fileType.id}>
                <TableCell>{fileType.merchantName}</TableCell>
                <TableCell>{fileType.code}</TableCell>
                <TableCell>{fileType.name}</TableCell>
                <TableCell>{fileType.remark}</TableCell>
                <TableCell>
                  <FileTypeActionButtons
                    fileType={fileType}
                    onEdit={handleEdit}
                    onDelete={handleDelete}
                  />
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Container>
  );
};

export { 
  FileTypeActionButtons, 
  CreateFileTypeButton,
  FileTypeEditDialog,
  FileTypeDeleteDialog,
  FileTypeListPage 
};
