{"name": "webadmin-client", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@codemirror/lang-json": "^6.0.2", "@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.15.10", "@mui/lab": "^5.0.0-alpha.165", "@mui/material": "^5.15.10", "@mui/x-data-grid": "^6.19.4", "@mui/x-date-pickers": "^6.20.2", "@uiw/react-codemirror": "^4.24.2", "axios": "^1.6.7", "chart.js": "^4.4.1", "date-fns": "^2.29.3", "formik": "^2.4.5", "jwt-decode": "^4.0.0", "notistack": "^3.0.1", "qrcode.react": "^4.2.0", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-feather": "^2.0.10", "react-router-dom": "^6.22.1", "recharts": "^2.15.3", "yup": "^1.3.3"}, "devDependencies": {"@types/react": "^18.2.55", "@types/react-dom": "^18.2.19", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.56.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "path": "^0.12.7", "vite": "^5.1.0"}}