# 终端文件上传协议
用于终端上传文件到服务端，复用当前TCP连接，协议可以和当前8583协议区分出来，共用一个连接完成。支持断点续传。

## 1 协议说明

协议结构：
| 字段 | 长度 | 说明 |
| --- | --- | --- |
| 固定头 | 4 | 0xCCCCCCCC |
| 协议版本 | 1 | 0x01 |
| 消息类型 | 1 | 0x01,0x02,0x03,0x04,0x05,0x06 |
| 请求ID | 4 | 例如 0x00000001 自增id,服务端响应时应原样返回 |
| 预留 | 2 | 0x0000 |
| 消息体长度 | 4 | 例如 0x000000A0 |
| 消息体 | 变长 | 内容看具体的消息类型 |

## 2 消息类型

| 消息类型 | 说明 |
| --- | --- |
| 0x01 | 文件上传请求 |
| 0x02 | 文件上传响应 |
| 0x03 | 文件块传输请求 |
| 0x04 | 文件块传输响应|
| 0x05 | 断点续传查询 |
| 0x06 | 断点续传响应 |

## 3 消息体说明

### 3.1 文件上传请求 0x01

| 字段 | 长度 | 说明 |
| --- | --- | --- |
| 商户号 | 4 | 例如 0x00009998 |
| 终端序列号 | 4 | 例如 0xABCDABCD |
| 终端类型(ascii) | 3 | 例如 PLT |
| 文件类型(ascii) | 3 | 例如 LOG |
| 文件大小 | 4 | 例如 000000A0 |
| 文件CRC32 | 4 | 例如 0x289AB34F |
| 终端传输最大块大小 | 4 | 例如 0x000000FF |
| 文件名长度 | 1 | 例如 0x20 |
| 文件名(ascii) | 变长 | 例如 log20250709122111.txt |

### 3.2 文件上传响应 0x02

| 字段 | 长度 | 说明 |
| --- | --- | --- |
| 状态 | 1 | 例如 0x00 请求成功，可以开始上传 0x01 上传请求失败 |
| 上传文件ID | 16 | 例如 0x00000001000000010000000100000001 |
| 指定上传块大小 | 4 | 例如 0x000000FF |

### 3.3 文件块传输请求 03

| 字段 | 长度 | 说明 |
| --- | --- | --- |
| 上传文件ID | 16 | 例如 0x00000001000000010000000100000001 |
| 文件块偏移量 | 4 | 例如 0x00000000 |
| 文件块大小 | 4 | 例如 0x000000FF |
| 文件块内容 | 变长 | 例如 0x0102030405060708090A0B0C0D0E0F10 |

### 3.4 文件块传输响应 04

| 字段 | 长度 | 说明 |
| --- | --- | --- |
| 上传文件ID | 16 | 例如 0x00000001000000010000000100000001 |
| 传输结果 | 1 | 例如 0x00 块传输成功 0x01 块传输失败 0x02 文件ID不存在 |
| 下一个块偏移量 | 4 | 例如 0x00000100 |
| 下一个块大小 | 4 | 例如 0x000000FF |


### 3.5 断点续传查询 0x05

| 字段 | 长度 | 说明 |
| --- | --- | --- |
| 上传文件ID | 16 | 例如 0x00000001000000010000000100000001 |

### 3.6 断点续传响应 0x06

| 字段 | 长度 | 说明 |
| --- | --- | --- |
| 上传文件ID | 16 | 例如 0x00000001000000010000000100000001 |
| 查询结果 | 1 | 例如 0x00 查询成功 0x01 查询失败 0x02 文件ID不存在 |
| 下一个块偏移量 | 4 | 例如 0x00000100 |
| 下一个块大小 | 4 | 例如 0x000000FF |

