# 微前端客户定制功能部署指南

## 概述

本指南介绍如何使用微前端架构为特定客户开发和部署定制功能，同时保持主项目的通用性。

## 架构特点

- ✅ **完全隔离**：客户项目独立开发、构建、部署
- ✅ **无缝集成**：通过iframe和菜单系统集成到主应用
- ✅ **认证共享**：自动共享主应用的认证状态
- ✅ **统一体验**：用户感觉就像在同一个系统中
- ✅ **零污染**：主项目代码完全不受影响

## 快速开始

### 1. 创建客户项目

```bash
# 进入项目根目录
cd SlzrCrossGate

# 创建客户项目（以customer-a为例）
chmod +x scripts/create-customer-project.sh
./scripts/create-customer-project.sh customer-a

# 进入客户项目目录
cd SlzrCrossGate.CustomerA

# 安装前端依赖
cd ClientApp
npm install
```

### 2. 开发客户功能

#### 后端开发
```csharp
// Controllers/CustomController.cs
[ApiController]
[Route("api/[controller]")]
[Authorize] // 使用共享的JWT认证
public class CustomController : ControllerBase
{
    [HttpGet("feature")]
    public IActionResult GetCustomFeature()
    {
        return Ok(new { message = "客户定制功能" });
    }
}
```

#### 前端开发
```jsx
// ClientApp/src/pages/CustomFeaturePage.jsx
import React from 'react';
import { Typography, Box } from '@mui/material';

const CustomFeaturePage = () => {
  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4">客户定制功能</Typography>
      {/* 客户特定的功能实现 */}
    </Box>
  );
};

export default CustomFeaturePage;
```

### 3. 配置菜单集成

在主项目的数据库中添加菜单项：

```sql
-- 添加客户功能菜单分组
INSERT INTO MenuGroups (GroupKey, Title, IconName, SortOrder, IsEnabled, VisibleToSystemAdmin, VisibleToMerchantAdmin, VisibleToUser)
VALUES ('customer-features', '客户功能', 'StarIcon', 10, 1, 1, 1, 0);

-- 添加客户功能菜单项
INSERT INTO MenuItems (MenuGroupId, ItemKey, Title, Href, IconName, SortOrder, IsEnabled, VisibleToSystemAdmin, VisibleToMerchantAdmin, VisibleToUser, IsExternal, Target)
VALUES (
  (SELECT Id FROM MenuGroups WHERE GroupKey = 'customer-features'),
  'customer-a-home',
  '客户A功能',
  'http://localhost:5271',  -- 客户项目的URL
  'SettingsIcon',
  1,
  1, 1, 1, 0,
  1,  -- IsExternal = true
  '_iframe'  -- 在iframe中打开
);
```

### 4. 构建和部署

#### 开发环境
```bash
# 启动主项目
cd SlzrCrossGate.WebAdmin
dotnet run

# 启动客户项目
cd SlzrCrossGate.CustomerA
dotnet run --urls="http://localhost:5271"
```

#### 生产环境
```bash
# 构建客户项目镜像
docker build -t your-registry/slzr-customer-a:latest ./SlzrCrossGate.CustomerA/

# 使用docker-compose部署
docker-compose -f docker-compose.yml -f docker-compose.customer.yml up -d
```

## 详细配置

### Docker Compose配置

```yaml
# docker-compose.customer.yml
version: '3.8'

services:
  customer-a:
    image: your-registry/slzr-customer-a:latest
    container_name: slzr-customer-a
    ports:
      - "5271:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ConnectionStrings__DefaultConnection=Server=mysql;Database=tcpserver;Uid=root;Pwd=**********;
      - Jwt__Key=${JWT_KEY}
      - Jwt__Issuer=${JWT_ISSUER}
      - Jwt__Audience=${JWT_AUDIENCE}
    networks:
      - slzr-network
    depends_on:
      - mysql
      - webadmin
```

### Nginx配置

```nginx
# nginx/conf.d/customer.conf
# 客户A功能路由
location /customer-a/ {
    proxy_pass http://customer-a:80/;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    
    # 支持iframe嵌入
    proxy_hide_header X-Frame-Options;
    add_header X-Frame-Options "SAMEORIGIN";
}
```

## 认证机制

### JWT Token共享

客户项目自动从主应用获取JWT token：

```javascript
// 客户项目中的认证服务
import AuthService from './services/AuthService';

// 获取认证信息
const authData = await AuthService.getAuthFromMainApp();
if (authData && authData.token) {
    // 设置axios默认header
    AuthService.setAuthToken(authData.token);
}
```

### 权限控制

客户项目可以使用主应用的用户角色进行权限控制：

```csharp
[Authorize(Roles = "SystemAdmin,MerchantAdmin")]
public IActionResult AdminOnlyFeature()
{
    return Ok();
}
```

## 最佳实践

### 1. 项目命名规范
- 项目名称：`SlzrCrossGate.Customer{CustomerName}`
- 容器名称：`slzr-customer-{name}`
- 菜单标识：`customer-{name}-{feature}`

### 2. 端口分配
- 主项目：5270
- 客户A：5271
- 客户B：5272
- 以此类推...

### 3. 数据库设计
- 客户特定数据使用独立表
- 表名前缀：`Customer{Name}_`
- 保持与主项目数据库的兼容性

### 4. 前端开发规范
- 使用与主项目相同的UI组件库
- 保持一致的主题和样式
- 遵循相同的代码规范

### 5. 安全考虑
- 所有API都必须使用JWT认证
- 敏感操作需要额外的权限验证
- iframe通信使用postMessage安全机制

## 故障排除

### 常见问题

1. **认证失败**
   - 检查JWT配置是否与主项目一致
   - 确认客户项目能够接收到认证信息

2. **iframe无法加载**
   - 检查X-Frame-Options设置
   - 确认CORS配置正确

3. **菜单不显示**
   - 检查数据库菜单配置
   - 确认用户权限设置

4. **样式不一致**
   - 确认使用相同的主题配置
   - 检查CSS变量设置

### 调试技巧

```javascript
// 在客户项目中添加调试日志
console.log('Auth token:', AuthService.getToken());
console.log('User info:', userInfo);

// 监听postMessage通信
window.addEventListener('message', (event) => {
    console.log('Received message:', event.data);
});
```

## 扩展功能

### 添加新客户项目

1. 运行创建脚本：`./scripts/create-customer-project.sh customer-b`
2. 配置docker-compose
3. 添加菜单配置
4. 部署和测试

### 升级到Module Federation

如果需要更高级的集成，可以考虑升级到Webpack Module Federation：

```javascript
// webpack.config.js
const ModuleFederationPlugin = require('@module-federation/webpack');

module.exports = {
  plugins: [
    new ModuleFederationPlugin({
      name: 'customerA',
      exposes: {
        './CustomerFeature': './src/components/CustomerFeature'
      }
    })
  ]
};
```

## 总结

通过这个微前端方案，你可以：

- 为不同客户开发完全独立的定制功能
- 保持主项目的通用性和纯净性
- 提供统一的用户体验
- 简化部署和维护工作

这个方案特别适合有多个客户需要不同定制功能的场景，既满足了个性化需求，又保持了系统的可维护性。
