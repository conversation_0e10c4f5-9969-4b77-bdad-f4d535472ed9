# 阶段 1: 构建前端
FROM devtest.pointlife365.net:5180/library/node:18-alpine AS frontend-build
WORKDIR /app
# 确保package.json文件路径正确
COPY ["SlzrCrossGate.CustomerTemplate/ClientApp/package*.json", "./"]
RUN npm install
COPY ["SlzrCrossGate.CustomerTemplate/ClientApp/", "./"]
# 设置环境变量控制输出目录和基础路径
ENV NODE_ENV=production
ENV VITE_BASE_PATH=/customer/
# 添加调试信息
RUN npm run build && echo "前端构建完成" && ls -la

# 阶段 2: 构建后端
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src
COPY ["SlzrCrossGate.CustomerTemplate/SlzrCrossGate.CustomerTemplate.csproj", "SlzrCrossGate.CustomerTemplate/"]
COPY ["SlzrCrossGate.Core/SlzrCrossGate.Core.csproj", "SlzrCrossGate.Core/"]
COPY ["SlzrCrossGate.Common/SlzrCrossGate.Common.csproj", "SlzrCrossGate.Common/"]
RUN dotnet restore "SlzrCrossGate.CustomerTemplate/SlzrCrossGate.CustomerTemplate.csproj"
COPY . .
WORKDIR "/src/SlzrCrossGate.CustomerTemplate"
RUN dotnet build "SlzrCrossGate.CustomerTemplate.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "SlzrCrossGate.CustomerTemplate.csproj" -c Release -o /app/publish

# 阶段 3: 最终镜像
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS final
WORKDIR /app
EXPOSE 80

# 将预下载的curl二进制文件复制到镜像中
COPY curl-amd64 /usr/local/bin/curl
RUN chmod +x /usr/local/bin/curl && \
    /usr/local/bin/curl --version

# 复制启动脚本
COPY ["SlzrCrossGate.CustomerTemplate/scripts/entrypoint.sh", "/entrypoint.sh"]
RUN chmod +x /entrypoint.sh

COPY --from=publish /app/publish .
# 创建wwwroot目录（如果不存在）
RUN mkdir -p ./wwwroot
# 复制前端构建结果 - 现在使用标准的dist目录
COPY --from=frontend-build /app/dist/ ./wwwroot/

# 创建日志目录
RUN mkdir -p /app/logs && chmod -R 755 /app/logs

# 设置环境变量
ENV ASPNETCORE_URLS=http://+:80
ENV ASPNETCORE_ENVIRONMENT=Production

# 定义持久卷
VOLUME ["/app/logs"]

# 使用启动脚本作为入口点
ENTRYPOINT ["/entrypoint.sh"]
CMD ["dotnet", "SlzrCrossGate.CustomerTemplate.dll"]
