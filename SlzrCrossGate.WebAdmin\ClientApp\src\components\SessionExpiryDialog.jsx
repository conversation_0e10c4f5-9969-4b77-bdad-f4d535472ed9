import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Button,
  Box,
  Typography,
  LinearProgress,
  Chip
} from '@mui/material';
import { AccessTime, Warning } from '@mui/icons-material';

const SessionExpiryDialog = ({ 
  open, 
  timeRemaining, 
  onContinue, 
  onLogout 
}) => {
  const minutes = Math.ceil(timeRemaining);
  const isUrgent = minutes <= 2;

  return (
    <Dialog
      open={open}
      disableEscapeKeyDown
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          boxShadow: '0 8px 32px rgba(0,0,0,0.12)',
        }
      }}
    >
      <DialogTitle sx={{ 
        display: 'flex', 
        alignItems: 'center', 
        gap: 1,
        pb: 1,
        borderBottom: '1px solid',
        borderColor: 'divider'
      }}>
        <Warning color={isUrgent ? 'error' : 'warning'} />
        <Typography variant="h6" component="span">
          会话即将过期
        </Typography>
        <Chip 
          icon={<AccessTime />}
          label={`${minutes} 分钟`}
          color={isUrgent ? 'error' : 'warning'}
          size="small"
          sx={{ ml: 'auto' }}
        />
      </DialogTitle>
      
      <DialogContent sx={{ pt: 3 }}>
        <Box sx={{ mb: 3 }}>
          <DialogContentText sx={{ mb: 2, fontSize: '1rem' }}>
            您已经长时间未操作，为了保护您的账户安全，系统将在 
            <Typography 
              component="span" 
              color={isUrgent ? 'error.main' : 'warning.main'}
              fontWeight="bold"
              sx={{ mx: 0.5 }}
            >
              {minutes} 分钟
            </Typography>
            后自动退出登录。
          </DialogContentText>
          
          <DialogContentText color="text.secondary">
            点击"继续会话"可延长登录时间，或点击"立即退出"安全退出系统。
          </DialogContentText>
        </Box>

        {/* 进度条显示剩余时间 */}
        <Box sx={{ mb: 2 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
            <Typography variant="caption" color="text.secondary">
              剩余时间
            </Typography>
            <Typography variant="caption" color={isUrgent ? 'error.main' : 'warning.main'}>
              {minutes} 分钟
            </Typography>
          </Box>
          <LinearProgress 
            variant="determinate" 
            value={Math.max(0, Math.min(100, (timeRemaining / 5) * 100))} // 假设警告时间为5分钟
            color={isUrgent ? 'error' : 'warning'}
            sx={{ 
              height: 6, 
              borderRadius: 3,
              backgroundColor: 'rgba(0,0,0,0.1)'
            }}
          />
        </Box>
      </DialogContent>
      
      <DialogActions sx={{ 
        px: 3, 
        pb: 3, 
        gap: 1,
        borderTop: '1px solid',
        borderColor: 'divider',
        pt: 2
      }}>
        <Button 
          onClick={onLogout} 
          color="inherit"
          variant="outlined"
          sx={{ 
            minWidth: 100,
            borderColor: 'divider',
            '&:hover': {
              borderColor: 'text.primary',
              backgroundColor: 'action.hover'
            }
          }}
        >
          立即退出
        </Button>
        <Button 
          onClick={onContinue} 
          color="primary"
          variant="contained"
          sx={{ 
            minWidth: 120,
            boxShadow: 2,
            '&:hover': {
              boxShadow: 4
            }
          }}
          autoFocus
        >
          继续会话
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default SessionExpiryDialog;
