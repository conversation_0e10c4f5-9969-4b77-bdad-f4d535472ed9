import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';

// https://vitejs.dev/config/
export default defineConfig(({ command, mode }) => {
  // 根据环境设置 base 路径
  // 生产环境使用 VITE_BASE_PATH 环境变量，开发环境使用根路径
  const base = process.env.VITE_BASE_PATH || (mode === 'production' ? '/customer/' : '/');

  return {
    base: base,
    plugins: [react()],
    define: {
      // 为客户端代码提供环境变量
      'process.env.NODE_ENV': JSON.stringify(mode),
      'process.env.REACT_APP_BASE_PATH': JSON.stringify(base),
    },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  server: {
    port: 3001,
    proxy: mode === 'development' ? {
      '/api': {
        target: 'https://localhost:7296',
        changeOrigin: true,
        secure: false,
      },
    } : {},
  },
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: true,
  },
  optimizeDeps: {
    esbuildOptions: {
      loader: { '.js': 'jsx' },
    },
  },
  };
});
