@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo === 珠海通客户项目启动脚本 ===

REM 检查是否在正确的目录
if not exist "SlzrCrossGate.CustomerZhuhaitong.csproj" (
    echo 错误: 请在项目根目录运行此脚本
    pause
    exit /b 1
)

REM 1. 检查数据库连接
echo 1. 检查数据库连接...
mysql -h localhost -u root -pslzr!12345 -e "USE tcpserver; SHOW TABLES LIKE 'Customer_Zhuhaitong_%%';" >nul 2>&1
if errorlevel 1 (
    echo 警告: 数据库连接失败或表不存在
    echo 请先执行: mysql -u root -p ^< setup-database.sql
    set /p continue="是否继续启动项目? (y/n): "
    if /i not "!continue!"=="y" exit /b 1
)

REM 2. 还原后端依赖
echo 2. 还原后端依赖...
dotnet restore
if errorlevel 1 (
    echo 错误: 后端依赖还原失败
    pause
    exit /b 1
)

REM 3. 检查前端依赖
echo 3. 检查前端依赖...
if not exist "ClientApp\node_modules" (
    echo 安装前端依赖...
    cd ClientApp
    call npm install
    if errorlevel 1 (
        echo 错误: 前端依赖安装失败
        pause
        exit /b 1
    )
    cd ..
)

REM 4. 启动前端开发服务器（后台运行）
echo 4. 启动前端开发服务器...
cd ClientApp
start "前端服务器" cmd /c "npm start"
cd ..

REM 等待前端服务器启动
echo 等待前端服务器启动...
timeout /t 5 /nobreak >nul

REM 5. 启动后端服务器
echo 5. 启动后端服务器...
echo 前端地址: http://localhost:3001
echo 后端地址: http://localhost:5271
echo API文档: http://localhost:5271/swagger
echo.
echo 按 Ctrl+C 停止服务器

REM 启动后端
dotnet run --urls="http://localhost:5271"

pause
