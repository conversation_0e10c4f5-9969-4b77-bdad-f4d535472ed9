import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  TablePagination,
  Chip,
  Grid,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Alert,
  CircularProgress,
  Tooltip,
  IconButton
} from '@mui/material';
import {
  Search as SearchIcon,
  Clear as ClearIcon,
  Download as DownloadIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { zhCN } from 'date-fns/locale';
import { useSnackbar } from 'notistack';
import { auditLogAPI } from '../../services/api';
import { useAuth } from '../../contexts/AuthContext';
import MerchantAutocomplete from '../../components/MerchantAutocomplete';
import { formatDateTime } from '../../utils/dateUtils';

const OperationLogs = () => {
  const { user } = useAuth();
  const { enqueueSnackbar } = useSnackbar();
  
  // 状态管理
  const [logs, setLogs] = useState([]);
  const [loading, setLoading] = useState(false);
  const [exporting, setExporting] = useState(false);
  const [totalCount, setTotalCount] = useState(0);
  const [page, setPage] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  // 移除手动商户管理，使用MerchantAutocomplete内置功能
  const [selectedMerchant, setSelectedMerchant] = useState(null);

  // 查询条件
  const [filters, setFilters] = useState({
    userName: '',
    realName: '',
    merchantId: '',
    ipAddress: '',
    module: '',
    operationType: '',
    operationTarget: '',
    requestPath: '',
    httpMethod: '',
    startTime: null,
    endTime: null,
    isSuccess: ''
  });

  // 操作模块选项
  const moduleOptions = [
    { value: '', label: '全部' },
    { value: '仪表盘', label: '仪表盘' },
    { value: '用户管理', label: '用户管理' },
    { value: '角色管理', label: '角色管理' },
    { value: '商户管理', label: '商户管理' },
    { value: '终端管理', label: '终端管理' },
    { value: '车辆管理', label: '车辆管理' },
    { value: '文件管理', label: '文件管理' },
    { value: '预约管理', label: '预约发布' },
    { value: '消息管理', label: '消息管理' },
    { value: '系统设置', label: '系统设置' },
    { value: '字典管理', label: '字典管理' },
    { value: '票价参数', label: '票价参数' },
    { value: '票价折扣方案', label: '票价折扣方案' },
    { value: '银联密钥管理', label: '银联密钥管理' },
    { value: '终端记录', label: '终端记录' },
    { value: '终端事件', label: '终端事件' },
    { value: '终端代理', label: '终端代理' },
    { value: '终端日志记录', label: '终端日志记录' },
    { value: '未注册线路', label: '未注册线路' },
    { value: '菜单管理', label: '菜单管理' },
    { value: '审计日志', label: '审计日志' },
    { value: '会话管理', label: '会话管理' },
    { value: '身份认证', label: '身份认证' },
    { value: '权限管理', label: '权限管理' },
    { value: '其他', label: '其他' }
  ];

  // 操作类型选项
  const operationTypeOptions = [
    { value: '', label: '全部' },
    { value: '创建', label: '创建' },
    { value: '更新', label: '更新' },
    { value: '删除', label: '删除' },
    { value: '查询', label: '查询' },
    { value: '导出', label: '导出' },
    { value: '导入', label: '导入' },
    { value: '发布', label: '发布' },
    { value: '发送', label: '发送' }
  ];

  // HTTP方法选项
  const httpMethodOptions = [
    { value: '', label: '全部' },
    { value: 'GET', label: 'GET' },
    { value: 'POST', label: 'POST' },
    { value: 'PUT', label: 'PUT' },
    { value: 'DELETE', label: 'DELETE' }
  ];

  // 成功状态选项
  const successOptions = [
    { value: '', label: '全部' },
    { value: 'true', label: '成功' },
    { value: 'false', label: '失败' }
  ];

  // 防重复请求
  const loadLogsRef = useRef();
  const hasLoadedRef = useRef(false);

  // 移除手动商户加载，使用MerchantAutocomplete内置功能

  // 加载操作日志
  const loadLogs = async (isInitialLoad = false, forceLoad = false) => {
    try {
      setLoading(true);

      const params = {
        page: page + 1,
        pageSize,
        ...filters,
        merchantId: selectedMerchant?.merchantID || undefined, // 使用selectedMerchant
        isSuccess: filters.isSuccess === '' ? undefined : filters.isSuccess === 'true'
      };

      // 移除filters中的merchantId，避免重复
      if (params.merchantId === undefined) {
        delete params.merchantId;
      }

      // 移除空值
      Object.keys(params).forEach(key => {
        if (params[key] === '' || params[key] === null || params[key] === undefined) {
          delete params[key];
        }
      });

      // 避免重复请求：检查参数是否真的发生了变化（除非强制加载）
      const paramsString = JSON.stringify(params);
      if (!isInitialLoad && !forceLoad && loadLogsRef.current === paramsString) {
        console.log('OperationLogs: 参数未变化，跳过重复请求');
        setLoading(false);
        return;
      }
      loadLogsRef.current = paramsString;

      console.log('OperationLogs: 执行数据请求', params);
      const response = await auditLogAPI.getOperationLogs(params);
      setLogs(response.items || []);
      setTotalCount(response.totalCount || 0);
    } catch (error) {
      console.error('加载操作日志失败:', error);
      enqueueSnackbar('加载操作日志失败', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };

  // 统一的数据加载逻辑
  useEffect(() => {
    // 防止重复加载
    if (hasLoadedRef.current) {
      console.log('OperationLogs: 已加载过，跳过重复请求');
      return;
    }

    console.log('OperationLogs: 执行首次加载');
    hasLoadedRef.current = true;
    loadLogs(true, false); // 标记为初始加载，非强制加载
  }, []);

  // 当参数变化时重新加载
  useEffect(() => {
    if (hasLoadedRef.current) { // 只有在首次加载完成后才响应参数变化
      console.log('OperationLogs: 参数变化，重新加载');
      loadLogs(false, false); // 非初始加载，非强制加载
    }
  }, [page, pageSize, selectedMerchant]);

  // 处理筛选条件变化
  const handleFilterChange = (field, value) => {
    setFilters(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // 搜索
  const handleSearch = () => {
    setPage(0);
    loadLogs(false, true); // 强制执行搜索
  };

  // 清除筛选条件
  const handleClearFilters = () => {
    setFilters({
      userName: '',
      realName: '',
      merchantId: '', // 保留这个字段，但实际使用selectedMerchant
      ipAddress: '',
      module: '',
      operationType: '',
      operationTarget: '',
      requestPath: '',
      httpMethod: '',
      startTime: null,
      endTime: null,
      isSuccess: ''
    });
    setSelectedMerchant(null); // 清除商户选择
    setPage(0);
  };

  // 刷新
  const handleRefresh = () => {
    loadLogs(false, true); // 强制执行刷新
  };

  // 导出
  const handleExport = async () => {
    setExporting(true);
    try {
      const params = {
        ...filters,
        isSuccess: filters.isSuccess === '' ? undefined : filters.isSuccess === 'true'
      };

      // 移除空值
      Object.keys(params).forEach(key => {
        if (params[key] === '' || params[key] === null || params[key] === undefined) {
          delete params[key];
        }
      });

      const blob = await auditLogAPI.exportOperationLogs(params);
      
      // 创建下载链接
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `操作日志_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.csv`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      enqueueSnackbar('导出成功', { variant: 'success' });
    } catch (error) {
      console.error('导出失败:', error);
      enqueueSnackbar('导出失败', { variant: 'error' });
    } finally {
      setExporting(false);
    }
  };

  // 处理分页变化
  const handlePageChange = (event, newPage) => {
    setPage(newPage);
  };

  const handlePageSizeChange = (event) => {
    setPageSize(parseInt(event.target.value, 10));
    setPage(0);
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={zhCN}>
      <Box sx={{ p: 3 }}>
        <Typography variant="h4" gutterBottom>
          操作日志
        </Typography>

        {/* 筛选条件 */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Grid container spacing={2} alignItems="center">
              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  fullWidth
                  label="用户名"
                  value={filters.userName}
                  onChange={(e) => handleFilterChange('userName', e.target.value)}
                  size="small"
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  fullWidth
                  label="真实姓名"
                  value={filters.realName}
                  onChange={(e) => handleFilterChange('realName', e.target.value)}
                  size="small"
                />
              </Grid>
              {user?.roles?.includes('SystemAdmin') && (
                <Grid item xs={12} sm={6} md={3}>
                  <MerchantAutocomplete
                    size="small"
                    value={selectedMerchant?.merchantID}
                    onChange={(event, newValue) => {
                      setSelectedMerchant(newValue);
                    }}
                    label="商户（留空表示全部）"
                  />
                </Grid>
              )}
              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  fullWidth
                  label="IP地址"
                  value={filters.ipAddress}
                  onChange={(e) => handleFilterChange('ipAddress', e.target.value)}
                  size="small"
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <FormControl fullWidth size="small">
                  <InputLabel>操作模块</InputLabel>
                  <Select
                    value={filters.module}
                    label="操作模块"
                    onChange={(e) => handleFilterChange('module', e.target.value)}
                  >
                    {moduleOptions.map((option) => (
                      <MenuItem key={option.value} value={option.value}>
                        {option.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <FormControl fullWidth size="small">
                  <InputLabel>操作类型</InputLabel>
                  <Select
                    value={filters.operationType}
                    label="操作类型"
                    onChange={(e) => handleFilterChange('operationType', e.target.value)}
                  >
                    {operationTypeOptions.map((option) => (
                      <MenuItem key={option.value} value={option.value}>
                        {option.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  fullWidth
                  label="操作对象"
                  value={filters.operationTarget}
                  onChange={(e) => handleFilterChange('operationTarget', e.target.value)}
                  size="small"
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  fullWidth
                  label="请求路径"
                  value={filters.requestPath}
                  onChange={(e) => handleFilterChange('requestPath', e.target.value)}
                  size="small"
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <FormControl fullWidth size="small">
                  <InputLabel>HTTP方法</InputLabel>
                  <Select
                    value={filters.httpMethod}
                    label="HTTP方法"
                    onChange={(e) => handleFilterChange('httpMethod', e.target.value)}
                  >
                    {httpMethodOptions.map((option) => (
                      <MenuItem key={option.value} value={option.value}>
                        {option.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <FormControl fullWidth size="small">
                  <InputLabel>结果</InputLabel>
                  <Select
                    value={filters.isSuccess}
                    label="结果"
                    onChange={(e) => handleFilterChange('isSuccess', e.target.value)}
                  >
                    {successOptions.map((option) => (
                      <MenuItem key={option.value} value={option.value}>
                        {option.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <DateTimePicker
                  label="开始时间"
                  value={filters.startTime}
                  onChange={(value) => handleFilterChange('startTime', value)}
                  slotProps={{
                    textField: {
                      size: 'small',
                      fullWidth: true
                    }
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <DateTimePicker
                  label="结束时间"
                  value={filters.endTime}
                  onChange={(value) => handleFilterChange('endTime', value)}
                  slotProps={{
                    textField: {
                      size: 'small',
                      fullWidth: true
                    }
                  }}
                />
              </Grid>
            </Grid>

            {/* 操作按钮 */}
            <Box sx={{ mt: 2, display: 'flex', gap: 1 }}>
              <Button
                variant="contained"
                startIcon={<SearchIcon />}
                onClick={handleSearch}
                disabled={loading}
              >
                搜索
              </Button>
              <Button
                variant="outlined"
                startIcon={<ClearIcon />}
                onClick={handleClearFilters}
              >
                清除
              </Button>
              <Button
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={handleRefresh}
                disabled={loading}
              >
                刷新
              </Button>
              <Button
                variant="outlined"
                startIcon={exporting ? <CircularProgress size={16} /> : <DownloadIcon />}
                onClick={handleExport}
                disabled={exporting}
              >
                导出
              </Button>
            </Box>
          </CardContent>
        </Card>

        {/* 数据表格 */}
        <Card>
          <CardContent>
            {loading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                <CircularProgress />
              </Box>
            ) : (
              <>
                <TableContainer component={Paper}>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>用户名</TableCell>
                        <TableCell>真实姓名</TableCell>
                        {user?.roles?.includes('SystemAdmin') && <TableCell>商户</TableCell>}
                        <TableCell>操作模块</TableCell>
                        <TableCell>操作类型</TableCell>
                        <TableCell>操作对象</TableCell>
                        <TableCell>请求路径</TableCell>
                        <TableCell>HTTP方法</TableCell>
                        <TableCell>操作时间</TableCell>
                        <TableCell>耗时(ms)</TableCell>
                        <TableCell>结果</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {logs.map((log) => (
                        <TableRow key={log.id}>
                          <TableCell>{log.userName}</TableCell>
                          <TableCell>{log.realName}</TableCell>
                          {user?.roles?.includes('SystemAdmin') && (
                            <TableCell>{log.merchantName || log.merchantId}</TableCell>
                          )}
                          <TableCell>
                            <Chip
                              label={log.module}
                              color="primary"
                              variant="outlined"
                              size="small"
                            />
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={log.operationType}
                              color="secondary"
                              variant="outlined"
                              size="small"
                            />
                          </TableCell>
                          <TableCell>
                            {log.operationTarget && (
                              <Tooltip title={log.operationTarget}>
                                <span>{log.operationTarget.length > 15 ? `${log.operationTarget.substring(0, 15)}...` : log.operationTarget}</span>
                              </Tooltip>
                            )}
                          </TableCell>
                          <TableCell>
                            <Tooltip title={log.requestPath}>
                              <span>{log.requestPath && log.requestPath.length > 20 ? `${log.requestPath.substring(0, 20)}...` : log.requestPath}</span>
                            </Tooltip>
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={log.httpMethod}
                              color={log.httpMethod === 'GET' ? 'info' : log.httpMethod === 'POST' ? 'success' : log.httpMethod === 'PUT' ? 'warning' : 'error'}
                              size="small"
                            />
                          </TableCell>
                          <TableCell>{formatDateTime(log.operationTime)}</TableCell>
                          <TableCell>{log.executionTime}</TableCell>
                          <TableCell>
                            <Chip
                              label={log.isSuccess ? '成功' : '失败'}
                              color={log.isSuccess ? 'success' : 'error'}
                              size="small"
                            />
                          </TableCell>
                        </TableRow>
                      ))}
                      {logs.length === 0 && (
                        <TableRow>
                          <TableCell colSpan={user?.roles?.includes('SystemAdmin') ? 11 : 10} align="center">
                            暂无数据
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </TableContainer>

                <TablePagination
                  component="div"
                  count={totalCount}
                  page={page}
                  onPageChange={handlePageChange}
                  rowsPerPage={pageSize}
                  onRowsPerPageChange={handlePageSizeChange}
                  rowsPerPageOptions={[5, 10, 25, 50]}
                  labelRowsPerPage="每页行数:"
                  labelDisplayedRows={({ from, to, count }) => `${from}-${to} 共 ${count} 条`}
                />
              </>
            )}
          </CardContent>
        </Card>
      </Box>
    </LocalizationProvider>
  );
};

export default OperationLogs;
