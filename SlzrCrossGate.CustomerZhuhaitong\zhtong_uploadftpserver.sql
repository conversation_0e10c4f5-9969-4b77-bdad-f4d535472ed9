/*
 Navicat Premium Dump SQL

 Source Server         : ZhtongMySql
 Source Server Type    : MySQL
 Source Server Version : 50740 (5.7.40-log)
 Source Host           : rm-wz98th087i908ftco4o.mysql.rds.aliyuncs.com:3306
 Source Schema         : zhtong_busdb

 Target Server Type    : MySQL
 Target Server Version : 50740 (5.7.40-log)
 File Encoding         : 65001

 Date: 25/07/2025 12:06:45
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for zhtong_uploadftpserver
-- ----------------------------
DROP TABLE IF EXISTS `zhtong_uploadftpserver`;
CREATE TABLE `zhtong_uploadftpserver`  (
  `ID` int(11) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `UpOrder` int(11) NULL DEFAULT NULL COMMENT '上传次序号',
  `isEnable` int(11) NULL DEFAULT NULL COMMENT '是否启用',
  `protocol` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '传输协议,ftp还是SFTP',
  `ftpIp` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '远程地址',
  `ftpPort` int(11) NULL DEFAULT NULL COMMENT '远程端口',
  `ftpUser` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '远程用户',
  `ftpPassword` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '远程密码',
  `originPath312` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '312文件源路径',
  `LocalBakPath312` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '上传成功后312文件备份路径',
  `originPath313` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '313文件源路径',
  `LocalBakPath313` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '上传成功后313文件备份路径',
  `originPath315` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '315文件源地址',
  `LocalBakPath315` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '上传成功后315文件备份路径',
  `RemotePathTemp` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '远程临时目录,为空则代表没有远程临时目录',
  `RemotePath312` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '312文件远程目录,为空则代表312文件不发送',
  `RemotePath313` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '313文件远程目录,为空则代表313文件不发送',
  `RemotePath315` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '315文件远程目录,为空则代表315文件不发送',
  `sServerDesc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'FTP服务器描述',
  `FtpType` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'FTP类型',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 16 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of zhtong_uploadftpserver  "BLACK@list851"
-- ----------------------------
INSERT INTO `zhtong_uploadftpserver` VALUES (9, 1, 0, 'ftp', '172.19.101.71', 21, 'blacklist', 'BLACK@list851', '/DataPacks/312/origin', '/DataPacks/312/cxt', '', '', '/DataPacks315/origin', '/DataPacks/315/cxt', NULL, '/TDfile/chexuntong', '', '/TDfile/chexuntong', '车讯通', NULL);
INSERT INTO `zhtong_uploadftpserver` VALUES (10, 3, 0, 'ftp', '172.19.101.71', 21, 'blacklist', 'BLACK@list851', '/DataPacks/312/cxt', '/DataPacks/312/clearing', NULL, NULL, '/DataPacks/315/first', '/DataPacks/315/clearing', NULL, '/TDfile/gongjiao', NULL, '/TDfile/gongjiao', '公交二级', NULL);
INSERT INTO `zhtong_uploadftpserver` VALUES (11, 2, 0, 'ftp', '172.19.101.71', 21, 'blacklist', 'BLACK@list851', NULL, NULL, NULL, NULL, '/DataPacks/315/cxt', '/DataPacks/315/first', NULL, NULL, NULL, '/TDfile/yiji', '一级清算', NULL);
INSERT INTO `zhtong_uploadftpserver` VALUES (12, 4, 0, 'ftp', '172.19.101.71', 21, 'blacklist', 'BLACK@list851', '/DataPacks/312/clearing', '/DataPacks/312/info', NULL, NULL, '/DataPacks/315/clearing', '/DataPacks/315/info', NULL, '/TDfile/jiaotong', NULL, '/TDfile/jiaotong', '交通信息中心', NULL);
INSERT INTO `zhtong_uploadftpserver` VALUES (13, 5, 0, 'ftp', '172.19.101.71', 21, 'blacklist', 'BLACK@list851', '/DataPacks/312/info', '/DataPacks/312/tuowang', NULL, NULL, '/DataPacks/315/info', '/DataPacks/315/tuowang', NULL, '/TDfile/tuowang', NULL, '/TDfile/tuowang', '托旺', NULL);
INSERT INTO `zhtong_uploadftpserver` VALUES (14, 6, 0, 'ftp', '172.19.101.71', 21, 'blacklist', 'BLACK@list851', '/DataPacks/312/tuowang', '/DataPacks/312/safe', NULL, NULL, '/DataPacks/315/tuowang', '/DataPacks/315/safe', NULL, '/TDfile/gongan', NULL, '/TDfile/gongan', '公安信息中心', NULL);
INSERT INTO `zhtong_uploadftpserver` VALUES (15, 7, 0, 'ftp', '172.19.101.71', 21, 'blacklist', 'BLACK@list851', '/DataPacks/312/safe', '/DataPacks/312/car', NULL, NULL, '/DataPacks/315/safe', '/DataPacks/315/car', NULL, '/TDfile/diaodu', NULL, '/TDfile/diaodu', '调度', NULL);

SET FOREIGN_KEY_CHECKS = 1;
