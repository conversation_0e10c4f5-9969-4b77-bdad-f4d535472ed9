using System.Security.Claims;
using SlzrCrossGate.Core.Models;

namespace SlzrCrossGate.Core.Services
{
    /// <summary>
    /// 功能权限服务接口
    /// </summary>
    public interface IFeaturePermissionService
    {
        /// <summary>
        /// 检查用户是否有指定功能权限
        /// </summary>
        Task<bool> HasPermissionAsync(ClaimsPrincipal user, string featureKey);
        
        /// <summary>
        /// 批量检查用户权限
        /// </summary>
        Task<Dictionary<string, bool>> CheckMultiplePermissionsAsync(ClaimsPrincipal user, IEnumerable<string> featureKeys);
        
        /// <summary>
        /// 获取用户所有权限
        /// </summary>
        Task<Dictionary<string, bool>> GetUserPermissionsAsync(ClaimsPrincipal user);
        
        /// <summary>
        /// 刷新权限缓存
        /// </summary>
        Task RefreshPermissionCacheAsync(string? userId = null);
        
        /// <summary>
        /// 获取功能配置列表
        /// </summary>
        Task<List<FeatureConfigDto>> GetFeatureConfigsAsync();
        
        /// <summary>
        /// 更新功能配置
        /// </summary>
        Task UpdateFeatureConfigAsync(string featureKey, bool isEnabled);
        
        /// <summary>
        /// 更新角色权限
        /// </summary>
        Task UpdateRolePermissionAsync(string roleName, string featureKey, bool? isEnabled);
        
        /// <summary>
        /// 批量更新功能配置
        /// </summary>
        Task BatchUpdateConfigsAsync(List<FeatureConfigUpdateDto> updates);
        
        /// <summary>
        /// 初始化默认功能配置
        /// </summary>
        Task InitializeDefaultConfigsAsync();
    }

    /// <summary>
    /// 功能配置DTO
    /// </summary>
    public class FeatureConfigDto
    {
        public string FeatureKey { get; set; } = string.Empty;
        public string FeatureName { get; set; } = string.Empty;
        public string? Description { get; set; }
        public string Category { get; set; } = string.Empty;
        public string RiskLevel { get; set; } = string.Empty;
        public bool IsGloballyEnabled { get; set; }
        public bool IsSystemBuiltIn { get; set; }
        public int SortOrder { get; set; }
        public Dictionary<string, bool?> RolePermissions { get; set; } = new();
    }

    /// <summary>
    /// 批量更新请求DTO
    /// </summary>
    public class BatchUpdateRequest
    {
        public List<FeatureConfigUpdateDto> Updates { get; set; } = new();
    }

    /// <summary>
    /// 功能配置更新DTO
    /// </summary>
    public class FeatureConfigUpdateDto
    {
        public string FeatureKey { get; set; } = string.Empty;
        public bool IsGloballyEnabled { get; set; }
        public Dictionary<string, bool?> RolePermissions { get; set; } = new();
    }
}
