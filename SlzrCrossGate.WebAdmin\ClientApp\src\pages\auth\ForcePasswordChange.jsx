import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Paper,
  Typography,
  TextField,
  Button,
  Alert,
  InputAdornment,
  IconButton,
  Container,
  Card,
  CardContent
} from '@mui/material';
import { LoadingButton } from '@mui/lab';
import { Visibility, VisibilityOff, Lock, Security } from '@mui/icons-material';
import { useSnackbar } from 'notistack';
import { authAPI } from '../../services/api';
import { useAuth } from '../../contexts/AuthContext';
import { parseErrorMessage } from '../../utils/errorHandler';

const ForcePasswordChange = () => {
  const [formData, setFormData] = useState({
    newPassword: '',
    confirmPassword: ''
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const { enqueueSnackbar } = useSnackbar();
  const { completePasswordChange, requireTwoFactorAfterPasswordChange, tempToken } = useAuth();
  const navigate = useNavigate();

  const handleInputChange = (field) => (event) => {
    setFormData({
      ...formData,
      [field]: event.target.value
    });
    // 清除错误信息
    if (error) setError('');
  };

  const handleSubmit = async (event) => {
    event.preventDefault();

    // 检查临时令牌
    if (!tempToken) {
      setError('会话已过期，请重新登录');
      return;
    }

    // 验证表单
    if (!formData.newPassword) {
      setError('请输入新密码');
      return;
    }

    if (formData.newPassword.length < 6) {
      setError('密码长度至少为6位');
      return;
    }

    if (formData.newPassword !== formData.confirmPassword) {
      setError('两次输入的密码不一致');
      return;
    }

    setLoading(true);
    setError('');

    try {
      console.log('开始强制密码更改请求...');
      const response = await authAPI.forcePasswordChange({
        newPassword: formData.newPassword,
        confirmPassword: formData.confirmPassword
      }, tempToken);

      console.log('密码更改API响应:', response);

      // 密码更改成功，直接使用返回的令牌登录
      if (response.token) {
        enqueueSnackbar('密码更改成功，正在登录...', { variant: 'success' });

        // 使用新令牌完成登录
        console.log('开始完成密码更改登录...');
        const result = await completePasswordChange(response.token);
        console.log('密码更改登录结果:', result);

        if (result.success) {
          // 登录成功，跳转到主页面
          console.log('登录成功，跳转到主页面');
          navigate('/app/dashboard');
        } else {
          console.error('登录失败:', result.message);
          setError(result.message || '登录失败，请重新尝试');
        }
      } else {
        setError('服务器响应异常，请重新尝试');
      }

    } catch (err) {
      console.error('强制密码更改失败:', err);
      const errorMessage = parseErrorMessage(err, '密码更改失败');
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container maxWidth="sm">
      <Box
        sx={{
          minHeight: '100vh',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          py: 3
        }}
      >
        <Card
          elevation={8}
          sx={{
            width: '100%',
            background: 'rgba(255, 255, 255, 0.1)',
            backdropFilter: 'blur(10px)',
            borderRadius: 3,
            boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
            border: '1px solid rgba(255, 255, 255, 0.2)',
          }}
        >
          <CardContent sx={{ p: 4 }}>
            {/* 标题 */}
            <Box sx={{ textAlign: 'center', mb: 4 }}>
              <Security
                sx={{
                  fontSize: 48,
                  color: 'primary.main',
                  mb: 2
                }}
              />
              <Typography variant="h4" gutterBottom>
                强制密码更改
              </Typography>
              <Typography variant="body1" color="text.secondary">
                为了您的账户安全，系统要求您更改密码
              </Typography>
            </Box>

            {/* 警告信息 */}
            <Alert severity="warning" sx={{ mb: 3 }}>
              您的密码已使用较长时间，为了保障账户安全，请设置一个新密码。
            </Alert>

            {/* 错误信息 */}
            {error && (
              <Alert severity="error" sx={{ mb: 3 }}>
                {error}
              </Alert>
            )}

            {/* 表单 */}
            <Box component="form" onSubmit={handleSubmit}>
              {/* 新密码 */}
              <TextField
                fullWidth
                label="新密码"
                type={showPassword ? 'text' : 'password'}
                value={formData.newPassword}
                onChange={handleInputChange('newPassword')}
                margin="normal"
                required
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Lock color="action" />
                    </InputAdornment>
                  ),
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        onClick={() => setShowPassword(!showPassword)}
                        edge="end"
                      >
                        {showPassword ? <VisibilityOff /> : <Visibility />}
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
                helperText="密码长度至少为6位"
              />

              {/* 确认密码 */}
              <TextField
                fullWidth
                label="确认新密码"
                type={showConfirmPassword ? 'text' : 'password'}
                value={formData.confirmPassword}
                onChange={handleInputChange('confirmPassword')}
                margin="normal"
                required
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Lock color="action" />
                    </InputAdornment>
                  ),
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                        edge="end"
                      >
                        {showConfirmPassword ? <VisibilityOff /> : <Visibility />}
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
                helperText="请再次输入新密码"
              />

              {/* 提交按钮 */}
              <LoadingButton
                type="submit"
                fullWidth
                variant="contained"
                loading={loading}
                sx={{ mt: 3, mb: 2, py: 1.5 }}
                size="large"
              >
                更改密码并登录
              </LoadingButton>
            </Box>

            {/* 提示信息 */}
            <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', mt: 2 }}>
              密码更改成功后，您将自动登录到系统
            </Typography>
          </CardContent>
        </Card>
      </Box>
    </Container>
  );
};

export default ForcePasswordChange;
