{
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;Database=slzrcrossgate;User=root;Password=password;AllowLoadLocalInfile=true"
  },
  "DatabaseProvider": "MySql",
  
  // 迁移配置
  "EnableMigration": true,  // WebAdmin: true, ApiService: false (推荐)
  "CheckIndexesOnStartup": false,  // ApiService 是否在启动时检查索引完整性
  
  // 迁移选项
  "Migration": {
    "CommandTimeout": 600,  // 迁移命令超时时间（秒）
    "CreateBackup": true,   // 是否在迁移前创建备份
    "BackupPath": "./backups",  // 备份文件路径
    "ValidateIndexes": true,    // 是否验证索引完整性
    "AutoRecovery": true,       // 是否启用自动索引恢复
    "LogDetailedErrors": true   // 是否记录详细的错误信息
  },
  
  // 索引恢复配置
  "IndexRecovery": {
    "EnableAutoRecovery": true,     // 是否启用自动恢复
    "MaxRetryAttempts": 3,          // 最大重试次数
    "RetryDelaySeconds": 5,         // 重试间隔（秒）
    "GenerateRecoveryScript": true, // 是否生成恢复脚本
    "ScriptOutputPath": "./recovery-scripts"  // 脚本输出路径
  },
  
  // 健康检查配置
  "HealthChecks": {
    "Database": {
      "Enabled": true,
      "CheckIndexes": true,  // 是否在健康检查中包含索引检查
      "Timeout": 30          // 健康检查超时时间（秒）
    }
  },
  
  // 日志配置
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "SlzrCrossGate.Core.Services.DatabaseMigrationService": "Debug",
      "SlzrCrossGate.Core.Services.IndexRecoveryService": "Debug",
      "Microsoft.EntityFrameworkCore.Database.Command": "Warning"
    }
  },
  
  // 其他配置...
  "Jwt": {
    "Key": "your-super-secret-jwt-key-here-must-be-at-least-32-characters",
    "Issuer": "SlzrCrossGate",
    "Audience": "SlzrCrossGate.WebAdmin"
  },
  
  "RabbitMQ": {
    "HostName": "localhost",
    "UserName": "admin",
    "Password": "admin123"
  },
  
  "FileService": {
    "DefaultStorageType": "MinIO",
    "LocalFilePath": "./uploads",
    "MinIO": {
      "Endpoint": "localhost:9000",
      "AccessKey": "minioadmin",
      "SecretKey": "minioadmin123",
      "BucketName": "slzr-files"
    }
  }
}

// 生产环境配置示例 (appsettings.Production.json)
{
  "EnableMigration": true,  // 只有主要的 WebAdmin 实例执行迁移
  "CheckIndexesOnStartup": true,  // 生产环境建议启用索引检查
  
  "Migration": {
    "CommandTimeout": 1800,  // 生产环境使用更长的超时时间
    "CreateBackup": true,    // 生产环境必须创建备份
    "AutoRecovery": true     // 启用自动恢复
  },
  
  "IndexRecovery": {
    "EnableAutoRecovery": true,
    "MaxRetryAttempts": 5,   // 生产环境更多重试次数
    "GenerateRecoveryScript": true
  },
  
  "Logging": {
    "LogLevel": {
      "Default": "Warning",
      "SlzrCrossGate.Core.Services.DatabaseMigrationService": "Information",
      "SlzrCrossGate.Core.Services.IndexRecoveryService": "Information"
    }
  }
}

// 开发环境配置示例 (appsettings.Development.json)
{
  "EnableMigration": true,  // 开发环境可以让多个实例都尝试迁移
  "CheckIndexesOnStartup": true,  // 开发环境启用索引检查便于调试
  
  "Migration": {
    "CommandTimeout": 300,   // 开发环境较短的超时时间
    "CreateBackup": false,   // 开发环境可以不创建备份
    "AutoRecovery": true,
    "LogDetailedErrors": true  // 开发环境记录详细错误
  },
  
  "IndexRecovery": {
    "EnableAutoRecovery": true,
    "MaxRetryAttempts": 2,
    "GenerateRecoveryScript": true
  },
  
  "Logging": {
    "LogLevel": {
      "Default": "Debug",
      "SlzrCrossGate.Core.Services.DatabaseMigrationService": "Debug",
      "SlzrCrossGate.Core.Services.IndexRecoveryService": "Debug"
    }
  }
}

// Docker Compose 环境变量配置示例
// 在 docker-compose.yml 中使用环境变量覆盖配置

// WebAdmin 容器
environment:
  - EnableMigration=true
  - Migration__CommandTimeout=1800
  - Migration__CreateBackup=true
  - Migration__AutoRecovery=true
  - IndexRecovery__EnableAutoRecovery=true
  - IndexRecovery__MaxRetryAttempts=5

// ApiService 容器
environment:
  - EnableMigration=false
  - CheckIndexesOnStartup=true
  - IndexRecovery__EnableAutoRecovery=false  // ApiService 只检查不恢复

// 配置说明：
// 1. EnableMigration: 控制是否执行数据库迁移
//    - WebAdmin: true (主要迁移执行者)
//    - ApiService: false (等待迁移完成)
//
// 2. CheckIndexesOnStartup: 控制启动时是否检查索引
//    - 生产环境建议启用，及时发现问题
//    - 开发环境可选，便于调试
//
// 3. Migration.AutoRecovery: 控制迁移失败时是否自动尝试索引恢复
//    - 建议在所有环境都启用
//
// 4. IndexRecovery.EnableAutoRecovery: 控制是否自动恢复丢失的索引
//    - WebAdmin: true (有权限修改数据库结构)
//    - ApiService: false (只读检查，不修改)
//
// 5. 日志级别建议：
//    - 生产环境: Information 或 Warning
//    - 开发环境: Debug
//    - 迁移相关服务建议使用 Information 以便追踪问题
