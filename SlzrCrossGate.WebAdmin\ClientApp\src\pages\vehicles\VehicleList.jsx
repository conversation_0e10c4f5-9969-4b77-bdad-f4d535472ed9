import React, { useState, useEffect, useRef } from 'react';
import {
  Box, Paper, Table, TableBody, TableCell, TableContainer,
  TableHead, TableRow, Button, TextField, MenuItem,
  Typography, Chip, IconButton, Tooltip, Grid,
  TablePagination, CircularProgress, Alert, Dialog,
  DialogTitle, DialogContent, DialogActions
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  DirectionsCar as CarIcon,
  Search as SearchIcon,
  FileDownload as ExportIcon,
  CloudDownload as DownloadIcon,
  CloudUpload as UploadIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { vehicleAPI } from '../../services/api';
import { parseErrorMessage } from '../../utils/errorHandler';
import MerchantAutocomplete from '../../components/MerchantAutocomplete';

const VehicleList = () => {
  const navigate = useNavigate();
  const [vehicles, setVehicles] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [filters, setFilters] = useState({
    search: '',
    merchantId: null, // 改为null，因为MerchantAutocomplete期望对象或null
    vehicleType: '',
    maintenanceStatus: ''
  });
  const [pagination, setPagination] = useState({
    page: 0,
    pageSize: 10,
    totalCount: 0
  });

  // 导入相关状态
  const [importDialog, setImportDialog] = useState(false);
  const [importFile, setImportFile] = useState(null);
  const [importing, setImporting] = useState(false);
  const [importResult, setImportResult] = useState(null);

  // 防重复请求
  const loadVehiclesRef = useRef();
  const hasLoadedRef = useRef(false);

  // 加载车辆列表
  const loadVehicles = async (isInitialLoad = false, forceLoad = false) => {
    try {
      setLoading(true);
      setError(null);

      const params = {
        ...filters,
        merchantId: filters.merchantId?.merchantID || '', // 提取商户ID
        page: pagination.page + 1, // API使用1基索引
        pageSize: pagination.pageSize
      };

      // 移除undefined的参数
      Object.keys(params).forEach(key => {
        if (params[key] === undefined) {
          delete params[key];
        }
      });

      // 避免重复请求：检查参数是否真的发生了变化（除非强制加载）
      const paramsString = JSON.stringify(params);
      if (!isInitialLoad && !forceLoad && loadVehiclesRef.current === paramsString) {
        console.log('VehicleList: 参数未变化，跳过重复请求');
        setLoading(false);
        return;
      }
      loadVehiclesRef.current = paramsString;

      console.log('VehicleList: 执行数据请求', params);
      const response = await vehicleAPI.getVehicles(params);
      setVehicles(response.items);
      setPagination(prev => ({
        ...prev,
        totalCount: response.totalCount
      }));
    } catch (error) {
      console.error('加载车辆列表失败:', error);
      const errorMessage = parseErrorMessage(error, '加载车辆列表失败');
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // 统一的数据加载逻辑
  useEffect(() => {
    // 防止重复加载
    if (hasLoadedRef.current) {
      console.log('VehicleList: 已加载过，跳过重复请求');
      return;
    }

    console.log('VehicleList: 执行首次加载');
    hasLoadedRef.current = true;
    loadVehicles(true, false); // 标记为初始加载，非强制加载
  }, []);

  // 当参数变化时重新加载
  useEffect(() => {
    if (hasLoadedRef.current) { // 只有在首次加载完成后才响应参数变化
      console.log('VehicleList: 参数变化，重新加载');
      loadVehicles(false, false); // 非初始加载，非强制加载
    }
  }, [filters, pagination.page, pagination.pageSize]);

  // 处理筛选条件变化
  const handleFilterChange = (field, value) => {
    setFilters(prev => ({ ...prev, [field]: value }));
    setPagination(prev => ({ ...prev, page: 0 })); // 重置到第一页
  };

  // 处理分页变化
  const handlePageChange = (event, newPage) => {
    setPagination(prev => ({ ...prev, page: newPage }));
  };

  const handlePageSizeChange = (event) => {
    setPagination(prev => ({
      ...prev,
      pageSize: parseInt(event.target.value, 10),
      page: 0
    }));
  };

  // 处理导出
  const handleExport = async () => {
    try {
      setLoading(true);
      const exportParams = {
        search: filters.search,
        merchantId: filters.merchantId?.merchantID || '',
        vehicleType: filters.vehicleType,
        maintenanceStatus: filters.maintenanceStatus
      };

      const blob = await vehicleAPI.exportVehicles(exportParams);

      // 创建下载链接
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `车辆信息_${new Date().toISOString().slice(0, 19).replace(/[:-]/g, '')}.csv`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('导出车辆信息失败:', error);
      const errorMessage = parseErrorMessage(error, '导出车辆信息失败');
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // 维护状态显示
  const getMaintenanceStatusChip = (status) => {
    const statusConfig = {
      'Normal': { label: '正常', color: 'success' },
      'Warning': { label: '预警', color: 'warning' },
      'Maintenance': { label: '维护中', color: 'error' },
      'Expired': { label: '过期', color: 'error' }
    };
    const config = statusConfig[status] || { label: status, color: 'default' };
    return <Chip label={config.label} color={config.color} size="small" />;
  };

  // 删除车辆
  const handleDeleteVehicle = async (vehicleId) => {
    if (!window.confirm('确定要删除这辆车吗？')) {
      return;
    }

    try {
      await vehicleAPI.deleteVehicle(vehicleId);
      loadVehicles(); // 重新加载列表
    } catch (error) {
      console.error('删除车辆失败:', error);
      const errorMessage = parseErrorMessage(error, '删除车辆失败');
      setError(errorMessage);
    }
  };

  // 下载导入模板
  const handleDownloadTemplate = async () => {
    try {
      const response = await vehicleAPI.downloadTemplate();
      const url = window.URL.createObjectURL(new Blob([response]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `车辆信息导入模板_${new Date().toISOString().slice(0, 10).replace(/-/g, '')}.xlsx`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('下载模板失败:', error);
      const errorMessage = parseErrorMessage(error, '下载模板失败');
      setError(errorMessage);
    }
  };

  // 处理文件选择
  const handleFileChange = (event) => {
    const file = event.target.files[0];
    if (file) {
      if (!file.name.endsWith('.xlsx')) {
        setError('只支持Excel文件格式(.xlsx)');
        return;
      }
      setImportFile(file);
    }
  };

  // 执行导入
  const handleImport = async () => {
    if (!importFile) {
      setError('请选择要导入的文件');
      return;
    }

    try {
      setImporting(true);
      const formData = new FormData();
      formData.append('file', importFile);

      const result = await vehicleAPI.importVehicles(formData);
      setImportResult(result);

      if (result.successCount > 0) {
        loadVehicles(false, true); // 强制重新加载列表
      }
    } catch (error) {
      console.error('导入失败:', error);
      const errorMessage = parseErrorMessage(error, '导入失败');
      setError(errorMessage);
    } finally {
      setImporting(false);
    }
  };

  // 关闭导入对话框
  const handleCloseImportDialog = () => {
    setImportDialog(false);
    setImportFile(null);
    setImportResult(null);
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          <CarIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
          车辆管理
        </Typography>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button
            variant="outlined"
            startIcon={<DownloadIcon />}
            onClick={handleDownloadTemplate}
            disabled={loading}
          >
            下载模板
          </Button>
          <Button
            variant="outlined"
            startIcon={<UploadIcon />}
            onClick={() => setImportDialog(true)}
            disabled={loading}
          >
            批量导入
          </Button>
          <Button
            variant="outlined"
            startIcon={<ExportIcon />}
            onClick={handleExport}
            disabled={loading}
          >
            导出
          </Button>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => navigate('/app/vehicles/new')}
          >
            新增车辆
          </Button>
        </Box>
      </Box>

      {/* 筛选条件 */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={6} md={3}>
            <TextField
              fullWidth
              label="搜索"
              placeholder="车牌号、设备编号、司机姓名"
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              InputProps={{
                startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
              }}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <MerchantAutocomplete
              value={filters.merchantId}
              onChange={(event, newValue) => handleFilterChange('merchantId', newValue)}
              label="商户"
              showAll={true}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <TextField
              fullWidth
              label="车辆类型"
              placeholder="输入车辆类型"
              value={filters.vehicleType}
              onChange={(e) => handleFilterChange('vehicleType', e.target.value)}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <TextField
              fullWidth
              select
              label="维护状态"
              value={filters.maintenanceStatus}
              onChange={(e) => handleFilterChange('maintenanceStatus', e.target.value)}
            >
              <MenuItem value="">全部</MenuItem>
              <MenuItem value="Normal">正常</MenuItem>
              <MenuItem value="Warning">预警</MenuItem>
              <MenuItem value="Maintenance">维护中</MenuItem>
              <MenuItem value="Expired">过期</MenuItem>
            </TextField>
          </Grid>
        </Grid>
      </Paper>

      {/* 错误提示 */}
      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* 车辆列表 */}
      <Paper>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>车牌号</TableCell>
                <TableCell>设备编号</TableCell>
                <TableCell>商户</TableCell>
                <TableCell>车辆类型</TableCell>
                <TableCell>品牌型号</TableCell>
                <TableCell>司机</TableCell>
                <TableCell>维护状态</TableCell>
                <TableCell>下次维护</TableCell>
                <TableCell align="right">操作</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={9} align="center">
                    <CircularProgress />
                  </TableCell>
                </TableRow>
              ) : vehicles.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={9} align="center">
                    <Typography color="textSecondary">暂无车辆数据</Typography>
                  </TableCell>
                </TableRow>
              ) : (
                vehicles.map((vehicle) => (
                  <TableRow key={vehicle.id} hover>
                    <TableCell>
                      <Typography variant="body2" fontWeight="bold">
                        {vehicle.licensePlate}
                      </Typography>
                    </TableCell>
                    <TableCell>{vehicle.deviceNO}</TableCell>
                    <TableCell>{vehicle.merchantName}</TableCell>
                    <TableCell>{vehicle.vehicleType || '-'}</TableCell>
                    <TableCell>
                      {vehicle.brand && vehicle.model 
                        ? `${vehicle.brand} ${vehicle.model}` 
                        : vehicle.brand || vehicle.model || '-'}
                    </TableCell>
                    <TableCell>
                      {vehicle.driverName || '-'}
                      {vehicle.driverPhone && (
                        <Typography variant="caption" display="block" color="textSecondary">
                          {vehicle.driverPhone}
                        </Typography>
                      )}
                    </TableCell>
                    <TableCell>
                      {getMaintenanceStatusChip(vehicle.maintenanceStatus)}
                    </TableCell>
                    <TableCell>
                      {vehicle.nextMaintenanceDate 
                        ? new Date(vehicle.nextMaintenanceDate).toLocaleDateString()
                        : '-'}
                    </TableCell>
                    <TableCell align="right">
                      <Tooltip title="查看详情">
                        <IconButton 
                          size="small" 
                          color="primary"
                          onClick={() => navigate(`/app/vehicles/${vehicle.id}`)}
                        >
                          <ViewIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="编辑">
                        <IconButton 
                          size="small" 
                          color="primary"
                          onClick={() => navigate(`/app/vehicles/${vehicle.id}/edit`)}
                        >
                          <EditIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="删除">
                        <IconButton 
                          size="small" 
                          color="error"
                          onClick={() => handleDeleteVehicle(vehicle.id)}
                        >
                          <DeleteIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </TableContainer>
        
        {/* 分页 */}
        <TablePagination
          component="div"
          count={pagination.totalCount}
          page={pagination.page}
          onPageChange={handlePageChange}
          rowsPerPage={pagination.pageSize}
          onRowsPerPageChange={handlePageSizeChange}
          rowsPerPageOptions={[5, 10, 25, 50]}
          labelRowsPerPage="每页行数:"
          labelDisplayedRows={({ from, to, count }) => `${from}-${to} 共 ${count} 条`}
        />
      </Paper>

      {/* 导入对话框 */}
      <Dialog open={importDialog} onClose={handleCloseImportDialog} maxWidth="sm" fullWidth>
        <DialogTitle>批量导入车辆信息</DialogTitle>
        <DialogContent>
          {!importResult ? (
            <Box sx={{ pt: 2 }}>
              <Typography variant="body2" color="textSecondary" sx={{ mb: 2 }}>
                请选择要导入的Excel文件（.xlsx格式）
              </Typography>
              <input
                type="file"
                accept=".xlsx"
                onChange={handleFileChange}
                style={{ marginBottom: '16px' }}
              />
              {importFile && (
                <Typography variant="body2" color="primary">
                  已选择文件：{importFile.name}
                </Typography>
              )}
            </Box>
          ) : (
            <Box sx={{ pt: 2 }}>
              <Typography variant="h6" gutterBottom>
                导入结果
              </Typography>
              <Typography variant="body2" sx={{ mb: 1 }}>
                总行数：{importResult.totalRows}
              </Typography>
              <Typography variant="body2" sx={{ mb: 1, color: 'success.main' }}>
                成功：{importResult.successCount}
              </Typography>
              <Typography variant="body2" sx={{ mb: 2, color: 'error.main' }}>
                失败：{importResult.failureCount}
              </Typography>
              {importResult.errors && importResult.errors.length > 0 && (
                <Box>
                  <Typography variant="body2" color="error" sx={{ mb: 1 }}>
                    错误详情：
                  </Typography>
                  <Box sx={{ maxHeight: 200, overflow: 'auto' }}>
                    {importResult.errors.map((error, index) => (
                      <Typography key={index} variant="body2" color="error" sx={{ fontSize: '0.8rem' }}>
                        {error}
                      </Typography>
                    ))}
                  </Box>
                </Box>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseImportDialog}>
            {importResult ? '关闭' : '取消'}
          </Button>
          {!importResult && (
            <Button
              onClick={handleImport}
              variant="contained"
              disabled={!importFile || importing}
            >
              {importing ? <CircularProgress size={20} /> : '开始导入'}
            </Button>
          )}
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default VehicleList;
