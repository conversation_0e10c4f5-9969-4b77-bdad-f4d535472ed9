using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SlzrCrossGate.CustomerTemplate.Services;
using System.Security.Claims;

namespace SlzrCrossGate.CustomerTemplate.Controllers
{
    /// <summary>
    /// 首页控制器 - 提供基础的客户功能
    /// </summary>
    [ApiController]
    [Route("api/customa/[controller]")]
    [Authorize]
    public class HomeController : ControllerBase
    {
        private readonly AuthService _authService;
        private readonly CustomerDataService _customerDataService;
        private readonly ILogger<HomeController> _logger;

        public HomeController(
            AuthService authService,
            CustomerDataService customerDataService,
            ILogger<HomeController> logger)
        {
            _authService = authService;
            _customerDataService = customerDataService;
            _logger = logger;
        }

        /// <summary>
        /// 获取首页数据
        /// </summary>
        /// <returns>首页数据</returns>
        [HttpGet("dashboard")]
        public async Task<ActionResult<DashboardResponse>> GetDashboard()
        {
            try
            {
                // 验证用户身份
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized("用户身份验证失败");
                }

                var user = await _authService.ValidateUserAsync(userId);
                if (user == null)
                {
                    return Unauthorized("用户验证失败");
                }

                _logger.LogInformation("用户 {UserId} 访问首页数据", userId);

                // 获取用户相关的统计数据
                var stats = await GetUserStatsAsync(user.MerchantID);

                var response = new DashboardResponse
                {
                    WelcomeMessage = $"欢迎，{user.RealName ?? user.UserName}！",
                    UserInfo = new UserSummary
                    {
                        UserName = user.UserName,
                        RealName = user.RealName,
                        MerchantId = user.MerchantID,
                        LastLoginTime = DateTime.Now // 这里可以从实际的登录记录中获取
                    },
                    Statistics = stats,
                    RecentActivities = await GetRecentActivitiesAsync(user.MerchantID),
                    QuickActions = GetQuickActions()
                };

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取首页数据时发生错误");
                return StatusCode(500, "获取首页数据失败");
            }
        }

        /// <summary>
        /// 获取用户统计数据
        /// </summary>
        /// <param name="merchantId">商户ID</param>
        /// <returns>统计数据</returns>
        private async Task<DashboardStats> GetUserStatsAsync(string? merchantId)
        {
            try
            {
                // 这里可以根据实际业务需求获取统计数据
                // 目前返回模拟数据
                return new DashboardStats
                {
                    TotalRecords = await _customerDataService.GetRecordCountAsync(merchantId),
                    ActiveTasks = 3,
                    CompletedTasks = 15,
                    PendingApprovals = 2
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取统计数据时发生错误");
                return new DashboardStats();
            }
        }

        /// <summary>
        /// 获取最近活动
        /// </summary>
        /// <param name="merchantId">商户ID</param>
        /// <returns>最近活动列表</returns>
        private async Task<List<ActivityItem>> GetRecentActivitiesAsync(string? merchantId)
        {
            try
            {
                // 这里可以从实际的活动日志中获取数据
                // 目前返回模拟数据
                await Task.Delay(1); // 模拟异步操作

                return new List<ActivityItem>
                {
                    new ActivityItem
                    {
                        Id = 1,
                        Description = "系统初始化完成",
                        Timestamp = DateTime.Now.AddMinutes(-2),
                        Type = "Success"
                    },
                    new ActivityItem
                    {
                        Id = 2,
                        Description = "用户认证成功",
                        Timestamp = DateTime.Now.AddMinutes(-5),
                        Type = "Info"
                    },
                    new ActivityItem
                    {
                        Id = 3,
                        Description = "数据同步完成",
                        Timestamp = DateTime.Now.AddMinutes(-10),
                        Type = "Success"
                    }
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取最近活动时发生错误");
                return new List<ActivityItem>();
            }
        }

        /// <summary>
        /// 获取快捷操作
        /// </summary>
        /// <returns>快捷操作列表</returns>
        private List<QuickAction> GetQuickActions()
        {
            return new List<QuickAction>
            {
                new QuickAction
                {
                    Id = 1,
                    Title = "定制功能A",
                    Description = "访问客户专属功能",
                    Icon = "settings",
                    Url = "/custom-feature"
                },
                new QuickAction
                {
                    Id = 2,
                    Title = "数据分析",
                    Description = "查看数据分析报表",
                    Icon = "analytics",
                    Url = "/analytics"
                },
                new QuickAction
                {
                    Id = 3,
                    Title = "系统设置",
                    Description = "配置系统参数",
                    Icon = "cog",
                    Url = "/settings"
                }
            };
        }
    }

    #region Response DTOs

    /// <summary>
    /// 首页响应数据
    /// </summary>
    public class DashboardResponse
    {
        public string WelcomeMessage { get; set; } = string.Empty;
        public UserSummary UserInfo { get; set; } = new();
        public DashboardStats Statistics { get; set; } = new();
        public List<ActivityItem> RecentActivities { get; set; } = new();
        public List<QuickAction> QuickActions { get; set; } = new();
    }

    /// <summary>
    /// 用户摘要信息
    /// </summary>
    public class UserSummary
    {
        public string UserName { get; set; } = string.Empty;
        public string? RealName { get; set; }
        public string? MerchantId { get; set; }
        public DateTime LastLoginTime { get; set; }
    }

    /// <summary>
    /// 首页统计数据
    /// </summary>
    public class DashboardStats
    {
        public int TotalRecords { get; set; }
        public int ActiveTasks { get; set; }
        public int CompletedTasks { get; set; }
        public int PendingApprovals { get; set; }
    }

    /// <summary>
    /// 活动项目
    /// </summary>
    public class ActivityItem
    {
        public int Id { get; set; }
        public string Description { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
        public string Type { get; set; } = string.Empty;
    }

    /// <summary>
    /// 快捷操作
    /// </summary>
    public class QuickAction
    {
        public int Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Icon { get; set; } = string.Empty;
        public string Url { get; set; } = string.Empty;
    }

    #endregion
}
