-- 为珠海通客户项目添加菜单项
USE tcpserver;

-- 1. 检查并创建客户功能菜单组
INSERT IGNORE INTO MenuGroups (GroupKey, Title, IconName, SortOrder, IsEnabled, VisibleToSystemAdmin, VisibleToMerchantAdmin, VisibleToUser, CreatedAt, UpdatedAt)
VALUES ('customer-features', '客户功能', 'ExtensionIcon', 10, 1, 1, 1, 0, NOW(), NOW());

-- 2. 添加珠海通查询菜单项
INSERT IGNORE INTO MenuItems (
    MenuGroupId, 
    ItemKey, 
    Title, 
    Href, 
    IconName, 
    SortOrder, 
    IsEnabled, 
    VisibleToSystemAdmin, 
    VisibleToMerchantAdmin, 
    VisibleToUser, 
    IsExternal, 
    Target,
    CreatedAt,
    UpdatedAt
)
VALUES (
    (SELECT Id FROM MenuGroups WHERE GroupKey = 'customer-features'),
    'zhuhaitong-query',
    '珠海通查询',
    'http://localhost:5271/',
    'SearchIcon',
    1, 
    1, 
    1, 
    1, 
    0, 
    1, 
    '_iframe',
    NOW(),
    NOW()
);

-- 3. 验证添加结果
SELECT 
    mg.Title as MenuGroup,
    mi.Title as MenuItem,
    mi.Href,
    mi.IsExternal,
    mi.Target
FROM MenuGroups mg
LEFT JOIN MenuItems mi ON mg.Id = mi.MenuGroupId
WHERE mg.GroupKey = 'customer-features'
ORDER BY mg.SortOrder, mi.SortOrder;
