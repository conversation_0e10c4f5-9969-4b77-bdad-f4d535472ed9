# 阶段 1: 构建前端
FROM devtest.pointlife365.net:5180/library/node:18-alpine AS frontend-build
WORKDIR /app
# 确保package.json文件路径正确
COPY ["SlzrCrossGate.WebAdmin/ClientApp/package*.json", "./"]
RUN npm ci
COPY ["SlzrCrossGate.WebAdmin/ClientApp/", "./"]
# 设置环境变量控制输出目录
ENV NODE_ENV=production
# 添加调试信息
RUN npm run build && echo "前端构建完成" && ls -la
# 备份默认配置文件到单独目录
RUN mkdir -p /app/config-defaults && \
    cp -r /app/public/config/* /app/config-defaults/ && \
    echo "默认配置已备份" && ls -la /app/config-defaults/

# 阶段 2: 构建后端
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src
COPY ["SlzrCrossGate.WebAdmin/SlzrCrossGate.WebAdmin.csproj", "SlzrCrossGate.WebAdmin/"]
COPY ["SlzrCrossGate.Core/SlzrCrossGate.Core.csproj", "SlzrCrossGate.Core/"]
COPY ["SlzrCrossGate.Common/SlzrCrossGate.Common.csproj", "SlzrCrossGate.Common/"]
COPY ["SlzrCrossGate.ServiceDefaults/SlzrCrossGate.ServiceDefaults.csproj", "SlzrCrossGate.ServiceDefaults/"]
RUN dotnet restore "SlzrCrossGate.WebAdmin/SlzrCrossGate.WebAdmin.csproj"
COPY . .
WORKDIR "/src/SlzrCrossGate.WebAdmin"
RUN dotnet build "SlzrCrossGate.WebAdmin.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "SlzrCrossGate.WebAdmin.csproj" -c Release -o /app/publish

# 阶段 3: 最终镜像
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS final
WORKDIR /app
EXPOSE 80

# 将预下载的curl二进制文件复制到镜像中
COPY curl-amd64 /usr/local/bin/curl
RUN chmod +x /usr/local/bin/curl && \
    /usr/local/bin/curl --version

# 复制启动脚本
COPY ["SlzrCrossGate.WebAdmin/scripts/entrypoint.sh", "/entrypoint.sh"]
RUN chmod +x /entrypoint.sh

COPY --from=publish /app/publish .
# 创建wwwroot目录（如果不存在）
RUN mkdir -p ./wwwroot
# 复制前端构建结果 - 现在使用标准的dist目录
COPY --from=frontend-build /app/dist/ ./wwwroot/

# 保存默认配置文件到专门目录
RUN mkdir -p /app/config-defaults
COPY --from=frontend-build /app/config-defaults/ /app/config-defaults/

# 创建文件存储目录
RUN mkdir -p /app/storage/files && chmod -R 755 /app/storage
# 创建数据保护密钥目录并设置权限
RUN mkdir -p /app/Keys && chmod -R 755 /app/Keys

# 设置环境变量
ENV ASPNETCORE_URLS=http://+:80
ENV ASPNETCORE_ENVIRONMENT=Production

# 定义持久卷
VOLUME ["/app/Keys", "/app/wwwroot/config"]

# 使用启动脚本作为入口点
ENTRYPOINT ["/entrypoint.sh"]
CMD ["dotnet", "SlzrCrossGate.WebAdmin.dll"]