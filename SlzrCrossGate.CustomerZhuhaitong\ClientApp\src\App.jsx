import { useEffect, useState } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider as MuiThemeProvider } from '@mui/material/styles';
import { CssBaseline, Box, CircularProgress } from '@mui/material';
import { SnackbarProvider } from 'notistack';

// 导入页面组件
import HomePage from './pages/HomePage';
import NotFoundPage from './pages/NotFoundPage';

// 导入服务
import AuthService from './services/AuthService';

// 导入主题系统
import ThemeProvider, { useTheme } from './contexts/ThemeContext';

// 主应用组件 - 包含认证逻辑
const MainApp = () => {
  const { theme, updateThemeConfig } = useTheme();
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [userInfo, setUserInfo] = useState(null);



  useEffect(() => {
    // 初始化认证状态
    initializeAuth();

    // 监听来自主应用的消息
    window.addEventListener('message', handleMainAppMessage);

    return () => {
      window.removeEventListener('message', handleMainAppMessage);
    };
  }, []);

  const initializeAuth = async () => {
    try {
      // 从主应用获取认证状态
      const authData = await AuthService.getAuthFromMainApp();
      if (authData && authData.token) {
        setIsAuthenticated(true);
        setUserInfo(authData.user);
        // 设置axios默认header
        AuthService.setAuthToken(authData.token);
      }
    } catch (error) {
      console.error('认证初始化失败:', error);
      // 重定向到主应用登录页
      window.parent.postMessage({ 
        type: 'REDIRECT_TO_LOGIN' 
      }, '*');
    } finally {
      setIsLoading(false);
    }
  };

  const handleMainAppMessage = (event) => {
    // 验证消息来源 - 允许来自主应用的消息
    // 注意：在iframe中，event.origin可能是主应用的域名

    console.log('珠海通项目收到消息:', event.data);

    switch (event.data.type) {
      case 'AUTH_INFO_RESPONSE':
        if (event.data.success && event.data.token) {
          setIsAuthenticated(true);
          setUserInfo(event.data.user);
          AuthService.setAuthToken(event.data.token);

          // 应用主题配置
          if (event.data.theme) {
            console.log('应用主题配置:', event.data.theme);
            updateThemeConfig(event.data.theme);
          }
        } else {
          setIsAuthenticated(false);
          setUserInfo(null);
          AuthService.clearAuthToken();
        }
        break;

      case 'THEME_UPDATE':
        // 处理主题更新消息
        if (event.data.theme) {
          console.log('收到主题更新:', event.data.theme);
          updateThemeConfig(event.data.theme);
        }
        break;

      case 'AUTH_TOKEN_UPDATE':
        if (event.data.token) {
          setIsAuthenticated(true);
          setUserInfo(event.data.user);
          AuthService.setAuthToken(event.data.token);
        } else {
          setIsAuthenticated(false);
          setUserInfo(null);
          AuthService.clearAuthToken();
        }
        break;

      case 'LOGOUT':
        setIsAuthenticated(false);
        setUserInfo(null);
        AuthService.clearAuthToken();
        break;

      default:
        break;
    }
  };

  if (isLoading) {
    return (
      <MuiThemeProvider theme={theme}>
        <CssBaseline />
        <Box
          display="flex"
          justifyContent="center"
          alignItems="center"
          minHeight="100vh"
        >
          <CircularProgress />
        </Box>
      </MuiThemeProvider>
    );
  }

  if (!isAuthenticated) {
    return (
      <MuiThemeProvider theme={theme}>
        <CssBaseline />
        <Box
          display="flex"
          justifyContent="center"
          alignItems="center"
          minHeight="100vh"
          flexDirection="column"
        >
          <CircularProgress />
          <Box mt={2}>正在验证身份...</Box>
        </Box>
      </MuiThemeProvider>
    );
  }

  return (
    <MuiThemeProvider theme={theme}>
      <CssBaseline />
      <SnackbarProvider
        maxSnack={3}
        anchorOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
      >
        <Router
          basename={process.env.REACT_APP_BASE_PATH || '/'}
          future={{
            v7_startTransition: true,
            v7_relativeSplatPath: true
          }}
        >
          <Box sx={{ minHeight: '100vh', bgcolor: 'background.default' }}>
            <Routes>
              <Route path="/" element={<HomePage userInfo={userInfo} />} />
              <Route path="/404" element={<NotFoundPage />} />
              <Route path="*" element={<Navigate to="/404" />} />
            </Routes>
          </Box>
        </Router>
      </SnackbarProvider>
    </MuiThemeProvider>
  );
};

// 根App组件 - 提供主题上下文
function App() {
  return (
    <ThemeProvider>
      <MainApp />
    </ThemeProvider>
  );
}

export default App;
