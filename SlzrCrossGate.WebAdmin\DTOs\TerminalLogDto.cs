using System.ComponentModel.DataAnnotations;

namespace SlzrCrossGate.WebAdmin.DTOs
{
    /// <summary>
    /// 终端日志记录DTO
    /// </summary>
    public class TerminalLogDto
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        public int ID { get; set; }

        /// <summary>
        /// 商户ID
        /// </summary>
        public string? MerchantID { get; set; }

        /// <summary>
        /// 商户名称
        /// </summary>
        public string? MerchantName { get; set; }

        /// <summary>
        /// 记录类型
        /// </summary>
        public int? LogType { get; set; }

        /// <summary>
        /// 记录类型名称
        /// </summary>
        public string? LogTypeName { get; set; }

        /// <summary>
        /// 设置方式
        /// </summary>
        public int? SetMethod { get; set; }

        /// <summary>
        /// 设置方式名称
        /// </summary>
        public string? SetMethodName { get; set; }

        /// <summary>
        /// 卡号
        /// </summary>
        public string? CardNO { get; set; }

        /// <summary>
        /// 设备序列号
        /// </summary>
        public string? MachineID { get; set; }

        /// <summary>
        /// 设备编号
        /// </summary>
        public string? MachineNO { get; set; }

        /// <summary>
        /// 线路号
        /// </summary>
        public string? LineNO { get; set; }

        /// <summary>
        /// 票价（分）
        /// </summary>
        public int? Price { get; set; }

        /// <summary>
        /// 司机卡号
        /// </summary>
        public string? DriverCardNO { get; set; }

        /// <summary>
        /// 记录时间
        /// </summary>
        public DateTime? LogTime { get; set; }

        /// <summary>
        /// 上传时间
        /// </summary>
        public DateTime? UploadTime { get; set; }
    }

    /// <summary>
    /// 终端日志查询参数DTO
    /// </summary>
    public class TerminalLogQueryDto
    {
        /// <summary>
        /// 商户ID
        /// </summary>
        public string? MerchantID { get; set; }

        /// <summary>
        /// 记录类型
        /// </summary>
        public int? LogType { get; set; }

        /// <summary>
        /// 卡号
        /// </summary>
        public string? CardNO { get; set; }

        /// <summary>
        /// 设备序列号
        /// </summary>
        public string? MachineID { get; set; }

        /// <summary>
        /// 设备编号
        /// </summary>
        public string? MachineNO { get; set; }

        /// <summary>
        /// 线路号
        /// </summary>
        public string? LineNO { get; set; }

        /// <summary>
        /// 记录时间开始
        /// </summary>
        public DateTime? LogTimeStart { get; set; }

        /// <summary>
        /// 记录时间结束
        /// </summary>
        public DateTime? LogTimeEnd { get; set; }

        /// <summary>
        /// 页码
        /// </summary>
        [Range(0, int.MaxValue)]
        public int Page { get; set; } = 0;

        /// <summary>
        /// 页大小
        /// </summary>
        [Range(1, 100)]
        public int PageSize { get; set; } = 20;

        /// <summary>
        /// 排序字段
        /// </summary>
        public string? SortBy { get; set; } = "LogTime";

        /// <summary>
        /// 排序方向
        /// </summary>
        public string? SortDirection { get; set; } = "desc";
    }

    /// <summary>
    /// 终端日志分页结果DTO
    /// </summary>
    public class TerminalLogPagedResultDto
    {
        /// <summary>
        /// 数据列表
        /// </summary>
        public List<TerminalLogDto> Items { get; set; } = new();

        /// <summary>
        /// 总记录数
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// 总页数
        /// </summary>
        public int TotalPages { get; set; }

        /// <summary>
        /// 当前页码
        /// </summary>
        public int CurrentPage { get; set; }

        /// <summary>
        /// 页大小
        /// </summary>
        public int PageSize { get; set; }
    }
}
