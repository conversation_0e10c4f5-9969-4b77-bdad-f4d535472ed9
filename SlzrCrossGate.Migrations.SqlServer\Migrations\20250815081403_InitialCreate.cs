﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace SlzrCrossGate.Migrations.SqlServer.Migrations
{
    /// <inheritdoc />
    public partial class InitialCreate : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "AspNetRoles",
                columns: table => new
                {
                    Id = table.Column<string>(type: "nvarchar(128)", maxLength: 128, nullable: false),
                    IsSysAdmin = table.Column<bool>(type: "bit", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(128)", maxLength: 128, nullable: true),
                    NormalizedName = table.Column<string>(type: "nvarchar(128)", maxLength: 128, nullable: true),
                    ConcurrencyStamp = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AspNetRoles", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "AspNetUsers",
                columns: table => new
                {
                    Id = table.Column<string>(type: "nvarchar(128)", maxLength: 128, nullable: false),
                    RealName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    MerchantID = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CreateTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    TwoFactorSecretKey = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    IsTwoFactorRequired = table.Column<bool>(type: "bit", nullable: false),
                    TwoFactorEnabledDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    FailedTwoFactorAttempts = table.Column<int>(type: "int", nullable: true),
                    LastFailedTwoFactorAttempt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    WechatOpenId = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    WechatUnionId = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    WechatNickname = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    WechatBindTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastPasswordChangeTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    RequirePasswordChange = table.Column<bool>(type: "bit", nullable: false),
                    UserName = table.Column<string>(type: "nvarchar(128)", maxLength: 128, nullable: true),
                    NormalizedUserName = table.Column<string>(type: "nvarchar(128)", maxLength: 128, nullable: true),
                    Email = table.Column<string>(type: "nvarchar(128)", maxLength: 128, nullable: true),
                    NormalizedEmail = table.Column<string>(type: "nvarchar(128)", maxLength: 128, nullable: true),
                    EmailConfirmed = table.Column<bool>(type: "bit", nullable: false),
                    PasswordHash = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    SecurityStamp = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ConcurrencyStamp = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    PhoneNumber = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    PhoneNumberConfirmed = table.Column<bool>(type: "bit", nullable: false),
                    TwoFactorEnabled = table.Column<bool>(type: "bit", nullable: false),
                    LockoutEnd = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    LockoutEnabled = table.Column<bool>(type: "bit", nullable: false),
                    AccessFailedCount = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AspNetUsers", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "ConsumeDatas",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    MerchantID = table.Column<string>(type: "nvarchar(8)", maxLength: 8, nullable: true),
                    MachineNO = table.Column<string>(type: "nvarchar(8)", maxLength: 8, nullable: true),
                    MachineID = table.Column<string>(type: "nvarchar(8)", maxLength: 8, nullable: true),
                    PsamNO = table.Column<string>(type: "nvarchar(12)", maxLength: 12, nullable: true),
                    Buffer = table.Column<byte[]>(type: "varbinary(2500)", maxLength: 2500, nullable: false),
                    ReceiveTime = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ConsumeDatas", x => x.Id)
                        .Annotation("SqlServer:Clustered", true);
                });

            migrationBuilder.CreateTable(
                name: "FeatureConfigs",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    FeatureKey = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    FeatureName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Description = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    Category = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    RiskLevel = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false, defaultValue: "Low"),
                    IsGloballyEnabled = table.Column<bool>(type: "bit", nullable: false, defaultValue: true),
                    IsSystemBuiltIn = table.Column<bool>(type: "bit", nullable: false, defaultValue: false),
                    SortOrder = table.Column<int>(type: "int", nullable: false, defaultValue: 0),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETDATE()"),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETDATE()"),
                    CreatedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    UpdatedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_FeatureConfigs", x => x.Id);
                    table.UniqueConstraint("AK_FeatureConfigs_FeatureKey", x => x.FeatureKey);
                });

            migrationBuilder.CreateTable(
                name: "FilePublishHistories",
                columns: table => new
                {
                    ID = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    MerchantID = table.Column<string>(type: "nvarchar(8)", maxLength: 8, nullable: false),
                    FileTypeID = table.Column<string>(type: "nvarchar(3)", maxLength: 3, nullable: false),
                    FilePara = table.Column<string>(type: "nvarchar(8)", maxLength: 8, nullable: false),
                    FileFullType = table.Column<string>(type: "nvarchar(11)", maxLength: 11, nullable: false),
                    Ver = table.Column<string>(type: "char(4)", maxLength: 4, nullable: false),
                    FileSize = table.Column<int>(type: "int", nullable: false),
                    Crc = table.Column<string>(type: "nvarchar(8)", maxLength: 8, nullable: false),
                    PublishTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    FileVerID = table.Column<int>(type: "int", nullable: false),
                    UploadFileID = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false),
                    Operator = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    OperationType = table.Column<string>(type: "varchar(10)", maxLength: 10, nullable: false),
                    PublishType = table.Column<int>(type: "int", nullable: false),
                    PublishTarget = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Remark = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_FilePublishHistories", x => x.ID)
                        .Annotation("SqlServer:Clustered", true);
                    table.CheckConstraint("CK_FilePublishHistory_OperationType", "OperationType IN ('Publish', 'Revoke')");
                });

            migrationBuilder.CreateTable(
                name: "FilePublishs",
                columns: table => new
                {
                    ID = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    MerchantID = table.Column<string>(type: "nvarchar(8)", maxLength: 8, nullable: false),
                    FileTypeID = table.Column<string>(type: "nvarchar(3)", maxLength: 3, nullable: false),
                    FilePara = table.Column<string>(type: "nvarchar(8)", maxLength: 8, nullable: false),
                    FileFullType = table.Column<string>(type: "nvarchar(11)", maxLength: 11, nullable: false),
                    Ver = table.Column<string>(type: "char(4)", maxLength: 4, nullable: false),
                    FileSize = table.Column<int>(type: "int", nullable: false),
                    Crc = table.Column<string>(type: "nvarchar(8)", maxLength: 8, nullable: false),
                    PublishTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    FileVerID = table.Column<int>(type: "int", nullable: false),
                    UploadFileID = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false),
                    Operator = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    PublishType = table.Column<int>(type: "int", nullable: false),
                    PublishTarget = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Remark = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_FilePublishs", x => x.ID)
                        .Annotation("SqlServer:Clustered", true);
                });

            migrationBuilder.CreateTable(
                name: "FileTypes",
                columns: table => new
                {
                    ID = table.Column<string>(type: "nvarchar(3)", maxLength: 3, nullable: false),
                    MerchantID = table.Column<string>(type: "nvarchar(8)", maxLength: 8, nullable: false),
                    Name = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    Description = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_FileTypes", x => new { x.ID, x.MerchantID })
                        .Annotation("SqlServer:Clustered", true);
                });

            migrationBuilder.CreateTable(
                name: "FileVers",
                columns: table => new
                {
                    ID = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    MerchantID = table.Column<string>(type: "nvarchar(8)", maxLength: 8, nullable: false),
                    FileTypeID = table.Column<string>(type: "nvarchar(3)", maxLength: 3, nullable: false),
                    FilePara = table.Column<string>(type: "nvarchar(8)", maxLength: 8, nullable: false),
                    FileFullType = table.Column<string>(type: "nvarchar(11)", maxLength: 11, nullable: false),
                    Ver = table.Column<string>(type: "nvarchar(4)", maxLength: 4, nullable: false),
                    CreateTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdateTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UploadFileID = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false),
                    FileSize = table.Column<int>(type: "int", nullable: false),
                    Crc = table.Column<string>(type: "nvarchar(8)", maxLength: 8, nullable: false),
                    Operator = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: true),
                    Remarks = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    IsDelete = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_FileVers", x => x.ID)
                        .Annotation("SqlServer:Clustered", true);
                });

            migrationBuilder.CreateTable(
                name: "IncrementContents",
                columns: table => new
                {
                    MerchantID = table.Column<string>(type: "nvarchar(8)", maxLength: 8, nullable: false),
                    IncrementType = table.Column<string>(type: "nvarchar(4)", maxLength: 4, nullable: false),
                    SerialNum = table.Column<int>(type: "int", nullable: false),
                    Content = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    CreateTime = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_IncrementContents", x => new { x.MerchantID, x.IncrementType, x.SerialNum })
                        .Annotation("SqlServer:Clustered", true);
                });

            migrationBuilder.CreateTable(
                name: "LoginLogs",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    LoginType = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    LoginMethod = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    SessionId = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    LogoutTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    UserId = table.Column<string>(type: "nvarchar(128)", maxLength: 128, nullable: true),
                    UserName = table.Column<string>(type: "nvarchar(128)", maxLength: 128, nullable: true),
                    RealName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    MerchantId = table.Column<string>(type: "nvarchar(8)", maxLength: 8, nullable: true),
                    MerchantName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    IpAddress = table.Column<string>(type: "nvarchar(45)", maxLength: 45, nullable: true),
                    UserAgent = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    OperationTime = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValue: new DateTime(2025, 8, 15, 16, 14, 2, 671, DateTimeKind.Local).AddTicks(9520)),
                    IsSuccess = table.Column<bool>(type: "bit", nullable: false),
                    FailureReason = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    Remarks = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_LoginLogs", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "MenuGroups",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    GroupKey = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    Title = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    IconName = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    SortOrder = table.Column<int>(type: "int", nullable: false),
                    IsEnabled = table.Column<bool>(type: "bit", nullable: false),
                    VisibleToSystemAdmin = table.Column<bool>(type: "bit", nullable: false),
                    VisibleToMerchantAdmin = table.Column<bool>(type: "bit", nullable: false),
                    VisibleToUser = table.Column<bool>(type: "bit", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValue: new DateTime(2025, 8, 15, 16, 14, 2, 672, DateTimeKind.Local).AddTicks(2568)),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValue: new DateTime(2025, 8, 15, 16, 14, 2, 672, DateTimeKind.Local).AddTicks(2743))
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MenuGroups", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Merchants",
                columns: table => new
                {
                    MerchantID = table.Column<string>(type: "nvarchar(8)", maxLength: 8, nullable: false),
                    Name = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    CompanyName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    ContactPerson = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    ContactInfo = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    Remark = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    Operator = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    AutoRegister = table.Column<bool>(type: "bit", nullable: false),
                    IsDelete = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Merchants", x => x.MerchantID)
                        .Annotation("SqlServer:Clustered", true);
                });

            migrationBuilder.CreateTable(
                name: "MsgBoxes",
                columns: table => new
                {
                    ID = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    MerchantID = table.Column<string>(type: "nvarchar(8)", maxLength: 8, nullable: false),
                    MsgContentID = table.Column<int>(type: "int", nullable: false),
                    TerminalID = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    Status = table.Column<int>(type: "int", nullable: false),
                    SendTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    ReadTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    ReplyTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    ReplyCode = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    ReplyContent = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MsgBoxes", x => x.ID)
                        .Annotation("SqlServer:Clustered", true);
                });

            migrationBuilder.CreateTable(
                name: "MsgContents",
                columns: table => new
                {
                    ID = table.Column<int>(type: "int", maxLength: 4, nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    MerchantID = table.Column<string>(type: "nvarchar(8)", maxLength: 8, nullable: false),
                    MsgTypeID = table.Column<string>(type: "nvarchar(4)", maxLength: 4, nullable: false),
                    Content = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: false),
                    CreateTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    Remark = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    Operator = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MsgContents", x => x.ID)
                        .Annotation("SqlServer:Clustered", true);
                });

            migrationBuilder.CreateTable(
                name: "MsgTypes",
                columns: table => new
                {
                    ID = table.Column<string>(type: "nvarchar(4)", maxLength: 4, nullable: false),
                    MerchantID = table.Column<string>(type: "nvarchar(8)", maxLength: 8, nullable: false),
                    Name = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    CodeType = table.Column<int>(type: "int", maxLength: 10, nullable: false),
                    ExampleMessage = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    Description = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MsgTypes", x => new { x.ID, x.MerchantID })
                        .Annotation("SqlServer:Clustered", true);
                });

            migrationBuilder.CreateTable(
                name: "OperationLogs",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Module = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    OperationType = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    OperationTarget = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    OperationDetails = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true),
                    RequestPath = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    HttpMethod = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: true),
                    ResponseStatusCode = table.Column<int>(type: "int", nullable: true),
                    ExecutionTime = table.Column<long>(type: "bigint", nullable: true),
                    UserId = table.Column<string>(type: "nvarchar(128)", maxLength: 128, nullable: true),
                    UserName = table.Column<string>(type: "nvarchar(128)", maxLength: 128, nullable: true),
                    RealName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    MerchantId = table.Column<string>(type: "nvarchar(8)", maxLength: 8, nullable: true),
                    MerchantName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    IpAddress = table.Column<string>(type: "nvarchar(45)", maxLength: 45, nullable: true),
                    UserAgent = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    OperationTime = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValue: new DateTime(2025, 8, 15, 16, 14, 2, 672, DateTimeKind.Local).AddTicks(1922)),
                    IsSuccess = table.Column<bool>(type: "bit", nullable: false),
                    FailureReason = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    Remarks = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_OperationLogs", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "PasswordChangeLogs",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    ChangeType = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    TargetUserId = table.Column<string>(type: "nvarchar(128)", maxLength: 128, nullable: true),
                    TargetUserName = table.Column<string>(type: "nvarchar(128)", maxLength: 128, nullable: true),
                    TargetRealName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    UserId = table.Column<string>(type: "nvarchar(128)", maxLength: 128, nullable: true),
                    UserName = table.Column<string>(type: "nvarchar(128)", maxLength: 128, nullable: true),
                    RealName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    MerchantId = table.Column<string>(type: "nvarchar(8)", maxLength: 8, nullable: true),
                    MerchantName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    IpAddress = table.Column<string>(type: "nvarchar(45)", maxLength: 45, nullable: true),
                    UserAgent = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    OperationTime = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValue: new DateTime(2025, 8, 15, 16, 14, 2, 672, DateTimeKind.Local).AddTicks(769)),
                    IsSuccess = table.Column<bool>(type: "bit", nullable: false),
                    FailureReason = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    Remarks = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PasswordChangeLogs", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "ScheduledFilePublishs",
                columns: table => new
                {
                    ID = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    FileVersionID = table.Column<int>(type: "int", nullable: false),
                    MerchantID = table.Column<string>(type: "nvarchar(8)", maxLength: 8, nullable: false),
                    FileTypeID = table.Column<string>(type: "nvarchar(3)", maxLength: 3, nullable: false),
                    FilePara = table.Column<string>(type: "nvarchar(8)", maxLength: 8, nullable: false),
                    FileFullType = table.Column<string>(type: "nvarchar(11)", maxLength: 11, nullable: false),
                    Ver = table.Column<string>(type: "char(4)", maxLength: 4, nullable: false),
                    PublishType = table.Column<int>(type: "int", nullable: false),
                    PublishTarget = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    ScheduledTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    Status = table.Column<int>(type: "int", nullable: false),
                    CreatedTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    ExecutedTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    ErrorMessage = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    Remarks = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ScheduledFilePublishs", x => x.ID)
                        .Annotation("SqlServer:Clustered", true);
                });

            migrationBuilder.CreateTable(
                name: "SystemSettings",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    EnableTwoFactorAuth = table.Column<bool>(type: "bit", nullable: false),
                    ForceTwoFactorAuth = table.Column<bool>(type: "bit", nullable: false),
                    EnableWechatLogin = table.Column<bool>(type: "bit", nullable: false),
                    ForcePasswordChangeDays = table.Column<int>(type: "int", nullable: false),
                    LastModified = table.Column<DateTime>(type: "datetime2", nullable: false),
                    LastModifiedBy = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SystemSettings", x => x.Id)
                        .Annotation("SqlServer:Clustered", true);
                });

            migrationBuilder.CreateTable(
                name: "TerminalEvents",
                columns: table => new
                {
                    ID = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    MerchantID = table.Column<string>(type: "nvarchar(8)", maxLength: 8, nullable: false),
                    TerminalID = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    EventTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    EventName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    EventType = table.Column<int>(type: "int", nullable: false),
                    Severity = table.Column<int>(type: "int", nullable: false),
                    Remark = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true),
                    Operator = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TerminalEvents", x => x.ID)
                        .Annotation("SqlServer:Clustered", true);
                });

            migrationBuilder.CreateTable(
                name: "TerminalLogs",
                columns: table => new
                {
                    ID = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    MerchantID = table.Column<string>(type: "nvarchar(8)", maxLength: 8, nullable: true),
                    LogType = table.Column<int>(type: "int", nullable: true),
                    SetMethod = table.Column<int>(type: "int", nullable: true),
                    CardNO = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    MachineID = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    MachineNO = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    LineNO = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    Price = table.Column<int>(type: "int", nullable: true),
                    DriverCardNO = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    LogTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    UploadTime = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TerminalLogs", x => x.ID)
                        .Annotation("SqlServer:Clustered", true);
                });

            migrationBuilder.CreateTable(
                name: "Terminals",
                columns: table => new
                {
                    ID = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    MerchantID = table.Column<string>(type: "nvarchar(8)", maxLength: 8, nullable: false),
                    MachineID = table.Column<string>(type: "nvarchar(8)", maxLength: 8, nullable: false),
                    DeviceNO = table.Column<string>(type: "nvarchar(8)", maxLength: 8, nullable: false),
                    LineNO = table.Column<string>(type: "nvarchar(8)", maxLength: 8, nullable: false),
                    TerminalType = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    CreateTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    StatusUpdateTime = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Terminals", x => x.ID)
                        .Annotation("SqlServer:Clustered", true);
                });

            migrationBuilder.CreateTable(
                name: "UnionPayTerminalKeys",
                columns: table => new
                {
                    ID = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    MerchantID = table.Column<string>(type: "nvarchar(8)", maxLength: 8, nullable: false),
                    UP_MerchantID = table.Column<string>(type: "nvarchar(15)", maxLength: 15, nullable: false),
                    UP_TerminalID = table.Column<string>(type: "nvarchar(8)", maxLength: 8, nullable: false),
                    UP_Key = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false),
                    UP_MerchantName = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    IsInUse = table.Column<bool>(type: "bit", nullable: false),
                    MachineID = table.Column<string>(type: "nvarchar(8)", maxLength: 8, nullable: true),
                    LineID = table.Column<string>(type: "nvarchar(8)", maxLength: 8, nullable: true),
                    MachineNO = table.Column<string>(type: "nvarchar(8)", maxLength: 8, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UnionPayTerminalKeys", x => x.ID)
                        .Annotation("SqlServer:Clustered", true);
                });

            migrationBuilder.CreateTable(
                name: "UploadFiles",
                columns: table => new
                {
                    ID = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false),
                    FileName = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    FilePath = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: false),
                    FileSize = table.Column<int>(type: "int", nullable: false),
                    Crc = table.Column<string>(type: "nvarchar(8)", maxLength: 8, nullable: false),
                    UploadTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    ObjectStorageUrl = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    BucketName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    ContentType = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    UploadedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    LastModifiedTime = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UploadFiles", x => x.ID)
                        .Annotation("SqlServer:Clustered", true);
                });

            migrationBuilder.CreateTable(
                name: "AspNetRoleClaims",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    RoleId = table.Column<string>(type: "nvarchar(128)", maxLength: 128, nullable: false),
                    ClaimType = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ClaimValue = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AspNetRoleClaims", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AspNetRoleClaims_AspNetRoles_RoleId",
                        column: x => x.RoleId,
                        principalTable: "AspNetRoles",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "AspNetUserClaims",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    UserId = table.Column<string>(type: "nvarchar(128)", maxLength: 128, nullable: false),
                    ClaimType = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ClaimValue = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AspNetUserClaims", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AspNetUserClaims_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "AspNetUserLogins",
                columns: table => new
                {
                    LoginProvider = table.Column<string>(type: "nvarchar(128)", maxLength: 128, nullable: false),
                    ProviderKey = table.Column<string>(type: "nvarchar(128)", maxLength: 128, nullable: false),
                    ProviderDisplayName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    UserId = table.Column<string>(type: "nvarchar(128)", maxLength: 128, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AspNetUserLogins", x => new { x.LoginProvider, x.ProviderKey });
                    table.ForeignKey(
                        name: "FK_AspNetUserLogins_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "AspNetUserRoles",
                columns: table => new
                {
                    UserId = table.Column<string>(type: "nvarchar(128)", maxLength: 128, nullable: false),
                    RoleId = table.Column<string>(type: "nvarchar(128)", maxLength: 128, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AspNetUserRoles", x => new { x.UserId, x.RoleId });
                    table.ForeignKey(
                        name: "FK_AspNetUserRoles_AspNetRoles_RoleId",
                        column: x => x.RoleId,
                        principalTable: "AspNetRoles",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_AspNetUserRoles_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "AspNetUserTokens",
                columns: table => new
                {
                    UserId = table.Column<string>(type: "nvarchar(128)", maxLength: 128, nullable: false),
                    LoginProvider = table.Column<string>(type: "nvarchar(128)", maxLength: 128, nullable: false),
                    Name = table.Column<string>(type: "nvarchar(128)", maxLength: 128, nullable: false),
                    Value = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AspNetUserTokens", x => new { x.UserId, x.LoginProvider, x.Name });
                    table.ForeignKey(
                        name: "FK_AspNetUserTokens_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "RoleFeaturePermissions",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    RoleName = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    FeatureKey = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    IsEnabled = table.Column<bool>(type: "bit", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETDATE()"),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETDATE()"),
                    CreatedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    UpdatedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RoleFeaturePermissions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_RoleFeaturePermissions_FeatureConfigs_FeatureKey",
                        column: x => x.FeatureKey,
                        principalTable: "FeatureConfigs",
                        principalColumn: "FeatureKey");
                });

            migrationBuilder.CreateTable(
                name: "MenuItems",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    MenuGroupId = table.Column<int>(type: "int", nullable: false),
                    ItemKey = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    Title = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Href = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    IconName = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    SortOrder = table.Column<int>(type: "int", nullable: false),
                    IsEnabled = table.Column<bool>(type: "bit", nullable: false),
                    VisibleToSystemAdmin = table.Column<bool>(type: "bit", nullable: false),
                    VisibleToMerchantAdmin = table.Column<bool>(type: "bit", nullable: false),
                    VisibleToUser = table.Column<bool>(type: "bit", nullable: false),
                    IsExternal = table.Column<bool>(type: "bit", nullable: false),
                    Target = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValue: new DateTime(2025, 8, 15, 16, 14, 2, 672, DateTimeKind.Local).AddTicks(3879)),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValue: new DateTime(2025, 8, 15, 16, 14, 2, 672, DateTimeKind.Local).AddTicks(4098))
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MenuItems", x => x.Id);
                    table.ForeignKey(
                        name: "FK_MenuItems_MenuGroups_MenuGroupId",
                        column: x => x.MenuGroupId,
                        principalTable: "MenuGroups",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "FareDiscountSchemes",
                columns: table => new
                {
                    ID = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    MerchantID = table.Column<string>(type: "nvarchar(8)", maxLength: 8, nullable: false),
                    SchemeName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    SchemeCode = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    Description = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    ExtraParamsJson = table.Column<string>(type: "text", nullable: true),
                    CardDiscountInfoJson = table.Column<string>(type: "text", nullable: true),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    CreateTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdateTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    Creator = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Updater = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    UsageCount = table.Column<int>(type: "int", nullable: false),
                    LastUsedTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    CurrentVersion = table.Column<string>(type: "nvarchar(4)", maxLength: 4, nullable: true),
                    CurrentFilePara = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    CurrentFileVerID = table.Column<int>(type: "int", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_FareDiscountSchemes", x => x.ID)
                        .Annotation("SqlServer:Clustered", true);
                    table.ForeignKey(
                        name: "FK_FareDiscountSchemes_Merchants_MerchantID",
                        column: x => x.MerchantID,
                        principalTable: "Merchants",
                        principalColumn: "MerchantID");
                });

            migrationBuilder.CreateTable(
                name: "MerchantDictionaries",
                columns: table => new
                {
                    ID = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    MerchantID = table.Column<string>(type: "nvarchar(8)", maxLength: 8, nullable: false),
                    DictionaryType = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    DictionaryCode = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    DictionaryLabel = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    DictionaryValue = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    ExtraValue1 = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    ExtraValue2 = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    SortOrder = table.Column<int>(type: "int", nullable: false),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    Description = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    CreateTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdateTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    Creator = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Updater = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MerchantDictionaries", x => x.ID)
                        .Annotation("SqlServer:Clustered", true);
                    table.ForeignKey(
                        name: "FK_MerchantDictionaries_Merchants_MerchantID",
                        column: x => x.MerchantID,
                        principalTable: "Merchants",
                        principalColumn: "MerchantID");
                });

            migrationBuilder.CreateTable(
                name: "VehicleInfos",
                columns: table => new
                {
                    ID = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    MerchantID = table.Column<string>(type: "nvarchar(8)", maxLength: 8, nullable: false),
                    DeviceNO = table.Column<string>(type: "nvarchar(8)", maxLength: 8, nullable: false),
                    LicensePlate = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    VehicleType = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Brand = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Model = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Color = table.Column<string>(type: "nvarchar(30)", maxLength: 30, nullable: true),
                    VIN = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    EngineNumber = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    RegistrationDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    ExpiryDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DriverName = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    DriverPhone = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    DriverLicense = table.Column<string>(type: "nvarchar(30)", maxLength: 30, nullable: true),
                    InsuranceCompany = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    InsurancePolicyNumber = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    InsuranceExpiryDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    MaintenanceStatus = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false, defaultValue: "Normal"),
                    LastMaintenanceDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    NextMaintenanceDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    Mileage = table.Column<decimal>(type: "decimal(10,2)", nullable: true),
                    FuelType = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    Remark = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    IsActive = table.Column<bool>(type: "bit", nullable: false, defaultValue: true),
                    CreateTime = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValue: new DateTime(2025, 8, 15, 16, 14, 2, 672, DateTimeKind.Local).AddTicks(6757)),
                    UpdateTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    Creator = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Updater = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VehicleInfos", x => x.ID);
                    table.ForeignKey(
                        name: "FK_VehicleInfos_Merchants_MerchantID",
                        column: x => x.MerchantID,
                        principalTable: "Merchants",
                        principalColumn: "MerchantID",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "TerminalStatuses",
                columns: table => new
                {
                    ID = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    LastActiveTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    ActiveStatus = table.Column<int>(type: "int", nullable: false),
                    LoginInTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    LoginOffTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    Token = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    ConnectionProtocol = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    EndPoint = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    FileVersions = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true),
                    Properties = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TerminalStatuses", x => x.ID)
                        .Annotation("SqlServer:Clustered", true);
                    table.ForeignKey(
                        name: "FK_TerminalStatuses_Terminals_ID",
                        column: x => x.ID,
                        principalTable: "Terminals",
                        principalColumn: "ID",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "TerminalFileUploads",
                columns: table => new
                {
                    ID = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false),
                    MerchantID = table.Column<string>(type: "nvarchar(8)", maxLength: 8, nullable: false),
                    TerminalID = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    TerminalType = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: false),
                    FileType = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: false),
                    FileName = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    FileSize = table.Column<long>(type: "bigint", nullable: false),
                    FileCRC = table.Column<string>(type: "nvarchar(8)", maxLength: 8, nullable: false),
                    ReceivedLength = table.Column<long>(type: "bigint", nullable: false),
                    ClientChunkSize = table.Column<int>(type: "int", nullable: false),
                    ServerChunkSize = table.Column<int>(type: "int", nullable: false),
                    Status = table.Column<int>(type: "int", nullable: false),
                    StartTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdateTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    TempFilePath = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    FinalUploadFileID = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: true),
                    ErrorMessage = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    UploadedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    LastActivityTime = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TerminalFileUploads", x => x.ID)
                        .Annotation("SqlServer:Clustered", true);
                    table.ForeignKey(
                        name: "FK_TerminalFileUploads_UploadFiles_FinalUploadFileID",
                        column: x => x.FinalUploadFileID,
                        principalTable: "UploadFiles",
                        principalColumn: "ID",
                        onDelete: ReferentialAction.SetNull);
                });

            migrationBuilder.CreateTable(
                name: "FareDiscountSchemeVersions",
                columns: table => new
                {
                    ID = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    MerchantID = table.Column<string>(type: "nvarchar(8)", maxLength: 8, nullable: false),
                    FareDiscountSchemeID = table.Column<int>(type: "int", nullable: false),
                    SchemeName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Version = table.Column<string>(type: "nvarchar(4)", maxLength: 4, nullable: false),
                    FilePara = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    ExtraParamsJson = table.Column<string>(type: "text", nullable: true),
                    CardDiscountInfoJson = table.Column<string>(type: "text", nullable: true),
                    FileContentJson = table.Column<string>(type: "text", nullable: true),
                    Status = table.Column<int>(type: "int", nullable: false),
                    IsPublished = table.Column<bool>(type: "bit", nullable: false),
                    FileVerID = table.Column<int>(type: "int", nullable: true),
                    CreateTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdateTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    SubmitTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    Creator = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Updater = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Submitter = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Remarks = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_FareDiscountSchemeVersions", x => x.ID)
                        .Annotation("SqlServer:Clustered", true);
                    table.ForeignKey(
                        name: "FK_FareDiscountSchemeVersions_FareDiscountSchemes_FareDiscountSchemeID",
                        column: x => x.FareDiscountSchemeID,
                        principalTable: "FareDiscountSchemes",
                        principalColumn: "ID");
                    table.ForeignKey(
                        name: "FK_FareDiscountSchemeVersions_Merchants_MerchantID",
                        column: x => x.MerchantID,
                        principalTable: "Merchants",
                        principalColumn: "MerchantID");
                });

            migrationBuilder.CreateTable(
                name: "LinePriceInfos",
                columns: table => new
                {
                    ID = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    MerchantID = table.Column<string>(type: "nvarchar(8)", maxLength: 8, nullable: false),
                    LineNumber = table.Column<string>(type: "nvarchar(4)", maxLength: 4, nullable: false),
                    GroupNumber = table.Column<string>(type: "nvarchar(2)", maxLength: 2, nullable: false),
                    LineName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Branch = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    Fare = table.Column<int>(type: "int", nullable: false),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    CreateTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdateTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    Creator = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Updater = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Remark = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    CurrentVersion = table.Column<string>(type: "nvarchar(4)", maxLength: 4, nullable: false),
                    CurrentFareDiscountSchemeID = table.Column<int>(type: "int", nullable: true),
                    CurrentSchemeName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_LinePriceInfos", x => x.ID)
                        .Annotation("SqlServer:Clustered", true);
                    table.ForeignKey(
                        name: "FK_LinePriceInfos_FareDiscountSchemes_CurrentFareDiscountSchemeID",
                        column: x => x.CurrentFareDiscountSchemeID,
                        principalTable: "FareDiscountSchemes",
                        principalColumn: "ID",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_LinePriceInfos_Merchants_MerchantID",
                        column: x => x.MerchantID,
                        principalTable: "Merchants",
                        principalColumn: "MerchantID");
                });

            migrationBuilder.CreateTable(
                name: "LinePriceInfoVersions",
                columns: table => new
                {
                    ID = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    MerchantID = table.Column<string>(type: "nvarchar(8)", maxLength: 8, nullable: false),
                    LinePriceInfoID = table.Column<int>(type: "int", nullable: false),
                    LineNumber = table.Column<string>(type: "nvarchar(4)", maxLength: 4, nullable: false),
                    GroupNumber = table.Column<string>(type: "nvarchar(2)", maxLength: 2, nullable: false),
                    LineName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Fare = table.Column<int>(type: "int", nullable: false),
                    Version = table.Column<string>(type: "nvarchar(4)", maxLength: 4, nullable: false),
                    ExtraParamsJson = table.Column<string>(type: "text", nullable: true),
                    CardDiscountInfoJson = table.Column<string>(type: "text", nullable: true),
                    FileContentJson = table.Column<string>(type: "text", nullable: true),
                    Status = table.Column<int>(type: "int", nullable: false),
                    IsPublished = table.Column<bool>(type: "bit", nullable: false),
                    FileVerID = table.Column<int>(type: "int", nullable: true),
                    CreateTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdateTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    SubmitTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    Creator = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Updater = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Submitter = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    FareDiscountSchemeID = table.Column<int>(type: "int", nullable: true),
                    SchemeName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_LinePriceInfoVersions", x => x.ID)
                        .Annotation("SqlServer:Clustered", true);
                    table.ForeignKey(
                        name: "FK_LinePriceInfoVersions_FareDiscountSchemes_FareDiscountSchemeID",
                        column: x => x.FareDiscountSchemeID,
                        principalTable: "FareDiscountSchemes",
                        principalColumn: "ID",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_LinePriceInfoVersions_LinePriceInfos_LinePriceInfoID",
                        column: x => x.LinePriceInfoID,
                        principalTable: "LinePriceInfos",
                        principalColumn: "ID");
                    table.ForeignKey(
                        name: "FK_LinePriceInfoVersions_Merchants_MerchantID",
                        column: x => x.MerchantID,
                        principalTable: "Merchants",
                        principalColumn: "MerchantID");
                });

            migrationBuilder.CreateIndex(
                name: "IX_AspNetRoleClaims_RoleId",
                table: "AspNetRoleClaims",
                column: "RoleId");

            migrationBuilder.CreateIndex(
                name: "RoleNameIndex",
                table: "AspNetRoles",
                column: "NormalizedName",
                unique: true,
                filter: "[NormalizedName] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_AspNetUserClaims_UserId",
                table: "AspNetUserClaims",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_AspNetUserLogins_UserId",
                table: "AspNetUserLogins",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_AspNetUserRoles_RoleId",
                table: "AspNetUserRoles",
                column: "RoleId");

            migrationBuilder.CreateIndex(
                name: "EmailIndex",
                table: "AspNetUsers",
                column: "NormalizedEmail");

            migrationBuilder.CreateIndex(
                name: "UserNameIndex",
                table: "AspNetUsers",
                column: "NormalizedUserName",
                unique: true,
                filter: "[NormalizedUserName] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_ConsumeDatas_ReceiveTime",
                table: "ConsumeDatas",
                column: "ReceiveTime")
                .Annotation("SqlServer:Include", new[] { "MerchantID", "MachineID", "MachineNO" });

            migrationBuilder.CreateIndex(
                name: "IX_FareDiscountSchemes_MerchantID_IsActive",
                table: "FareDiscountSchemes",
                columns: new[] { "MerchantID", "IsActive" });

            migrationBuilder.CreateIndex(
                name: "IX_FareDiscountSchemes_MerchantID_SchemeCode",
                table: "FareDiscountSchemes",
                columns: new[] { "MerchantID", "SchemeCode" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_FareDiscountSchemes_MerchantID_UsageCount",
                table: "FareDiscountSchemes",
                columns: new[] { "MerchantID", "UsageCount" });

            migrationBuilder.CreateIndex(
                name: "IX_FareDiscountSchemeVersions_CreateTime",
                table: "FareDiscountSchemeVersions",
                column: "CreateTime",
                descending: new bool[0]);

            migrationBuilder.CreateIndex(
                name: "IX_FareDiscountSchemeVersions_FareDiscountSchemeID",
                table: "FareDiscountSchemeVersions",
                column: "FareDiscountSchemeID");

            migrationBuilder.CreateIndex(
                name: "IX_FareDiscountSchemeVersions_MerchantID_FareDiscountSchemeID",
                table: "FareDiscountSchemeVersions",
                columns: new[] { "MerchantID", "FareDiscountSchemeID" });

            migrationBuilder.CreateIndex(
                name: "IX_FareDiscountSchemeVersions_MerchantID_Status",
                table: "FareDiscountSchemeVersions",
                columns: new[] { "MerchantID", "Status" });

            migrationBuilder.CreateIndex(
                name: "IDX_FeatureConfig_Category",
                table: "FeatureConfigs",
                column: "Category");

            migrationBuilder.CreateIndex(
                name: "UK_FeatureConfig_Key",
                table: "FeatureConfigs",
                column: "FeatureKey",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_FilePublishHistories_MerchantID_FileTypeID_FilePara_Ver",
                table: "FilePublishHistories",
                columns: new[] { "MerchantID", "FileTypeID", "FilePara", "Ver" });

            migrationBuilder.CreateIndex(
                name: "IX_FilePublishHistories_PublishTime",
                table: "FilePublishHistories",
                column: "PublishTime",
                descending: new bool[0])
                .Annotation("SqlServer:Include", new[] { "MerchantID", "FileTypeID", "Ver", "FilePara", "FileFullType", "PublishTarget", "OperationType" });

            migrationBuilder.CreateIndex(
                name: "IX_FilePublishs_MerchantID_FileFullType_PublishType_PublishTarget",
                table: "FilePublishs",
                columns: new[] { "MerchantID", "FileFullType", "PublishType", "PublishTarget" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_FilePublishs_MerchantID_FileTypeID_FilePara_PublishType_PublishTarget",
                table: "FilePublishs",
                columns: new[] { "MerchantID", "FileTypeID", "FilePara", "PublishType", "PublishTarget" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_FilePublishs_MerchantID_FileTypeID_PublishTime",
                table: "FilePublishs",
                columns: new[] { "MerchantID", "FileTypeID", "PublishTime" });

            migrationBuilder.CreateIndex(
                name: "IX_FilePublishs_PublishTime",
                table: "FilePublishs",
                column: "PublishTime",
                descending: new bool[0])
                .Annotation("SqlServer:Include", new[] { "MerchantID", "FileTypeID", "Ver", "FilePara", "FileFullType", "PublishTarget" });

            migrationBuilder.CreateIndex(
                name: "IX_FileVers_CreateTime",
                table: "FileVers",
                column: "CreateTime",
                descending: new bool[0])
                .Annotation("SqlServer:Include", new[] { "MerchantID", "FileTypeID", "Ver", "FilePara", "FileFullType" });

            migrationBuilder.CreateIndex(
                name: "IX_FileVers_MerchantID_FileFullType_Ver",
                table: "FileVers",
                columns: new[] { "MerchantID", "FileFullType", "Ver" });

            migrationBuilder.CreateIndex(
                name: "IX_FileVers_MerchantID_FileTypeID_FilePara_Ver",
                table: "FileVers",
                columns: new[] { "MerchantID", "FileTypeID", "FilePara", "Ver" });

            migrationBuilder.CreateIndex(
                name: "IX_LinePriceInfos_CurrentFareDiscountSchemeID",
                table: "LinePriceInfos",
                column: "CurrentFareDiscountSchemeID");

            migrationBuilder.CreateIndex(
                name: "IX_LinePriceInfos_MerchantID_LineNumber_GroupNumber",
                table: "LinePriceInfos",
                columns: new[] { "MerchantID", "LineNumber", "GroupNumber" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_LinePriceInfoVersions_FareDiscountSchemeID",
                table: "LinePriceInfoVersions",
                column: "FareDiscountSchemeID");

            migrationBuilder.CreateIndex(
                name: "IX_LinePriceInfoVersions_LinePriceInfoID",
                table: "LinePriceInfoVersions",
                column: "LinePriceInfoID");

            migrationBuilder.CreateIndex(
                name: "IX_LinePriceInfoVersions_MerchantID_LineNumber_GroupNumber",
                table: "LinePriceInfoVersions",
                columns: new[] { "MerchantID", "LineNumber", "GroupNumber" });

            migrationBuilder.CreateIndex(
                name: "IX_LinePriceInfoVersions_MerchantID_LinePriceInfoID",
                table: "LinePriceInfoVersions",
                columns: new[] { "MerchantID", "LinePriceInfoID" });

            migrationBuilder.CreateIndex(
                name: "IX_LinePriceInfoVersions_MerchantID_Status",
                table: "LinePriceInfoVersions",
                columns: new[] { "MerchantID", "Status" });

            migrationBuilder.CreateIndex(
                name: "IX_LoginLogs_LoginType_OperationTime",
                table: "LoginLogs",
                columns: new[] { "LoginType", "OperationTime" });

            migrationBuilder.CreateIndex(
                name: "IX_LoginLogs_MerchantId_OperationTime",
                table: "LoginLogs",
                columns: new[] { "MerchantId", "OperationTime" });

            migrationBuilder.CreateIndex(
                name: "IX_LoginLogs_OperationTime",
                table: "LoginLogs",
                column: "OperationTime",
                descending: new bool[0]);

            migrationBuilder.CreateIndex(
                name: "IX_LoginLogs_UserId_OperationTime",
                table: "LoginLogs",
                columns: new[] { "UserId", "OperationTime" });

            migrationBuilder.CreateIndex(
                name: "IX_MenuGroups_GroupKey",
                table: "MenuGroups",
                column: "GroupKey",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_MenuGroups_SortOrder",
                table: "MenuGroups",
                column: "SortOrder");

            migrationBuilder.CreateIndex(
                name: "IX_MenuItems_MenuGroupId_ItemKey",
                table: "MenuItems",
                columns: new[] { "MenuGroupId", "ItemKey" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_MenuItems_MenuGroupId_SortOrder",
                table: "MenuItems",
                columns: new[] { "MenuGroupId", "SortOrder" });

            migrationBuilder.CreateIndex(
                name: "IX_MerchantDictionaries_MerchantID_DictionaryType",
                table: "MerchantDictionaries",
                columns: new[] { "MerchantID", "DictionaryType" });

            migrationBuilder.CreateIndex(
                name: "IX_MerchantDictionaries_MerchantID_DictionaryType_DictionaryCode",
                table: "MerchantDictionaries",
                columns: new[] { "MerchantID", "DictionaryType", "DictionaryCode" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_MsgBoxes_MerchantID_Status_TerminalID_SendTime",
                table: "MsgBoxes",
                columns: new[] { "MerchantID", "Status", "TerminalID", "SendTime" });

            migrationBuilder.CreateIndex(
                name: "IX_MsgBoxes_SendTime",
                table: "MsgBoxes",
                column: "SendTime",
                descending: new bool[0])
                .Annotation("SqlServer:Include", new[] { "MerchantID", "Status", "TerminalID", "MsgContentID" });

            migrationBuilder.CreateIndex(
                name: "IX_MsgContents_MerchantID_CreateTime",
                table: "MsgContents",
                columns: new[] { "MerchantID", "CreateTime" },
                descending: new[] { false, true });

            migrationBuilder.CreateIndex(
                name: "IX_OperationLogs_MerchantId_OperationTime",
                table: "OperationLogs",
                columns: new[] { "MerchantId", "OperationTime" });

            migrationBuilder.CreateIndex(
                name: "IX_OperationLogs_Module_OperationType_OperationTime",
                table: "OperationLogs",
                columns: new[] { "Module", "OperationType", "OperationTime" });

            migrationBuilder.CreateIndex(
                name: "IX_OperationLogs_OperationTime",
                table: "OperationLogs",
                column: "OperationTime",
                descending: new bool[0]);

            migrationBuilder.CreateIndex(
                name: "IX_OperationLogs_RequestPath_OperationTime",
                table: "OperationLogs",
                columns: new[] { "RequestPath", "OperationTime" });

            migrationBuilder.CreateIndex(
                name: "IX_OperationLogs_UserId_OperationTime",
                table: "OperationLogs",
                columns: new[] { "UserId", "OperationTime" });

            migrationBuilder.CreateIndex(
                name: "IX_PasswordChangeLogs_ChangeType_OperationTime",
                table: "PasswordChangeLogs",
                columns: new[] { "ChangeType", "OperationTime" });

            migrationBuilder.CreateIndex(
                name: "IX_PasswordChangeLogs_MerchantId_OperationTime",
                table: "PasswordChangeLogs",
                columns: new[] { "MerchantId", "OperationTime" });

            migrationBuilder.CreateIndex(
                name: "IX_PasswordChangeLogs_OperationTime",
                table: "PasswordChangeLogs",
                column: "OperationTime",
                descending: new bool[0]);

            migrationBuilder.CreateIndex(
                name: "IX_PasswordChangeLogs_UserId_OperationTime",
                table: "PasswordChangeLogs",
                columns: new[] { "UserId", "OperationTime" });

            migrationBuilder.CreateIndex(
                name: "IDX_RoleFeaturePermission_Feature",
                table: "RoleFeaturePermissions",
                column: "FeatureKey");

            migrationBuilder.CreateIndex(
                name: "IDX_RoleFeaturePermission_Role",
                table: "RoleFeaturePermissions",
                column: "RoleName");

            migrationBuilder.CreateIndex(
                name: "UK_RoleFeaturePermission_RoleFeature",
                table: "RoleFeaturePermissions",
                columns: new[] { "RoleName", "FeatureKey" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_ScheduledFilePublishs_MerchantID_CreatedTime",
                table: "ScheduledFilePublishs",
                columns: new[] { "MerchantID", "CreatedTime" },
                descending: new[] { false, true });

            migrationBuilder.CreateIndex(
                name: "IX_ScheduledFilePublishs_MerchantID_Status",
                table: "ScheduledFilePublishs",
                columns: new[] { "MerchantID", "Status" });

            migrationBuilder.CreateIndex(
                name: "IX_ScheduledFilePublishs_Status_ScheduledTime",
                table: "ScheduledFilePublishs",
                columns: new[] { "Status", "ScheduledTime" });

            migrationBuilder.CreateIndex(
                name: "IX_TerminalEvents_EventTime",
                table: "TerminalEvents",
                column: "EventTime",
                descending: new bool[0])
                .Annotation("SqlServer:Include", new[] { "MerchantID", "TerminalID", "EventType" });

            migrationBuilder.CreateIndex(
                name: "IX_TerminalEvents_TerminalID_EventTime",
                table: "TerminalEvents",
                columns: new[] { "TerminalID", "EventTime" },
                descending: new[] { false, true })
                .Annotation("SqlServer:Include", new[] { "MerchantID", "EventType" });

            migrationBuilder.CreateIndex(
                name: "IX_TerminalFileUploads_FinalUploadFileID",
                table: "TerminalFileUploads",
                column: "FinalUploadFileID");

            migrationBuilder.CreateIndex(
                name: "IX_TerminalFileUploads_LastActivityTime",
                table: "TerminalFileUploads",
                column: "LastActivityTime");

            migrationBuilder.CreateIndex(
                name: "IX_TerminalFileUploads_MerchantID_TerminalID",
                table: "TerminalFileUploads",
                columns: new[] { "MerchantID", "TerminalID" });

            migrationBuilder.CreateIndex(
                name: "IX_TerminalFileUploads_StartTime",
                table: "TerminalFileUploads",
                column: "StartTime");

            migrationBuilder.CreateIndex(
                name: "IX_TerminalFileUploads_Status",
                table: "TerminalFileUploads",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_TerminalFileUploads_TerminalID_StartTime",
                table: "TerminalFileUploads",
                columns: new[] { "TerminalID", "StartTime" });

            migrationBuilder.CreateIndex(
                name: "IX_TerminalLogs_CardNO_LogTime",
                table: "TerminalLogs",
                columns: new[] { "CardNO", "LogTime" },
                descending: new[] { false, true });

            migrationBuilder.CreateIndex(
                name: "IX_TerminalLogs_LineNO_LogTime",
                table: "TerminalLogs",
                columns: new[] { "LineNO", "LogTime" },
                descending: new[] { false, true });

            migrationBuilder.CreateIndex(
                name: "IX_TerminalLogs_LogTime",
                table: "TerminalLogs",
                column: "LogTime",
                descending: new bool[0]);

            migrationBuilder.CreateIndex(
                name: "IX_TerminalLogs_LogType_LogTime",
                table: "TerminalLogs",
                columns: new[] { "LogType", "LogTime" },
                descending: new[] { false, true });

            migrationBuilder.CreateIndex(
                name: "IX_TerminalLogs_MachineID_LogTime",
                table: "TerminalLogs",
                columns: new[] { "MachineID", "LogTime" },
                descending: new[] { false, true });

            migrationBuilder.CreateIndex(
                name: "IX_TerminalLogs_MachineNO_LogTime",
                table: "TerminalLogs",
                columns: new[] { "MachineNO", "LogTime" },
                descending: new[] { false, true });

            migrationBuilder.CreateIndex(
                name: "IX_TerminalLogs_MerchantID_LogTime",
                table: "TerminalLogs",
                columns: new[] { "MerchantID", "LogTime" },
                descending: new[] { false, true });

            migrationBuilder.CreateIndex(
                name: "IX_Terminals_MerchantID_DeviceNO",
                table: "Terminals",
                columns: new[] { "MerchantID", "DeviceNO" });

            migrationBuilder.CreateIndex(
                name: "IX_Terminals_MerchantID_MachineID",
                table: "Terminals",
                columns: new[] { "MerchantID", "MachineID" });

            migrationBuilder.CreateIndex(
                name: "IX_UnionPayTerminalKeys_MerchantID_IsInUse",
                table: "UnionPayTerminalKeys",
                columns: new[] { "MerchantID", "IsInUse" });

            migrationBuilder.CreateIndex(
                name: "IX_UnionPayTerminalKeys_MerchantID_MachineID",
                table: "UnionPayTerminalKeys",
                columns: new[] { "MerchantID", "MachineID" });

            migrationBuilder.CreateIndex(
                name: "IX_UnionPayTerminalKeys_UP_MerchantID_UP_TerminalID",
                table: "UnionPayTerminalKeys",
                columns: new[] { "UP_MerchantID", "UP_TerminalID" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IDX_Vehicle_Driver",
                table: "VehicleInfos",
                column: "DriverName");

            migrationBuilder.CreateIndex(
                name: "IDX_Vehicle_License",
                table: "VehicleInfos",
                column: "LicensePlate");

            migrationBuilder.CreateIndex(
                name: "IDX_Vehicle_Merchant",
                table: "VehicleInfos",
                column: "MerchantID");

            migrationBuilder.CreateIndex(
                name: "UK_Vehicle_MerchantDevice",
                table: "VehicleInfos",
                columns: new[] { "MerchantID", "DeviceNO" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "UK_Vehicle_MerchantLicense",
                table: "VehicleInfos",
                columns: new[] { "MerchantID", "LicensePlate" },
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "AspNetRoleClaims");

            migrationBuilder.DropTable(
                name: "AspNetUserClaims");

            migrationBuilder.DropTable(
                name: "AspNetUserLogins");

            migrationBuilder.DropTable(
                name: "AspNetUserRoles");

            migrationBuilder.DropTable(
                name: "AspNetUserTokens");

            migrationBuilder.DropTable(
                name: "ConsumeDatas");

            migrationBuilder.DropTable(
                name: "FareDiscountSchemeVersions");

            migrationBuilder.DropTable(
                name: "FilePublishHistories");

            migrationBuilder.DropTable(
                name: "FilePublishs");

            migrationBuilder.DropTable(
                name: "FileTypes");

            migrationBuilder.DropTable(
                name: "FileVers");

            migrationBuilder.DropTable(
                name: "IncrementContents");

            migrationBuilder.DropTable(
                name: "LinePriceInfoVersions");

            migrationBuilder.DropTable(
                name: "LoginLogs");

            migrationBuilder.DropTable(
                name: "MenuItems");

            migrationBuilder.DropTable(
                name: "MerchantDictionaries");

            migrationBuilder.DropTable(
                name: "MsgBoxes");

            migrationBuilder.DropTable(
                name: "MsgContents");

            migrationBuilder.DropTable(
                name: "MsgTypes");

            migrationBuilder.DropTable(
                name: "OperationLogs");

            migrationBuilder.DropTable(
                name: "PasswordChangeLogs");

            migrationBuilder.DropTable(
                name: "RoleFeaturePermissions");

            migrationBuilder.DropTable(
                name: "ScheduledFilePublishs");

            migrationBuilder.DropTable(
                name: "SystemSettings");

            migrationBuilder.DropTable(
                name: "TerminalEvents");

            migrationBuilder.DropTable(
                name: "TerminalFileUploads");

            migrationBuilder.DropTable(
                name: "TerminalLogs");

            migrationBuilder.DropTable(
                name: "TerminalStatuses");

            migrationBuilder.DropTable(
                name: "UnionPayTerminalKeys");

            migrationBuilder.DropTable(
                name: "VehicleInfos");

            migrationBuilder.DropTable(
                name: "AspNetRoles");

            migrationBuilder.DropTable(
                name: "AspNetUsers");

            migrationBuilder.DropTable(
                name: "LinePriceInfos");

            migrationBuilder.DropTable(
                name: "MenuGroups");

            migrationBuilder.DropTable(
                name: "FeatureConfigs");

            migrationBuilder.DropTable(
                name: "UploadFiles");

            migrationBuilder.DropTable(
                name: "Terminals");

            migrationBuilder.DropTable(
                name: "FareDiscountSchemes");

            migrationBuilder.DropTable(
                name: "Merchants");
        }
    }
}
