{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore.Database.Command": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Server=localhost;Database=TcpserverTms;User Id=sa;Password=***********;TrustServerCertificate=true;MultipleActiveResultSets=true"}, "DatabaseProvider": "SqlServer", "RabbitMQ": {"HostName": "localhost", "UserName": "guest", "Password": "guest", "Port": 5672}, "FileService": {"DefaultStorageType": "Local", "LocalFilePath": "C:\\Files", "MinIO": {"Endpoint": "minio.example.com", "AccessKey": "your-access-key", "SecretKey": "your-secret-key", "BucketName": "your-bucket-name"}}}