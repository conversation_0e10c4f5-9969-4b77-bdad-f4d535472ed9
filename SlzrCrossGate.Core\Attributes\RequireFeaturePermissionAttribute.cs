using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.DependencyInjection;
using SlzrCrossGate.Core.Services;

namespace SlzrCrossGate.Core.Attributes
{
    /// <summary>
    /// 功能权限验证特性
    /// 用于API方法的权限验证
    /// </summary>
    [AttributeUsage(AttributeTargets.Method | AttributeTargets.Class)]
    public class RequireFeaturePermissionAttribute : Attribute, IAuthorizationFilter
    {
        private readonly string _featureKey;
        private readonly bool _requireAll;

        public RequireFeaturePermissionAttribute(string featureKey, bool requireAll = false)
        {
            _featureKey = featureKey;
            _requireAll = requireAll;
        }

        public void OnAuthorization(AuthorizationFilterContext context)
        {
            var permissionService = context.HttpContext.RequestServices
                .GetRequiredService<IFeaturePermissionService>();
            
            var user = context.HttpContext.User;
            if (!user.Identity?.IsAuthenticated == true)
            {
                context.Result = new UnauthorizedResult();
                return;
            }

            var hasPermission = permissionService.HasPermissionAsync(user, _featureKey).Result;
            if (!hasPermission)
            {
                context.Result = new ForbidResult();
            }
        }
    }
}
