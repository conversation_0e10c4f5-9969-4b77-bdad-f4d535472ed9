{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"AuthConnection": "Server=localhost;Port=3306;Database=tcpserver;User=root;Password=**********;SslMode=Required;AllowLoadLocalInfile=true;", "CustomerConnection": "Server=localhost;Port=3306;Database=tcpserver;User=root;Password=**********;SslMode=Required;AllowLoadLocalInfile=true;"}, "Jwt": {"Key": "YourSecretKeyHere12345678901234567890", "Issuer": "slzr-cn.com", "Audience": "WebAdmin"}, "MainApp": {"BaseUrl": "http://localhost:5270"}, "Customer": {"Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DisplayName": "珠海通", "TablePrefix": "Customer_<PERSON>g", "BasePath": "/zhuhaitong"}, "FileStats": {"RootPath": "/app/data"}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/zhuhaitong-.log", "rollingInterval": "Day", "retainedFileCountLimit": 7, "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"]}}