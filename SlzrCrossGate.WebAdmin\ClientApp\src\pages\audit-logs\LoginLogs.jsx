import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  TablePagination,
  Chip,
  Grid,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Alert,
  CircularProgress,
  Tooltip,
  IconButton
} from '@mui/material';
import {
  Search as SearchIcon,
  Clear as ClearIcon,
  Download as DownloadIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { zhCN } from 'date-fns/locale';
import { useSnackbar } from 'notistack';
import { auditLogAPI } from '../../services/api';
import { useAuth } from '../../contexts/AuthContext';
import MerchantAutocomplete from '../../components/MerchantAutocomplete';
import { formatDateTime } from '../../utils/dateUtils';

const LoginLogs = () => {
  const { user } = useAuth();
  const { enqueueSnackbar } = useSnackbar();
  
  // 状态管理
  const [logs, setLogs] = useState([]);
  const [loading, setLoading] = useState(false);
  const [exporting, setExporting] = useState(false);
  const [totalCount, setTotalCount] = useState(0);
  const [page, setPage] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  // 移除手动商户管理，使用MerchantAutocomplete内置功能
  const [selectedMerchant, setSelectedMerchant] = useState(null);

  // 查询条件
  const [filters, setFilters] = useState({
    userName: '',
    realName: '',
    merchantId: '',
    ipAddress: '',
    loginType: '',
    loginMethod: '',
    startTime: null,
    endTime: null,
    isSuccess: ''
  });

  // 登录类型选项
  const loginTypeOptions = [
    { value: '', label: '全部' },
    { value: '用户名密码', label: '用户名密码' },
    { value: '微信扫码', label: '微信扫码' },
    { value: '双因素认证', label: '双因素认证' }
  ];

  // 登录方式选项
  const loginMethodOptions = [
    { value: '', label: '全部' },
    { value: 'Web', label: 'Web' },
    { value: 'API', label: 'API' },
    { value: 'Mobile', label: 'Mobile' }
  ];

  // 成功状态选项
  const successOptions = [
    { value: '', label: '全部' },
    { value: 'true', label: '成功' },
    { value: 'false', label: '失败' }
  ];

  // 防重复请求
  const loadLogsRef = useRef();
  const hasLoadedRef = useRef(false);

  // 移除手动商户加载，使用MerchantAutocomplete内置功能

  // 加载登录日志
  const loadLogs = async (isInitialLoad = false, forceLoad = false) => {
    try {
      setLoading(true);

      const params = {
        page: page + 1,
        pageSize,
        ...filters,
        merchantId: selectedMerchant?.merchantID || undefined, // 使用selectedMerchant
        isSuccess: filters.isSuccess === '' ? undefined : filters.isSuccess === 'true'
      };

      // 移除filters中的merchantId，避免重复
      if (params.merchantId === undefined) {
        delete params.merchantId;
      }

      // 移除空值
      Object.keys(params).forEach(key => {
        if (params[key] === '' || params[key] === null || params[key] === undefined) {
          delete params[key];
        }
      });

      // 避免重复请求：检查参数是否真的发生了变化（除非强制加载）
      const paramsString = JSON.stringify(params);
      if (!isInitialLoad && !forceLoad && loadLogsRef.current === paramsString) {
        console.log('LoginLogs: 参数未变化，跳过重复请求');
        setLoading(false);
        return;
      }
      loadLogsRef.current = paramsString;

      console.log('LoginLogs: 执行数据请求', params);
      const response = await auditLogAPI.getLoginLogs(params);
      setLogs(response.items || []);
      setTotalCount(response.totalCount || 0);
    } catch (error) {
      console.error('加载登录日志失败:', error);
      enqueueSnackbar('加载登录日志失败', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };

  // 统一的数据加载逻辑
  useEffect(() => {
    // 防止重复加载
    if (hasLoadedRef.current) {
      console.log('LoginLogs: 已加载过，跳过重复请求');
      return;
    }

    console.log('LoginLogs: 执行首次加载');
    hasLoadedRef.current = true;
    loadLogs(true, false); // 标记为初始加载，非强制加载
  }, []);

  // 当参数变化时重新加载
  useEffect(() => {
    if (hasLoadedRef.current) { // 只有在首次加载完成后才响应参数变化
      console.log('LoginLogs: 参数变化，重新加载');
      loadLogs(false, false); // 非初始加载，非强制加载
    }
  }, [page, pageSize, selectedMerchant]);

  // 处理筛选条件变化
  const handleFilterChange = (field, value) => {
    setFilters(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // 搜索
  const handleSearch = () => {
    setPage(0);
    loadLogs(false, true); // 强制执行搜索
  };

  // 清除筛选条件
  const handleClearFilters = () => {
    setFilters({
      userName: '',
      realName: '',
      merchantId: '', // 保留这个字段，但实际使用selectedMerchant
      ipAddress: '',
      loginType: '',
      loginMethod: '',
      startTime: null,
      endTime: null,
      isSuccess: ''
    });
    setSelectedMerchant(null); // 清除商户选择
    setPage(0);
  };

  // 刷新
  const handleRefresh = () => {
    loadLogs(false, true); // 强制执行刷新
  };

  // 导出
  const handleExport = async () => {
    setExporting(true);
    try {
      const params = {
        ...filters,
        isSuccess: filters.isSuccess === '' ? undefined : filters.isSuccess === 'true'
      };

      // 移除空值
      Object.keys(params).forEach(key => {
        if (params[key] === '' || params[key] === null || params[key] === undefined) {
          delete params[key];
        }
      });

      const blob = await auditLogAPI.exportLoginLogs(params);
      
      // 创建下载链接
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `登录日志_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.csv`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      //enqueueSnackbar('导出成功', { variant: 'success' });
    } catch (error) {
      console.error('导出失败:', error);
      enqueueSnackbar('导出失败', { variant: 'error' });
    } finally {
      setExporting(false);
    }
  };

  // 处理分页变化
  const handlePageChange = (event, newPage) => {
    setPage(newPage);
  };

  const handlePageSizeChange = (event) => {
    setPageSize(parseInt(event.target.value, 10));
    setPage(0);
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={zhCN}>
      <Box sx={{ p: 3 }}>
        <Typography variant="h4" gutterBottom>
          登录日志
        </Typography>

        {/* 筛选条件 */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Grid container spacing={2} alignItems="center">
              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  fullWidth
                  label="用户名"
                  value={filters.userName}
                  onChange={(e) => handleFilterChange('userName', e.target.value)}
                  size="small"
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  fullWidth
                  label="真实姓名"
                  value={filters.realName}
                  onChange={(e) => handleFilterChange('realName', e.target.value)}
                  size="small"
                />
              </Grid>
              {user?.roles?.includes('SystemAdmin') && (
                <Grid item xs={12} sm={6} md={3}>
                  <MerchantAutocomplete
                    size="small"
                    value={selectedMerchant?.merchantID}
                    onChange={(event, newValue) => {
                      setSelectedMerchant(newValue);
                    }}
                    label="商户（留空表示全部）"
                  />
                </Grid>
              )}
              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  fullWidth
                  label="IP地址"
                  value={filters.ipAddress}
                  onChange={(e) => handleFilterChange('ipAddress', e.target.value)}
                  size="small"
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <FormControl fullWidth size="small">
                  <InputLabel>登录类型</InputLabel>
                  <Select
                    value={filters.loginType}
                    label="登录类型"
                    onChange={(e) => handleFilterChange('loginType', e.target.value)}
                  >
                    {loginTypeOptions.map((option) => (
                      <MenuItem key={option.value} value={option.value}>
                        {option.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <FormControl fullWidth size="small">
                  <InputLabel>登录方式</InputLabel>
                  <Select
                    value={filters.loginMethod}
                    label="登录方式"
                    onChange={(e) => handleFilterChange('loginMethod', e.target.value)}
                  >
                    {loginMethodOptions.map((option) => (
                      <MenuItem key={option.value} value={option.value}>
                        {option.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <FormControl fullWidth size="small">
                  <InputLabel>结果</InputLabel>
                  <Select
                    value={filters.isSuccess}
                    label="结果"
                    onChange={(e) => handleFilterChange('isSuccess', e.target.value)}
                  >
                    {successOptions.map((option) => (
                      <MenuItem key={option.value} value={option.value}>
                        {option.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <DateTimePicker
                  label="开始时间"
                  value={filters.startTime}
                  onChange={(value) => handleFilterChange('startTime', value)}
                  slotProps={{
                    textField: {
                      size: 'small',
                      fullWidth: true
                    }
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <DateTimePicker
                  label="结束时间"
                  value={filters.endTime}
                  onChange={(value) => handleFilterChange('endTime', value)}
                  slotProps={{
                    textField: {
                      size: 'small',
                      fullWidth: true
                    }
                  }}
                />
              </Grid>
            </Grid>

            {/* 操作按钮 */}
            <Box sx={{ mt: 2, display: 'flex', gap: 1 }}>
              <Button
                variant="contained"
                startIcon={<SearchIcon />}
                onClick={handleSearch}
                disabled={loading}
              >
                搜索
              </Button>
              <Button
                variant="outlined"
                startIcon={<ClearIcon />}
                onClick={handleClearFilters}
              >
                清除
              </Button>
              <Button
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={handleRefresh}
                disabled={loading}
              >
                刷新
              </Button>
              <Button
                variant="outlined"
                startIcon={exporting ? <CircularProgress size={16} /> : <DownloadIcon />}
                onClick={handleExport}
                disabled={exporting}
              >
                导出
              </Button>
            </Box>
          </CardContent>
        </Card>

        {/* 数据表格 */}
        <Card>
          <CardContent>
            {loading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                <CircularProgress />
              </Box>
            ) : (
              <>
                <TableContainer component={Paper}>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>用户名</TableCell>
                        <TableCell>真实姓名</TableCell>
                        {user?.roles?.includes('SystemAdmin') && <TableCell>商户</TableCell>}
                        <TableCell>IP地址</TableCell>
                        <TableCell>登录类型</TableCell>
                        <TableCell>登录方式</TableCell>
                        <TableCell>登录时间</TableCell>
                        <TableCell>结果</TableCell>
                        <TableCell>失败原因</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {logs.map((log) => (
                        <TableRow key={log.id}>
                          <TableCell>{log.userName}</TableCell>
                          <TableCell>{log.realName}</TableCell>
                          {user?.roles?.includes('SystemAdmin') && (
                            <TableCell>{log.merchantName || log.merchantId}</TableCell>
                          )}
                          <TableCell>
                            <Tooltip title={log.ipAddress}>
                              <span>{log.ipAddress}</span>
                            </Tooltip>
                          </TableCell>
                          <TableCell>{log.loginType}</TableCell>
                          <TableCell>{log.loginMethod}</TableCell>
                          <TableCell>{formatDateTime(log.operationTime)}</TableCell>
                          <TableCell>
                            <Chip
                              label={log.isSuccess ? '成功' : '失败'}
                              color={log.isSuccess ? 'success' : 'error'}
                              size="small"
                            />
                          </TableCell>
                          <TableCell>
                            {log.failureReason && (
                              <Tooltip title={log.failureReason}>
                                <span>{log.failureReason.length > 20 ? `${log.failureReason.substring(0, 20)}...` : log.failureReason}</span>
                              </Tooltip>
                            )}
                          </TableCell>
                        </TableRow>
                      ))}
                      {logs.length === 0 && (
                        <TableRow>
                          <TableCell colSpan={user?.roles?.includes('SystemAdmin') ? 9 : 8} align="center">
                            暂无数据
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </TableContainer>

                <TablePagination
                  component="div"
                  count={totalCount}
                  page={page}
                  onPageChange={handlePageChange}
                  rowsPerPage={pageSize}
                  onRowsPerPageChange={handlePageSizeChange}
                  rowsPerPageOptions={[5, 10, 25, 50]}
                  labelRowsPerPage="每页行数:"
                  labelDisplayedRows={({ from, to, count }) => `${from}-${to} 共 ${count} 条`}
                />
              </>
            )}
          </CardContent>
        </Card>
      </Box>
    </LocalizationProvider>
  );
};

export default LoginLogs;
