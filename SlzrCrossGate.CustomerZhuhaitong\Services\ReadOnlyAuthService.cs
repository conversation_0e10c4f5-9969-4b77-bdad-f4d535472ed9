using Microsoft.EntityFrameworkCore;
using SlzrCrossGate.CustomerZhuhaitong.Data;
using SlzrCrossGate.CustomerZhuhaitong.Models;

namespace SlzrCrossGate.CustomerZhuhaitong.Services
{
    /// <summary>
    /// 只读认证服务 - 使用只读模型进行认证验证
    /// </summary>
    public class ReadOnlyAuthService
    {
        private readonly AuthReadOnlyContext _context;
        private readonly ILogger<ReadOnlyAuthService> _logger;

        public ReadOnlyAuthService(AuthReadOnlyContext context, ILogger<ReadOnlyAuthService> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// 验证用户是否存在且有效
        /// </summary>
        public async Task<UserReadOnly?> ValidateUserAsync(string userId)
        {
            try
            {
                var users = await _context.Users
                    .FromSqlRaw("SELECT Id, UserName, Email, MerchantID, EmailConfirmed, CreateTime, IsDeleted FROM AspNetUsers WHERE Id = {0} AND IsDeleted = 0", userId)
                    .ToListAsync();
                
                return users.FirstOrDefault();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证用户失败: {UserId}", userId);
                return null;
            }
        }

        /// <summary>
        /// 获取用户的商户信息
        /// </summary>
        public async Task<MerchantReadOnly?> GetUserMerchantAsync(string merchantId)
        {
            try
            {
                var merchants = await _context.Merchants
                    .FromSqlRaw("SELECT MerchantID, MerchantName, IsActive, CreatedAt FROM Merchants WHERE MerchantID = {0} AND IsActive = 1", merchantId)
                    .ToListAsync();
                
                return merchants.FirstOrDefault();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取商户信息失败: {MerchantId}", merchantId);
                return null;
            }
        }

        /// <summary>
        /// 获取用户角色
        /// </summary>
        public async Task<List<string>> GetUserRolesAsync(string userId)
        {
            try
            {
                var roles = await _context.Database
                    .SqlQueryRaw<string>(@"
                        SELECT r.Name 
                        FROM UserRoles ur 
                        INNER JOIN Roles r ON ur.RoleId = r.Id 
                        WHERE ur.UserId = {0}", userId)
                    .ToListAsync();

                return roles;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户角色失败: {UserId}", userId);
                return new List<string>();
            }
        }

        /// <summary>
        /// 验证用户是否有指定角色
        /// </summary>
        public async Task<bool> UserHasRoleAsync(string userId, string roleName)
        {
            try
            {
                var count = await _context.Database
                    .SqlQueryRaw<int>(@"
                        SELECT COUNT(*) 
                        FROM UserRoles ur 
                        INNER JOIN Roles r ON ur.RoleId = r.Id 
                        WHERE ur.UserId = {0} AND r.NormalizedName = {1}", 
                        userId, roleName.ToUpper())
                    .FirstOrDefaultAsync();

                return count > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证用户角色失败: {UserId}, {RoleName}", userId, roleName);
                return false;
            }
        }
    }
}
