﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace SlzrCrossGate.Migrations.SqlServer.Migrations
{
    /// <inheritdoc />
    public partial class AddExpireTimeForIncrementContent : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<DateTime>(
                name: "CreateTime",
                table: "VehicleInfos",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(2025, 8, 18, 10, 21, 2, 396, DateTimeKind.Local).AddTicks(2326),
                oldClrType: typeof(DateTime),
                oldType: "datetime2",
                oldDefaultValue: new DateTime(2025, 8, 15, 16, 14, 2, 672, DateTimeKind.Local).AddTicks(6757));

            migrationBuilder.AlterColumn<DateTime>(
                name: "OperationTime",
                table: "PasswordChangeLogs",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(2025, 8, 18, 10, 21, 2, 395, DateTimeKind.Local).AddTicks(6032),
                oldClrType: typeof(DateTime),
                oldType: "datetime2",
                oldDefaultValue: new DateTime(2025, 8, 15, 16, 14, 2, 672, DateTimeKind.Local).AddTicks(769));

            migrationBuilder.AlterColumn<DateTime>(
                name: "OperationTime",
                table: "OperationLogs",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(2025, 8, 18, 10, 21, 2, 395, DateTimeKind.Local).AddTicks(7350),
                oldClrType: typeof(DateTime),
                oldType: "datetime2",
                oldDefaultValue: new DateTime(2025, 8, 15, 16, 14, 2, 672, DateTimeKind.Local).AddTicks(1922));

            migrationBuilder.AlterColumn<DateTime>(
                name: "UpdatedAt",
                table: "MenuItems",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(2025, 8, 18, 10, 21, 2, 395, DateTimeKind.Local).AddTicks(9670),
                oldClrType: typeof(DateTime),
                oldType: "datetime2",
                oldDefaultValue: new DateTime(2025, 8, 15, 16, 14, 2, 672, DateTimeKind.Local).AddTicks(4098));

            migrationBuilder.AlterColumn<DateTime>(
                name: "CreatedAt",
                table: "MenuItems",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(2025, 8, 18, 10, 21, 2, 395, DateTimeKind.Local).AddTicks(9419),
                oldClrType: typeof(DateTime),
                oldType: "datetime2",
                oldDefaultValue: new DateTime(2025, 8, 15, 16, 14, 2, 672, DateTimeKind.Local).AddTicks(3879));

            migrationBuilder.AlterColumn<DateTime>(
                name: "UpdatedAt",
                table: "MenuGroups",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(2025, 8, 18, 10, 21, 2, 395, DateTimeKind.Local).AddTicks(8275),
                oldClrType: typeof(DateTime),
                oldType: "datetime2",
                oldDefaultValue: new DateTime(2025, 8, 15, 16, 14, 2, 672, DateTimeKind.Local).AddTicks(2743));

            migrationBuilder.AlterColumn<DateTime>(
                name: "CreatedAt",
                table: "MenuGroups",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(2025, 8, 18, 10, 21, 2, 395, DateTimeKind.Local).AddTicks(8117),
                oldClrType: typeof(DateTime),
                oldType: "datetime2",
                oldDefaultValue: new DateTime(2025, 8, 15, 16, 14, 2, 672, DateTimeKind.Local).AddTicks(2568));

            migrationBuilder.AlterColumn<DateTime>(
                name: "OperationTime",
                table: "LoginLogs",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(2025, 8, 18, 10, 21, 2, 395, DateTimeKind.Local).AddTicks(4718),
                oldClrType: typeof(DateTime),
                oldType: "datetime2",
                oldDefaultValue: new DateTime(2025, 8, 15, 16, 14, 2, 671, DateTimeKind.Local).AddTicks(9520));

            migrationBuilder.AddColumn<DateTime>(
                name: "ExpireTime",
                table: "IncrementContents",
                type: "datetime2",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ExpireTime",
                table: "IncrementContents");

            migrationBuilder.AlterColumn<DateTime>(
                name: "CreateTime",
                table: "VehicleInfos",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(2025, 8, 15, 16, 14, 2, 672, DateTimeKind.Local).AddTicks(6757),
                oldClrType: typeof(DateTime),
                oldType: "datetime2",
                oldDefaultValue: new DateTime(2025, 8, 18, 10, 21, 2, 396, DateTimeKind.Local).AddTicks(2326));

            migrationBuilder.AlterColumn<DateTime>(
                name: "OperationTime",
                table: "PasswordChangeLogs",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(2025, 8, 15, 16, 14, 2, 672, DateTimeKind.Local).AddTicks(769),
                oldClrType: typeof(DateTime),
                oldType: "datetime2",
                oldDefaultValue: new DateTime(2025, 8, 18, 10, 21, 2, 395, DateTimeKind.Local).AddTicks(6032));

            migrationBuilder.AlterColumn<DateTime>(
                name: "OperationTime",
                table: "OperationLogs",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(2025, 8, 15, 16, 14, 2, 672, DateTimeKind.Local).AddTicks(1922),
                oldClrType: typeof(DateTime),
                oldType: "datetime2",
                oldDefaultValue: new DateTime(2025, 8, 18, 10, 21, 2, 395, DateTimeKind.Local).AddTicks(7350));

            migrationBuilder.AlterColumn<DateTime>(
                name: "UpdatedAt",
                table: "MenuItems",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(2025, 8, 15, 16, 14, 2, 672, DateTimeKind.Local).AddTicks(4098),
                oldClrType: typeof(DateTime),
                oldType: "datetime2",
                oldDefaultValue: new DateTime(2025, 8, 18, 10, 21, 2, 395, DateTimeKind.Local).AddTicks(9670));

            migrationBuilder.AlterColumn<DateTime>(
                name: "CreatedAt",
                table: "MenuItems",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(2025, 8, 15, 16, 14, 2, 672, DateTimeKind.Local).AddTicks(3879),
                oldClrType: typeof(DateTime),
                oldType: "datetime2",
                oldDefaultValue: new DateTime(2025, 8, 18, 10, 21, 2, 395, DateTimeKind.Local).AddTicks(9419));

            migrationBuilder.AlterColumn<DateTime>(
                name: "UpdatedAt",
                table: "MenuGroups",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(2025, 8, 15, 16, 14, 2, 672, DateTimeKind.Local).AddTicks(2743),
                oldClrType: typeof(DateTime),
                oldType: "datetime2",
                oldDefaultValue: new DateTime(2025, 8, 18, 10, 21, 2, 395, DateTimeKind.Local).AddTicks(8275));

            migrationBuilder.AlterColumn<DateTime>(
                name: "CreatedAt",
                table: "MenuGroups",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(2025, 8, 15, 16, 14, 2, 672, DateTimeKind.Local).AddTicks(2568),
                oldClrType: typeof(DateTime),
                oldType: "datetime2",
                oldDefaultValue: new DateTime(2025, 8, 18, 10, 21, 2, 395, DateTimeKind.Local).AddTicks(8117));

            migrationBuilder.AlterColumn<DateTime>(
                name: "OperationTime",
                table: "LoginLogs",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(2025, 8, 15, 16, 14, 2, 671, DateTimeKind.Local).AddTicks(9520),
                oldClrType: typeof(DateTime),
                oldType: "datetime2",
                oldDefaultValue: new DateTime(2025, 8, 18, 10, 21, 2, 395, DateTimeKind.Local).AddTicks(4718));
        }
    }
}
