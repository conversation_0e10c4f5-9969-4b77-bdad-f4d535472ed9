<Project>
  <PropertyGroup>
    <!-- 解决方案级别的版本配置 -->
    <MajorVersion>1</MajorVersion>
    <MinorVersion>0</MinorVersion>
    <PatchVersion>4</PatchVersion>
    <BuildVersion>0</BuildVersion>
    
    <!-- 组合版本号 -->
    <VersionPrefix>$(MajorVersion).$(MinorVersion).$(PatchVersion)</VersionPrefix>
    <VersionSuffix Condition="'$(Configuration)' == 'Debug'">dev</VersionSuffix>

    <!-- 完整版本号配置 -->
    <Version>$(MajorVersion).$(MinorVersion).$(PatchVersion).$(BuildVersion)</Version>
    <AssemblyVersion>$(MajorVersion).$(MinorVersion).$(PatchVersion).$(BuildVersion)</AssemblyVersion>
    <FileVersion>$(MajorVersion).$(MinorVersion).$(PatchVersion).$(BuildVersion)</FileVersion>
    
    <!-- 通用属性 -->
    <Company>Your Company Name</Company>
    <Product>SlzrCrossGate Terminal Management System</Product>
    <Copyright>Copyright © $(Company) $([System.DateTime]::Now.Year)</Copyright>
    
    <!-- 构建时间戳 -->
    <BuildTime>$([System.DateTime]::Now.ToString("yyyy-MM-ddTHH:mm:ss.fffffffK"))</BuildTime>
  </PropertyGroup>

  <!-- 为程序集添加构建时间元数据 -->
  <ItemGroup>
    <AssemblyMetadata Include="BuildTime" Value="$(BuildTime)" />
  </ItemGroup>
</Project>
