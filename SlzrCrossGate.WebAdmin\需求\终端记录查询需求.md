# 需求
在前端增加一个终端记录查询页面，用于查询终端的交易记录，页面需要包含以下功能：
- [x] 支持选择商户，使用商户下拉框组件，非系统管理员只能选择自己的商户
- [x] 支持输入出厂序列号条件
- [x] 支持输入设备编号条件
- [x] 支持选择接收时间范围条件
- [x] 支持查询按钮，点击后查询符合条件的交易记录
- [x] 支持导出记录按钮，点击后导出符合条件的交易记录到文件，文件格式为CSV，交易数据用HEX格式展示，每行一条记录，只包含交易数据，不包含其它信息。

终端记录表是ConsumeData表（对应SlzrCrossGate.Core.Models.ConsumeData），字段说明如下：
| 字段名 | 字段类型 | 字段说明 |
| --- | --- | --- |
| Id | bigint | 主键，自增 |
| MerchantID | varchar(8) | 商户编号 |
| MachineID | varchar(8) | 出厂序列号 |
| MachineNO | varchar(8) | 设备编号 |
| PsamNO | varchar(12) | PSAM卡号 |
| Buffer | varbinary(2500) | 交易数据，需要用HEX格式展示 |
| ReceiveTime | datetime(6) | 接收时间 |

Buffer字段需要转化为HEX格式的字符串，然后展示出来。

