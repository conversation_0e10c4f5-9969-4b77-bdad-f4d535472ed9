using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using SlzrCrossGate.Core.Database;
using SlzrCrossGate.Core.Models;
using SlzrCrossGate.WebAdmin.DTOs;
using NPOI.XSSF.UserModel;
using NPOI.SS.UserModel;
using NPOI.SS.Util;
using System.Text;
using System.Globalization;

namespace SlzrCrossGate.WebAdmin.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class VehiclesController : ControllerBase
    {
        private readonly TcpDbContext _dbContext;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly ILogger<VehiclesController> _logger;

        public VehiclesController(
            TcpDbContext dbContext,
            UserManager<ApplicationUser> userManager,
            ILogger<VehiclesController> logger)
        {
            _dbContext = dbContext;
            _userManager = userManager;
            _logger = logger;
        }

        // GET: api/vehicles
        [HttpGet]
        public async Task<ActionResult<PaginatedResult<VehicleInfoDto>>> GetVehicles(
            [FromQuery] string? merchantId,
            [FromQuery] string? search,
            [FromQuery] string? vehicleType,
            [FromQuery] string? maintenanceStatus,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 10)
        {
            try
            {
                var currentUser = await _userManager.GetUserAsync(User);
                if (currentUser == null) return Unauthorized();

                var isSystemAdmin = await _userManager.IsInRoleAsync(currentUser, "SystemAdmin");

                var query = from vehicle in _dbContext.VehicleInfos
                           join merchant in _dbContext.Merchants on vehicle.MerchantID equals merchant.MerchantID
                           where vehicle.IsActive && !merchant.IsDelete
                           select new { Vehicle = vehicle, Merchant = merchant };

                // 权限控制
                if (!isSystemAdmin && !string.IsNullOrEmpty(currentUser.MerchantID))
                {
                    query = query.Where(x => x.Vehicle.MerchantID == currentUser.MerchantID);
                }

                // 商户筛选
                if (!string.IsNullOrEmpty(merchantId))
                {
                    query = query.Where(x => x.Vehicle.MerchantID == merchantId);
                }

                // 搜索筛选
                if (!string.IsNullOrEmpty(search))
                {
                    query = query.Where(x => 
                        x.Vehicle.LicensePlate.Contains(search) ||
                        x.Vehicle.DeviceNO.Contains(search) ||
                        (x.Vehicle.DriverName != null && x.Vehicle.DriverName.Contains(search)) ||
                        (x.Vehicle.Brand != null && x.Vehicle.Brand.Contains(search)) ||
                        (x.Vehicle.Model != null && x.Vehicle.Model.Contains(search)));
                }

                // 车辆类型筛选
                if (!string.IsNullOrEmpty(vehicleType))
                {
                    query = query.Where(x => x.Vehicle.VehicleType == vehicleType);
                }

                // 维护状态筛选
                if (!string.IsNullOrEmpty(maintenanceStatus))
                {
                    query = query.Where(x => x.Vehicle.MaintenanceStatus == maintenanceStatus);
                }

                var totalCount = await query.CountAsync();
                var vehicles = await query
                    .OrderByDescending(x => x.Vehicle.CreateTime)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .Select(x => new VehicleInfoDto
                    {
                        ID = x.Vehicle.ID,
                        MerchantID = x.Vehicle.MerchantID,
                        MerchantName = x.Merchant.Name ?? "",
                        DeviceNO = x.Vehicle.DeviceNO,
                        LicensePlate = x.Vehicle.LicensePlate,
                        VehicleType = x.Vehicle.VehicleType,
                        Brand = x.Vehicle.Brand,
                        Model = x.Vehicle.Model,
                        Color = x.Vehicle.Color,
                        VIN = x.Vehicle.VIN,
                        EngineNumber = x.Vehicle.EngineNumber,
                        RegistrationDate = x.Vehicle.RegistrationDate,
                        ExpiryDate = x.Vehicle.ExpiryDate,
                        DriverName = x.Vehicle.DriverName,
                        DriverPhone = x.Vehicle.DriverPhone,
                        DriverLicense = x.Vehicle.DriverLicense,
                        InsuranceCompany = x.Vehicle.InsuranceCompany,
                        InsurancePolicyNumber = x.Vehicle.InsurancePolicyNumber,
                        InsuranceExpiryDate = x.Vehicle.InsuranceExpiryDate,
                        MaintenanceStatus = x.Vehicle.MaintenanceStatus,
                        LastMaintenanceDate = x.Vehicle.LastMaintenanceDate,
                        NextMaintenanceDate = x.Vehicle.NextMaintenanceDate,
                        Mileage = x.Vehicle.Mileage,
                        FuelType = x.Vehicle.FuelType,
                        Remark = x.Vehicle.Remark,
                        IsActive = x.Vehicle.IsActive,
                        CreateTime = x.Vehicle.CreateTime,
                        UpdateTime = x.Vehicle.UpdateTime,
                        Creator = x.Vehicle.Creator,
                        Updater = x.Vehicle.Updater
                    })
                    .ToListAsync();

                return Ok(new PaginatedResult<VehicleInfoDto>
                {
                    Items = vehicles,
                    TotalCount = totalCount,
                    Page = page,
                    PageSize = pageSize
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取车辆列表时发生错误");
                return StatusCode(500, new { message = "服务器内部错误" });
            }
        }

        // GET: api/vehicles/{id}
        [HttpGet("{id}")]
        public async Task<ActionResult<VehicleInfoDto>> GetVehicle(int id)
        {
            try
            {
                var currentUser = await _userManager.GetUserAsync(User);
                if (currentUser == null) return Unauthorized();

                var isSystemAdmin = await _userManager.IsInRoleAsync(currentUser, "SystemAdmin");

                var vehicleWithMerchant = await (from vehicle in _dbContext.VehicleInfos
                                               join merchant in _dbContext.Merchants on vehicle.MerchantID equals merchant.MerchantID
                                               where vehicle.ID == id && vehicle.IsActive && !merchant.IsDelete
                                               select new { Vehicle = vehicle, Merchant = merchant })
                                               .FirstOrDefaultAsync();

                if (vehicleWithMerchant == null)
                {
                    return NotFound(new { message = "车辆不存在" });
                }

                // 权限检查
                if (!isSystemAdmin && currentUser.MerchantID != vehicleWithMerchant.Vehicle.MerchantID)
                {
                    return Forbid();
                }

                var vehicleDto = new VehicleInfoDto
                {
                    ID = vehicleWithMerchant.Vehicle.ID,
                    MerchantID = vehicleWithMerchant.Vehicle.MerchantID,
                    MerchantName = vehicleWithMerchant.Merchant.Name ?? "",
                    DeviceNO = vehicleWithMerchant.Vehicle.DeviceNO,
                    LicensePlate = vehicleWithMerchant.Vehicle.LicensePlate,
                    VehicleType = vehicleWithMerchant.Vehicle.VehicleType,
                    Brand = vehicleWithMerchant.Vehicle.Brand,
                    Model = vehicleWithMerchant.Vehicle.Model,
                    Color = vehicleWithMerchant.Vehicle.Color,
                    VIN = vehicleWithMerchant.Vehicle.VIN,
                    EngineNumber = vehicleWithMerchant.Vehicle.EngineNumber,
                    RegistrationDate = vehicleWithMerchant.Vehicle.RegistrationDate,
                    ExpiryDate = vehicleWithMerchant.Vehicle.ExpiryDate,
                    DriverName = vehicleWithMerchant.Vehicle.DriverName,
                    DriverPhone = vehicleWithMerchant.Vehicle.DriverPhone,
                    DriverLicense = vehicleWithMerchant.Vehicle.DriverLicense,
                    InsuranceCompany = vehicleWithMerchant.Vehicle.InsuranceCompany,
                    InsurancePolicyNumber = vehicleWithMerchant.Vehicle.InsurancePolicyNumber,
                    InsuranceExpiryDate = vehicleWithMerchant.Vehicle.InsuranceExpiryDate,
                    MaintenanceStatus = vehicleWithMerchant.Vehicle.MaintenanceStatus,
                    LastMaintenanceDate = vehicleWithMerchant.Vehicle.LastMaintenanceDate,
                    NextMaintenanceDate = vehicleWithMerchant.Vehicle.NextMaintenanceDate,
                    Mileage = vehicleWithMerchant.Vehicle.Mileage,
                    FuelType = vehicleWithMerchant.Vehicle.FuelType,
                    Remark = vehicleWithMerchant.Vehicle.Remark,
                    IsActive = vehicleWithMerchant.Vehicle.IsActive,
                    CreateTime = vehicleWithMerchant.Vehicle.CreateTime,
                    UpdateTime = vehicleWithMerchant.Vehicle.UpdateTime,
                    Creator = vehicleWithMerchant.Vehicle.Creator,
                    Updater = vehicleWithMerchant.Vehicle.Updater
                };

                return Ok(vehicleDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取车辆详情时发生错误，车辆ID: {VehicleId}", id);
                return StatusCode(500, new { message = "服务器内部错误" });
            }
        }

        // POST: api/vehicles
        [HttpPost]
        [Authorize(Roles = "SystemAdmin,MerchantAdmin")]
        public async Task<ActionResult<VehicleInfoDto>> CreateVehicle([FromBody] CreateVehicleDto createDto)
        {
            try
            {
                var currentUser = await _userManager.GetUserAsync(User);
                if (currentUser == null) return Unauthorized();

                var isSystemAdmin = await _userManager.IsInRoleAsync(currentUser, "SystemAdmin");

                // 权限检查：非系统管理员只能为自己的商户创建车辆
                if (!isSystemAdmin && currentUser.MerchantID != createDto.MerchantID)
                {
                    return Forbid();
                }

                // 检查商户是否存在
                var merchant = await _dbContext.Merchants.FindAsync(createDto.MerchantID);
                if (merchant == null || merchant.IsDelete)
                {
                    return BadRequest(new { message = "商户不存在" });
                }

                // 检查设备编号是否已被使用
                var existingVehicleByDevice = await _dbContext.VehicleInfos
                    .FirstOrDefaultAsync(v => v.MerchantID == createDto.MerchantID &&
                                            v.DeviceNO == createDto.DeviceNO &&
                                            v.IsActive);
                if (existingVehicleByDevice != null)
                {
                    return BadRequest(new { message = "该设备编号已被其他车辆使用" });
                }

                // 检查车牌号是否已被使用
                var existingVehicleByLicense = await _dbContext.VehicleInfos
                    .FirstOrDefaultAsync(v => v.MerchantID == createDto.MerchantID &&
                                            v.LicensePlate == createDto.LicensePlate &&
                                            v.IsActive);
                if (existingVehicleByLicense != null)
                {
                    return BadRequest(new { message = "该车牌号已被其他车辆使用" });
                }

                // 创建车辆信息
                var vehicle = new VehicleInfo
                {
                    MerchantID = createDto.MerchantID,
                    DeviceNO = createDto.DeviceNO,
                    LicensePlate = createDto.LicensePlate,
                    VehicleType = createDto.VehicleType,
                    Brand = createDto.Brand,
                    Model = createDto.Model,
                    Color = createDto.Color,
                    VIN = createDto.VIN,
                    EngineNumber = createDto.EngineNumber,
                    RegistrationDate = createDto.RegistrationDate,
                    ExpiryDate = createDto.ExpiryDate,
                    DriverName = createDto.DriverName,
                    DriverPhone = createDto.DriverPhone,
                    DriverLicense = createDto.DriverLicense,
                    InsuranceCompany = createDto.InsuranceCompany,
                    InsurancePolicyNumber = createDto.InsurancePolicyNumber,
                    InsuranceExpiryDate = createDto.InsuranceExpiryDate,
                    MaintenanceStatus = createDto.MaintenanceStatus ?? "Normal",
                    LastMaintenanceDate = createDto.LastMaintenanceDate,
                    NextMaintenanceDate = createDto.NextMaintenanceDate,
                    Mileage = createDto.Mileage,
                    FuelType = createDto.FuelType,
                    Remark = createDto.Remark,
                    Creator = currentUser.UserName,
                    CreateTime = DateTime.Now,
                    IsActive = true
                };

                _dbContext.VehicleInfos.Add(vehicle);
                await _dbContext.SaveChangesAsync();

                // 返回创建的车辆信息
                var vehicleDto = new VehicleInfoDto
                {
                    ID = vehicle.ID,
                    MerchantID = vehicle.MerchantID,
                    MerchantName = merchant.Name ?? "",
                    DeviceNO = vehicle.DeviceNO,
                    LicensePlate = vehicle.LicensePlate,
                    VehicleType = vehicle.VehicleType,
                    Brand = vehicle.Brand,
                    Model = vehicle.Model,
                    Color = vehicle.Color,
                    VIN = vehicle.VIN,
                    EngineNumber = vehicle.EngineNumber,
                    RegistrationDate = vehicle.RegistrationDate,
                    ExpiryDate = vehicle.ExpiryDate,
                    DriverName = vehicle.DriverName,
                    DriverPhone = vehicle.DriverPhone,
                    DriverLicense = vehicle.DriverLicense,
                    InsuranceCompany = vehicle.InsuranceCompany,
                    InsurancePolicyNumber = vehicle.InsurancePolicyNumber,
                    InsuranceExpiryDate = vehicle.InsuranceExpiryDate,
                    MaintenanceStatus = vehicle.MaintenanceStatus,
                    LastMaintenanceDate = vehicle.LastMaintenanceDate,
                    NextMaintenanceDate = vehicle.NextMaintenanceDate,
                    Mileage = vehicle.Mileage,
                    FuelType = vehicle.FuelType,
                    Remark = vehicle.Remark,
                    IsActive = vehicle.IsActive,
                    CreateTime = vehicle.CreateTime,
                    UpdateTime = vehicle.UpdateTime,
                    Creator = vehicle.Creator,
                    Updater = vehicle.Updater
                };

                return CreatedAtAction(nameof(GetVehicle), new { id = vehicle.ID }, vehicleDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建车辆时发生错误");
                return StatusCode(500, new { message = "服务器内部错误" });
            }
        }

        // PUT: api/vehicles/{id}
        [HttpPut("{id}")]
        [Authorize(Roles = "SystemAdmin,MerchantAdmin")]
        public async Task<IActionResult> UpdateVehicle(int id, [FromBody] UpdateVehicleDto updateDto)
        {
            try
            {
                var currentUser = await _userManager.GetUserAsync(User);
                if (currentUser == null) return Unauthorized();

                var isSystemAdmin = await _userManager.IsInRoleAsync(currentUser, "SystemAdmin");

                // 获取现有车辆信息
                var vehicle = await _dbContext.VehicleInfos.FindAsync(id);
                if (vehicle == null || !vehicle.IsActive)
                {
                    return NotFound(new { message = "车辆不存在" });
                }

                // 权限检查
                if (!isSystemAdmin && currentUser.MerchantID != vehicle.MerchantID)
                {
                    return Forbid();
                }

                // 权限检查：非系统管理员不能修改商户ID
                if (!isSystemAdmin && vehicle.MerchantID != updateDto.MerchantID)
                {
                    return Forbid();
                }

                // 检查设备编号是否被其他车辆使用
                if (vehicle.DeviceNO != updateDto.DeviceNO)
                {
                    var existingVehicleByDevice = await _dbContext.VehicleInfos
                        .FirstOrDefaultAsync(v => v.MerchantID == updateDto.MerchantID &&
                                                v.DeviceNO == updateDto.DeviceNO &&
                                                v.IsActive && v.ID != id);
                    if (existingVehicleByDevice != null)
                    {
                        return BadRequest(new { message = "该设备编号已被其他车辆使用" });
                    }
                }

                // 检查车牌号是否被其他车辆使用
                if (vehicle.LicensePlate != updateDto.LicensePlate)
                {
                    var existingVehicleByLicense = await _dbContext.VehicleInfos
                        .FirstOrDefaultAsync(v => v.MerchantID == updateDto.MerchantID &&
                                                v.LicensePlate == updateDto.LicensePlate &&
                                                v.IsActive && v.ID != id);
                    if (existingVehicleByLicense != null)
                    {
                        return BadRequest(new { message = "该车牌号已被其他车辆使用" });
                    }
                }

                // 更新车辆信息
                vehicle.MerchantID = updateDto.MerchantID;
                vehicle.DeviceNO = updateDto.DeviceNO;
                vehicle.LicensePlate = updateDto.LicensePlate;
                vehicle.VehicleType = updateDto.VehicleType;
                vehicle.Brand = updateDto.Brand;
                vehicle.Model = updateDto.Model;
                vehicle.Color = updateDto.Color;
                vehicle.VIN = updateDto.VIN;
                vehicle.EngineNumber = updateDto.EngineNumber;
                vehicle.RegistrationDate = updateDto.RegistrationDate;
                vehicle.ExpiryDate = updateDto.ExpiryDate;
                vehicle.DriverName = updateDto.DriverName;
                vehicle.DriverPhone = updateDto.DriverPhone;
                vehicle.DriverLicense = updateDto.DriverLicense;
                vehicle.InsuranceCompany = updateDto.InsuranceCompany;
                vehicle.InsurancePolicyNumber = updateDto.InsurancePolicyNumber;
                vehicle.InsuranceExpiryDate = updateDto.InsuranceExpiryDate;
                vehicle.MaintenanceStatus = updateDto.MaintenanceStatus ?? "Normal";
                vehicle.LastMaintenanceDate = updateDto.LastMaintenanceDate;
                vehicle.NextMaintenanceDate = updateDto.NextMaintenanceDate;
                vehicle.Mileage = updateDto.Mileage;
                vehicle.FuelType = updateDto.FuelType;
                vehicle.Remark = updateDto.Remark;
                vehicle.Updater = currentUser.UserName;
                vehicle.UpdateTime = DateTime.Now;

                await _dbContext.SaveChangesAsync();

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新车辆时发生错误，车辆ID: {VehicleId}", id);
                return StatusCode(500, new { message = "服务器内部错误" });
            }
        }

        // DELETE: api/vehicles/{id}
        [HttpDelete("{id}")]
        [Authorize(Roles = "SystemAdmin,MerchantAdmin")]
        public async Task<IActionResult> DeleteVehicle(int id)
        {
            try
            {
                var currentUser = await _userManager.GetUserAsync(User);
                if (currentUser == null) return Unauthorized();

                var isSystemAdmin = await _userManager.IsInRoleAsync(currentUser, "SystemAdmin");

                var vehicle = await _dbContext.VehicleInfos.FindAsync(id);
                if (vehicle == null || !vehicle.IsActive)
                {
                    return NotFound(new { message = "车辆不存在" });
                }

                // 权限检查
                if (!isSystemAdmin && currentUser.MerchantID != vehicle.MerchantID)
                {
                    return Forbid();
                }

                // 软删除
                vehicle.IsActive = false;
                vehicle.Updater = currentUser.UserName;
                vehicle.UpdateTime = DateTime.Now;

                await _dbContext.SaveChangesAsync();

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除车辆时发生错误，车辆ID: {VehicleId}", id);
                return StatusCode(500, new { message = "服务器内部错误" });
            }
        }

        // GET: api/vehicles/by-device/{deviceNo}
        [HttpGet("by-device/{deviceNo}")]
        public async Task<ActionResult<VehicleInfoDto>> GetVehicleByDevice(string deviceNo, [FromQuery] string? merchantId = null)
        {
            try
            {
                var currentUser = await _userManager.GetUserAsync(User);
                if (currentUser == null) return Unauthorized();

                var isSystemAdmin = await _userManager.IsInRoleAsync(currentUser, "SystemAdmin");

                var query = from vehicle in _dbContext.VehicleInfos
                           join merchant in _dbContext.Merchants on vehicle.MerchantID equals merchant.MerchantID
                           where vehicle.DeviceNO == deviceNo && vehicle.IsActive && !merchant.IsDelete
                           select new { Vehicle = vehicle, Merchant = merchant };

                // 如果指定了商户ID，则筛选
                if (!string.IsNullOrEmpty(merchantId))
                {
                    query = query.Where(x => x.Vehicle.MerchantID == merchantId);
                }

                // 权限控制
                if (!isSystemAdmin && !string.IsNullOrEmpty(currentUser.MerchantID))
                {
                    query = query.Where(x => x.Vehicle.MerchantID == currentUser.MerchantID);
                }

                var vehicleWithMerchant = await query.FirstOrDefaultAsync();

                if (vehicleWithMerchant == null)
                {
                    return NotFound(new { message = "未找到对应的车辆信息" });
                }

                var vehicleDto = new VehicleInfoDto
                {
                    ID = vehicleWithMerchant.Vehicle.ID,
                    MerchantID = vehicleWithMerchant.Vehicle.MerchantID,
                    MerchantName = vehicleWithMerchant.Merchant.Name ?? "",
                    DeviceNO = vehicleWithMerchant.Vehicle.DeviceNO,
                    LicensePlate = vehicleWithMerchant.Vehicle.LicensePlate,
                    VehicleType = vehicleWithMerchant.Vehicle.VehicleType,
                    Brand = vehicleWithMerchant.Vehicle.Brand,
                    Model = vehicleWithMerchant.Vehicle.Model,
                    Color = vehicleWithMerchant.Vehicle.Color,
                    VIN = vehicleWithMerchant.Vehicle.VIN,
                    EngineNumber = vehicleWithMerchant.Vehicle.EngineNumber,
                    RegistrationDate = vehicleWithMerchant.Vehicle.RegistrationDate,
                    ExpiryDate = vehicleWithMerchant.Vehicle.ExpiryDate,
                    DriverName = vehicleWithMerchant.Vehicle.DriverName,
                    DriverPhone = vehicleWithMerchant.Vehicle.DriverPhone,
                    DriverLicense = vehicleWithMerchant.Vehicle.DriverLicense,
                    InsuranceCompany = vehicleWithMerchant.Vehicle.InsuranceCompany,
                    InsurancePolicyNumber = vehicleWithMerchant.Vehicle.InsurancePolicyNumber,
                    InsuranceExpiryDate = vehicleWithMerchant.Vehicle.InsuranceExpiryDate,
                    MaintenanceStatus = vehicleWithMerchant.Vehicle.MaintenanceStatus,
                    LastMaintenanceDate = vehicleWithMerchant.Vehicle.LastMaintenanceDate,
                    NextMaintenanceDate = vehicleWithMerchant.Vehicle.NextMaintenanceDate,
                    Mileage = vehicleWithMerchant.Vehicle.Mileage,
                    FuelType = vehicleWithMerchant.Vehicle.FuelType,
                    Remark = vehicleWithMerchant.Vehicle.Remark,
                    IsActive = vehicleWithMerchant.Vehicle.IsActive,
                    CreateTime = vehicleWithMerchant.Vehicle.CreateTime,
                    UpdateTime = vehicleWithMerchant.Vehicle.UpdateTime,
                    Creator = vehicleWithMerchant.Vehicle.Creator,
                    Updater = vehicleWithMerchant.Vehicle.Updater
                };

                return Ok(vehicleDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据设备编号获取车辆信息时发生错误，设备编号: {DeviceNo}", deviceNo);
                return StatusCode(500, new { message = "服务器内部错误" });
            }
        }

        // GET: api/vehicles/stats
        [HttpGet("stats")]
        public async Task<ActionResult<VehicleStatsDto>> GetVehicleStats([FromQuery] string? merchantId = null)
        {
            try
            {
                var currentUser = await _userManager.GetUserAsync(User);
                if (currentUser == null) return Unauthorized();

                var isSystemAdmin = await _userManager.IsInRoleAsync(currentUser, "SystemAdmin");

                var query = _dbContext.VehicleInfos.Where(v => v.IsActive);

                // 权限控制
                if (!isSystemAdmin && !string.IsNullOrEmpty(currentUser.MerchantID))
                {
                    query = query.Where(v => v.MerchantID == currentUser.MerchantID);
                }

                // 商户筛选
                if (!string.IsNullOrEmpty(merchantId))
                {
                    query = query.Where(v => v.MerchantID == merchantId);
                }

                var totalCount = await query.CountAsync();
                var activeCount = await query.CountAsync(v => v.MaintenanceStatus == "Normal");
                var maintenanceCount = await query.CountAsync(v => v.MaintenanceStatus == "Maintenance");
                var warningCount = await query.CountAsync(v => v.MaintenanceStatus == "Warning");
                var expiredCount = await query.CountAsync(v => v.MaintenanceStatus == "Expired");

                return Ok(new VehicleStatsDto
                {
                    TotalCount = totalCount,
                    ActiveCount = activeCount,
                    MaintenanceCount = maintenanceCount,
                    WarningCount = warningCount,
                    ExpiredCount = expiredCount
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取车辆统计信息时发生错误");
                return StatusCode(500, new { message = "服务器内部错误" });
            }
        }

        // GET: api/vehicles/export
        [HttpGet("export")]
        public async Task<IActionResult> ExportVehicles(
            [FromQuery] string? merchantId,
            [FromQuery] string? search,
            [FromQuery] string? vehicleType,
            [FromQuery] string? maintenanceStatus)
        {
            try
            {
                var currentUser = await _userManager.GetUserAsync(User);
                if (currentUser == null) return Unauthorized();

                var isSystemAdmin = await _userManager.IsInRoleAsync(currentUser, "SystemAdmin");

                var query = from vehicle in _dbContext.VehicleInfos
                           join merchant in _dbContext.Merchants on vehicle.MerchantID equals merchant.MerchantID
                           where vehicle.IsActive && !merchant.IsDelete
                           select new { Vehicle = vehicle, Merchant = merchant };

                // 权限控制
                if (!isSystemAdmin && !string.IsNullOrEmpty(currentUser.MerchantID))
                {
                    query = query.Where(x => x.Vehicle.MerchantID == currentUser.MerchantID);
                }

                // 商户筛选
                if (!string.IsNullOrEmpty(merchantId))
                {
                    query = query.Where(x => x.Vehicle.MerchantID == merchantId);
                }

                // 搜索筛选
                if (!string.IsNullOrEmpty(search))
                {
                    query = query.Where(x =>
                        x.Vehicle.LicensePlate.Contains(search) ||
                        x.Vehicle.DeviceNO.Contains(search) ||
                        (x.Vehicle.DriverName != null && x.Vehicle.DriverName.Contains(search)) ||
                        (x.Vehicle.Brand != null && x.Vehicle.Brand.Contains(search)) ||
                        (x.Vehicle.Model != null && x.Vehicle.Model.Contains(search)));
                }

                // 车辆类型筛选
                if (!string.IsNullOrEmpty(vehicleType))
                {
                    query = query.Where(x => x.Vehicle.VehicleType != null && x.Vehicle.VehicleType.Contains(vehicleType));
                }

                // 维护状态筛选
                if (!string.IsNullOrEmpty(maintenanceStatus))
                {
                    query = query.Where(x => x.Vehicle.MaintenanceStatus == maintenanceStatus);
                }

                // 获取数据（限制最大5000条记录）
                var vehicles = await query
                    .OrderByDescending(x => x.Vehicle.CreateTime)
                    .Take(5000)
                    .Select(x => new VehicleInfoDto
                    {
                        ID = x.Vehicle.ID,
                        MerchantID = x.Vehicle.MerchantID,
                        MerchantName = x.Merchant.Name ?? "",
                        DeviceNO = x.Vehicle.DeviceNO,
                        LicensePlate = x.Vehicle.LicensePlate,
                        VehicleType = x.Vehicle.VehicleType,
                        Brand = x.Vehicle.Brand,
                        Model = x.Vehicle.Model,
                        Color = x.Vehicle.Color,
                        VIN = x.Vehicle.VIN,
                        EngineNumber = x.Vehicle.EngineNumber,
                        RegistrationDate = x.Vehicle.RegistrationDate,
                        ExpiryDate = x.Vehicle.ExpiryDate,
                        DriverName = x.Vehicle.DriverName,
                        DriverPhone = x.Vehicle.DriverPhone,
                        DriverLicense = x.Vehicle.DriverLicense,
                        InsuranceCompany = x.Vehicle.InsuranceCompany,
                        InsurancePolicyNumber = x.Vehicle.InsurancePolicyNumber,
                        InsuranceExpiryDate = x.Vehicle.InsuranceExpiryDate,
                        MaintenanceStatus = x.Vehicle.MaintenanceStatus,
                        LastMaintenanceDate = x.Vehicle.LastMaintenanceDate,
                        NextMaintenanceDate = x.Vehicle.NextMaintenanceDate,
                        Mileage = x.Vehicle.Mileage,
                        FuelType = x.Vehicle.FuelType,
                        Remark = x.Vehicle.Remark,
                        CreateTime = x.Vehicle.CreateTime,
                        Creator = x.Vehicle.Creator
                    })
                    .ToListAsync();

                // 创建CSV内容
                var csv = new System.Text.StringBuilder();

                // 添加BOM以支持中文显示
                csv.Append('\uFEFF');

                // CSV标题行
                csv.AppendLine("商户ID,商户名称,设备编号,车牌号,车辆类型,品牌,型号,颜色,车架号,发动机号,注册日期,到期日期,司机姓名,司机电话,驾驶证号,保险公司,保险单号,保险到期日期,维护状态,最后维护日期,下次维护日期,里程数,燃料类型,备注,创建时间,创建人");

                foreach (var vehicle in vehicles)
                {
                    csv.AppendLine($"{EscapeCsvField(vehicle.MerchantID)}," +
                                  $"{EscapeCsvField(vehicle.MerchantName)}," +
                                  $"{EscapeCsvField(vehicle.DeviceNO)}," +
                                  $"{EscapeCsvField(vehicle.LicensePlate)}," +
                                  $"{EscapeCsvField(vehicle.VehicleType)}," +
                                  $"{EscapeCsvField(vehicle.Brand)}," +
                                  $"{EscapeCsvField(vehicle.Model)}," +
                                  $"{EscapeCsvField(vehicle.Color)}," +
                                  $"{EscapeCsvField(vehicle.VIN)}," +
                                  $"{EscapeCsvField(vehicle.EngineNumber)}," +
                                  $"{EscapeCsvField(vehicle.RegistrationDate?.ToString("yyyy-MM-dd"))}," +
                                  $"{EscapeCsvField(vehicle.ExpiryDate?.ToString("yyyy-MM-dd"))}," +
                                  $"{EscapeCsvField(vehicle.DriverName)}," +
                                  $"{EscapeCsvField(vehicle.DriverPhone)}," +
                                  $"{EscapeCsvField(vehicle.DriverLicense)}," +
                                  $"{EscapeCsvField(vehicle.InsuranceCompany)}," +
                                  $"{EscapeCsvField(vehicle.InsurancePolicyNumber)}," +
                                  $"{EscapeCsvField(vehicle.InsuranceExpiryDate?.ToString("yyyy-MM-dd"))}," +
                                  $"{EscapeCsvField(vehicle.MaintenanceStatus)}," +
                                  $"{EscapeCsvField(vehicle.LastMaintenanceDate?.ToString("yyyy-MM-dd"))}," +
                                  $"{EscapeCsvField(vehicle.NextMaintenanceDate?.ToString("yyyy-MM-dd"))}," +
                                  $"{EscapeCsvField(vehicle.Mileage?.ToString())}," +
                                  $"{EscapeCsvField(vehicle.FuelType)}," +
                                  $"{EscapeCsvField(vehicle.Remark)}," +
                                  $"{EscapeCsvField(vehicle.CreateTime.ToString("yyyy-MM-dd HH:mm:ss"))}," +
                                  $"{EscapeCsvField(vehicle.Creator)}");
                }

                // 返回CSV文件
                var csvBytes = System.Text.Encoding.UTF8.GetBytes(csv.ToString());
                var fileName = $"车辆信息_{DateTime.Now:yyyyMMddHHmmss}.csv";

                _logger.LogInformation($"导出车辆信息 - 记录数:{vehicles.Count}, 用户:{currentUser.UserName}");

                return File(csvBytes, "text/csv; charset=utf-8", fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导出车辆信息时发生错误");
                return StatusCode(500, new { message = "导出车辆信息失败" });
            }
        }

        // GET: api/vehicles/template
        [HttpGet("template")]
        public IActionResult DownloadTemplate()
        {
            try
            {
                // 创建Excel文件
                using (XSSFWorkbook workbook = new XSSFWorkbook())
                {
                ISheet sheet = workbook.CreateSheet("车辆信息");

                // 创建表头样式
                ICellStyle headerStyle = workbook.CreateCellStyle();
                IFont headerFont = workbook.CreateFont();
                headerFont.IsBold = true;
                headerStyle.SetFont(headerFont);
                headerStyle.FillForegroundColor = IndexedColors.Grey25Percent.Index;
                headerStyle.FillPattern = FillPattern.SolidForeground;

                // 创建说明行样式
                ICellStyle noteStyle = workbook.CreateCellStyle();
                IFont noteFont = workbook.CreateFont();
                noteFont.IsItalic = true;
                noteFont.Color = IndexedColors.Grey50Percent.Index;
                noteStyle.SetFont(noteFont);

                // 创建文本格式样式（用于保持前导零）
                ICellStyle textStyle = workbook.CreateCellStyle();
                IDataFormat dataFormat = workbook.CreateDataFormat();
                textStyle.DataFormat = dataFormat.GetFormat("@"); // @ 表示文本格式

                // 创建表头
                IRow headerRow = sheet.CreateRow(0);
                string[] headers = {
                    "商户ID", "设备编号", "车牌号", "车辆类型", "品牌", "型号", "颜色",
                    "车架号", "发动机号", "注册日期", "到期日期", "司机姓名", "司机电话",
                    "驾驶证号", "保险公司", "保险单号", "保险到期日期", "维护状态",
                    "上次维护日期", "下次维护日期", "里程数", "燃料类型", "备注"
                };

                for (int i = 0; i < headers.Length; i++)
                {
                    ICell cell = headerRow.CreateCell(i);
                    cell.SetCellValue(headers[i]);
                    cell.CellStyle = headerStyle;
                }

                // 创建说明行
                IRow noteRow = sheet.CreateRow(1);
                string[] notes = {
                    "必填，8位字符", "必填，8位数字或英文字母", "必填，20位字符", "可选，50位字符", "可选，50位字符", "可选，50位字符", "可选，30位字符",
                    "可选，50位字符", "可选，50位字符", "可选，格式：yyyy-MM-dd", "可选，格式：yyyy-MM-dd", "可选，50位字符", "可选，20位字符",
                    "可选，30位字符", "可选，100位字符", "可选，50位字符", "可选，格式：yyyy-MM-dd", "可选，Normal/Maintenance/Warning",
                    "可选，格式：yyyy-MM-dd", "可选，格式：yyyy-MM-dd", "可选，数字", "可选，20位字符", "可选"
                };

                for (int i = 0; i < notes.Length; i++)
                {
                    ICell cell = noteRow.CreateCell(i);
                    cell.SetCellValue(notes[i]);
                    cell.CellStyle = noteStyle;
                }

                // 创建示例数据行
                IRow exampleRow = sheet.CreateRow(2);
                ICellStyle exampleStyle = workbook.CreateCellStyle();
                IFont exampleFont = workbook.CreateFont();
                exampleFont.Color = IndexedColors.Grey50Percent.Index;
                exampleStyle.SetFont(exampleFont);
                exampleStyle.FillForegroundColor = IndexedColors.LightGreen.Index;
                exampleStyle.FillPattern = FillPattern.SolidForeground;

                string[] examples = {
                    "12345678", "DEV12345", "京A12345", "客车", "宇通", "ZK6120", "白色",
                    "LZYTBH123456789", "YC4E140-45", "2023-01-01", "2025-01-01", "张三", "13800138000",
                    "110101199001011234", "中国人保", "PICC2023001", "2024-12-31", "Normal",
                    "2023-12-01", "2024-06-01", "50000", "柴油", "示例备注"
                };

                for (int i = 0; i < examples.Length; i++)
                {
                    ICell cell = exampleRow.CreateCell(i);
                    cell.SetCellValue(examples[i]);
                    cell.CellStyle = exampleStyle;
                }

                // 创建导入说明行
                IRow instructionRow = sheet.CreateRow(3);
                ICellStyle instructionStyle = workbook.CreateCellStyle();
                IFont instructionFont = workbook.CreateFont();
                instructionFont.IsBold = true;
                instructionFont.Color = IndexedColors.Red.Index;
                instructionStyle.SetFont(instructionFont);
                instructionStyle.FillForegroundColor = IndexedColors.LightYellow.Index;
                instructionStyle.FillPattern = FillPattern.SolidForeground;

                ICell instructionCell = instructionRow.CreateCell(0);
                instructionCell.SetCellValue("【重要提示】请从第5行开始填写数据，前4行为模板说明，不会被导入！");
                instructionCell.CellStyle = instructionStyle;

                // 合并说明单元格
                sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(3, 3, 0, headers.Length - 1));

                // 为数据区域设置文本格式（防止自动格式化数字）
                // 设置前1000行的格式，足够大部分导入需求
                for (int rowIndex = 4; rowIndex < 1004; rowIndex++)
                {
                    IRow dataRow = sheet.CreateRow(rowIndex);
                    for (int colIndex = 0; colIndex < headers.Length; colIndex++)
                    {
                        ICell cell = dataRow.CreateCell(colIndex);
                        // 对于商户ID、设备编号、车牌号、车架号、发动机号、司机电话、驾驶证号、保险单号这些可能有前导零或特殊格式的字段，设置为文本格式
                        if (colIndex == 0 || colIndex == 1 || colIndex == 2 || colIndex == 7 || colIndex == 8 || colIndex == 12 || colIndex == 13 || colIndex == 15)
                        {
                            cell.CellStyle = textStyle;
                        }
                    }
                }

                // 添加数据验证
                var helper = sheet.GetDataValidationHelper();

                // 为商户ID列添加长度验证（8位）
                var merchantIdConstraint = helper.CreateTextLengthConstraint(OperatorType.EQUAL, "8", null);
                var merchantIdValidation = helper.CreateValidation(merchantIdConstraint, new CellRangeAddressList(4, 1003, 0, 0));
                merchantIdValidation.CreateErrorBox("输入错误", "商户ID必须为8位字符");
                merchantIdValidation.ShowErrorBox = true;
                sheet.AddValidationData(merchantIdValidation);

                // 为设备编号列添加长度验证（8位）
                var deviceIdConstraint = helper.CreateTextLengthConstraint(OperatorType.EQUAL, "8", null);
                var deviceIdValidation = helper.CreateValidation(deviceIdConstraint, new CellRangeAddressList(4, 1003, 1, 1));
                deviceIdValidation.CreateErrorBox("输入错误", "设备编号必须为8位数字或英文字母");
                deviceIdValidation.ShowErrorBox = true;
                sheet.AddValidationData(deviceIdValidation);

                // 设置列宽
                for (int i = 0; i < headers.Length; i++)
                {
                    sheet.SetColumnWidth(i, 18 * 256); // 稍微增加列宽以容纳更多内容
                }

                // 写入内存流并获取字节数组
                using (MemoryStream ms = new MemoryStream())
                {
                    workbook.Write(ms);
                    ms.Flush();
                    byte[] excelBytes = ms.ToArray();

                    // 返回文件
                    string fileName = $"车辆信息导入模板_{DateTime.Now:yyyyMMddHHmmss}.xlsx";
                    return File(excelBytes, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
                }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "下载车辆信息导入模板时发生错误");
                return StatusCode(500, new { message = "服务器内部错误" });
            }
        }

        // POST: api/vehicles/import
        [HttpPost("import")]
        [Authorize(Roles = "SystemAdmin,MerchantAdmin")]
        public async Task<IActionResult> ImportVehicles(IFormFile file)
        {
            if (file == null || file.Length == 0)
            {
                return BadRequest(new { message = "请选择要导入的文件" });
            }

            if (!file.FileName.EndsWith(".xlsx", StringComparison.OrdinalIgnoreCase))
            {
                return BadRequest(new { message = "只支持Excel文件格式(.xlsx)" });
            }

            try
            {
                var currentUser = await _userManager.GetUserAsync(User);
                if (currentUser == null) return Unauthorized();

                var isSystemAdmin = await _userManager.IsInRoleAsync(currentUser, "SystemAdmin");
                var username = currentUser.UserName ?? "Unknown";

                var result = new
                {
                    totalRows = 0,
                    successCount = 0,
                    failureCount = 0,
                    errors = new List<string>()
                };

                var vehiclesToInsert = new List<VehicleInfo>();
                var errors = new List<string>();

                // 一次性获取所有商户ID，避免重复查询
                var merchantIdList = await _dbContext.Merchants
                    .Where(m => !m.IsDelete)
                    .Select(m => m.MerchantID)
                    .ToListAsync();
                var allMerchantIds = new HashSet<string>(merchantIdList);

                using (var stream = file.OpenReadStream())
                {
                    XSSFWorkbook workbook = new XSSFWorkbook(stream);
                    ISheet sheet = workbook.GetSheetAt(0);

                    // 跳过表头、说明行、示例行和提示行，从第5行开始读取数据
                    for (int rowIndex = 4; rowIndex <= sheet.LastRowNum; rowIndex++)
                    {
                        IRow row = sheet.GetRow(rowIndex);
                        if (row == null) continue;

                        try
                        {
                            // 检查是否为空行
                            bool isEmptyRow = true;
                            for (int i = 0; i < 23; i++) // 检查前23列
                            {
                                var cell = row.GetCell(i);
                                if (cell != null && !string.IsNullOrWhiteSpace(cell.ToString()))
                                {
                                    isEmptyRow = false;
                                    break;
                                }
                            }

                            if (isEmptyRow) continue;

                            result = new
                            {
                                totalRows = result.totalRows + 1,
                                successCount = result.successCount,
                                failureCount = result.failureCount,
                                errors = result.errors
                            };

                            // 读取必填字段
                            string merchantID = GetCellValue(row, 0)?.Trim() ?? "";
                            string deviceNO = GetCellValue(row, 1)?.Trim() ?? "";
                            string licensePlate = GetCellValue(row, 2)?.Trim() ?? "";

                            // 验证必填字段
                            if (string.IsNullOrEmpty(merchantID))
                            {
                                errors.Add($"第{rowIndex + 1}行：商户ID不能为空");
                                continue;
                            }

                            if (string.IsNullOrEmpty(deviceNO))
                            {
                                errors.Add($"第{rowIndex + 1}行：设备编号不能为空");
                                continue;
                            }

                            if (string.IsNullOrEmpty(licensePlate))
                            {
                                errors.Add($"第{rowIndex + 1}行：车牌号不能为空");
                                continue;
                            }

                            // 权限检查：非系统管理员只能导入自己商户的车辆
                            if (!isSystemAdmin && currentUser.MerchantID != merchantID)
                            {
                                errors.Add($"第{rowIndex + 1}行：无权限为商户{merchantID}导入车辆");
                                continue;
                            }

                            // 检查商户是否存在（内存检查）
                            if (!allMerchantIds.Contains(merchantID))
                            {
                                errors.Add($"第{rowIndex + 1}行：商户{merchantID}不存在");
                                continue;
                            }

                            // 检查车牌号是否已存在（带商户号）
                            var existingByPlate = await _dbContext.VehicleInfos
                                .FirstOrDefaultAsync(v => v.MerchantID == merchantID && v.LicensePlate == licensePlate && v.IsActive);
                            if (existingByPlate != null)
                            {
                                errors.Add($"第{rowIndex + 1}行：该商户下车牌号{licensePlate}已存在");
                                continue;
                            }

                            // 检查设备编号是否已存在（带商户号）
                            var existingByDevice = await _dbContext.VehicleInfos
                                .FirstOrDefaultAsync(v => v.MerchantID == merchantID && v.DeviceNO == deviceNO && v.IsActive);
                            if (existingByDevice != null)
                            {
                                errors.Add($"第{rowIndex + 1}行：该商户下设备编号{deviceNO}已存在");
                                continue;
                            }

                            // 解析日期字段
                            DateTime? registrationDate = ParseDate(GetCellValue(row, 9));
                            DateTime? expiryDate = ParseDate(GetCellValue(row, 10));
                            DateTime? insuranceExpiryDate = ParseDate(GetCellValue(row, 16));
                            DateTime? lastMaintenanceDate = ParseDate(GetCellValue(row, 18));
                            DateTime? nextMaintenanceDate = ParseDate(GetCellValue(row, 19));

                            // 解析里程数
                            decimal? mileage = null;
                            string mileageStr = GetCellValue(row, 20)?.Trim();
                            if (!string.IsNullOrEmpty(mileageStr) && decimal.TryParse(mileageStr, out decimal parsedMileage))
                            {
                                mileage = parsedMileage;
                            }

                            // 验证维护状态
                            string maintenanceStatus = GetCellValue(row, 17)?.Trim() ?? "Normal";
                            if (!new[] { "Normal", "Maintenance", "Warning" }.Contains(maintenanceStatus))
                            {
                                maintenanceStatus = "Normal";
                            }

                            // 创建车辆对象
                            var vehicle = new VehicleInfo
                            {
                                MerchantID = merchantID,
                                DeviceNO = deviceNO,
                                LicensePlate = licensePlate,
                                VehicleType = GetCellValue(row, 3)?.Trim(),
                                Brand = GetCellValue(row, 4)?.Trim(),
                                Model = GetCellValue(row, 5)?.Trim(),
                                Color = GetCellValue(row, 6)?.Trim(),
                                VIN = GetCellValue(row, 7)?.Trim(),
                                EngineNumber = GetCellValue(row, 8)?.Trim(),
                                RegistrationDate = registrationDate,
                                ExpiryDate = expiryDate,
                                DriverName = GetCellValue(row, 11)?.Trim(),
                                DriverPhone = GetCellValue(row, 12)?.Trim(),
                                DriverLicense = GetCellValue(row, 13)?.Trim(),
                                InsuranceCompany = GetCellValue(row, 14)?.Trim(),
                                InsurancePolicyNumber = GetCellValue(row, 15)?.Trim(),
                                InsuranceExpiryDate = insuranceExpiryDate,
                                MaintenanceStatus = maintenanceStatus,
                                LastMaintenanceDate = lastMaintenanceDate,
                                NextMaintenanceDate = nextMaintenanceDate,
                                Mileage = mileage,
                                FuelType = GetCellValue(row, 21)?.Trim(),
                                Remark = GetCellValue(row, 22)?.Trim(),
                                IsActive = true,
                                CreateTime = DateTime.Now,
                                Creator = username
                            };

                            vehiclesToInsert.Add(vehicle);
                        }
                        catch (Exception ex)
                        {
                            errors.Add($"第{rowIndex + 1}行：处理数据时发生错误 - {ex.Message}");
                        }
                    }
                }

                // 批量保存成功的记录
                if (vehiclesToInsert.Count > 0)
                {
                    _dbContext.VehicleInfos.AddRange(vehiclesToInsert);
                    await _dbContext.SaveChangesAsync();
                }

                var finalResult = new
                {
                    totalRows = result.totalRows,
                    successCount = vehiclesToInsert.Count,
                    failureCount = errors.Count,
                    errors = errors
                };

                _logger.LogInformation("车辆信息导入完成 - 总行数:{TotalRows}, 成功:{SuccessCount}, 失败:{FailureCount}, 用户:{Username}",
                    finalResult.totalRows, finalResult.successCount, finalResult.failureCount, username);

                return Ok(finalResult);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导入车辆信息时发生错误");
                return StatusCode(500, new { message = "服务器内部错误" });
            }
        }

        private static string? GetCellValue(IRow row, int cellIndex)
        {
            var cell = row.GetCell(cellIndex);
            if (cell == null) return null;

            return cell.CellType switch
            {
                CellType.String => cell.StringCellValue,
                CellType.Numeric => cell.NumericCellValue.ToString(),
                CellType.Boolean => cell.BooleanCellValue.ToString(),
                CellType.Formula => cell.CachedFormulaResultType switch
                {
                    CellType.String => cell.StringCellValue,
                    CellType.Numeric => cell.NumericCellValue.ToString(),
                    _ => cell.ToString()
                },
                _ => cell.ToString()
            };
        }

        private static DateTime? ParseDate(string? dateStr)
        {
            if (string.IsNullOrWhiteSpace(dateStr)) return null;

            if (DateTime.TryParseExact(dateStr, "yyyy-MM-dd", CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime result))
            {
                return result;
            }

            if (DateTime.TryParse(dateStr, out result))
            {
                return result;
            }

            return null;
        }

        private static string EscapeCsvField(string? field)
        {
            if (string.IsNullOrEmpty(field))
                return "";

            // 如果字段包含逗号、引号或换行符，需要用引号包围并转义内部引号
            if (field.Contains(',') || field.Contains('"') || field.Contains('\n') || field.Contains('\r'))
            {
                return $"\"{field.Replace("\"", "\"\"")}\"";
            }

            return field;
        }
    }
}
