#!/bin/bash

# 珠海通客户项目开发启动脚本

echo "=== 珠海通客户项目启动脚本 ==="

# 检查是否在正确的目录
if [ ! -f "SlzrCrossGate.CustomerZhuhaitong.csproj" ]; then
    echo "错误: 请在项目根目录运行此脚本"
    exit 1
fi

# 1. 检查数据库连接
echo "1. 检查数据库连接..."
mysql -h localhost -u root -pslzr!12345 -e "USE tcpserver; SHOW TABLES LIKE 'Customer_Zhuhaitong_%';" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "警告: 数据库连接失败或表不存在"
    echo "请先执行: mysql -u root -p < setup-database.sql"
    read -p "是否继续启动项目? (y/n): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# 2. 还原后端依赖
echo "2. 还原后端依赖..."
dotnet restore
if [ $? -ne 0 ]; then
    echo "错误: 后端依赖还原失败"
    exit 1
fi

# 3. 检查前端依赖
echo "3. 检查前端依赖..."
if [ ! -d "ClientApp/node_modules" ]; then
    echo "安装前端依赖..."
    cd ClientApp
    npm install
    if [ $? -ne 0 ]; then
        echo "错误: 前端依赖安装失败"
        exit 1
    fi
    cd ..
fi

# 4. 启动前端开发服务器（后台运行）
echo "4. 启动前端开发服务器..."
cd ClientApp
npm start &
FRONTEND_PID=$!
cd ..

# 等待前端服务器启动
echo "等待前端服务器启动..."
sleep 5

# 5. 启动后端服务器
echo "5. 启动后端服务器..."
echo "前端地址: http://localhost:3001"
echo "后端地址: http://localhost:5271"
echo "API文档: http://localhost:5271/swagger"
echo ""
echo "按 Ctrl+C 停止服务器"

# 设置信号处理，确保前端进程也被终止
trap "echo '正在停止服务器...'; kill $FRONTEND_PID 2>/dev/null; exit" INT TERM

# 启动后端
dotnet run --urls="http://localhost:5271"

# 如果后端退出，也终止前端
kill $FRONTEND_PID 2>/dev/null
