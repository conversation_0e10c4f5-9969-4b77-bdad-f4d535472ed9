-- 添加新的iframe打开方式支持
-- 用于区分支持认证共享的iframe和普通iframe

USE tcpserver;

-- 查看当前菜单项的Target设置
SELECT 
    'Current Menu Targets' as Info,
    ItemKey, 
    Title, 
    IsExternal, 
    Target,
    Href
FROM MenuItems 
WHERE IsExternal = 1
ORDER BY ItemKey;

-- 可以手动更新特定菜单项为普通iframe模式
-- 示例：将某个第三方网站设置为普通iframe
-- UPDATE MenuItems 
-- SET 
--     IsExternal = 1,
--     Target = '_iframe_simple'
-- WHERE ItemKey = 'third-party-site';

-- 查看所有可用的Target选项说明
SELECT 
    'Target Options' as Info,
    '_self' as Target,
    '当前窗口打开' as Description
UNION ALL
SELECT 
    'Target Options' as Info,
    '_blank' as Target,
    '新窗口打开' as Description
UNION ALL
SELECT 
    'Target Options' as Info,
    '_iframe' as Target,
    'iframe内嵌（支持认证共享和主题同步）' as Description
UNION ALL
SELECT 
    'Target Options' as Info,
    '_iframe_simple' as Target,
    'iframe内嵌（普通显示，不支持认证共享）' as Description;
