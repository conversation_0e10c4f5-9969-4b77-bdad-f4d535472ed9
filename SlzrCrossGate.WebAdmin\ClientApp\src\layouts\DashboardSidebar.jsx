import React, { useEffect, useRef, useState } from 'react';
import { Link as RouterLink, useLocation } from 'react-router-dom';
import PropTypes from 'prop-types';
import {
  Avatar,
  Box,
  Divider,
  Drawer,
  List,
  Typography,
  useMediaQuery,
  useTheme as useMuiTheme,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Collapse,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  alpha,
  CircularProgress
} from '@mui/material';
import {
  BarChart as BarChartIcon,
  Lock as LockIcon,
  Settings as SettingsIcon,
  ShoppingBag as ShoppingBagIcon,
  Users as UsersIcon,
  Server as ServerIcon,
  MessageCircle as MessageCircleIcon,
  FileText as FileTextIcon,
  Monitor as MonitorIcon,
  User as UserIcon,
  CreditCard as CreditCardIcon,
  Database as DatabaseIcon,
  Archive as ArchiveIcon,
  Book as BookIcon,
  Smartphone as SmartphoneIcon,
  Cpu as CpuIcon,
  List as ListIcon,
  ChevronDown as ChevronDownIcon,
  ChevronRight as ChevronRightIcon,
  Activity as ActivityIcon,
  Folder as FolderIcon,
  Sliders as SlidersIcon,
  Shield as ShieldIcon,
  Clock as HistoryIcon
} from 'react-feather';
import NavItem from './NavItem';
import { useTheme } from '../contexts/ThemeContext';
import { useAuth } from '../contexts/AuthContext';
import { menuAPI } from '../services/api';
import { getIconComponent } from '../utils/iconMapping';
import { useSnackbar } from 'notistack';

// 动态菜单配置将从API加载

const DashboardSidebar = ({
  onMobileClose = () => {},
  openMobile = false,
  isCollapsed = false
}) => {
  const location = useLocation();
  const muiTheme = useMuiTheme();
  const { mode, theme } = useTheme();
  const { user } = useAuth();
  const { enqueueSnackbar } = useSnackbar();
  const isMobile = useMediaQuery(muiTheme.breakpoints.down('lg'));

  // 创建一个 ref 来跟踪是否是首次渲染
  const isFirstRender = useRef(true);
  // 记录上一次的路径
  const prevPathRef = useRef(location.pathname);

  // 菜单数据状态
  const [menuGroups, setMenuGroups] = useState([]);
  const [loading, setLoading] = useState(true);
  const hasLoadedMenusRef = useRef(false);

  // 分组展开状态管理
  const [expandedGroups, setExpandedGroups] = useState(() => {
    // 从localStorage读取展开状态，默认全部展开
    const saved = localStorage.getItem('sidebar-expanded-groups');
    if (saved) {
      try {
        return JSON.parse(saved);
      } catch (e) {
        console.warn('Failed to parse saved expanded groups:', e);
      }
    }
    // 默认全部展开（初始为空对象，加载菜单后会更新）
    return {};
  });

  // 保存展开状态到localStorage
  const saveExpandedState = (newState) => {
    try {
      localStorage.setItem('sidebar-expanded-groups', JSON.stringify(newState));
    } catch (e) {
      console.warn('Failed to save expanded groups:', e);
    }
  };

  // 切换分组展开状态
  const toggleGroup = (groupId) => {
    const newState = {
      ...expandedGroups,
      [groupId]: !expandedGroups[groupId]
    };
    setExpandedGroups(newState);
    saveExpandedState(newState);
  };

  // 加载菜单数据
  const loadMenus = async () => {
    // 防止重复加载
    if (hasLoadedMenusRef.current) {
      console.log('菜单已加载，跳过重复请求');
      return;
    }

    try {
      setLoading(true);
      hasLoadedMenusRef.current = true; // 标记正在加载

      const response = await menuAPI.getUserMenus();
      const menus = response || [];

      // 转换菜单数据格式，添加图标组件
      const processedMenus = menus.map(group => ({
        id: group.groupKey,
        title: group.title,
        icon: getIconComponent(group.iconName),
        items: group.menuItems.map(item => ({
          href: item.href,
          icon: getIconComponent(item.iconName),
          title: item.title,
          itemKey: item.itemKey, // 添加itemKey字段
          isExternal: item.isExternal || false,
          target: item.target || '_self',
          roles: [] // 权限控制已在后端处理，前端不再需要
        }))
      }));

      setMenuGroups(processedMenus);

      // 初始化展开状态（如果localStorage中没有保存的状态）
      const currentExpanded = JSON.parse(localStorage.getItem('sidebar-expanded-groups') || '{}');
      if (Object.keys(currentExpanded).length === 0) {
        const defaultExpanded = processedMenus.reduce((acc, group) => {
          acc[group.id] = true;
          return acc;
        }, {});
        setExpandedGroups(defaultExpanded);
        saveExpandedState(defaultExpanded);
      }
    } catch (error) {
      console.error('加载菜单失败:', error);
      enqueueSnackbar('加载菜单失败', { variant: 'error' });
      hasLoadedMenusRef.current = false; // 加载失败时重置标记，允许重试
    } finally {
      setLoading(false);
    }
  };

  // 组件挂载时加载菜单
  useEffect(() => {
    if (user && !hasLoadedMenusRef.current) {
      loadMenus();
    }
  }, [user]);

  // 检查用户是否有权限访问菜单项（现在主要用于兼容性，实际权限在后端控制）
  const hasPermission = (item) => {
    // 由于权限控制已在后端处理，这里直接返回true
    return true;
  };

  // 检查分组是否有可见的菜单项
  const hasVisibleItems = (group) => {
    return group.items.some(item => hasPermission(item));
  };

  // 分组菜单项组件
  const MenuGroup = ({ group }) => {
    const GroupIcon = group.icon;
    const isExpanded = expandedGroups[group.id];
    const visibleItems = group.items.filter(item => hasPermission(item));

    if (visibleItems.length === 0) {
      return null;
    }

    // 折叠模式下的处理
    if (isCollapsed && !isMobile) {
      return (
        <Box sx={{ mb: 1 }}>
          {visibleItems.map((item) => (
            <NavItem
              key={item.title}
              href={item.href}
              title={item.title}
              itemKey={item.itemKey}
              icon={item.icon}
              isCollapsed={true}
              isExternal={item.isExternal}
              target={item.target}
            />
          ))}
        </Box>
      );
    }

    return (
      <Box sx={{ mb: 1 }}>
        {/* 分组标题 */}
        <ListItemButton
          onClick={() => toggleGroup(group.id)}
          sx={{
            py: 1.25,
            px: 2,
            borderRadius: 2,
            mb: 0.75,
            mt: group.id === 'overview' ? 0 : 1,
            transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
            '&:hover': {
              backgroundColor: mode === 'dark'
                ? alpha(theme.palette.primary.main, 0.08)
                : alpha(theme.palette.primary.main, 0.04),
            }
          }}
        >
          <ListItemIcon
            sx={{
              minWidth: 'auto',
              mr: 1,
              color: mode === 'dark' ? theme.palette.primary.light : theme.palette.primary.main,
            }}
          >
            <GroupIcon size="18" />
          </ListItemIcon>
          <ListItemText
            primary={group.title}
            primaryTypographyProps={{
              fontSize: '1rem',
              fontWeight: 600,
              textTransform: 'none',
              letterSpacing: '0.15px',
              color: mode === 'dark' ? theme.palette.primary.light : theme.palette.primary.main,
            }}
          />
          {isExpanded ? (
            <ChevronDownIcon size="16" style={{
              color: mode === 'dark' ? theme.palette.primary.light : theme.palette.primary.main,
              transition: 'transform 0.2s'
            }} />
          ) : (
            <ChevronRightIcon size="16" style={{
              color: mode === 'dark' ? theme.palette.primary.light : theme.palette.primary.main,
              transition: 'transform 0.2s'
            }} />
          )}
        </ListItemButton>

        {/* 分组菜单项 */}
        <Collapse in={isExpanded} timeout="auto" unmountOnExit>
          <List sx={{ pl: 1 }}>
            {visibleItems.map((item) => (
              <NavItem
                key={item.title}
                href={item.href}
                title={item.title}
                itemKey={item.itemKey}
                icon={item.icon}
                isCollapsed={false}
                isExternal={item.isExternal}
                target={item.target}
              />
            ))}
          </List>
        </Collapse>
      </Box>
    );
  };

  // 只在路由变化时关闭移动端侧边栏，而不是在openMobile变化时
  useEffect(() => {
    // 路由变化时关闭侧边栏，但不在组件首次挂载时执行
    if (isFirstRender.current) {
      isFirstRender.current = false;
      prevPathRef.current = location.pathname;
      return;
    }

    // 只有当路径变化时才关闭侧边栏
    if (prevPathRef.current !== location.pathname) {
      prevPathRef.current = location.pathname;
      if (openMobile && onMobileClose) {
        onMobileClose();
      }
    }
  }, [location.pathname, onMobileClose]);

  const content = (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        height: '100%',
        overflowX: 'hidden',
        width: isCollapsed && !isMobile ? 80 : 280,
        transition: muiTheme.transitions.create('width', {
          easing: muiTheme.transitions.easing.sharp,
          duration: muiTheme.transitions.duration.enteringScreen,
        }),
      }}
    >
      <Box
        sx={{
          alignItems: 'center',
          display: 'flex',
          flexDirection: 'column',
          p: isCollapsed && !isMobile ? 1 : 2,
          mb: 1,
        }}
      >
        <Avatar
          component={RouterLink}
          src={user.avatar}
          sx={{
            cursor: 'pointer',
            width: isCollapsed && !isMobile ? 40 : 64,
            height: isCollapsed && !isMobile ? 40 : 64,
            transition: muiTheme.transitions.create(['width', 'height', 'box-shadow', 'transform'], {
              duration: muiTheme.transitions.duration.shorter,
              easing: muiTheme.transitions.easing.easeInOut,
            }),
            boxShadow: mode === 'dark'
              ? '0 0 15px rgba(192, 132, 252, 0.5)'
              : '0 0 15px rgba(126, 34, 206, 0.3)',
            border: `2px solid ${mode === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(255, 255, 255, 0.8)'}`,
            '&:hover': {
              transform: 'scale(1.05)',
              boxShadow: mode === 'dark'
                ? '0 0 20px rgba(192, 132, 252, 0.7)'
                : '0 0 20px rgba(126, 34, 206, 0.5)',
            }
          }}
          to="/app/account"
        />
        {(!isCollapsed || isMobile) && (
          <>
            <Typography
              color="textPrimary"
              variant="h5"
              sx={{
                mt: 1,
                fontWeight: 600,
                textAlign: 'center',
                background: mode === 'dark'
                  ? `linear-gradient(45deg, ${theme.palette.primary.light} 30%, ${theme.palette.secondary.light} 90%)`
                  : `linear-gradient(45deg, ${theme.palette.primary.main} 30%, ${theme.palette.secondary.main} 90%)`,
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                textShadow: mode === 'dark'
                  ? '0 0 10px rgba(192, 132, 252, 0.3)'
                  : '0 0 10px rgba(126, 34, 206, 0.1)',
              }}
            >
              {user?.realName || user?.name || '用户'}
            </Typography>
            <Typography
              color="textSecondary"
              variant="body2"
              sx={{
                textAlign: 'center',
                fontWeight: 500,
                opacity: 0.8,
              }}
            >
              {user?.roles?.join(', ') || '用户'}
            </Typography>
          </>
        )}
      </Box>
      <Divider />
      <Box sx={{ p: isCollapsed && !isMobile ? 1 : 2 }}>
        <List sx={{ py: 0 }}>
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', py: 3 }}>
              <CircularProgress size={24} />
            </Box>
          ) : (
            menuGroups
              .filter(group => hasVisibleItems(group))
              .map((group) => (
                <MenuGroup key={group.id} group={group} />
              ))
          )}
        </List>
      </Box>
      <Box sx={{ flexGrow: 1 }} />
    </Box>
  );

  return (
    <>
      {isMobile ? (
        <Drawer
          anchor="left"
          onClose={(e) => {
            e.stopPropagation(); // 阻止事件冒泡
            onMobileClose();
          }}
          open={openMobile}
          variant="temporary"
          ModalProps={{
            keepMounted: true, // 改善移动端性能
            disableScrollLock: false, // 防止背景滚动
            disablePortal: false, // 使用传送门渲染
            disableAutoFocus: false, // 允许自动聚焦
            disableEnforceFocus: false, // 强制聚焦
          }}
          PaperProps={{
            sx: {
              width: 280,
              backdropFilter: 'blur(10px)',
              backgroundColor: mode === 'dark'
                ? 'rgba(15, 23, 42, 0.9)'
                : 'rgba(255, 255, 255, 0.9)',
              borderRight: `1px solid ${mode === 'dark' ? 'rgba(255, 255, 255, 0.08)' : 'rgba(0, 0, 0, 0.08)'}`,
              boxShadow: mode === 'dark'
                ? '0px 8px 24px rgba(0, 0, 0, 0.4)'
                : '0px 8px 24px rgba(0, 0, 0, 0.1)',
              zIndex: muiTheme.zIndex.drawer + 2, // 确保在移动端上正确显示
            }
          }}
        >
          {content}
        </Drawer>
      ) : (
        <Drawer
          anchor="left"
          open
          variant="persistent"
          PaperProps={{
            sx: {
              width: isCollapsed ? 80 : 280,
              top: 64,
              height: 'calc(100% - 64px)',
              transition: muiTheme.transitions.create(['width'], {
                easing: muiTheme.transitions.easing.easeInOut,
                duration: muiTheme.transitions.duration.standard,
              }),
              overflowX: 'hidden',
              backdropFilter: 'blur(10px)',
              backgroundColor: mode === 'dark'
                ? 'rgba(15, 23, 42, 0.9)'
                : 'rgba(255, 255, 255, 0.9)',
              borderRight: `1px solid ${mode === 'dark' ? 'rgba(255, 255, 255, 0.08)' : 'rgba(0, 0, 0, 0.08)'}`,
              boxShadow: mode === 'dark'
                ? 'none'
                : '0px 2px 8px rgba(0, 0, 0, 0.05)',
            }
          }}
        >
          {content}
        </Drawer>
      )}
    </>
  );
};

DashboardSidebar.propTypes = {
  onMobileClose: PropTypes.func,
  openMobile: PropTypes.bool,
  isCollapsed: PropTypes.bool
};

// 使用 JavaScript 默认参数代替 defaultProps

export default DashboardSidebar;
