# 已完成的终端指令处理
- [x] 签退
- [x] 心跳
- [x] 消息获取
- [x] 消息确认
- [x] 数据上传
- [x] 增量名单下载
- [x] 银联终端密钥绑定
- [x] 文件内容下载
- [x] 签到
- [x] 统一提醒（未读消息及重签到）

# Todo（近期）

- [ ] 核心功能
  - [x] 签到，版本比对
  - [x] 统一的签到及未读消息提醒
  - [ ] 从数据库中读取原始交易记录打包成文件（通过配置文件控制是否启用）。
  - [x] 增加TCP终端文件上传功能，实现自动续传，支持大文件上传，自定义上传协议
  - [ ] 增加终端流量统计功能
  - [x] 终端签到时，若线路/商户出现变更，即时进行版本比对
- [ ] WEB后台
  - [x] 登录，登录增加多因素认证（短信验证码，动态口令等），考虑增加微信扫码登录
  - [x] 用户管理
  - [x] 角色管理
  - [x] 商户管理
  - [x] 终端管理，终端状态，终端版本，终端属性等。链接终端日志、事件、消费数据等。
  - [x] 文件发布，手动文件上上传，文件发布（按商户、线路、终端发布），文件版本管理
  - [x] 消息管理，查看消息内容，消息类型，消息状态（未读，已读，已回）
  - [x] 终端事件查询（签到，属性变更，版本变更，离线）
  - [x] 终端日志信息（考虑服务端解析终端日志数据并保存，待定）。通过消息让终端上传日志文件。
  - [x] 终端消费数据查询（直接提供原始数据的查询页面，根据设备序列号，查找指定时段的记录）
  - [ ] 控制台实时日志(服务端日志文件保存，SSE输出实时日志)
  - [x] 审计日志查询（操作日志，改密日志，登录日志）
  - [x] web页面增加程序版本和版本时间的展示
  - [x] 银联密钥管理，增加，导入，回收。
  - [x] 票价文件管理，编辑票价信息，生成票价文件，下载票价文件，发布票价文件（默认线路发布）。
  - [x] 菜单管理功能，可手动配置菜单信息，包含菜单名称，图标，链接，顺序，权限控制等信息
  - [x] 订阅MQ中的文件发布信息，自动发布文件。（兼容原通讯平台的处理）
  - [x] 增加终端文件上传管理页面
  - [x] 票价折扣方案管理。线路参数中可以选择已经设置好的折扣方案。票价折扣方案可单独提交到文件版本管理中。
  - [x] 增加终端日志记录解析与展示
  - [x] 增加微前端架构，支持FTP文件处理查询（珠海通）页面
  - [x] 增加功能按钮权限控制功能，对前端的主要按钮可自定义权限控制
  - [x] 未注册线路中，增加一个创建线路的功能
  - [x] 终端管理页面顶部增加未注册线路数统计
  - [x] 车辆管理增加导入功能，模板下载
  - [x] 线路管理增加导入功能，模板下载
  - [x] 线路增加分公司字段，可以输入分公司名称
  - [ ] 上传终端采集文件或文本格式记录文件，自动推送到MQ
  - [x] 文件版本管理增加备注字段
  - [x] 消息管理增加删除功能
  - [x] 消息管理增加批量消息功能（给整条线路或整个商户的设备，都发送消息）
  - [x] 后台定时发布，预约发布文件。
- [x] 一个文件自动发布工具，实现一个目录监控，当有文件放入目录时，自动上传文件并发布到指定的商户。（兼容原通讯平台处理）。可设置目录，可部署在windows程序上。

# Todo（远期）
- [ ] IC卡自助充值
- [ ] 守护卡
- [ ] 聚合支付


# 需求调整
- [x] 增加线路票价参数文件编辑和发布功能
- [x] 终端管理中，出厂序列号要求用PSAM卡号
- [x] 终端管理中，需要能自定义显示字段
- [x] 终端管理中，显示离线时长
- [x] 银联密钥增加导出功能，解绑功能，显示出厂序列号
- [x] 票价参数卡类折扣编辑时，需要采用表格形式编辑
- [x] 增加审计日志：登录日志，改密日志，审查日志
- [x] 增加单独的终端事件查询页面，可查询全部事件
- [x] 增加未注册线路页面，展示终端线路中未录入的线路信息
- [ ] 珠海通对接调度平台。（未实现）：需求取消，暂不实现
- [x] 珠海通增加FTP文件上传统计功能
- [x] 用户1小时不操作，自动登出
- [x] 要求每90天强制用户更改密码
- [x] 增加统一的票价折扣编辑功能
- [x] 票价折扣编辑功能要能自定义发布
- [x] 需要隐藏线路参数版本管理中的部分按钮。
- [x] 增加线路批量导入功能
- [x] 增加车辆批量导入功能
- [x] 增加未注册线路功能
- [x] 票价折扣管理页面，使用次数改为引用次数，增加更新时间的字段
- [x] 线路编号和车号要支持字符
- [x] 文件版本管理中增加备注字段

# BUG修复
- [x] 操作日志中操作模板下拉框内容不完整
- [ ] 




## 当前在做的事情
### 20250402
1. 调整handler的处理逻辑，IIso8583MessageHandler 中不再直接回复，而是返回一个response对象，由handler的调用者进行回复
2. 增加流量统计功能，统计每个终端的流量消耗情况
3. 修改数据库中保存的原始交易信息，采用BLOB存储，而不是TEXT，以节约空间
4. 仓储增加批量插入、更新、删除等方法

### 20250403
1. 调整Terminal中属性和版本为JSON字段，自动序列化
2. 拆分Terminal为Terminal和TerminalStatus两个表
3. 实现TerminalManager中的终端信息对比

### 20250409
1. 实现完整的签到逻辑，包括版本比对
2. 增加文件发布事件订阅处理

### 20250410
1. 文件发布事件处理，更新终端的期望版本
2. 完成终端期望版本过期后的重新处理逻辑

### 20250411
1. 优化Repository查询性能，增加asNoTracking参数
2. 完善期望版本的处理逻辑
3. TerminalManager中增加定时任务，定时检查终端的文件版本是否与期望版本一致，并提醒更新. 1分钟检查一次
4. TerminalManager中增加定时任务，定期对期望版本过期的终端进行更新. 5分钟检查一次
5. 增加消息类型类，避免魔法字符串
6. 增加消息表、文件发布表等表的查询索引
7. 增加消息发送事件服务，实现未读消息提醒相关功能


