import api from '../services/api';

/**
 * 会话管理器
 * 处理用户会话超时和自动登出
 */
class SessionManager {
  constructor() {
    this.config = {
      timeoutMinutes: 30,
      warningMinutes: 5,
      checkIntervalSeconds: 60
    };

    this.timers = {
      checkTimer: null,
      warningTimer: null,
      logoutTimer: null
    };

    this.isWarningShown = false;
    this.isInitialized = false;
    this.isCheckingImmediately = false; // 防止重复的立即检查
    this.callbacks = {
      onWarning: null,
      onLogout: null,
      onRefresh: null
    };
  }

  /**
   * 初始化会话管理器
   */
  async initialize() {
    if (this.isInitialized) return;

    try {
      // 获取服务器配置
      await this.loadConfig();

      // 立即检查一次会话状态（用于页面刷新或重新打开时的即时检查）
      await this.checkSessionStatus();

      // 开始监听用户活动
      this.bindUserActivityEvents();

      // 开始定期检查会话状态
      this.startSessionCheck();

      this.isInitialized = true;
      console.log('会话管理器已初始化', this.config);
    } catch (error) {
      console.error('初始化会话管理器失败:', error);
    }
  }

  /**
   * 加载服务器配置
   */
  async loadConfig() {
    try {
      // api.js 的响应拦截器已经返回了 response.data，所以这里直接使用返回值
      const data = await api.get('/Session/config');
      this.config = {
        ...this.config,
        ...data
      };
    } catch (error) {
      console.warn('加载会话配置失败，使用默认配置:', error);
    }
  }

  /**
   * 设置回调函数
   */
  setCallbacks({ onWarning, onLogout, onRefresh }) {
    this.callbacks.onWarning = onWarning;
    this.callbacks.onLogout = onLogout;
    this.callbacks.onRefresh = onRefresh;
  }

  /**
   * 绑定用户活动事件
   */
  bindUserActivityEvents() {
    const events = [
      'mousedown', 'mousemove', 'keypress', 'scroll',
      'touchstart', 'click', 'focus', 'blur'
    ];

    const handleActivity = this.throttle(() => {
      this.onUserActivity();
    }, 5000); // 5秒内最多触发一次

    events.forEach(event => {
      document.addEventListener(event, handleActivity, true);
    });

    // 监听页面可见性变化，当页面重新获得焦点时检查会话状态
    document.addEventListener('visibilitychange', () => {
      if (!document.hidden && this.isInitialized) {
        console.log('页面重新获得焦点，检查会话状态');
        this.checkSessionStatusImmediately();
      }
    });

    // 监听窗口焦点事件
    window.addEventListener('focus', () => {
      if (this.isInitialized) {
        console.log('窗口重新获得焦点，检查会话状态');
        this.checkSessionStatusImmediately();
      }
    });
  }

  /**
   * 用户活动处理
   */
  onUserActivity() {
    // 如果正在显示警告，隐藏警告
    if (this.isWarningShown) {
      this.hideWarning();
    }

    // 不需要主动发送心跳，因为用户的正常操作（API请求）会自动更新活动状态
    // this.sendHeartbeat(); // 注释掉，减少不必要的请求
  }

  /**
   * 发送心跳到服务器（已废弃，使用中间件自动更新）
   */
  async sendHeartbeat() {
    // 不再需要主动发送心跳，中间件会在每次API请求时自动更新用户活动状态
    // 这样可以减少不必要的网络请求和操作日志记录
    return;
  }



  /**
   * 开始定期检查会话状态
   */
  startSessionCheck() {
    this.timers.checkTimer = setInterval(async () => {
      await this.checkSessionStatus();
    }, this.config.checkIntervalSeconds * 1000);
  }

  /**
   * 检查会话状态
   */
  async checkSessionStatus() {
    try {
      // api.js 的响应拦截器已经返回了 response.data，所以这里直接使用返回值
      const data = await api.get('/Session/status');
      const { isActive, isExpired, isNearExpiry, timeRemaining } = data;

      if (isExpired || !isActive) {
        this.handleLogout('会话已过期');
        return;
      }

      // 会话即将过期时也直接登出，不显示警告对话框
      if (isNearExpiry && !this.isWarningShown) {
        this.handleLogout('会话已过期');
        return;
      }

    } catch (error) {
      if (error.response?.status === 401) {
        this.handleLogout('认证失败');
      } else {
        console.error('检查会话状态失败:', error);
      }
    }
  }

  /**
   * 立即检查会话状态（用于页面加载时的即时验证）
   */
  async checkSessionStatusImmediately() {
    // 防止重复调用：如果正在检查中，直接返回true
    if (this.isCheckingImmediately) {
      console.log('会话状态检查正在进行中，跳过重复调用');
      return true;
    }

    try {
      this.isCheckingImmediately = true;
      console.log('立即检查会话状态...');
      const data = await api.get('/Session/status');
      const { isActive, isExpired } = data;

      if (isExpired || !isActive) {
        console.log('会话已失效，立即退出登录');
        this.handleLogout('会话已过期');
        return false;
      }

      console.log('会话状态正常');
      return true;
    } catch (error) {
      if (error.response?.status === 401 || error.response?.status === 403) {
        console.log('认证失败，立即退出登录');
        this.handleLogout('认证失败');
        return false;
      } else if (error.code === 'NETWORK_ERROR' || error.message?.includes('Network Error')) {
        // 网络错误，不强制退出，给用户一次机会
        console.warn('网络错误，跳过此次会话检查:', error.message);
        return true;
      } else {
        console.error('立即检查会话状态失败:', error);
        // 其他错误也不强制退出，避免误判
        return true;
      }
    } finally {
      this.isCheckingImmediately = false;
    }
  }

  /**
   * 显示即将过期警告
   */
  showWarning(timeRemaining) {
    this.isWarningShown = true;

    if (this.callbacks.onWarning) {
      this.callbacks.onWarning(timeRemaining);
    } else {
      // 默认警告实现
      const minutes = Math.ceil(timeRemaining);
      const continueSession = confirm(
        `您已经长时间未操作，会话将在 ${minutes} 分钟后过期。\n点击确定继续会话，点击取消立即退出。`
      );

      if (continueSession) {
        this.refreshSession();
      } else {
        this.handleLogout('用户选择退出');
      }
    }
  }

  /**
   * 隐藏警告
   */
  hideWarning() {
    this.isWarningShown = false;
  }

  /**
   * 刷新会话
   */
  async refreshSession() {
    try {
      await api.post('/Session/refresh');
      this.hideWarning();

      if (this.callbacks.onRefresh) {
        this.callbacks.onRefresh();
      }

      console.log('会话已刷新');
    } catch (error) {
      console.error('刷新会话失败:', error);
      this.handleLogout('刷新会话失败');
    }
  }

  /**
   * 处理登出
   */
  async handleLogout(reason) {
    console.log('自动登出:', reason);

    try {
      // 通知服务器登出
      await api.post('/Session/logout');
    } catch (error) {
      console.error('服务器登出失败:', error);
    }

    // 清理本地状态
    this.cleanup();

    // 清除本地存储的token
    localStorage.removeItem('token');
    sessionStorage.removeItem('token');

    if (this.callbacks.onLogout) {
      this.callbacks.onLogout(reason);
    } else {
      // 直接跳转到登录页面，不显示提示
      window.location.href = '/login';
    }
  }

  /**
   * 手动登出
   */
  async logout() {
    await this.handleLogout('用户主动登出');
  }

  /**
   * 清理定时器和事件监听
   */
  cleanup() {
    Object.values(this.timers).forEach(timer => {
      if (timer) clearInterval(timer);
    });

    this.timers = {
      checkTimer: null,
      warningTimer: null,
      logoutTimer: null
    };



    this.isInitialized = false;
    this.isWarningShown = false;
  }

  /**
   * 节流函数
   */
  throttle(func, limit) {
    let inThrottle;
    return function() {
      const args = arguments;
      const context = this;
      if (!inThrottle) {
        func.apply(context, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  }

  /**
   * 获取当前会话状态
   */
  async getCurrentStatus() {
    try {
      // api.js 的响应拦截器已经返回了 response.data，所以这里直接使用返回值
      const data = await api.get('/Session/status');
      return data;
    } catch (error) {
      console.error('获取会话状态失败:', error);
      return null;
    }
  }
}

// 创建单例实例
const sessionManager = new SessionManager();

export default sessionManager;
