# TCP终端文件上传功能

## 最新更新

### 2025-07-12 优化终端文件上传管理功能权限控制和商户信息显示
- **需求背景**：用户反馈终端文件上传管理页面需要改进商户筛选方式，添加商户信息显示，并实现基于用户角色的权限控制
- **解决方案**：
  1. **后端权限控制优化**：在TerminalFileUploadsController中添加UserManager依赖注入，实现基于用户角色的数据访问控制，在所有API方法中添加权限检查
  2. **商户信息显示增强**：修改TerminalFileUploadDto添加MerchantName字段，在查询中联接Merchants表获取商户名称
  3. **前端界面改进**：商户筛选从文本框改为下拉选择框，添加获取商户列表API接口，表格中添加商户列显示商户名称
  4. **权限控制实现**：非管理员只能查看/操作自己商户的数据，系统管理员可以查看所有商户数据
- **影响**：提升了数据安全性，改善了用户体验，简化了商户筛选操作，保持了与其他模块一致的权限控制模式

### 2025-07-11 完成TCP终端文件上传功能
- **需求背景**：用户希望在TCP服务中增加终端上传文件的支持，使用一个TCP服务端口，通过协议区分处理，支持断点续传

## 技术方案

### 协议设计
- 固定头：0xCCCCCCCC (4字节)
- 协议版本：0x01 (1字节)
- 消息类型：0x01-0x06 (1字节)
- 请求ID：4字节，用于请求响应匹配
- 预留：2字节
- 消息体长度：4字节
- 消息体：变长

### 消息类型定义
- 0x01: 文件上传请求
- 0x02: 文件上传响应
- 0x03: 文件块传输请求
- 0x04: 文件块传输响应
- 0x05: 断点续传查询
- 0x06: 断点续传响应

## 数据库设计

### TerminalFileUploads表
- 存储终端文件上传的完整信息
- 支持断点续传（ReceivedLength字段）
- 包含商户号、终端序列号、文件类型、文件名、文件大小、CRC等
- 支持上传状态管理（上传中、完成、失败、取消）
- 关联最终文件存储（FinalUploadFileID）

## 协议实现

### FileUploadMessage基类
- 统一的协议消息解析和构建
- 支持所有6种消息类型的序列化/反序列化

### 具体消息类
- FileUploadRequestMessage：文件上传请求
- FileUploadResponseMessage：文件上传响应
- FileChunkRequestMessage：文件块传输请求
- FileChunkResponseMessage：文件块传输响应
- ResumeQueryMessage：断点续传查询
- ResumeResponseMessage：断点续传响应

## 服务层实现

### TerminalFileUploadService
- 完整的文件上传生命周期管理
- 支持创建上传、写入块、完成上传、取消上传
- 文件完整性校验（大小和CRC32）
- 集成现有文件服务（本地/MinIO存储）
- 临时文件管理和清理

### FileUploadProtocolHandler
- 处理所有文件上传协议消息
- 数据类型转换（uint到字符串）
- 错误处理和状态管理
- 与TCP连接上下文集成

## TCP集成

### TcpConnectionHandler增强
- 协议检测和分发（ISO8583 vs 文件上传）
- 文件上传消息处理流程
- 响应消息发送

### 服务注册
- 在Core和Tcp项目中注册相关服务
- 依赖注入配置完成

## 后台清理服务

### TerminalFileUploadCleanupService
- 定时清理过期的上传记录（24小时）
- 删除临时文件
- 防止重叠执行的信号量保护
- 每小时执行一次清理任务

## 技术特点
- ✅ 复用现有TCP端口，通过协议头区分
- ✅ 完整的断点续传支持
- ✅ 文件完整性校验（大小+CRC32）
- ✅ 集成现有文件服务，统一存储管理
- ✅ 与Web文件发布功能完全分离
- ✅ 完善的错误处理和日志记录
- ✅ 自动清理过期记录和临时文件

## 使用流程
1. 终端发送文件上传请求(0x01)，包含文件信息
2. 服务端创建上传记录，返回16字节唯一ID
3. 终端按块发送文件数据(0x03)
4. 服务端接收并写入临时文件，返回下一块信息
5. 支持断点续传查询(0x05)获取当前进度
6. 上传完成后校验文件，保存到最终存储
7. 后台服务定期清理过期的上传记录

## 测试客户端

### FileUploadTestClient项目
- 完整的控制台测试应用程序
- 实现了所有文件上传协议消息类
- 支持命令行参数配置
- 自动CRC32校验计算（对于终端CRC32为AABBAABB的文件不校验CRC32）
- 分块上传和进度显示

## WebAdmin管理界面

### 功能特性
- 统计卡片展示（总数、完成数、上传中、总大小）
- 多条件筛选（商户、终端ID、终端类型、文件类型、文件名、状态、时间范围）
- 数据表格展示（支持排序、分页）
- 文件下载功能
- 删除记录功能
- 详细统计对话框
- 基于用户角色的权限控制

### API接口
- POST /api/TerminalFileUploads/list - 获取上传列表
- GET /api/TerminalFileUploads/stats - 获取统计信息
- GET /api/TerminalFileUploads/download/{id} - 下载文件
- DELETE /api/TerminalFileUploads/{id} - 删除记录
- GET /api/TerminalFileUploads/file-types - 获取文件类型列表
- GET /api/TerminalFileUploads/terminal-types - 获取终端类型列表
- GET /api/TerminalFileUploads/merchants - 获取商户列表

## 权限控制

### 用户角色权限
- **系统管理员**：可以查看和操作所有商户的上传记录
- **商户管理员**：只能查看和操作自己商户的上传记录

### 权限检查实现
```csharp
var isSystemAdmin = await _userManager.IsInRoleAsync(currentUser, "SystemAdmin");
if (!isSystemAdmin && upload.MerchantID != currentUser.MerchantID) {
    return Forbid("无权限操作其他商户的数据");
}
```
