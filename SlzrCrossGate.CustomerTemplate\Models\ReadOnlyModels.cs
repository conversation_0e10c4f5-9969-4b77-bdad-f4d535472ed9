using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace SlzrCrossGate.CustomerTemplate.Models
{
    /// <summary>
    /// 用户只读模型 - 仅用于认证验证，不参与EF迁移
    /// </summary>
    [Table("AspNetUsers")]
    public class UserReadOnly
    {
        public string Id { get; set; } = string.Empty;
        public string UserName { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string? RealName { get; set; }
        public string? MerchantID { get; set; }
        public bool EmailConfirmed { get; set; }
        public DateTime CreateTime { get; set; }
        public bool IsDeleted { get; set; }

        // 双因素认证相关字段
        public string? TwoFactorSecretKey { get; set; }
        public bool IsTwoFactorRequired { get; set; }
        public DateTime? TwoFactorEnabledDate { get; set; }

        // 微信登录相关字段
        public string? WechatOpenId { get; set; }
        public string? WechatUnionId { get; set; }
        public string? WechatNickname { get; set; }
        public DateTime? WechatBindTime { get; set; }

        // 密码策略相关字段
        public DateTime? LastPasswordChangeTime { get; set; }
        public bool RequirePasswordChange { get; set; }

        // 只包含认证验证需要的字段，不包含敏感信息
    }

    /// <summary>
    /// 商户只读模型 - 仅用于获取商户信息，不参与EF迁移
    /// </summary>
    [Table("Merchants")]
    public class MerchantReadOnly
    {
        public string MerchantID { get; set; } = string.Empty;
        public string MerchantName { get; set; } = string.Empty;
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
    }

    /// <summary>
    /// 用户角色只读模型 - 用于权限验证
    /// </summary>
    [Table("UserRoles")]
    public class UserRoleReadOnly
    {
        public string UserId { get; set; } = string.Empty;
        public string RoleId { get; set; } = string.Empty;
    }

    /// <summary>
    /// 角色只读模型
    /// </summary>
    [Table("Roles")]
    public class RoleReadOnly
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string NormalizedName { get; set; } = string.Empty;
    }
}

namespace SlzrCrossGate.CustomerTemplate.Data
{
    /// <summary>
    /// 认证只读上下文 - 不参与迁移
    /// </summary>
    public class AuthReadOnlyContext : DbContext
    {
        public AuthReadOnlyContext(DbContextOptions<AuthReadOnlyContext> options) : base(options) { }

        // 只读实体，不生成迁移
        public DbSet<UserReadOnly> Users { get; set; }
        public DbSet<MerchantReadOnly> Merchants { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            // 映射到现有表，但不参与迁移
            modelBuilder.Entity<UserReadOnly>(entity =>
            {
                entity.ToTable("AspNetUsers");
                entity.HasNoKey(); // 标记为无键实体，不会生成迁移
            });

            modelBuilder.Entity<MerchantReadOnly>(entity =>
            {
                entity.ToTable("Merchants");
                entity.HasNoKey();
            });
        }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            // 配置为只读模式
            optionsBuilder.UseQueryTrackingBehavior(QueryTrackingBehavior.NoTracking);
        }
    }
}

namespace SlzrCrossGate.CustomerTemplate.Services
{
    /// <summary>
    /// 只读认证服务 - 使用只读模型进行认证验证
    /// </summary>
    public class ReadOnlyAuthService
    {
        private readonly AuthReadOnlyContext _context;
        private readonly ILogger<ReadOnlyAuthService> _logger;

        public ReadOnlyAuthService(AuthReadOnlyContext context, ILogger<ReadOnlyAuthService> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// 验证用户是否存在且有效
        /// </summary>
        public async Task<UserReadOnly?> ValidateUserAsync(string userId)
        {
            try
            {
                var users = await _context.Users
                    .FromSqlRaw("SELECT Id, UserName, Email, MerchantID, IsActive, CreatedAt FROM Users WHERE Id = {0} AND IsActive = 1", userId)
                    .ToListAsync();

                return users.FirstOrDefault();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证用户失败: {UserId}", userId);
                return null;
            }
        }

        /// <summary>
        /// 获取用户的商户信息
        /// </summary>
        public async Task<MerchantReadOnly?> GetUserMerchantAsync(string merchantId)
        {
            try
            {
                var merchants = await _context.Merchants
                    .FromSqlRaw("SELECT MerchantID, MerchantName, IsActive, CreatedAt FROM Merchants WHERE MerchantID = {0} AND IsActive = 1", merchantId)
                    .ToListAsync();

                return merchants.FirstOrDefault();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取商户信息失败: {MerchantId}", merchantId);
                return null;
            }
        }

        /// <summary>
        /// 获取用户角色
        /// </summary>
        public async Task<List<string>> GetUserRolesAsync(string userId)
        {
            try
            {
                var roles = await _context.Database
                    .SqlQueryRaw<string>(@"
                        SELECT r.Name 
                        FROM UserRoles ur 
                        INNER JOIN Roles r ON ur.RoleId = r.Id 
                        WHERE ur.UserId = {0}", userId)
                    .ToListAsync();

                return roles;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户角色失败: {UserId}", userId);
                return new List<string>();
            }
        }

        /// <summary>
        /// 验证用户是否有指定角色
        /// </summary>
        public async Task<bool> UserHasRoleAsync(string userId, string roleName)
        {
            try
            {
                var count = await _context.Database
                    .SqlQueryRaw<int>(@"
                        SELECT COUNT(*) 
                        FROM UserRoles ur 
                        INNER JOIN Roles r ON ur.RoleId = r.Id 
                        WHERE ur.UserId = {0} AND r.NormalizedName = {1}", 
                        userId, roleName.ToUpper())
                    .FirstOrDefaultAsync();

                return count > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证用户角色失败: {UserId}, {RoleName}", userId, roleName);
                return false;
            }
        }
    }
}
