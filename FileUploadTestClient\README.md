# TCP文件上传测试客户端

这是一个用于测试TCP文件上传服务的简易客户端程序。

## 功能特性

- ✅ 支持完整的文件上传协议
- ✅ 自动计算文件CRC32校验值
- ✅ 分块上传，支持大文件
- ✅ 实时显示上传进度
- ✅ 详细的日志输出
- ✅ 错误处理和重试机制
- ✅ 命令行参数配置

## 编译和运行

### 编译项目
```bash
cd FileUploadTestClient
dotnet build
```

### 运行测试
```bash
# 使用默认参数上传test.log文件（如果不存在会自动创建）
dotnet run test.log

# 指定服务器地址和端口
dotnet run test.log localhost 8080

# 完整参数
dotnet run test.log localhost 8080 12345678 87654321 POS
```

## 命令行参数

```
FileUploadTestClient <文件路径> [服务器地址] [端口] [商户号] [终端号] [终端类型]
```

### 参数说明

| 参数 | 必需 | 默认值 | 说明 |
|------|------|--------|------|
| 文件路径 | ✅ | - | 要上传的文件路径 |
| 服务器地址 | ❌ | localhost | TCP服务器地址 |
| 端口 | ❌ | 8080 | TCP服务器端口 |
| 商户号 | ❌ | 12345678 | 商户ID（8位数字） |
| 终端号 | ❌ | 87654321 | 终端序列号（十六进制） |
| 终端类型 | ❌ | POS | 终端类型（3字符） |

## 使用示例

### 1. 基本测试
```bash
# 上传当前目录的test.log文件到本地服务器
dotnet run test.log
```

### 2. 指定服务器
```bash
# 上传到指定服务器
dotnet run myfile.dat ************* 8080
```

### 3. 完整参数
```bash
# 使用完整参数
dotnet run transaction.log ************* 8080 12345678 ABCDEF01 ATM
```

## 测试文件

如果指定的文件是 `test.log` 且文件不存在，程序会自动创建一个包含中英文内容的测试文件。

## 协议支持

客户端实现了完整的文件上传协议：

1. **文件上传请求 (0x01)** - 发送文件信息，获取上传ID
2. **文件上传响应 (0x02)** - 接收服务器分配的上传ID和块大小
3. **文件块传输请求 (0x03)** - 分块发送文件数据
4. **文件块传输响应 (0x04)** - 接收传输确认和下一块信息

## 日志输出

程序会输出详细的日志信息，包括：
- 连接状态
- 文件信息（大小、CRC32）
- 上传进度
- 错误信息

## 错误处理

客户端会处理以下错误情况：
- 文件不存在
- 网络连接失败
- 服务器响应错误
- 数据传输错误

## 注意事项

1. 确保TCP服务器已启动并监听指定端口
2. 文件路径必须存在且可读
3. 商户号和终端号需要与服务器配置匹配
4. 大文件上传可能需要较长时间，请耐心等待

## 故障排除

### 连接失败
- 检查服务器是否启动
- 确认端口号是否正确
- 检查防火墙设置

### 上传失败
- 检查文件是否存在
- 确认文件权限
- 查看服务器日志

### CRC校验失败
- 确认文件在传输过程中未被修改
- 检查网络连接稳定性
