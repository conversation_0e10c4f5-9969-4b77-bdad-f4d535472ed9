import React from 'react';
import { Navigate } from 'react-router-dom';
import AuthLayout from './layouts/AuthLayout';
import DashboardLayout from './layouts/DashboardLayout';
import TwoFactorGuard from './components/TwoFactorGuard';
import RoleGuard from './components/RoleGuard';

import Login from './pages/auth/Login';
import VerifyCode from './pages/auth/VerifyCode';

import RegisterView from './pages/auth/Register';
import ForgotPasswordView from './pages/auth/ForgotPassword';
import ResetPasswordView from './pages/auth/ResetPassword';
import TwoFactorVerifyView from './pages/auth/TwoFactorVerify';
import TwoFactorSetupView from './pages/auth/TwoFactorSetup';
import WechatLoginView from './pages/auth/WechatLogin';
import ForcePasswordChange from './pages/auth/ForcePasswordChange';
import NotFoundView from './pages/errors/NotFoundView';
import DashboardView from './pages/dashboard/DashboardView';
import UserListView from './pages/users/UserListView';
import UserDetailView from './pages/users/UserDetailView';
import RoleListView from './pages/roles/RoleListView';
import RoleDetailView from './pages/roles/RoleDetailView';
import MerchantListView from './pages/merchants/MerchantListView';
import MerchantDetailView from './pages/merchants/MerchantDetailView';
import TerminalList from './pages/terminals/TerminalList';
import TerminalDetail from './pages/terminals/TerminalDetail';
import TerminalEvents from './pages/terminals/TerminalEvents';
import TerminalEventsList from './pages/terminals/TerminalEventsList';
import TerminalRecords from './pages/terminals/TerminalRecords';
import FileManagementView from './pages/files/FileManagementView';
import FileTypeList from './pages/files/FileTypeList';
import FileVersionList from './pages/files/FileVersionList';
import FilePublish from './pages/files/FilePublish';
import FilePublishList from './pages/files/FilePublishList';
import ScheduledFilePublishList from './pages/files/ScheduledFilePublishList';
import MessageTypeList from './pages/messages/MessageTypeList';
import MessageSend from './pages/messages/MessageSend';
import MessageList from './pages/messages/MessageList';
import AccountView from './pages/account/AccountView';
import SystemManagement from './pages/settings/SystemManagement';
import FeaturePermissionManagement from './pages/settings/FeaturePermissionManagement';
import DictionaryListView from './pages/dictionary/DictionaryListView';
import LinePriceListView from './pages/fare-params/LinePriceListView';
import LinePriceEditView from './pages/fare-params/LinePriceEditView';
import LinePriceVersionsView from './pages/fare-params/LinePriceVersionsView';
import LinePriceVersionEditView from './pages/fare-params/LinePriceVersionEditView';
import LinePricePreviewView from './pages/fare-params/LinePricePreviewView';

// 引入票价折扣方案管理页面
import FareDiscountSchemesView from './pages/fare-discount-schemes/FareDiscountSchemesView';
import FareDiscountSchemeCreateView from './pages/fare-discount-schemes/FareDiscountSchemeCreateView';
import FareDiscountSchemeEditView from './pages/fare-discount-schemes/FareDiscountSchemeEditView';
import FareDiscountSchemeVersionsView from './pages/fare-discount-schemes/FareDiscountSchemeVersionsView';

// 引入银联终端密钥管理页面
import UnionPayTerminalKeyList from './pages/unionPayTerminalKeys/UnionPayTerminalKeyList';

// 引入未注册线路页面
import UnregisteredLines from './pages/unregisteredLines/UnregisteredLines';

// 引入审计日志页面
import LoginLogs from './pages/audit-logs/LoginLogs';
import PasswordChangeLogs from './pages/audit-logs/PasswordChangeLogs';
import OperationLogs from './pages/audit-logs/OperationLogs';

// 引入菜单管理页面
import MenuManagement from './pages/menu-management/MenuManagement';

// 引入车辆管理页面
import VehicleList from './pages/vehicles/VehicleList';
import VehicleDetail from './pages/vehicles/VehicleDetail';
import VehicleCreate from './pages/vehicles/VehicleCreate';

// 引入终端文件上传管理页面
import TerminalFileUploads from './pages/terminal-file-uploads/TerminalFileUploads';

// 引入终端日志页面
import TerminalLogs from './pages/terminalLogs/TerminalLogs';

// 引入外部系统页面
import ExternalSystemPage from './pages/external/ExternalSystemPage';

// 引入调试页面
import PermissionDebug from './components/PermissionDebug';


const routes = [
  {
    path: 'app',
    element: (
      <TwoFactorGuard>
        <DashboardLayout />
      </TwoFactorGuard>
    ),
    children: [
      { path: 'dashboard', element: <DashboardView /> },
      { path: 'account', element: <AccountView /> },
      {
        path: 'users',
        element: (
          <RoleGuard roles={['SystemAdmin', 'MerchantAdmin']}>
            <UserListView />
          </RoleGuard>
        )
      },
      {
        path: 'users/:id',
        element: (
          <RoleGuard roles={['SystemAdmin', 'MerchantAdmin']}>
            <UserDetailView />
          </RoleGuard>
        )
      },
      {
        path: 'roles',
        element: (
          <RoleGuard roles={['SystemAdmin']}>
            <RoleListView />
          </RoleGuard>
        )
      },
      {
        path: 'roles/:id',
        element: (
          <RoleGuard roles={['SystemAdmin']}>
            <RoleDetailView />
          </RoleGuard>
        )
      },
      {
        path: 'merchants',
        element: (
          <RoleGuard roles={['SystemAdmin']}>
            <MerchantListView />
          </RoleGuard>
        )
      },
      {
        path: 'merchants/:id',
        element: (
          <RoleGuard roles={['SystemAdmin']}>
            <MerchantDetailView />
          </RoleGuard>
        )
      },
      {
        path: 'terminals',
        element: (
          <RoleGuard roles={['SystemAdmin', 'MerchantAdmin']}>
            <TerminalList />
          </RoleGuard>
        )
      },
      {
        path: 'terminals/:id',
        element: (
          <RoleGuard roles={['SystemAdmin', 'MerchantAdmin']}>
            <TerminalDetail />
          </RoleGuard>
        )
      },
      {
        path: 'terminals/:id/events',
        element: (
          <RoleGuard roles={['SystemAdmin', 'MerchantAdmin']}>
            <TerminalEvents />
          </RoleGuard>
        )
      },
      {
        path: 'terminal-events',
        element: (
          <RoleGuard roles={['SystemAdmin', 'MerchantAdmin']}>
            <TerminalEventsList />
          </RoleGuard>
        )
      },
      {
        path: 'terminal-records',
        element: (
          <RoleGuard roles={['SystemAdmin', 'MerchantAdmin']}>
            <TerminalRecords />
          </RoleGuard>
        )
      },
      {
        path: 'unregistered-lines',
        element: (
          <RoleGuard roles={['SystemAdmin', 'MerchantAdmin']}>
            <UnregisteredLines />
          </RoleGuard>
        )
      },
      {
        path: 'terminal-logs',
        element: (
          <RoleGuard roles={['SystemAdmin', 'MerchantAdmin']}>
            <TerminalLogs />
          </RoleGuard>
        )
      },
      {
        path: 'vehicles',
        element: (
          <RoleGuard roles={['SystemAdmin', 'MerchantAdmin']}>
            <VehicleList />
          </RoleGuard>
        )
      },
      {
        path: 'vehicles/new',
        element: (
          <RoleGuard roles={['SystemAdmin', 'MerchantAdmin']}>
            <VehicleCreate />
          </RoleGuard>
        )
      },
      {
        path: 'vehicles/:id',
        element: (
          <RoleGuard roles={['SystemAdmin', 'MerchantAdmin']}>
            <VehicleDetail />
          </RoleGuard>
        )
      },
      {
        path: 'vehicles/:id/edit',
        element: (
          <RoleGuard roles={['SystemAdmin', 'MerchantAdmin']}>
            <VehicleDetail />
          </RoleGuard>
        )
      },
      {
        path: 'files',
        element: (
          <RoleGuard roles={['SystemAdmin', 'MerchantAdmin']}>
            <FileManagementView />
          </RoleGuard>
        )
      },
      {
        path: 'files/types',
        element: (
          <RoleGuard roles={['SystemAdmin', 'MerchantAdmin']}>
            <FileTypeList />
          </RoleGuard>
        )
      },
      {
        path: 'files/versions',
        element: (
          <RoleGuard roles={['SystemAdmin', 'MerchantAdmin']}>
            <FileVersionList />
          </RoleGuard>
        )
      },
      {
        path: 'files/publish',
        element: (
          <RoleGuard roles={['SystemAdmin', 'MerchantAdmin']}>
            <FilePublish />
          </RoleGuard>
        )
      },
      {
        path: 'files/publish-list',
        element: (
          <RoleGuard roles={['SystemAdmin', 'MerchantAdmin']}>
            <FilePublishList />
          </RoleGuard>
        )
      },
      {
        path: 'files/scheduled-publish',
        element: (
          <RoleGuard roles={['SystemAdmin', 'MerchantAdmin']}>
            <ScheduledFilePublishList />
          </RoleGuard>
        )
      },
      {
        path: 'terminal-file-uploads',
        element: (
          <RoleGuard roles={['SystemAdmin', 'MerchantAdmin']}>
            <TerminalFileUploads />
          </RoleGuard>
        )
      },
      {
        path: 'messages-types',
        element: (
          <RoleGuard roles={['SystemAdmin', 'MerchantAdmin']}>
            <MessageTypeList />
          </RoleGuard>
        )
      },
      {
        path: 'messages/send',
        element: (
          <RoleGuard roles={['SystemAdmin', 'MerchantAdmin']}>
            <MessageSend />
          </RoleGuard>
        )
      },
      {
        path: 'messages',
        element: (
          <RoleGuard roles={['SystemAdmin', 'MerchantAdmin']}>
            <MessageList />
          </RoleGuard>
        )
      },
      {
        path: 'dictionary',
        element: (
          <RoleGuard roles={['SystemAdmin', 'MerchantAdmin']}>
            <DictionaryListView />
          </RoleGuard>
        )
      },
      {
        path: 'fare-params',
        element: (
          <RoleGuard roles={['SystemAdmin', 'MerchantAdmin']}>
            <LinePriceListView />
          </RoleGuard>
        )
      },

      {
        path: 'fare-params/:id',
        element: (
          <RoleGuard roles={['SystemAdmin', 'MerchantAdmin']}>
            <LinePriceEditView />
          </RoleGuard>
        )
      },
      {
        path: 'fare-params/:id/versions',
        element: (
          <RoleGuard roles={['SystemAdmin', 'MerchantAdmin']}>
            <LinePriceVersionsView />
          </RoleGuard>
        )
      },
      {
        path: 'fare-params/:id/versions/:versionId',
        element: (
          <RoleGuard roles={['SystemAdmin', 'MerchantAdmin']}>
            <LinePriceVersionEditView />
          </RoleGuard>
        )
      },
      {
        path: 'fare-params/:id/versions/:versionId/preview',
        element: (
          <RoleGuard roles={['SystemAdmin', 'MerchantAdmin']}>
            <LinePricePreviewView />
          </RoleGuard>
        )
      },

      // 票价折扣方案管理路由
      {
        path: 'fare-discount-schemes',
        element: (
          <RoleGuard roles={['SystemAdmin', 'MerchantAdmin']}>
            <FareDiscountSchemesView />
          </RoleGuard>
        )
      },
      {
        path: 'fare-discount-schemes/create',
        element: (
          <RoleGuard roles={['SystemAdmin', 'MerchantAdmin']}>
            <FareDiscountSchemeCreateView />
          </RoleGuard>
        )
      },
      {
        path: 'fare-discount-schemes/edit/:id',
        element: (
          <RoleGuard roles={['SystemAdmin', 'MerchantAdmin']}>
            <FareDiscountSchemeEditView />
          </RoleGuard>
        )
      },
      {
        path: 'fare-discount-schemes/:id/versions',
        element: (
          <RoleGuard roles={['SystemAdmin', 'MerchantAdmin']}>
            <FareDiscountSchemeVersionsView />
          </RoleGuard>
        )
      },
      {
        path: 'fare-discount-schemes/:id/versions',
        element: (
          <RoleGuard roles={['SystemAdmin', 'MerchantAdmin']}>
            <FareDiscountSchemeVersionsView />
          </RoleGuard>
        )
      },
      {
        path: 'audit-logs/login-logs',
        element: (
          <RoleGuard roles={['SystemAdmin', 'MerchantAdmin']}>
            <LoginLogs />
          </RoleGuard>
        )
      },
      {
        path: 'audit-logs/password-logs',
        element: (
          <RoleGuard roles={['SystemAdmin', 'MerchantAdmin']}>
            <PasswordChangeLogs />
          </RoleGuard>
        )
      },
      {
        path: 'audit-logs/operation-logs',
        element: (
          <RoleGuard roles={['SystemAdmin', 'MerchantAdmin']}>
            <OperationLogs />
          </RoleGuard>
        )
      },
      {
        path: 'settings',
        element: (
          <RoleGuard roles={['SystemAdmin']}>
            <SystemManagement />
          </RoleGuard>
        )
      },
      {
        path: 'feature-permissions',
        element: (
          <RoleGuard roles={['SystemAdmin']}>
            <FeaturePermissionManagement />
          </RoleGuard>
        )
      },
      {
        path: 'union-pay-terminal-keys',
        element: (
          <RoleGuard roles={['SystemAdmin', 'MerchantAdmin']}>
            <UnionPayTerminalKeyList />
          </RoleGuard>
        )
      },
      {
        path: 'menu-management',
        element: (
          <RoleGuard roles={['SystemAdmin']}>
            <MenuManagement />
          </RoleGuard>
        )
      },
      {
        path: 'external/:systemKey',
        element: (
          <TwoFactorGuard>
            <ExternalSystemPage />
          </TwoFactorGuard>
        )
      },
      // 调试页面 - 仅开发环境
      ...(process.env.NODE_ENV === 'development' ? [
        { path: 'debug/permissions', element: <PermissionDebug /> }
      ] : []),
      { path: '404', element: <NotFoundView /> },
      { path: '*', element: <Navigate to="/app/404" /> }
    ]
  },
  {
    path: '/',
    element: <AuthLayout />,
    children: [
      { path: 'login', element: <Login /> },
      { path: 'verify-code', element: <VerifyCode /> },
      { path: 'auth/login', element: <Navigate to="/login" /> },
      { path: 'register', element: <RegisterView /> },
      { path: 'forgot-password', element: <ForgotPasswordView /> },
      { path: 'reset-password', element: <ResetPasswordView /> },
      { path: 'two-factor-verify', element: <TwoFactorVerifyView /> },
      { path: 'two-factor-setup', element: <TwoFactorSetupView /> },
      { path: 'wechat-login', element: <WechatLoginView /> },
      { path: 'force-password-change', element: <ForcePasswordChange /> },
      {
        path: '/',
        element: (
          <TwoFactorGuard>
            <Navigate to="/app/dashboard" />
          </TwoFactorGuard>
        )
      },
      { path: '*', element: <Navigate to="/app/404" /> }
    ]
  }
];

export default routes;
