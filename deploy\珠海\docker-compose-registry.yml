

services:
  # MySQL 数据库
  # mysql:
  #   image: devtest.pointlife365.net:5180/library/mysql:8.0
  #   container_name: slzr-mysql
  #   command: --default-authentication-plugin=mysql_native_password
  #   volumes:
  #     - mysql_data:/var/lib/mysql
  #   environment:
  #     MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD:-slzr!12345}
  #     MYSQL_DATABASE: tcpserver
  #   ports:
  #     - "3307:3306"
  #   networks:
  #     - slzr-network
  #   restart: unless-stopped
  #   healthcheck:
  #     test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p${MYSQL_ROOT_PASSWORD:-slzr!12345}"]
  #     interval: 10s
  #     timeout: 5s
  #     retries: 5

  # RabbitMQ 消息队列
  rabbitmq:
    image: devtest.pointlife365.net:5180/library/rabbitmq:3.13.7-management
    container_name: slzr-rabbitmq
    hostname: slzr-rabbitmq  # 重要：设置hostname确保数据一致性
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
      - rabbitmq_logs:/var/log/rabbitmq  # 添加日志卷
    environment:
      - RABBITMQ_DEFAULT_USER=${RABBITMQ_USER:-guest}
      - RABBITMQ_DEFAULT_PASS=${RABBITMQ_PASS:-guest}
      - RABBITMQ_NODENAME=rabbit@slzr-rabbitmq  # 确保节点名称一致
      - RABBITMQ_USE_LONGNAME=true  # 使用长名称确保集群稳定性
    ports:
      - "5672:5672"   # AMQP端口
      - "15672:15672" # 管理界面端口
    networks:
      - slzr-network
    restart: unless-stopped
    # healthcheck:
    #   test: ["CMD-SHELL", "rabbitmq-diagnostics ping || rabbitmqctl node_health_check || exit 1"]
    #   interval: 30s
    #   timeout: 10s
    #   retries: 5
    #   start_period: 60s  # 给RabbitMQ更多启动时间

  # MinIO 对象存储
  minio:
    image: devtest.pointlife365.net:5180/library/minio:latest
    container_name: minio
    command: server /data --console-address ":9001"
    volumes:
      - minio_data:/data
    environment:
      - MINIO_ROOT_USER=${MINIO_ROOT_USER:-minioadmin}
      - MINIO_ROOT_PASSWORD=${MINIO_ROOT_PASSWORD:-minioadmin123}
    #ports:
    #  - "9000:9000"   # MinIO API端口
    #  - "9001:9001"   # MinIO Console端口
    networks:
      - slzr-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3

  api-service:
    image: devtest.pointlife365.net:5180/slzr/tcpserver-api:latest
    container_name: tcpserver-api
    #deploy:
    #  #replicas: 2  # 启动 1 个副本
    #  resources:
    #    limits:
    #      cpus: "4.0"  # 4 核 CPU
    #      memory: 8G    # 8GB 内存
    extra_hosts:  # 添加宿主机IP的DNS映射
      - "host.docker.internal:host-gateway"
    #depends_on:
    #  rabbitmq:
    #    condition: service_healthy
    #   mysql:
    #     condition: service_healthy
    ports:
    #  - "6000:8000"  # HTTP API (通过nginx代理)
      - "18822:8001"  # TCP 服务 (通过nginx代理)
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - HTTP_PORT=8000
      - TCP_PORT=8001
      - ConnectionStrings__DefaultConnection=Server=${MYSQL_HOST:-host.docker.internal};Port=3306;Database=zhtong_busdb;User=${MYSQL_ROOT_USER:-3306};Password=${MYSQL_ROOT_PASSWORD:-slzr!12345};SslMode=None;AllowLoadLocalInfile=true;
      - DatabaseProvider=MySql
      - RabbitMQ__HostName=slzr-rabbitmq
      - RabbitMQ__Port=5672
      - RabbitMQ__UserName=${RABBITMQ_USER:-guest}
      - RabbitMQ__Password=${RABBITMQ_PASS:-guest}
      - FileService__DefaultStorageType=${FILE_STORAGE_TYPE:-MinIO}
      - FileService__LocalFilePath=/app/storage/files
      - FileService__MinIO__Endpoint=minio:9000
      - FileService__MinIO__AccessKey=${MINIO_ROOT_USER:-minioadmin}
      - FileService__MinIO__SecretKey=${MINIO_ROOT_PASSWORD:-minioadmin123}
      - FileService__MinIO__BucketName=${MINIO_BUCKET_NAME:-slzr-files}
      - TZ=Asia/Shanghai
    volumes:
      - api_storage:/app/storage
      - /etc/localtime:/etc/localtime:ro
    networks:
      - slzr-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]  # 与HTTP_PORT环境变量保持一致
      interval: 30s
      timeout: 10s
      retries: 3

  # WebAdmin 服务
  web-admin:
    image: devtest.pointlife365.net:5180/slzr/tcpserver-web:latest
    container_name: tcpserver-web
    #deploy:
    #  replicas: 1  # 启动副本数量，使用多副本时，需要删除容器名称，因为多副本时不能指定唯一容器名。
    extra_hosts:  # 添加宿主机IP的DNS映射
      - "host.docker.internal:host-gateway"
    #depends_on:
    #  rabbitmq:
    #    condition: service_healthy
    #   mysql:
    #     condition: service_healthy
    #ports:
    #  - "11822:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ConnectionStrings__DefaultConnection=Server=${MYSQL_HOST:-host.docker.internal};Port=3306;Database=zhtong_busdb;User=${MYSQL_ROOT_USER:-3306};Password=${MYSQL_ROOT_PASSWORD:-slzr!12345};SslMode=None;AllowLoadLocalInfile=true;
      - DatabaseProvider=MySql
      - RabbitMQ__HostName=slzr-rabbitmq
      - RabbitMQ__Port=5672
      - RabbitMQ__UserName=${RABBITMQ_USER:-guest}
      - RabbitMQ__Password=${RABBITMQ_PASS:-guest}
      - Jwt__Key=${JWT_KEY:-YourSecretKeyHere12345678901234567890}
      - FileService__DefaultStorageType=${FILE_STORAGE_TYPE:-MinIO}
      - FileService__LocalFilePath=/app/storage/files
      - FileService__MinIO__Endpoint=minio:9000
      - FileService__MinIO__AccessKey=${MINIO_ROOT_USER:-minioadmin}
      - FileService__MinIO__SecretKey=${MINIO_ROOT_PASSWORD:-minioadmin123}
      - FileService__MinIO__BucketName=${MINIO_BUCKET_NAME:-slzr-files}
      - TZ=Asia/Shanghai
    volumes:
      - webadmin_storage:/app/storage
      - webadmin_keys:/app/Keys
      - /etc/localtime:/etc/localtime:ro
      - ./webadmin-config:/app/wwwroot/config
    networks:
      - slzr-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
# 珠海通客户项目
  zhuhaitong:
    image: devtest.pointlife365.net:5180/slzr/tcpserver-zhuhaitong:latest
    container_name: tcpserver-zhuhaitong
    extra_hosts:  # 添加宿主机IP的DNS映射
      - "host.docker.internal:host-gateway"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_BASEPATH=/customa
      - ConnectionStrings__DefaultConnection=Server=${MYSQL_HOST:-host.docker.internal};Port=3306;Database=zhtong_busdb;User=${MYSQL_ROOT_USER:-3306};Password=${MYSQL_ROOT_PASSWORD:-slzr!12345};SslMode=None;AllowLoadLocalInfile=true;
      - ConnectionStrings__AuthReadOnlyConnection=Server=${MYSQL_HOST:-host.docker.internal};Port=3306;Database=zhtong_busdb;User=${MYSQL_ROOT_USER:-3306};Password=${MYSQL_ROOT_PASSWORD:-slzr!12345};SslMode=None;AllowLoadLocalInfile=true;
      # JWT 配置 - 需要与主项目保持一致
      - Jwt__Key=${JWT_KEY:-YourSecretKeyHere12345678901234567890}
      - Jwt__Issuer=slzr-cn.com
      - Jwt__Audience=WebAdmin
      # 主项目 API 地址
      - MainProject__ApiUrl=http://web-admin:80
    volumes:
      - zhuhaitong_logs:/app/logs
      - /slzr/dotnet-app:/app/data
    networks:
      - slzr-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    logging:
      driver: json-file
      options:
        max-size: "50m"
        max-file: "10"

  # Nginx 反向代理
  nginx:
    image: devtest.pointlife365.net:5180/library/nginx:alpine
    container_name: slzr-nginx
    ports:
      - "11822:80"  
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./nginx/logs:/var/log/nginx
#    depends_on:
#      - web-admin
#      - api-service
#      - zhuhaitong
    networks:
      - slzr-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    logging:
      driver: json-file
      options:
        max-size: "50m"
        max-file: "10"


networks:
  slzr-network:
    driver: bridge

volumes:
  mysql_data:
  rabbitmq_data:
  rabbitmq_logs:
  minio_data:
  api_storage:
  webadmin_storage:
  webadmin_keys:
  zhuhaitong_logs:
