using Microsoft.EntityFrameworkCore;
using SlzrCrossGate.Core.Database;
using SlzrCrossGate.Core.Models;
using SlzrCrossGate.Core.Service.BusinessServices;
using SlzrCrossGate.Core.Service.FileStorage;
using SlzrCrossGate.Core.Services;

public class SyncDataService : IHostedService
{

    private readonly string EXCHANGE_NAME = "SlzrCrossGate.Task";
    private readonly string QUEUE_NAME = "SlzrCrossGate.Task.Queue.FilePublish";
    private readonly string ROUTING_KEY = "Task.FilePublish.#";

    private readonly IRabbitMQService _rabbitMQService;
    private readonly ILogger<SyncDataService> _logger;
    private readonly IServiceProvider _serviceProvider;
    private readonly FileService _fileService;

    public SyncDataService(IRabbitMQService rabbitMQService, ILogger<SyncDataService> logger,
        IServiceProvider serviceProvider, FileService fileService)
    {
        _rabbitMQService = rabbitMQService;
        _logger = logger;
        _serviceProvider = serviceProvider;
        _fileService = fileService;
    }

    public Task StartAsync(CancellationToken cancellationToken)
    {
        _rabbitMQService.SubscribeAsync<SyncData>(EXCHANGE_NAME, QUEUE_NAME, ROUTING_KEY,async (message,ea) =>
        {
            try
            {
                using var scope = _serviceProvider.CreateScope();
                var dbContext = scope.ServiceProvider.GetRequiredService<TcpDbContext>();
                var filePublishEventService = scope.ServiceProvider.GetRequiredService<FilePublishEventService>();

                var targetMerchantId = "";
                if (ea.RoutingKey.StartsWith("Task.FilePublish."))
                {
                    // 处理文件发布任务
                    // 从RoutingKey中获取要发布的商户信息  Task.FilePublish.{merchantid}.{FileType}
                    targetMerchantId = ea.RoutingKey.Split('.')[2];
                }
                else if (ea.RoutingKey.StartsWith("city."))
                {
                    targetMerchantId = "ALL";
                }
                else
                {
                    _logger.LogWarning("Rabbitmq发布文件失败，未知的 RoutingKey: {RoutingKey}", ea.RoutingKey);
                    return;
                }
                targetMerchantId = targetMerchantId.ToUpper();
                if (targetMerchantId != "ALL")
                {
                    // 检查商户是否存在
                    var merchantExists = await dbContext.Merchants.AnyAsync(m => m.MerchantID == targetMerchantId);
                    if (!merchantExists)
                    {
                        _logger.LogWarning("Rabbitmq发布文件失败,指定商户不存在: {merchantId}", targetMerchantId);
                        return;
                    }
                }

                var fileFullType = message.FileType.ToString();
                if (string.IsNullOrEmpty(message.FileType) == false && message.FileType.Length >= 3 && message.FileType.Length <= 11)
                {
                    fileFullType = message.FileType;
                }

                //准备上传文件

                var fileName = $"{fileFullType}_{message.Version}";
                var fileId = Guid.NewGuid().ToString("N");
                var fileSize = message.Content.Length;
                var crc = SlzrCrossGate.Common.CRC.calFileCRC(message.Content).ToString("X2").PadLeft(8, '0');
                var username = "Rabbitmq";

                // 保存文件
                var filePath = await _fileService.SaveTemporaryFile(message.Content, fileName, username);

                // 创建上传文件记录
                var uploadFile = new UploadFile
                {
                    ID = fileId,
                    FileName = fileName ?? "未知文件",
                    FileSize = fileSize,
                    FilePath = filePath,
                    UploadTime = DateTime.Now,
                    Crc = crc,
                    UploadedBy = username,
                    ContentType = "application/octet-stream"
                };

                dbContext.UploadFiles.Add(uploadFile);
                await dbContext.SaveChangesAsync();

                //创建临时文件版本记录
                var fileVersion = new FileVer
                {
                    MerchantID = "",
                    FileTypeID = fileFullType[..3],
                    FilePara = fileFullType[3..],
                    FileFullType = fileFullType,
                    Ver = message.Version,
                    CreateTime = DateTime.Now,
                    UpdateTime = DateTime.Now,
                    UploadFileID = fileId,
                    FileSize = fileSize,
                    Crc = crc,
                    Operator = username,
                    IsDelete = false
                };

                if (targetMerchantId == "ALL")
                {
                    await PublishToAllMerchants(fileVersion, dbContext, filePublishEventService);
                }
                else
                {
                    await PublishToMerchant(fileVersion, targetMerchantId, dbContext, filePublishEventService);
                }
                _logger.LogInformation("Rabbitmq发布文件成功, 文件类型: {fileFullType} , 版本: {version}, RoutingKey: {RoutingKey}", fileVersion.FileFullType, fileVersion.Ver, ea.RoutingKey);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "RabbitMQ发布文件异常");
                //静默一段时间，避免死循环
                await Task.Delay(5000);
                throw;
            }

        }, true);

        return Task.CompletedTask;
    }


    public async Task PublishToAllMerchants(FileVer fileverTmp, TcpDbContext dbContext, FilePublishEventService filePublishEventService)
    {
        //查询所有商户号，然后在所有商户下发布文件
        var merchants = await dbContext.Merchants.Select(m => m.MerchantID).ToListAsync();
        foreach (var merchant in merchants)
        {
            await PublishToMerchant(fileverTmp, merchant, dbContext, filePublishEventService);
        }

    }


    public async Task PublishToMerchant(FileVer fileverTmp, string merchantid, TcpDbContext dbContext, FilePublishEventService filePublishEventService)
    {

        // 检查文件类型是否存在
        var fileTypeExists = await dbContext.FileTypes
            .AnyAsync(t => t.ID == fileverTmp.FileTypeID && t.MerchantID == merchantid);
        if (!fileTypeExists)
        {
            _logger.LogWarning("Rabbitmq发布文件失败,文件类型不存在: {FileTypeID} , 商户: {MerchantID}", fileverTmp.FileTypeID, merchantid);
            return;
        }

        //检查文件版本是否存在
        var existingVersion = await dbContext.FileVers
            .FirstOrDefaultAsync(f => f.MerchantID == merchantid && f.FileTypeID == fileverTmp.FileTypeID && f.FilePara == fileverTmp.FilePara && f.Ver == fileverTmp.Ver);

        if (existingVersion != null && !existingVersion.IsDelete)
        {
            // 检查是否有关联的文件发布
            var hasFilePublish = await dbContext.FilePublishs
            .AnyAsync(p => p.FileVerID == existingVersion.ID);

            if (hasFilePublish)
            {
                _logger.LogWarning("Rabbitmq发布文件失败,当前版本已经发布，不能重复创建，请上传其它版本号或删除已发布版本: {FileFullType} , 版本: {Ver} , 商户: {MerchantID}", fileverTmp.FileFullType, fileverTmp.Ver, merchantid);
                return;
            }
            else
            {
                // 删除旧版本
                existingVersion.IsDelete = true;
            }
        }
        // 创建文件版本记录
        var filever = new FileVer
        {
            MerchantID = merchantid,
            FileTypeID = fileverTmp.FileTypeID,
            FilePara = fileverTmp.FilePara,
            FileFullType = fileverTmp.FileFullType,
            Ver = fileverTmp.Ver,
            CreateTime = DateTime.Now,
            UpdateTime = DateTime.Now,
            UploadFileID = fileverTmp.UploadFileID,
            FileSize = fileverTmp.FileSize,
            Crc = fileverTmp.Crc,
            Operator = fileverTmp.Operator,
            IsDelete = false
        };
        dbContext.FileVers.Add(filever);
        await dbContext.SaveChangesAsync();

        _logger.LogInformation("Rabbitmq文件版本创建成功, 文件ID: {fileVersionId}", filever.ID);


        // 检查该文件类型和发布目标与发布类型是否已存在
        var filePublish = await dbContext.FilePublishs
            .FirstOrDefaultAsync(p =>p.FileFullType== filever.FileFullType && p.PublishType == PublishTypeOption.Merchant && p.PublishTarget == filever.MerchantID && p.MerchantID == filever.MerchantID);
        if (filePublish != null)
        {
            if (filePublish.FileVerID == filever.ID)
            {
                _logger.LogWarning("Rabbitmq发布文件失败,该发布已存在: {FileFullType} , 版本: {Ver} , 商户: {MerchantID}", filever.FileFullType, filever.Ver, filever.MerchantID);
                return;
            }
            //存在其它版本的发布，更新版本信息
            filePublish.FileVerID = filever.ID;
            filePublish.Ver = filever.Ver;
            filePublish.FileSize = filever.FileSize;
            filePublish.Crc = filever.Crc;
            filePublish.UploadFileID = filever.UploadFileID;
            filePublish.PublishTime = DateTime.Now;
            filePublish.Operator = fileverTmp.Operator;
        }
        else
        {
            // 创建文件发布记录
            filePublish = new FilePublish
            {
                MerchantID = filever.MerchantID,
                FileTypeID = filever.FileTypeID,
                FilePara = filever.FilePara,
                FileFullType = filever.FileFullType,
                Ver = filever.Ver,
                FileSize = filever.FileSize,
                Crc = filever.Crc,
                FileVerID = filever.ID,
                UploadFileID = filever.UploadFileID,
                PublishType = PublishTypeOption.Merchant,
                PublishTarget = filever.MerchantID,
                PublishTime = DateTime.Now,
                Operator = fileverTmp.Operator
            };
            dbContext.FilePublishs.Add(filePublish);
        }

        // 创建文件发布历史记录
        var filePublishHistory = new FilePublishHistory
        {
            MerchantID = merchantid,
            FileTypeID = fileverTmp.FileTypeID,
            FilePara = fileverTmp.FilePara,
            FileFullType = fileverTmp.FileFullType,
            Ver = fileverTmp.Ver,
            FileSize = fileverTmp.FileSize,
            Crc = fileverTmp.Crc,
            FileVerID = filever.ID,
            UploadFileID = fileverTmp.UploadFileID,
            PublishType = PublishTypeOption.Merchant,
            PublishTarget = merchantid,
            PublishTime = DateTime.Now,
            Operator = fileverTmp.Operator
        };
        dbContext.FilePublishHistories.Add(filePublishHistory);
        await dbContext.SaveChangesAsync();


        await filePublishEventService.Publish(new FilePublishEventMessage
        {
            ActionType = FilePublishEventActionType.Publish,
            FilePublishID = filePublish.ID,
            MerchantID = filePublish.MerchantID,
            FileTypeID = filePublish.FileTypeID,
            FilePara = filePublish.FilePara,
            FileFullType = filePublish.FileFullType,
            Ver = filePublish.Ver,
            FileCrc = filePublish.Crc,
            FileSize = filePublish.FileSize,
            Operator = filePublish.Operator!,
            PublishTarget = filePublish.PublishTarget,
            PublishType = filePublish.PublishType,
            ActionTime = filePublish.PublishTime
        });

        _logger.LogInformation("Rabbitmq发布文件成功, 文件发布ID: {filePublishId} , 商户: {merchantId} , 文件类型: {fileFullType} , 版本: {version}", filePublish.ID, merchantid, fileverTmp.FileFullType, fileverTmp.Ver);
    }


    public Task StopAsync(CancellationToken cancellationToken)
    {
        return Task.CompletedTask;
    }
}
