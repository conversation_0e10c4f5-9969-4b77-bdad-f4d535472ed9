using Microsoft.EntityFrameworkCore;
using SlzrCrossGate.CustomerTemplate.Data;
using SlzrCrossGate.CustomerTemplate.Models;
using SlzrCrossGate.Core.Models;
using System.Security.Claims;

namespace SlzrCrossGate.CustomerTemplate.Services
{
    /// <summary>
    /// 认证服务 - 用于验证用户身份和获取用户信息
    /// </summary>
    public class AuthService
    {
        private readonly AuthReadOnlyContext _context;
        private readonly ILogger<AuthService> _logger;

        public AuthService(AuthReadOnlyContext context, ILogger<AuthService> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// 验证用户是否存在且有效
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户信息，如果用户不存在或无效则返回null</returns>
        public async Task<UserReadOnly?> ValidateUserAsync(string userId)
        {
            try
            {
                var user = await _context.Users
                    .FirstOrDefaultAsync(u => u.Id == userId && !u.IsDeleted);

                if (user == null)
                {
                    _logger.LogWarning("用户不存在或已被删除: {UserId}", userId);
                    return null;
                }

                return user;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证用户时发生错误: {UserId}", userId);
                return null;
            }
        }

        /// <summary>
        /// 根据用户名获取用户信息
        /// </summary>
        /// <param name="userName">用户名</param>
        /// <returns>用户信息</returns>
        public async Task<UserReadOnly?> GetUserByNameAsync(string userName)
        {
            try
            {
                return await _context.Users
                    .FirstOrDefaultAsync(u => u.UserName == userName && !u.IsDeleted);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据用户名获取用户信息时发生错误: {UserName}", userName);
                return null;
            }
        }

        /// <summary>
        /// 根据邮箱获取用户信息
        /// </summary>
        /// <param name="email">邮箱</param>
        /// <returns>用户信息</returns>
        public async Task<UserReadOnly?> GetUserByEmailAsync(string email)
        {
            try
            {
                return await _context.Users
                    .FirstOrDefaultAsync(u => u.Email == email && !u.IsDeleted);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据邮箱获取用户信息时发生错误: {Email}", email);
                return null;
            }
        }

        /// <summary>
        /// 获取用户的商户信息
        /// </summary>
        /// <param name="merchantId">商户ID</param>
        /// <returns>商户信息</returns>
        public async Task<MerchantReadOnly?> GetMerchantAsync(string merchantId)
        {
            try
            {
                return await _context.Merchants
                    .FirstOrDefaultAsync(m => m.MerchantID == merchantId && m.IsActive);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取商户信息时发生错误: {MerchantId}", merchantId);
                return null;
            }
        }

        /// <summary>
        /// 从Claims中提取用户信息
        /// </summary>
        /// <param name="claims">用户Claims</param>
        /// <returns>用户信息DTO</returns>
        public UserInfoDto ExtractUserInfo(IEnumerable<Claim> claims)
        {
            var userInfo = new UserInfoDto();

            foreach (var claim in claims)
            {
                switch (claim.Type)
                {
                    case ClaimTypes.NameIdentifier:
                        userInfo.UserId = claim.Value;
                        break;
                    case ClaimTypes.Name:
                        userInfo.UserName = claim.Value;
                        break;
                    case ClaimTypes.Email:
                        userInfo.Email = claim.Value;
                        break;
                    case "RealName":
                        userInfo.RealName = claim.Value;
                        break;
                    case "MerchantID":
                        userInfo.MerchantId = claim.Value;
                        break;
                    case ClaimTypes.Role:
                        userInfo.Roles.Add(claim.Value);
                        break;
                }
            }

            return userInfo;
        }

        /// <summary>
        /// 检查用户是否有指定角色
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="roleName">角色名称</param>
        /// <returns>是否有指定角色</returns>
        public async Task<bool> IsInRoleAsync(string userId, string roleName)
        {
            try
            {
                // 这里需要查询用户角色表，但由于是只读上下文，我们简化处理
                // 在实际应用中，可能需要额外的角色查询逻辑
                _logger.LogInformation("检查用户角色: {UserId}, {RoleName}", userId, roleName);
                
                // 临时实现：假设所有用户都有基本角色
                return roleName == "User";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查用户角色时发生错误: {UserId}, {RoleName}", userId, roleName);
                return false;
            }
        }
    }

    /// <summary>
    /// 用户信息DTO
    /// </summary>
    public class UserInfoDto
    {
        public string UserId { get; set; } = string.Empty;
        public string UserName { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string RealName { get; set; } = string.Empty;
        public string MerchantId { get; set; } = string.Empty;
        public List<string> Roles { get; set; } = new();
        public string MerchantName { get; set; } = string.Empty;
    }
}
