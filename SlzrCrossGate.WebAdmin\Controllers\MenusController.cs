using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using SlzrCrossGate.Core.Database;
using SlzrCrossGate.Core.Models;
using SlzrCrossGate.WebAdmin.DTOs;
using SlzrCrossGate.WebAdmin.Services;

namespace SlzrCrossGate.WebAdmin.Controllers
{
    /// <summary>
    /// 菜单管理控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class MenusController : ControllerBase
    {
        private readonly MenuService _menuService;
        private readonly TcpDbContext _dbContext;
        private readonly ILogger<MenusController> _logger;

        public MenusController(MenuService menuService, TcpDbContext dbContext, ILogger<MenusController> logger)
        {
            _menuService = menuService;
            _dbContext = dbContext;
            _logger = logger;
        }

        /// <summary>
        /// 获取当前用户可见的菜单
        /// </summary>
        /// <returns></returns>
        [HttpGet("user-menus")]
        public async Task<ActionResult<List<MenuGroupDto>>> GetUserMenus()
        {
            try
            {
                // 从JWT令牌中获取用户角色
                var userRoles = User.Claims
                    .Where(c => c.Type == "http://schemas.microsoft.com/ws/2008/06/identity/claims/role")
                    .Select(c => c.Value)
                    .ToList();

                var menus = await _menuService.GetMenusByRoleAsync(userRoles);
                return Ok(menus);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户菜单时发生错误");
                return StatusCode(500, new { message = "服务器内部错误" });
            }
        }

        /// <summary>
        /// 获取所有菜单（管理员专用）
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Authorize(Roles = "SystemAdmin")]
        public async Task<ActionResult<List<MenuGroupDto>>> GetAllMenus()
        {
            try
            {
                var menus = await _menuService.GetAllMenusAsync();
                return Ok(menus);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有菜单时发生错误");
                return StatusCode(500, new { message = "服务器内部错误" });
            }
        }

        /// <summary>
        /// 获取菜单分组详情
        /// </summary>
        /// <param name="id">分组ID</param>
        /// <returns></returns>
        [HttpGet("groups/{id}")]
        [Authorize(Roles = "SystemAdmin")]
        public async Task<ActionResult<MenuGroupDto>> GetMenuGroup(int id)
        {
            try
            {
                var menuGroup = await _dbContext.MenuGroups
                    .Include(g => g.MenuItems)
                    .FirstOrDefaultAsync(g => g.Id == id);

                if (menuGroup == null)
                {
                    return NotFound(new { message = "菜单分组不存在" });
                }

                var dto = new MenuGroupDto
                {
                    Id = menuGroup.Id,
                    GroupKey = menuGroup.GroupKey,
                    Title = menuGroup.Title,
                    IconName = menuGroup.IconName,
                    SortOrder = menuGroup.SortOrder,
                    IsEnabled = menuGroup.IsEnabled,
                    VisibleToSystemAdmin = menuGroup.VisibleToSystemAdmin,
                    VisibleToMerchantAdmin = menuGroup.VisibleToMerchantAdmin,
                    VisibleToUser = menuGroup.VisibleToUser,
                    CreatedAt = menuGroup.CreatedAt,
                    UpdatedAt = menuGroup.UpdatedAt,
                    MenuItems = menuGroup.MenuItems
                        .OrderBy(i => i.SortOrder)
                        .Select(i => new MenuItemDto
                        {
                            Id = i.Id,
                            MenuGroupId = i.MenuGroupId,
                            ItemKey = i.ItemKey,
                            Title = i.Title,
                            Href = i.Href,
                            IconName = i.IconName,
                            SortOrder = i.SortOrder,
                            IsEnabled = i.IsEnabled,
                            VisibleToSystemAdmin = i.VisibleToSystemAdmin,
                            VisibleToMerchantAdmin = i.VisibleToMerchantAdmin,
                            VisibleToUser = i.VisibleToUser,
                            IsExternal = i.IsExternal,
                            Target = i.Target,
                            CreatedAt = i.CreatedAt,
                            UpdatedAt = i.UpdatedAt
                        }).ToList()
                };

                return Ok(dto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取菜单分组详情时发生错误，ID: {Id}", id);
                return StatusCode(500, new { message = "服务器内部错误" });
            }
        }

        /// <summary>
        /// 创建菜单分组
        /// </summary>
        /// <param name="dto">创建菜单分组请求</param>
        /// <returns></returns>
        [HttpPost("groups")]
        [Authorize(Roles = "SystemAdmin")]
        public async Task<ActionResult<MenuGroupDto>> CreateMenuGroup([FromBody] CreateMenuGroupDto dto)
        {
            try
            {
                // 检查GroupKey是否已存在
                var existingGroup = await _dbContext.MenuGroups
                    .FirstOrDefaultAsync(g => g.GroupKey == dto.GroupKey);
                if (existingGroup != null)
                {
                    return BadRequest(new { message = "分组标识符已存在" });
                }

                var menuGroup = new MenuGroup
                {
                    GroupKey = dto.GroupKey,
                    Title = dto.Title,
                    IconName = dto.IconName,
                    SortOrder = dto.SortOrder,
                    IsEnabled = dto.IsEnabled,
                    VisibleToSystemAdmin = dto.VisibleToSystemAdmin,
                    VisibleToMerchantAdmin = dto.VisibleToMerchantAdmin,
                    VisibleToUser = dto.VisibleToUser
                };

                _dbContext.MenuGroups.Add(menuGroup);
                await _dbContext.SaveChangesAsync();

                var result = new MenuGroupDto
                {
                    Id = menuGroup.Id,
                    GroupKey = menuGroup.GroupKey,
                    Title = menuGroup.Title,
                    IconName = menuGroup.IconName,
                    SortOrder = menuGroup.SortOrder,
                    IsEnabled = menuGroup.IsEnabled,
                    VisibleToSystemAdmin = menuGroup.VisibleToSystemAdmin,
                    VisibleToMerchantAdmin = menuGroup.VisibleToMerchantAdmin,
                    VisibleToUser = menuGroup.VisibleToUser,
                    CreatedAt = menuGroup.CreatedAt,
                    UpdatedAt = menuGroup.UpdatedAt,
                    MenuItems = new List<MenuItemDto>()
                };

                return CreatedAtAction(nameof(GetMenuGroup), new { id = menuGroup.Id }, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建菜单分组时发生错误");
                return StatusCode(500, new { message = "服务器内部错误" });
            }
        }

        /// <summary>
        /// 更新菜单分组
        /// </summary>
        /// <param name="id">分组ID</param>
        /// <param name="dto">更新菜单分组请求</param>
        /// <returns></returns>
        [HttpPut("groups/{id}")]
        [Authorize(Roles = "SystemAdmin")]
        public async Task<ActionResult<MenuGroupDto>> UpdateMenuGroup(int id, [FromBody] UpdateMenuGroupDto dto)
        {
            try
            {
                var menuGroup = await _dbContext.MenuGroups.FindAsync(id);
                if (menuGroup == null)
                {
                    return NotFound(new { message = "菜单分组不存在" });
                }

                menuGroup.Title = dto.Title;
                menuGroup.IconName = dto.IconName;
                menuGroup.SortOrder = dto.SortOrder;
                menuGroup.IsEnabled = dto.IsEnabled;
                menuGroup.VisibleToSystemAdmin = dto.VisibleToSystemAdmin;
                menuGroup.VisibleToMerchantAdmin = dto.VisibleToMerchantAdmin;
                menuGroup.VisibleToUser = dto.VisibleToUser;
                menuGroup.UpdatedAt = DateTime.Now;

                await _dbContext.SaveChangesAsync();

                var result = new MenuGroupDto
                {
                    Id = menuGroup.Id,
                    GroupKey = menuGroup.GroupKey,
                    Title = menuGroup.Title,
                    IconName = menuGroup.IconName,
                    SortOrder = menuGroup.SortOrder,
                    IsEnabled = menuGroup.IsEnabled,
                    VisibleToSystemAdmin = menuGroup.VisibleToSystemAdmin,
                    VisibleToMerchantAdmin = menuGroup.VisibleToMerchantAdmin,
                    VisibleToUser = menuGroup.VisibleToUser,
                    CreatedAt = menuGroup.CreatedAt,
                    UpdatedAt = menuGroup.UpdatedAt,
                    MenuItems = new List<MenuItemDto>()
                };

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新菜单分组时发生错误，ID: {Id}", id);
                return StatusCode(500, new { message = "服务器内部错误" });
            }
        }

        /// <summary>
        /// 删除菜单分组
        /// </summary>
        /// <param name="id">分组ID</param>
        /// <returns></returns>
        [HttpDelete("groups/{id}")]
        [Authorize(Roles = "SystemAdmin")]
        public async Task<ActionResult> DeleteMenuGroup(int id)
        {
            try
            {
                var menuGroup = await _dbContext.MenuGroups
                    .Include(g => g.MenuItems)
                    .FirstOrDefaultAsync(g => g.Id == id);

                if (menuGroup == null)
                {
                    return NotFound(new { message = "菜单分组不存在" });
                }

                _dbContext.MenuGroups.Remove(menuGroup);
                await _dbContext.SaveChangesAsync();

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除菜单分组时发生错误，ID: {Id}", id);
                return StatusCode(500, new { message = "服务器内部错误" });
            }
        }

        /// <summary>
        /// 获取菜单项详情
        /// </summary>
        /// <param name="id">菜单项ID</param>
        /// <returns></returns>
        [HttpGet("items/{id}")]
        [Authorize(Roles = "SystemAdmin")]
        public async Task<ActionResult<MenuItemDto>> GetMenuItem(int id)
        {
            try
            {
                var menuItem = await _dbContext.MenuItems.FindAsync(id);
                if (menuItem == null)
                {
                    return NotFound(new { message = "菜单项不存在" });
                }

                var dto = new MenuItemDto
                {
                    Id = menuItem.Id,
                    MenuGroupId = menuItem.MenuGroupId,
                    ItemKey = menuItem.ItemKey,
                    Title = menuItem.Title,
                    Href = menuItem.Href,
                    IconName = menuItem.IconName,
                    SortOrder = menuItem.SortOrder,
                    IsEnabled = menuItem.IsEnabled,
                    VisibleToSystemAdmin = menuItem.VisibleToSystemAdmin,
                    VisibleToMerchantAdmin = menuItem.VisibleToMerchantAdmin,
                    VisibleToUser = menuItem.VisibleToUser,
                    IsExternal = menuItem.IsExternal,
                    Target = menuItem.Target,
                    CreatedAt = menuItem.CreatedAt,
                    UpdatedAt = menuItem.UpdatedAt
                };

                return Ok(dto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取菜单项详情时发生错误，ID: {Id}", id);
                return StatusCode(500, new { message = "服务器内部错误" });
            }
        }

        /// <summary>
        /// 创建菜单项
        /// </summary>
        /// <param name="dto">创建菜单项请求</param>
        /// <returns></returns>
        [HttpPost("items")]
        [Authorize(Roles = "SystemAdmin")]
        public async Task<ActionResult<MenuItemDto>> CreateMenuItem([FromBody] CreateMenuItemDto dto)
        {
            try
            {
                // 检查菜单分组是否存在
                var menuGroup = await _dbContext.MenuGroups.FindAsync(dto.MenuGroupId);
                if (menuGroup == null)
                {
                    return BadRequest(new { message = "菜单分组不存在" });
                }

                // 检查ItemKey是否在同一分组内已存在
                var existingItem = await _dbContext.MenuItems
                    .FirstOrDefaultAsync(i => i.MenuGroupId == dto.MenuGroupId && i.ItemKey == dto.ItemKey);
                if (existingItem != null)
                {
                    return BadRequest(new { message = "菜单项标识符在该分组内已存在" });
                }

                var menuItem = new MenuItem
                {
                    MenuGroupId = dto.MenuGroupId,
                    ItemKey = dto.ItemKey,
                    Title = dto.Title,
                    Href = dto.Href,
                    IconName = dto.IconName,
                    SortOrder = dto.SortOrder,
                    IsEnabled = dto.IsEnabled,
                    VisibleToSystemAdmin = dto.VisibleToSystemAdmin,
                    VisibleToMerchantAdmin = dto.VisibleToMerchantAdmin,
                    VisibleToUser = dto.VisibleToUser,
                    IsExternal = dto.IsExternal,
                    Target = dto.Target
                };

                _dbContext.MenuItems.Add(menuItem);
                await _dbContext.SaveChangesAsync();

                var result = new MenuItemDto
                {
                    Id = menuItem.Id,
                    MenuGroupId = menuItem.MenuGroupId,
                    ItemKey = menuItem.ItemKey,
                    Title = menuItem.Title,
                    Href = menuItem.Href,
                    IconName = menuItem.IconName,
                    SortOrder = menuItem.SortOrder,
                    IsEnabled = menuItem.IsEnabled,
                    VisibleToSystemAdmin = menuItem.VisibleToSystemAdmin,
                    VisibleToMerchantAdmin = menuItem.VisibleToMerchantAdmin,
                    VisibleToUser = menuItem.VisibleToUser,
                    IsExternal = menuItem.IsExternal,
                    Target = menuItem.Target,
                    CreatedAt = menuItem.CreatedAt,
                    UpdatedAt = menuItem.UpdatedAt
                };

                return CreatedAtAction(nameof(GetMenuItem), new { id = menuItem.Id }, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建菜单项时发生错误");
                return StatusCode(500, new { message = "服务器内部错误" });
            }
        }

        /// <summary>
        /// 更新菜单项
        /// </summary>
        /// <param name="id">菜单项ID</param>
        /// <param name="dto">更新菜单项请求</param>
        /// <returns></returns>
        [HttpPut("items/{id}")]
        [Authorize(Roles = "SystemAdmin")]
        public async Task<ActionResult<MenuItemDto>> UpdateMenuItem(int id, [FromBody] UpdateMenuItemDto dto)
        {
            try
            {
                var menuItem = await _dbContext.MenuItems.FindAsync(id);
                if (menuItem == null)
                {
                    return NotFound(new { message = "菜单项不存在" });
                }

                menuItem.Title = dto.Title;
                menuItem.Href = dto.Href;
                menuItem.IconName = dto.IconName;
                menuItem.SortOrder = dto.SortOrder;
                menuItem.IsEnabled = dto.IsEnabled;
                menuItem.VisibleToSystemAdmin = dto.VisibleToSystemAdmin;
                menuItem.VisibleToMerchantAdmin = dto.VisibleToMerchantAdmin;
                menuItem.VisibleToUser = dto.VisibleToUser;
                menuItem.IsExternal = dto.IsExternal;
                menuItem.Target = dto.Target;
                menuItem.UpdatedAt = DateTime.Now;

                await _dbContext.SaveChangesAsync();

                var result = new MenuItemDto
                {
                    Id = menuItem.Id,
                    MenuGroupId = menuItem.MenuGroupId,
                    ItemKey = menuItem.ItemKey,
                    Title = menuItem.Title,
                    Href = menuItem.Href,
                    IconName = menuItem.IconName,
                    SortOrder = menuItem.SortOrder,
                    IsEnabled = menuItem.IsEnabled,
                    VisibleToSystemAdmin = menuItem.VisibleToSystemAdmin,
                    VisibleToMerchantAdmin = menuItem.VisibleToMerchantAdmin,
                    VisibleToUser = menuItem.VisibleToUser,
                    IsExternal = menuItem.IsExternal,
                    Target = menuItem.Target,
                    CreatedAt = menuItem.CreatedAt,
                    UpdatedAt = menuItem.UpdatedAt
                };

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新菜单项时发生错误，ID: {Id}", id);
                return StatusCode(500, new { message = "服务器内部错误" });
            }
        }

        /// <summary>
        /// 删除菜单项
        /// </summary>
        /// <param name="id">菜单项ID</param>
        /// <returns></returns>
        [HttpDelete("items/{id}")]
        [Authorize(Roles = "SystemAdmin")]
        public async Task<ActionResult> DeleteMenuItem(int id)
        {
            try
            {
                var menuItem = await _dbContext.MenuItems.FindAsync(id);
                if (menuItem == null)
                {
                    return NotFound(new { message = "菜单项不存在" });
                }

                _dbContext.MenuItems.Remove(menuItem);
                await _dbContext.SaveChangesAsync();

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除菜单项时发生错误，ID: {Id}", id);
                return StatusCode(500, new { message = "服务器内部错误" });
            }
        }

        /// <summary>
        /// 初始化默认菜单数据
        /// </summary>
        /// <returns></returns>
        [HttpPost("initialize")]
        [Authorize(Roles = "SystemAdmin")]
        public async Task<ActionResult> InitializeMenus()
        {
            try
            {
                await _menuService.InitializeDefaultMenusAsync();
                return Ok(new { message = "菜单数据初始化完成" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "初始化菜单数据时发生错误");
                return StatusCode(500, new { message = "服务器内部错误" });
            }
        }
    }
}
