import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Tabs,
  Tab,
  Button,
  TextField,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Paper,
  Tooltip,
  Alert,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Switch,
  FormControlLabel
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon
} from '@mui/icons-material';
import DynamicFormGenerator from '../../components/DynamicFormGenerator';

// 自定义TabPanel组件
function TabPanel({ children, value, index, ...other }) {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`tabpanel-${index}`}
      aria-labelledby={`tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ py: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const LinePriceVersionEditForm = ({
  extraParams,
  cardDiscountInfo,
  onExtraParamsChange,
  onCardDiscountInfoChange,
  hideSubmitButton = false,
  onSubmit,
  extraParamsConfig = null,
  cardDiscountConfig = null
}) => {
  const [tabValue, setTabValue] = useState(0);

  // 初始化卡类折扣配置的默认值
  useEffect(() => {
    // 只有在卡类配置加载完成并且卡类配置数据为空时才执行
    if (!cardDiscountConfig || cardDiscountConfig.length === 0) {
      console.log('跳过卡类默认值初始化 - 字典配置未加载');
      return;
    }

    // 如果已经有卡类配置数据，则不需要初始化
    if (cardDiscountInfo && cardDiscountInfo.length > 0) {
      console.log('跳过卡类默认值初始化 - 已有配置数据');
      return;
    }

    console.log('初始化卡类折扣配置默认值');

    // 创建一个包含默认值的初始卡类配置
    const defaultCardConfig = {};
    cardDiscountConfig.forEach(config => {
      // 只处理非分隔符类型的字段
      if (config.controlType?.toLowerCase() !== 'section' &&
          config.controlType?.toLowerCase() !== 'divider' &&
          config.dictionaryCode) {
        // 如果配置有默认值，则添加到新卡类配置中
        if (config.dictionaryValue !== undefined) {
          defaultCardConfig[config.dictionaryCode] = config.dictionaryValue;
        }
      }
    });

    // 只有当默认配置有值时才添加
    if (Object.keys(defaultCardConfig).length > 0) {
      console.log('添加默认卡类配置:', defaultCardConfig);
      onCardDiscountInfoChange([defaultCardConfig]);
    }
  }, [cardDiscountConfig, cardDiscountInfo, onCardDiscountInfoChange]);

  // 默认的额外参数配置
  const defaultExtraParamsConfig = [

  ];

  // 使用传入的配置或默认配置
  const finalExtraParamsConfig = extraParamsConfig || defaultExtraParamsConfig;

  // Tab切换处理
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  // 处理额外参数变化
  const handleExtraParamsChange = (fieldName, value) => {
    const newExtraParams = {
      ...extraParams,
      [fieldName]: value
    };
    onExtraParamsChange(newExtraParams);
  };

  // 处理卡类信息变化
  const handleCardDiscountChange = (index, field, value) => {
    const newCardDiscountInfo = [...cardDiscountInfo];
    if (!newCardDiscountInfo[index]) {
      newCardDiscountInfo[index] = {};
    }
    newCardDiscountInfo[index] = {
      ...newCardDiscountInfo[index],
      [field]: value
    };
    onCardDiscountInfoChange(newCardDiscountInfo);
  };

  // 添加新卡类
  const handleAddCard = () => {
    // 创建包含默认值的新卡类配置
    const newCardConfig = {};

    // 从配置中提取默认值
    if (cardDiscountConfig && cardDiscountConfig.length > 0) {
      cardDiscountConfig.forEach(config => {
        // 只处理非分隔符类型的字段
        if (config.controlType?.toLowerCase() !== 'section' &&
            config.controlType?.toLowerCase() !== 'divider' &&
            config.dictionaryCode) {
          // 如果配置有默认值，则添加到新卡类配置中
          if (config.dictionaryValue !== undefined) {
            newCardConfig[config.dictionaryCode] = config.dictionaryValue;
          }
        }
      });
    } else {
      // 如果没有字典配置，使用默认的硬编码配置
      newCardConfig.cardType = '';
      newCardConfig.cardTypeName = '';
      newCardConfig.discount = 100;
      newCardConfig.voiceFile = '';
      newCardConfig.voiceText = '';
    }

    console.log('添加新卡类配置，带默认值:', newCardConfig);
    onCardDiscountInfoChange([...cardDiscountInfo, newCardConfig]);
  };

  // 删除卡类
  const handleDeleteCard = (index) => {
    const newCardDiscountInfo = cardDiscountInfo.filter((_, i) => i !== index);
    onCardDiscountInfoChange(newCardDiscountInfo);
  };

  // 渲染表格单元格
  const renderTableCell = (config, value, onChange) => {
    const { controlType, dictionaryCode, dictionaryLabel, controlConfig } = config;

    // 调试信息：查看控件配置
    if (import.meta.env.DEV && controlConfig) {
      console.log(`控件 ${dictionaryCode} (${controlType}) 配置:`, controlConfig);
    }

    switch (controlType?.toLowerCase()) {
      case 'textbox':
      case 'text':
        return (
          <TextField
            size="small"
            value={value || ''}
            onChange={(e) => onChange(dictionaryCode, e.target.value)}
            placeholder={dictionaryLabel}
            fullWidth
            variant="outlined"
            sx={{
              '& .MuiInputBase-input': {
                padding: '4px 6px',
                fontSize: '0.875rem'
              }
            }}
          />
        );

      case 'shorttext':
      case 'short_text':
        return (
          <TextField
            size="small"
            value={value || ''}
            onChange={(e) => onChange(dictionaryCode, e.target.value)}
            placeholder={dictionaryLabel}
            fullWidth
            variant="outlined"
            inputProps={{
              maxLength: 10, // 限制最大长度为10个字符
              style: { textAlign: 'center' } // 短文本居中显示
            }}
            sx={{
              '& .MuiInputBase-input': {
                padding: '4px 4px', // 更紧凑的内边距
                fontSize: '0.875rem'
              }
            }}
          />
        );

      case 'number':
        return (
          <TextField
            size="small"
            type="number"
            value={value || ''}
            onChange={(e) => onChange(dictionaryCode, e.target.value)}
            placeholder={dictionaryLabel}
            fullWidth
            variant="outlined"
            inputProps={{
              min: controlConfig?.min || 0,
              max: controlConfig?.max || 999999,
              step: controlConfig?.step || 1,
              style: { textAlign: 'center' } // 数字居中显示
            }}
            sx={{
              '& .MuiInputBase-input': {
                padding: '4px 6px', // 更紧凑的内边距
                fontSize: '0.875rem'
              }
            }}
          />
        );

      case 'radio':
        // Radio控件改为下拉选择
        const radioOptions = controlConfig?.options || [];
        return (
          <FormControl size="small" fullWidth>
            <Select
              value={value || ''}
              onChange={(e) => onChange(dictionaryCode, e.target.value)}
              displayEmpty
              sx={{
                '& .MuiSelect-select': {
                  padding: '4px 6px',
                  fontSize: '0.875rem'
                }
              }}
            >
              <MenuItem value="">
                <em>请选择</em>
              </MenuItem>
              {radioOptions.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        );

      case 'select':
      case 'dropdown':
        const selectOptions = controlConfig?.options || [];
        return (
          <FormControl size="small" fullWidth>
            <Select
              value={value || ''}
              onChange={(e) => onChange(dictionaryCode, e.target.value)}
              displayEmpty
              sx={{
                '& .MuiSelect-select': {
                  padding: '4px 6px',
                  fontSize: '0.875rem'
                }
              }}
            >
              <MenuItem value="">
                <em>请选择</em>
              </MenuItem>
              {selectOptions.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        );

      case 'switch':
      case 'boolean':
        return (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
            <Switch
              checked={Boolean(value)}
              onChange={(e) => onChange(dictionaryCode, e.target.checked)}
              size="small"
              color="primary"
            />
          </Box>
        );

      case 'checkbox':
        return (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
            <FormControlLabel
              control={
                <Switch
                  checked={Boolean(value)}
                  onChange={(e) => onChange(dictionaryCode, e.target.checked)}
                  size="small"
                  color="primary"
                />
              }
              label=""
              sx={{ m: 0 }}
            />
          </Box>
        );

      default:
        return (
          <TextField
            size="small"
            value={value || ''}
            onChange={(e) => onChange(dictionaryCode, e.target.value)}
            placeholder={dictionaryLabel}
            fullWidth
            variant="outlined"
          />
        );
    }
  };

  // 根据控件类型设置不同的列宽
  const getColumnWidth = (controlType) => {
    switch (controlType?.toLowerCase()) {
      case 'number':
        return { minWidth: 100, maxWidth: 120, width: 100 };
      case 'switch':
      case 'boolean':
        return { minWidth: 80, maxWidth: 100, width: 80 };
      case 'shorttext':
      case 'short_text':
        return { minWidth: 80, maxWidth: 100, width: 80 };
      case 'radio':
      case 'select':
        return { minWidth: 140, maxWidth: 180, width: 140 };
      case 'text':
      case 'textbox':
      default:
        return { minWidth: 160, maxWidth: 200, width: 160 };
    }
  };

  return (
    <Box>
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs value={tabValue} onChange={handleTabChange} aria-label="票价参数配置">
          <Tab label="额外参数配置" id="tab-0" aria-controls="tabpanel-0" />
          <Tab label="卡类折扣配置" id="tab-1" aria-controls="tabpanel-1" />
        </Tabs>
      </Box>

      <TabPanel value={tabValue} index={0}>
        <Typography variant="subtitle1" gutterBottom color="textSecondary">
          设置线路额外参数，如换乘时间、是否支持换乘等
        </Typography>
        <DynamicFormGenerator
          configs={finalExtraParamsConfig}
          values={extraParams}
          onChange={handleExtraParamsChange}
        />
      </TabPanel>

      <TabPanel value={tabValue} index={1}>
        <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="subtitle1" gutterBottom color="textSecondary">
            设置各卡类的折扣比例、播报语音等参数
          </Typography>
          <Button
            variant="outlined"
            startIcon={<AddIcon />}
            onClick={handleAddCard}
            size="small"
          >
            添加卡类
          </Button>
        </Box>

        {cardDiscountInfo && cardDiscountInfo.length > 0 && (
          <Box sx={{ mb: 2 }}>
            <Typography variant="body2" color="text.secondary">
              当前共有 {cardDiscountInfo.length} 个卡类配置
            </Typography>
          </Box>
        )}

        {cardDiscountConfig && cardDiscountConfig.length > 0 ? (
          // 使用动态配置的表格
          <TableContainer
            component={Paper}
            sx={{
              maxHeight: cardDiscountInfo && cardDiscountInfo.length > 8 ? 600 : 'auto',
              overflowY: 'auto',
              overflowX: 'auto',
              minHeight: cardDiscountInfo && cardDiscountInfo.length > 0 ? 200 : 'auto',
              '&::-webkit-scrollbar': {
                width: '8px',
                height: '8px'
              },
              '&::-webkit-scrollbar-track': {
                background: '#f1f1f1',
                borderRadius: '4px',
              },
              '&::-webkit-scrollbar-thumb': {
                background: '#c1c1c1',
                borderRadius: '4px',
              },
              '&::-webkit-scrollbar-thumb:hover': {
                background: '#a8a8a8',
              },
            }}
          >
            <Table stickyHeader size="small">
              <TableHead>
                <TableRow>
                  <TableCell sx={{
                    width: 50,
                    minWidth: 50,
                    maxWidth: 50,
                    fontWeight: 600,
                    textAlign: 'center',
                    padding: '8px 4px',
                    position: 'sticky',
                    top: 0,
                    backgroundColor: 'background.paper',
                    zIndex: 2
                  }}>序号</TableCell>
                  {cardDiscountConfig
                    .filter(config =>
                      config.controlType?.toLowerCase() !== 'section' &&
                      config.controlType?.toLowerCase() !== 'divider'
                    )
                    .map((config) => {
                      const columnStyle = getColumnWidth(config.controlType);
                      return (
                        <TableCell
                          key={config.dictionaryCode}
                          sx={{
                            ...columnStyle,
                            fontWeight: 600,
                            padding: '8px 8px',
                            position: 'sticky',
                            top: 0,
                            backgroundColor: 'background.paper',
                            zIndex: 2
                          }}
                        >
                          {config.dictionaryLabel || config.displayName}
                          {config.required && <span style={{ color: 'red' }}>*</span>}
                        </TableCell>
                      );
                    })
                  }
                  <TableCell sx={{
                    width: 80,
                    fontWeight: 600,
                    position: 'sticky',
                    top: 0,
                    backgroundColor: 'background.paper',
                    zIndex: 2
                  }}>操作</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {cardDiscountInfo && cardDiscountInfo.map((card, index) => (
                  <TableRow key={index} hover>
                    <TableCell sx={{
                      width: 50,
                      minWidth: 50,
                      maxWidth: 50,
                      textAlign: 'center',
                      fontWeight: 500,
                      padding: '6px 4px'
                    }}>
                      {index + 1}
                    </TableCell>
                    {cardDiscountConfig
                      .filter(config =>
                        config.controlType?.toLowerCase() !== 'section' &&
                        config.controlType?.toLowerCase() !== 'divider'
                      )
                      .map((config) => {
                        const columnStyle = getColumnWidth(config.controlType);
                        return (
                          <TableCell
                            key={config.dictionaryCode}
                            sx={{
                              ...columnStyle,
                              padding: '6px 8px'
                            }}
                          >
                            {renderTableCell(
                              config,
                              card[config.dictionaryCode],
                              (fieldName, value) => handleCardDiscountChange(index, fieldName, value)
                            )}
                          </TableCell>
                        );
                      })
                    }
                    <TableCell>
                      <Tooltip title="删除此卡类">
                        <IconButton
                          size="small"
                          onClick={() => handleDeleteCard(index)}
                          color="error"
                        >
                          <DeleteIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        ) : (
          // 使用默认的硬编码表格（向后兼容）
          <TableContainer
            component={Paper}
            sx={{
              maxHeight: cardDiscountInfo && cardDiscountInfo.length > 8 ? 600 : 'auto',
              overflowY: 'auto',
              minHeight: cardDiscountInfo && cardDiscountInfo.length > 0 ? 200 : 'auto',
              '&::-webkit-scrollbar': {
                width: '8px',
              },
              '&::-webkit-scrollbar-track': {
                background: '#f1f1f1',
                borderRadius: '4px',
              },
              '&::-webkit-scrollbar-thumb': {
                background: '#c1c1c1',
                borderRadius: '4px',
              },
              '&::-webkit-scrollbar-thumb:hover': {
                background: '#a8a8a8',
              },
            }}
          >
            <Table stickyHeader>
              <TableHead>
                <TableRow>
                  <TableCell sx={{
                    position: 'sticky',
                    top: 0,
                    backgroundColor: 'background.paper',
                    zIndex: 2,
                    fontWeight: 600
                  }}>卡类型</TableCell>
                  <TableCell sx={{
                    position: 'sticky',
                    top: 0,
                    backgroundColor: 'background.paper',
                    zIndex: 2,
                    fontWeight: 600
                  }}>卡类型名称</TableCell>
                  <TableCell sx={{
                    position: 'sticky',
                    top: 0,
                    backgroundColor: 'background.paper',
                    zIndex: 2,
                    fontWeight: 600
                  }}>折扣(%)</TableCell>
                  <TableCell sx={{
                    position: 'sticky',
                    top: 0,
                    backgroundColor: 'background.paper',
                    zIndex: 2,
                    fontWeight: 600
                  }}>语音文件</TableCell>
                  <TableCell sx={{
                    position: 'sticky',
                    top: 0,
                    backgroundColor: 'background.paper',
                    zIndex: 2,
                    fontWeight: 600
                  }}>语音文本</TableCell>
                  <TableCell sx={{
                    width: 80,
                    position: 'sticky',
                    top: 0,
                    backgroundColor: 'background.paper',
                    zIndex: 2,
                    fontWeight: 600
                  }}>操作</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {cardDiscountInfo && cardDiscountInfo.map((card, index) => (
                  <TableRow key={index}>
                    <TableCell>
                      <TextField
                        size="small"
                        value={card.cardType || ''}
                        onChange={(e) => handleCardDiscountChange(index, 'cardType', e.target.value)}
                        placeholder="如：1"
                      />
                    </TableCell>
                    <TableCell>
                      <TextField
                        size="small"
                        value={card.cardTypeName || ''}
                        onChange={(e) => handleCardDiscountChange(index, 'cardTypeName', e.target.value)}
                        placeholder="如：普通卡"
                      />
                    </TableCell>
                    <TableCell>
                      <TextField
                        size="small"
                        type="number"
                        value={card.discount || 100}
                        onChange={(e) => handleCardDiscountChange(index, 'discount', parseInt(e.target.value) || 100)}
                        inputProps={{ min: 0, max: 100 }}
                      />
                    </TableCell>
                    <TableCell>
                      <TextField
                        size="small"
                        value={card.voiceFile || ''}
                        onChange={(e) => handleCardDiscountChange(index, 'voiceFile', e.target.value)}
                        placeholder="语音文件路径"
                      />
                    </TableCell>
                    <TableCell>
                      <TextField
                        size="small"
                        value={card.voiceText || ''}
                        onChange={(e) => handleCardDiscountChange(index, 'voiceText', e.target.value)}
                        placeholder="语音文本"
                      />
                    </TableCell>
                    <TableCell>
                      <Tooltip title="删除此卡类">
                        <IconButton
                          size="small"
                          onClick={() => handleDeleteCard(index)}
                          color="error"
                        >
                          <DeleteIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        )}

        {cardDiscountInfo && cardDiscountInfo.length > 5 && (
          <Box sx={{ mt: 1, textAlign: 'center' }}>
            <Typography variant="caption" color="text.secondary">
              💡 提示：表格内容较多时可以滚动查看
            </Typography>
          </Box>
        )}

        {(!cardDiscountInfo || cardDiscountInfo.length === 0) && (
          <Alert severity="info" sx={{ mt: 2 }}>
            暂无卡类配置，请点击"添加卡类"按钮添加
          </Alert>
        )}
      </TabPanel>

      {!hideSubmitButton && onSubmit && (
        <Box sx={{ mt: 3, display: 'flex', justifyContent: 'center' }}>
          <Button
            variant="contained"
            onClick={onSubmit}
            size="large"
          >
            保存配置
          </Button>
        </Box>
      )}
    </Box>
  );
};

export default LinePriceVersionEditForm;
