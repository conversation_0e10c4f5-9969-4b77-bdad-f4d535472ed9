import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  TextField,
  Grid,
  Divider,
  Alert,
  Breadcrumbs,
  Link,
  MenuItem
} from '@mui/material';
import {
  Save as SaveIcon,
  Cancel as CancelIcon,
  ArrowBack as ArrowBackIcon
} from '@mui/icons-material';
import { Formik, Form, Field } from 'formik';
import * as Yup from 'yup';
import { vehicleAPI, merchantAPI } from '../../services/api';
import { parseErrorMessage } from '../../utils/errorHandler';
import { useSnackbar } from 'notistack';
import MerchantAutocomplete from '../../components/MerchantAutocomplete';

// 验证模式
const validationSchema = Yup.object({
  licensePlate: Yup.string()
    .required('车牌号不能为空')
    .max(20, '车牌号不能超过20个字符'),
  deviceNumber: Yup.string()
    .required('设备编号不能为空')
    .max(50, '设备编号不能超过50个字符'),
  merchantId: Yup.number()
    .required('请选择商户')
    .positive('请选择有效的商户'),
  vehicleType: Yup.string()
    .max(50, '车辆类型不能超过50个字符'),
  brand: Yup.string()
    .max(50, '品牌不能超过50个字符'),
  model: Yup.string()
    .max(50, '型号不能超过50个字符'),
  year: Yup.string()
    .test('year-range', '年份必须在1900到明年之间', function(value) {
      if (!value) return true; // 允许空值
      const year = parseInt(value);
      if (isNaN(year)) return false;
      const currentYear = new Date().getFullYear();
      return year >= 1900 && year <= currentYear + 1;
    }),
  color: Yup.string()
    .max(30, '颜色不能超过30个字符'),
  engineNumber: Yup.string()
    .max(50, '发动机号不能超过50个字符'),
  chassisNumber: Yup.string()
    .max(50, '车架号不能超过50个字符'),
  driverName: Yup.string()
    .max(50, '司机姓名不能超过50个字符'),
  driverPhone: Yup.string()
    .max(20, '司机电话不能超过20个字符'),
  driverLicense: Yup.string()
    .max(30, '驾驶证号不能超过30个字符'),
  insuranceCompany: Yup.string()
    .max(100, '保险公司不能超过100个字符'),
  insuranceNumber: Yup.string()
    .max(50, '保险单号不能超过50个字符'),
  maintenanceStatus: Yup.string()
    .max(20, '维护状态不能超过20个字符'),
  mileage: Yup.string()
    .test('mileage-valid', '里程数必须是有效的数字', function(value) {
      if (!value) return true; // 允许空值
      const num = parseFloat(value);
      return !isNaN(num) && num >= 0;
    }),
  fuelType: Yup.string()
    .max(20, '燃料类型不能超过20个字符'),
  remarks: Yup.string()
    .max(500, '备注不能超过500个字符')
});

const VehicleCreate = () => {
  const navigate = useNavigate();
  const { enqueueSnackbar } = useSnackbar();
  
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState(null);
  const [selectedMerchant, setSelectedMerchant] = useState(null);
  const [merchants, setMerchants] = useState([]);

  // 初始值
  const initialValues = {
    licensePlate: '',
    deviceNumber: '',
    merchantId: '',
    vehicleType: '',
    brand: '',
    model: '',
    year: '',
    color: '',
    engineNumber: '',
    chassisNumber: '',
    registrationDate: '', // 注册日期
    expiryDate: '', // 到期日期
    driverName: '',
    driverPhone: '',
    driverLicense: '',
    insuranceCompany: '',
    insuranceNumber: '',
    insuranceExpiryDate: '',
    maintenanceStatus: 'Normal', // 维护状态
    lastMaintenanceDate: '',
    nextMaintenanceDate: '',
    mileage: '', // 里程数
    fuelType: '', // 燃料类型
    remarks: ''
  };

  // 加载商户列表
  useEffect(() => {
    loadMerchants();
  }, []);

  const loadMerchants = async () => {
    try {
      const response = await merchantAPI.getMerchants({ pageSize: 100 });
      setMerchants(response.items || []);
    } catch (error) {
      console.error('加载商户列表失败:', error);
    }
  };

  // 保存车辆信息
  const handleSubmit = async (values) => {
    try {
      setSaving(true);
      setError(null);

      // 处理数据格式，映射前端字段名到后端字段名
      const submitData = {
        MerchantID: values.merchantId,
        DeviceNO: values.deviceNumber,
        LicensePlate: values.licensePlate,
        VehicleType: values.vehicleType,
        Brand: values.brand,
        Model: values.model,
        Color: values.color,
        VIN: values.chassisNumber, // 前端chassisNumber对应后端VIN
        EngineNumber: values.engineNumber,
        RegistrationDate: values.registrationDate || null, // 注册日期
        ExpiryDate: values.expiryDate || null, // 到期日期
        DriverName: values.driverName,
        DriverPhone: values.driverPhone,
        DriverLicense: values.driverLicense,
        InsuranceCompany: values.insuranceCompany,
        InsurancePolicyNumber: values.insuranceNumber, // 前端insuranceNumber对应后端InsurancePolicyNumber
        InsuranceExpiryDate: values.insuranceExpiryDate || null,
        MaintenanceStatus: values.maintenanceStatus || 'Normal', // 维护状态
        LastMaintenanceDate: values.lastMaintenanceDate || null,
        NextMaintenanceDate: values.nextMaintenanceDate || null,
        Mileage: values.mileage ? parseFloat(values.mileage) : null, // 里程数
        FuelType: values.fuelType, // 燃料类型
        Remark: values.remarks
      };

      console.log('提交数据:', submitData);

      await vehicleAPI.createVehicle(submitData);
      enqueueSnackbar('车辆创建成功', { variant: 'success' });
      navigate('/app/vehicles');
    } catch (error) {
      console.error('保存车辆信息失败:', error);
      setError(parseErrorMessage(error));
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    navigate('/app/vehicles');
  };

  return (
    <Box sx={{ p: 3 }}>
      {/* 面包屑导航 */}
      <Breadcrumbs sx={{ mb: 3 }}>
        <Link 
          color="inherit" 
          href="#" 
          onClick={(e) => { e.preventDefault(); navigate('/app/vehicles'); }}
        >
          车辆管理
        </Link>
        <Typography color="text.primary">
          新增车辆
        </Typography>
      </Breadcrumbs>

      {/* 错误提示 */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Card>
        <CardContent>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
            <Typography variant="h5" component="h1">
              新增车辆
            </Typography>
          </Box>

          <Formik
            initialValues={initialValues}
            validationSchema={validationSchema}
            onSubmit={handleSubmit}
          >
            {({ errors, touched, values, setFieldValue }) => (
              <Form>
                <Grid container spacing={3}>
                  {/* 基本信息 */}
                  <Grid item xs={12}>
                    <Typography variant="h6" gutterBottom>
                      基本信息
                    </Typography>
                    <Divider sx={{ mb: 2 }} />
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Field
                      as={TextField}
                      name="licensePlate"
                      label="车牌号"
                      fullWidth
                      required
                      error={touched.licensePlate && !!errors.licensePlate}
                      helperText={touched.licensePlate && errors.licensePlate}
                    />
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Field
                      as={TextField}
                      name="deviceNumber"
                      label="设备编号"
                      fullWidth
                      required
                      error={touched.deviceNumber && !!errors.deviceNumber}
                      helperText={touched.deviceNumber && errors.deviceNumber}
                    />
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <MerchantAutocomplete
                      value={selectedMerchant}
                      onChange={(event, newValue) => {
                        setSelectedMerchant(newValue);
                        setFieldValue('merchantId', newValue ? newValue.merchantID : '');
                      }}
                      merchants={merchants}
                      error={touched.merchantId && !!errors.merchantId}
                      helperText={touched.merchantId && errors.merchantId}
                      required
                    />
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Field
                      as={TextField}
                      name="vehicleType"
                      label="车辆类型"
                      fullWidth
                      error={touched.vehicleType && !!errors.vehicleType}
                      helperText={touched.vehicleType && errors.vehicleType}
                    />
                  </Grid>

                  <Grid item xs={12} md={4}>
                    <Field
                      as={TextField}
                      name="brand"
                      label="品牌"
                      fullWidth
                      error={touched.brand && !!errors.brand}
                      helperText={touched.brand && errors.brand}
                    />
                  </Grid>

                  <Grid item xs={12} md={4}>
                    <Field
                      as={TextField}
                      name="model"
                      label="型号"
                      fullWidth
                      error={touched.model && !!errors.model}
                      helperText={touched.model && errors.model}
                    />
                  </Grid>

                  <Grid item xs={12} md={4}>
                    <Field
                      as={TextField}
                      name="year"
                      label="年份"
                      type="number"
                      fullWidth
                      error={touched.year && !!errors.year}
                      helperText={touched.year && errors.year}
                    />
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Field
                      as={TextField}
                      name="color"
                      label="颜色"
                      fullWidth
                      error={touched.color && !!errors.color}
                      helperText={touched.color && errors.color}
                    />
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Field
                      as={TextField}
                      name="engineNumber"
                      label="发动机号"
                      fullWidth
                      error={touched.engineNumber && !!errors.engineNumber}
                      helperText={touched.engineNumber && errors.engineNumber}
                    />
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Field
                      as={TextField}
                      name="chassisNumber"
                      label="车架号"
                      fullWidth
                      error={touched.chassisNumber && !!errors.chassisNumber}
                      helperText={touched.chassisNumber && errors.chassisNumber}
                    />
                  </Grid>

                  {/* 车辆证件信息 */}
                  <Grid item xs={12}>
                    <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
                      车辆证件信息
                    </Typography>
                    <Divider sx={{ mb: 2 }} />
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Field
                      as={TextField}
                      name="registrationDate"
                      label="注册日期"
                      type="date"
                      fullWidth
                      InputLabelProps={{ shrink: true }}
                      error={touched.registrationDate && !!errors.registrationDate}
                      helperText={touched.registrationDate && errors.registrationDate}
                    />
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Field
                      as={TextField}
                      name="expiryDate"
                      label="到期日期"
                      type="date"
                      fullWidth
                      InputLabelProps={{ shrink: true }}
                      error={touched.expiryDate && !!errors.expiryDate}
                      helperText={touched.expiryDate && errors.expiryDate}
                    />
                  </Grid>

                  {/* 司机信息 */}
                  <Grid item xs={12}>
                    <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
                      司机信息
                    </Typography>
                    <Divider sx={{ mb: 2 }} />
                  </Grid>

                  <Grid item xs={12} md={4}>
                    <Field
                      as={TextField}
                      name="driverName"
                      label="司机姓名"
                      fullWidth
                      error={touched.driverName && !!errors.driverName}
                      helperText={touched.driverName && errors.driverName}
                    />
                  </Grid>

                  <Grid item xs={12} md={4}>
                    <Field
                      as={TextField}
                      name="driverPhone"
                      label="司机电话"
                      fullWidth
                      error={touched.driverPhone && !!errors.driverPhone}
                      helperText={touched.driverPhone && errors.driverPhone}
                    />
                  </Grid>

                  <Grid item xs={12} md={4}>
                    <Field
                      as={TextField}
                      name="driverLicense"
                      label="驾驶证号"
                      fullWidth
                      error={touched.driverLicense && !!errors.driverLicense}
                      helperText={touched.driverLicense && errors.driverLicense}
                    />
                  </Grid>

                  {/* 保险信息 */}
                  <Grid item xs={12}>
                    <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
                      保险信息
                    </Typography>
                    <Divider sx={{ mb: 2 }} />
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Field
                      as={TextField}
                      name="insuranceCompany"
                      label="保险公司"
                      fullWidth
                      error={touched.insuranceCompany && !!errors.insuranceCompany}
                      helperText={touched.insuranceCompany && errors.insuranceCompany}
                    />
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Field
                      as={TextField}
                      name="insuranceNumber"
                      label="保险单号"
                      fullWidth
                      error={touched.insuranceNumber && !!errors.insuranceNumber}
                      helperText={touched.insuranceNumber && errors.insuranceNumber}
                    />
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Field
                      as={TextField}
                      name="insuranceExpiryDate"
                      label="保险到期日期"
                      type="date"
                      fullWidth
                      InputLabelProps={{ shrink: true }}
                      error={touched.insuranceExpiryDate && !!errors.insuranceExpiryDate}
                      helperText={touched.insuranceExpiryDate && errors.insuranceExpiryDate}
                    />
                  </Grid>

                  {/* 维护信息 */}
                  <Grid item xs={12}>
                    <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
                      维护信息
                    </Typography>
                    <Divider sx={{ mb: 2 }} />
                  </Grid>

                  <Grid item xs={12} md={4}>
                    <Field
                      as={TextField}
                      name="maintenanceStatus"
                      label="维护状态"
                      select
                      fullWidth
                      error={touched.maintenanceStatus && !!errors.maintenanceStatus}
                      helperText={touched.maintenanceStatus && errors.maintenanceStatus}
                    >
                      <MenuItem value="Normal">正常</MenuItem>
                      <MenuItem value="Maintenance">维护中</MenuItem>
                      <MenuItem value="Warning">警告</MenuItem>
                      <MenuItem value="Expired">过期</MenuItem>
                    </Field>
                  </Grid>

                  <Grid item xs={12} md={4}>
                    <Field
                      as={TextField}
                      name="mileage"
                      label="里程数(公里)"
                      type="number"
                      fullWidth
                      error={touched.mileage && !!errors.mileage}
                      helperText={touched.mileage && errors.mileage}
                    />
                  </Grid>

                  <Grid item xs={12} md={4}>
                    <Field
                      as={TextField}
                      name="fuelType"
                      label="燃料类型"
                      select
                      fullWidth
                      error={touched.fuelType && !!errors.fuelType}
                      helperText={touched.fuelType && errors.fuelType}
                    >
                      <MenuItem value="">请选择</MenuItem>
                      <MenuItem value="Gasoline">汽油</MenuItem>
                      <MenuItem value="Diesel">柴油</MenuItem>
                      <MenuItem value="Electric">电动</MenuItem>
                      <MenuItem value="Hybrid">混合动力</MenuItem>
                      <MenuItem value="CNG">天然气</MenuItem>
                      <MenuItem value="LPG">液化石油气</MenuItem>
                    </Field>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Field
                      as={TextField}
                      name="lastMaintenanceDate"
                      label="上次维护日期"
                      type="date"
                      fullWidth
                      InputLabelProps={{ shrink: true }}
                      error={touched.lastMaintenanceDate && !!errors.lastMaintenanceDate}
                      helperText={touched.lastMaintenanceDate && errors.lastMaintenanceDate}
                    />
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Field
                      as={TextField}
                      name="nextMaintenanceDate"
                      label="下次维护日期"
                      type="date"
                      fullWidth
                      InputLabelProps={{ shrink: true }}
                      error={touched.nextMaintenanceDate && !!errors.nextMaintenanceDate}
                      helperText={touched.nextMaintenanceDate && errors.nextMaintenanceDate}
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <Field
                      as={TextField}
                      name="remarks"
                      label="备注"
                      fullWidth
                      multiline
                      rows={3}
                      error={touched.remarks && !!errors.remarks}
                      helperText={touched.remarks && errors.remarks}
                    />
                  </Grid>

                  {/* 操作按钮 */}
                  <Grid item xs={12}>
                    <Box display="flex" gap={2} justifyContent="flex-end" mt={3}>
                      <Button
                        variant="outlined"
                        startIcon={<CancelIcon />}
                        onClick={handleCancel}
                        disabled={saving}
                      >
                        取消
                      </Button>
                      <Button
                        type="submit"
                        variant="contained"
                        startIcon={<SaveIcon />}
                        disabled={saving}
                      >
                        {saving ? '保存中...' : '保存'}
                      </Button>
                    </Box>
                  </Grid>
                </Grid>
              </Form>
            )}
          </Formik>
        </CardContent>
      </Card>
    </Box>
  );
};

export default VehicleCreate;
