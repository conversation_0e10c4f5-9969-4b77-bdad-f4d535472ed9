# 需求
需要添加一个终端日志记录的查询页面，展示以下字段：商户号，商户名称，记录类型，设置方式，卡号，设备序列号，设备编号，线路号，票价，司机卡号，记录时间，上传时间

可按商户，记录类型，卡号，设备序列号，设备编号，线路号，以及记录时间区间 这些条件做筛选

这个页面添加到终端管理菜单组下

记录类型下面几种，有个映射关系，记录类型是整形：
10 //司机卡
12 //线路设置
16 //设备设置
19 //发车卡
20 //到站卡

设置方式有下面几种
0x01 //M1卡
0x02 //CPU卡
0x0B //交通卡
0xA0 //无线
0xA1 //调度


终端记录表结构描述：
```sql
create table TerminalLogs
(
    ID int auto_increment primary key,
    MerchantID varchar(8) null,
    LogType int null,
    SetMethod int null,
    CardNO varchar(20) null,
    MachineID varchar(20) null,
    MachineNO varchar(20) null,
    LineNO varchar(20) null,
    Price int null,
    DriverCardNO varchar(20) null,
    LogTime datetime null,
    UploadTime datetime null
);
```


