# 权限检查逻辑说明

## 🎯 核心原则：最小权限原则

**只有明确配置为允许(true)的角色才有权限，其他所有情况都无权限。**

## 🔍 权限检查流程

### 1. 功能全局开关检查
```csharp
if (!featureConfig.IsGloballyEnabled)
{
    return false; // 功能完全关闭，直接拒绝
}
```

### 2. 权限控制检查
如果功能启用了权限控制，则进入权限验证流程。

### 3. 角色权限验证（核心逻辑）
```csharp
// 检查用户的任一角色是否有明确的允许权限
foreach (var role in roles)
{
    var rolePermission = rolePermissions.FirstOrDefault(rp => rp.RoleName == role);
    
    if (rolePermission?.IsEnabled == true)  // 只有明确为true才允许
    {
        return true; // 找到明确允许的权限，立即返回true
    }
}

// 其他所有情况都拒绝访问
return false;
```

## 📊 权限状态对应表

| 数据库值 | 前端显示 | 权限结果 | 说明 |
|---------|---------|---------|------|
| `true` | 开启 ✅ | **允许** | 明确配置为允许 |
| `false` | 关闭 ❌ | **拒绝** | 明确配置为禁止 |
| `null` | 关闭 ❌ | **拒绝** | 未配置，默认禁止 |
| 无记录 | 关闭 ❌ | **拒绝** | 未配置，默认禁止 |

## 🔐 权限验证场景

### 场景1：完全未配置权限
- **数据库状态**: 无任何角色权限记录
- **权限结果**: ❌ 拒绝访问
- **原因**: 没有明确的允许权限

### 场景2：部分角色有权限
- **数据库状态**: SystemAdmin=true, MerchantAdmin=null, User=false
- **权限结果**: 
  - SystemAdmin用户: ✅ 允许访问
  - MerchantAdmin用户: ❌ 拒绝访问
  - User用户: ❌ 拒绝访问

### 场景3：所有角色都禁止
- **数据库状态**: SystemAdmin=false, MerchantAdmin=false, User=false
- **权限结果**: ❌ 所有用户都拒绝访问
- **原因**: 没有任何明确的允许权限

### 场景4：混合角色用户
- **用户角色**: [SystemAdmin, MerchantAdmin]
- **权限配置**: SystemAdmin=false, MerchantAdmin=true
- **权限结果**: ✅ 允许访问
- **原因**: MerchantAdmin角色有明确的允许权限

## 🛡️ 安全特性

### 1. 默认拒绝
- 未配置的权限默认为拒绝
- 确保系统安全性

### 2. 明确授权
- 只有明确设置为`true`的权限才生效
- 避免意外的权限泄露

### 3. 任一角色原则
- 用户只要有任一角色具有明确允许权限即可访问
- 支持多角色用户的灵活权限管理

## 💡 实现优势

### 1. 逻辑清晰
- 简单的二元判断：有明确允许权限 vs 没有明确允许权限
- 易于理解和维护

### 2. 安全可靠
- 遵循最小权限原则
- 默认拒绝，明确授权

### 3. 性能优化
- 一旦找到允许权限立即返回
- 避免不必要的权限检查

## 🔧 前端对应

前端的Switch组件状态：
- **开启**: 表示该角色有明确的允许权限 (`IsEnabled = true`)
- **关闭**: 表示该角色没有明确的允许权限 (`IsEnabled = null/false`)

这样的设计确保了前后端逻辑的一致性和用户体验的直观性。
