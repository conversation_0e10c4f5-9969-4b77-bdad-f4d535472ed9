﻿# SlzrCrossGate

## git-svn 管理办法

步骤包括：

1. 在SVN服务器上创建新仓库，并建立trunk、branches、tags。
2. 在本地Git项目中配置git-svn，指向新SVN仓库的URL。
3. 使用git svn init和git svn fetch来拉取SVN结构（虽然初始为空）。
4. 将Git的提交逐个应用到SVN，可能需要rebase。
5. 使用git svn dcommit推送所有提交到SVN。

过程中需要注意处理可能的冲突，以及确保SVN的目录结构正确。此外，如果Git项目有分支，可能需要额外处理，但用户可能只需要迁移主分支到SVN的trunk。

总结，结合搜索结果，步骤大致是：创建SVN仓库结构，配置git-svn，迁移Git历史到SVN，处理作者映射，解决冲突，最后定期同步。




以下是针对**已有Git项目**且需要**新建SVN仓库**的场景，使用`git-svn`工具的详细操作流程：

---

### 一、初始化SVN仓库
1. **创建SVN仓库结构**  
   在SVN服务器上新建仓库，并按照标准目录结构初始化（若未自动生成）：
   ```bash
   svn mkdir http://svn-server/project -m "Create repo"  # 创建仓库根目录
   svn mkdir http://svn-server/project/trunk -m "Add trunk"  # 主开发目录
   svn mkdir http://svn-server/project/branches -m "Add branches"  # 分支目录
   svn mkdir http://svn-server/project/tags -m "Add tags"  # 标签目录
   ```
   这是SVN的标准布局，后续`git-svn`会依赖此结构。

2. **本地生成SVN工作副本**  
   若需本地验证，可先检出空仓库：
   ```bash
   svn checkout http://svn-server/project/trunk svn_project
   ```

---

### 二、配置Git项目与SVN仓库关联
1. **进入现有Git项目目录**  
   ```bash
   cd my_git_project
   ```

2. **初始化git-svn追踪**  
   关联本地Git仓库与远程SVN仓库：
   ```bash
   git svn init http://svn-server/project --stdlayout  # --stdlayout表示识别trunk/branches/tags结构
   ```

3. **设置作者映射文件**  
   创建`authors.txt`文件，将Git提交者映射为SVN用户格式（例如 `johndoe = John Doe <<EMAIL>>`），避免提交时因作者格式不兼容报错：
   ```bash
   git config svn.authorsfile /path/to/authors.txt
   ```

---

### 三、迁移Git历史到SVN仓库
1. **拉取SVN初始状态**  
   执行以下命令拉取SVN仓库当前内容（此时为空）：
   ```bash
   git svn fetch
   ```

2. **将Git提交迁移到SVN**  
   通过`rebase`将本地Git提交应用到SVN分支：
   ```bash
   git rebase --onto origin/trunk --root master  # 将master分支所有提交迁移到SVN的trunk，可通过`git branch -a`查看SVN分支
   ```
   • 若遇到冲突，需手动解决后执行`git rebase --continue`  
   • 若Git有多个分支，需逐个分支迁移（建议先合并到主分支）

3. **推送提交到SVN服务器**  
   将所有迁移后的提交推送到SVN：
   ```bash
   git svn dcommit  # 将Git提交逐条转换为SVN提交
   ```

---

### 四、日常开发流程（Git与SVN同步）
1. **从SVN拉取更新**  
   其他成员若通过SVN提交代码，需先同步到本地Git：
   ```bash
   git svn rebase  # 相当于git pull + SVN格式转换
   ```

2. **本地Git提交**  
   正常使用Git功能开发：
   ```bash
   git checkout -b feature_x  # 创建分支
   git add .
   git commit -m "实现新功能"
   ```

3. **推送到SVN仓库**  
   开发完成后合并到主分支并推送：
   ```bash
   git checkout master
   git merge feature_x
   git svn dcommit  # 推送至SVN
   ```

---

### 五、注意事项
1. **避免混合使用Git与原生SVN命令**  
   直接操作SVN工作副本可能导致Git仓库状态不一致，所有操作应通过`git-svn`完成。

2. **分支与标签处理**  
   • 创建分支：`git svn branch new_feature`（在SVN的branches目录生成）  
   • 创建标签：`git svn tag v1.0 -m "Release 1.0"`（在SVN的tags目录生成）

3. **大文件处理**  
   SVN对二进制文件差异处理较弱，建议通过`.gitignore`过滤不必要的文件。

---

### 六、扩展场景
若需将现有Git分支完整迁移到SVN分支，可使用：
```bash
git svn branch legacy_code  # 在SVN创建分支
git checkout -b svn_legacy remotes/legacy_code  # 本地关联分支
git merge master  # 合并代码
git svn dcommit  # 推送
```

通过以上步骤，你可以在保留Git灵活性的同时，无缝对接公司要求的SVN工作流。




cd ClientApp
npm run build
cp -r dist/* ../wwwroot/
