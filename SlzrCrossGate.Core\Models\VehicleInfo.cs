using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace SlzrCrossGate.Core.Models
{
    /// <summary>
    /// 车辆信息表
    /// </summary>
    public class VehicleInfo : ITenantEntity
    {
        [Key]
        public int ID { get; set; }
        
        [Required]
        [MaxLength(8)]
        public required string MerchantID { get; set; }
        
        [Required]
        [MaxLength(8)]
        public required string DeviceNO { get; set; }
        
        [Required]
        [MaxLength(20)]
        public required string LicensePlate { get; set; }
        
        [MaxLength(50)]
        public string? VehicleType { get; set; }
        
        [MaxLength(50)]
        public string? Brand { get; set; }
        
        [MaxLength(50)]
        public string? Model { get; set; }
        
        [MaxLength(30)]
        public string? Color { get; set; }
        
        [MaxLength(50)]
        public string? VIN { get; set; }
        
        [MaxLength(50)]
        public string? EngineNumber { get; set; }
        
        public DateTime? RegistrationDate { get; set; }
        public DateTime? ExpiryDate { get; set; }
        
        [MaxLength(50)]
        public string? DriverName { get; set; }
        
        [MaxLength(20)]
        public string? DriverPhone { get; set; }
        
        [MaxLength(30)]
        public string? DriverLicense { get; set; }
        
        [MaxLength(100)]
        public string? InsuranceCompany { get; set; }
        
        [MaxLength(50)]
        public string? InsurancePolicyNumber { get; set; }
        
        public DateTime? InsuranceExpiryDate { get; set; }
        
        [MaxLength(20)]
        public string MaintenanceStatus { get; set; } = "Normal";
        
        public DateTime? LastMaintenanceDate { get; set; }
        public DateTime? NextMaintenanceDate { get; set; }
        
        [Column(TypeName = "decimal(10,2)")]
        public decimal? Mileage { get; set; }
        
        [MaxLength(20)]
        public string? FuelType { get; set; }
        
        public string? Remark { get; set; }
        
        public bool IsActive { get; set; } = true;
        public DateTime CreateTime { get; set; } = DateTime.Now;
        public DateTime? UpdateTime { get; set; }
        
        [MaxLength(50)]
        public string? Creator { get; set; }
        
        [MaxLength(50)]
        public string? Updater { get; set; }
        
        // 导航属性
        public virtual Merchant? Merchant { get; set; }
    }
}
