{"version": "1.0.0", "lastUpdated": "2025-07-04T14:36:07.198Z", "description": "终端列表字段配置文件 - 支持动态添加和修改字段", "fieldCategories": {"basic": {"name": "基础信息", "description": "终端的基本属性字段", "fields": {"merchantName": {"key": "merchantName", "displayName": "商户", "dataPath": "merchantName", "sortable": true, "width": 150, "hideOn": ["xs"], "type": "text", "enabled": true, "order": 1, "tooltip": {"dataPath": "merchantID", "template": "商户ID: {tooltipValue}"}}, "machineID": {"key": "machineID", "displayName": "出厂序列号", "dataPath": "machineID", "sortable": true, "width": 120, "hideOn": ["xs", "sm"], "type": "text", "enabled": true, "order": 2}, "deviceNO": {"key": "deviceNO", "displayName": "设备编号", "dataPath": "deviceNO", "sortable": true, "width": 120, "hideOn": [], "type": "text", "enabled": true, "order": 3, "tooltip": "设备编号: {deviceNO} | 线路: {lineNO}"}, "lineNO": {"key": "lineNO", "displayName": "线路编号", "dataPath": "lineNO", "sortable": true, "width": 100, "hideOn": ["xs", "sm"], "type": "text", "enabled": true, "order": 4}, "terminalType": {"key": "terminalType", "displayName": "终端类型", "dataPath": "terminalType", "sortable": true, "width": 100, "hideOn": ["xs", "sm"], "type": "text", "enabled": true, "order": 5}, "createTime": {"key": "createTime", "displayName": "注册日期", "dataPath": "createTime", "sortable": true, "width": 120, "hideOn": ["xs", "sm"], "type": "date", "enabled": true, "order": 6}, "lastActiveTime": {"key": "lastActiveTime", "displayName": "最后活跃时间", "dataPath": "status.lastActiveTime", "sortable": true, "width": 180, "hideOn": ["xs"], "type": "datetime", "enabled": true, "order": 7}, "status": {"key": "status", "displayName": "状态", "dataPath": "status", "sortable": true, "width": 120, "hideOn": [], "type": "status", "enabled": true, "order": 8}, "offlineDuration": {"key": "offlineDuration", "displayName": "离线时长", "dataPath": "status.lastActiveTime", "sortable": false, "width": 120, "hideOn": ["xs", "sm"], "type": "offlineDuration", "enabled": false, "order": 8.5, "tooltip": "终端离线时长，仅在终端离线时显示"}}}, "fileVersions": {"name": "文件版本", "description": "终端上的各种文件版本信息", "fields": {"appVersion": {"key": "appVersion", "displayName": "APP版本", "dataPath": "status.fileVersionMetadata", "sortable": false, "width": 150, "hideOn": ["xs", "sm"], "type": "fileVersion", "enabled": true, "order": 9, "config": {"versionKeys": ["PRO{terminalType}", "APK{terminalType}"], "showVersionKey": true}, "tooltip": "终端当前运行的APP版本信息"}, "lineVersion": {"key": "lineVersion", "displayName": "票价版本", "dataPath": "status.fileVersionMetadata", "sortable": false, "width": 150, "hideOn": ["xs", "sm"], "type": "fileVersion", "enabled": true, "order": 10, "config": {"versionKeys": ["PZB{lineNO}", "PRI{lineNO}", "PRF{lineNO}", "PRG{lineNO}"], "showVersionKey": true}}, "blacklistVersion": {"key": "blacklistVersion", "displayName": "黑名单版本", "dataPath": "status.fileVersionMetadata.BLKBUS.current", "sortable": false, "width": 120, "hideOn": ["xs", "sm"], "type": "text", "enabled": true, "order": 11}, "allVersions": {"key": "allVersions", "displayName": "所有版本", "dataPath": "status.fileVersionMetadata", "sortable": false, "width": 200, "hideOn": ["xs", "sm", "md"], "type": "keyValue", "enabled": false, "order": 12, "tooltip": "显示终端上所有文件版本信息"}}}, "properties": {"name": "设备属性", "description": "从propertyMetadata中提取的设备属性信息", "fields": {"psamno": {"key": "psamno", "displayName": "PSAM卡号", "dataPath": "status.propertyMetadata.0C", "sortable": true, "width": 100, "hideOn": ["xs", "sm"], "type": "percentage", "enabled": true, "order": 13, "tooltip": "PSAM卡号"}, "IMMEI": {"key": "IMMEI", "displayName": "流量卡SIM号", "dataPath": "status.propertyMetadata.0B", "sortable": true, "width": 100, "hideOn": ["xs", "sm"], "type": "percentage", "enabled": false, "order": 14, "tooltip": "流量卡SIM号"}}}}, "renderTypes": {"text": {"description": "普通文本显示"}, "date": {"description": "日期格式显示"}, "datetime": {"description": "日期时间格式显示"}, "status": {"description": "状态芯片显示"}, "offlineDuration": {"description": "离线时长显示，仅在终端离线时显示"}, "fileVersion": {"description": "文件版本信息显示，支持动态键名"}, "keyValue": {"description": "键值对显示，用于显示对象的键和值"}, "percentage": {"description": "百分比显示，带颜色状态"}, "signal": {"description": "信号强度显示，单位dBm"}, "temperature": {"description": "温度显示，单位°C"}, "networkType": {"description": "网络类型显示"}}, "tooltipConfiguration": {"description": "Tooltip配置说明", "staticTooltip": {"description": "静态文本tooltip", "example": "tooltip: \"这是一个静态提示文本\"", "supportedVariables": ["{merchantID} - 商户ID", "{merchantName} - 商户名称", "{deviceNO} - 设备编号", "{lineNO} - 线路号", "{terminalType} - 终端类型", "{serialNO} - 序列号", "以及任意终端数据字段路径，如 {status.lastActiveTime}"]}, "dynamicTooltip": {"description": "动态数据tooltip", "example": {"tooltip": {"dataPath": "merchantID", "template": "商户ID: {tooltipValue}"}}, "properties": {"dataPath": "从终端数据中获取值的路径", "template": "显示模板，使用 {tooltipValue} 表示获取的值"}}}, "defaultSettings": {"enabledFields": ["merchantName", "machineID", "deviceNO", "lineNO", "terminalType", "createTime", "lastActiveTime", "status", "appVersion", "lineVersion", "blacklistVersion"], "columnSettings": {"autoWidth": false, "stickyHeader": true, "stickyActions": true}}}