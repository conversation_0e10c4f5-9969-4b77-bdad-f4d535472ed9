using Microsoft.EntityFrameworkCore;
using SlzrCrossGate.CustomerZhuhaitong.Data;
using SlzrCrossGate.CustomerZhuhaitong.Models;
using Microsoft.Extensions.Configuration;

namespace SlzrCrossGate.CustomerZhuhaitong.Services
{
    /// <summary>
    /// FTP文件统计服务
    /// </summary>
    public class FtpFileStatsService
    {
        private readonly CustomerDataContext _context;
        private readonly ILogger<FtpFileStatsService> _logger;
        private readonly IConfiguration _configuration;

        public FtpFileStatsService(CustomerDataContext context, ILogger<FtpFileStatsService> logger, IConfiguration configuration)
        {
            _context = context;
            _logger = logger;
            _configuration = configuration;
        }

        /// <summary>
        /// 获取指定日期的FTP文件上传统计
        /// </summary>
        public async Task<FtpUploadStatsResponseDto> GetFtpUploadStatsAsync(DateTime queryDate)
        {
            try
            {
                var ftpServers = await _context.FtpUploadServers
                    .Where(f => f.IsEnable == 1)
                    .OrderBy(f => f.UpOrder)
                    .ToListAsync();

                var uploadTargets = new List<FtpUploadStatsDto>();

                foreach (var server in ftpServers)
                {
                    var stats = new FtpUploadStatsDto
                    {
                        UploadTarget = server.ServerDesc ?? "",
                        UpOrder = server.UpOrder ?? 0,
                        OriginPath312 = server.OriginPath312 ?? "",
                        LocalBakPath312 = server.LocalBakPath312 ?? "",
                        OriginPath315 = server.OriginPath315 ?? "",
                        LocalBakPath315 = server.LocalBakPath315 ?? ""
                    };

                    // 统计312文件待上传数
                    if (!string.IsNullOrEmpty(server.OriginPath312))
                    {
                        stats.File312PendingCount = await CountLocalFilesAsync(server.OriginPath312, "*.312");
                    }

                    // 统计315文件待上传数
                    if (!string.IsNullOrEmpty(server.OriginPath315))
                    {
                        stats.File315PendingCount = await CountLocalFilesAsync(server.OriginPath315, "*.315");
                    }

                    uploadTargets.Add(stats);
                }

                // 单独统计按日期的上传成功数（与上传目标无关）
                var file312SuccessCount = await CountLocalUploadedFilesAsync("312", queryDate);
                var file315SuccessCount = await CountLocalUploadedFilesAsync("315", queryDate);

                return new FtpUploadStatsResponseDto
                {
                    QueryDate = queryDate,
                    UploadTargets = uploadTargets,
                    File312SuccessCount = file312SuccessCount,
                    File315SuccessCount = file315SuccessCount
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取FTP文件上传统计失败，查询日期: {QueryDate}", queryDate);
                throw;
            }
        }

        /// <summary>
        /// 统计本地目录中的文件数量
        /// </summary>
        private async Task<int> CountLocalFilesAsync(string relativePath, string searchPattern)
        {
            return await Task.Run(() =>
            {
                try
                {
                    // 获取根目录配置
                    var rootPath = _configuration["FileStats:RootPath"] ?? "/app/data";

                    // 构建完整路径
                    var fullPath = Path.Combine(rootPath, relativePath.TrimStart('/'));

                    // 检查目录是否存在
                    if (!Directory.Exists(fullPath))
                    {
                        _logger.LogWarning("本地目录不存在: {FullPath}", fullPath);
                        return 0;
                    }

                    // 获取目录中的文件列表
                    var files = Directory.GetFiles(fullPath, searchPattern, SearchOption.TopDirectoryOnly);
                    var count = files.Length;

                    _logger.LogInformation("本地文件统计完成: 路径: {FullPath}, 模式: {SearchPattern}, 数量: {Count}",
                        fullPath, searchPattern, count);

                    return count;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "统计本地文件失败，路径: {RelativePath}, 模式: {SearchPattern}",
                        relativePath, searchPattern);
                    return 0;
                }
            });
        }

        /// <summary>
        /// 统计已上传成功的文件数量（从本地日期目录统计）
        /// </summary>
        private async Task<int> CountLocalUploadedFilesAsync(string fileType, DateTime queryDate)
        {
            return await Task.Run(() =>
            {
                try
                {
                    // 获取根目录配置
                    var rootPath = _configuration["FileStats:RootPath"] ?? "/app/data";

                    // 根据文件类型和日期构建本地路径
                    var dateStr = queryDate.ToString("yyyyMMdd");
                    var relativePath = $"DataPacks/{fileType}/{dateStr}";
                    var fullPath = Path.Combine(rootPath, relativePath);

                    _logger.LogInformation("统计已上传文件，文件类型: {FileType}, 日期: {QueryDate}, 本地路径: {FullPath}",
                        fileType, queryDate, fullPath);

                    // 检查目录是否存在
                    if (!Directory.Exists(fullPath))
                    {
                        _logger.LogWarning("本地已上传文件目录不存在: {FullPath}", fullPath);
                        return 0;
                    }

                    // 获取目录中的文件列表
                    var files = Directory.GetFiles(fullPath, $"*.{fileType}", SearchOption.TopDirectoryOnly);
                    var count = files.Length;

                    _logger.LogInformation("本地已上传文件统计完成: 路径: {FullPath}, 文件类型: {FileType}, 数量: {Count}",
                        fullPath, fileType, count);

                    return count;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "统计已上传文件失败，文件类型: {FileType}, 日期: {QueryDate}",
                        fileType, queryDate);
                    return 0;
                }
            });
        }



        /// <summary>
        /// 获取FTP服务器配置列表
        /// </summary>
        public async Task<List<FtpUploadServer>> GetFtpServersAsync()
        {
            try
            {
                return await _context.FtpUploadServers
                    .OrderBy(f => f.UpOrder)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取FTP服务器配置列表失败");
                throw;
            }
        }


    }
}
