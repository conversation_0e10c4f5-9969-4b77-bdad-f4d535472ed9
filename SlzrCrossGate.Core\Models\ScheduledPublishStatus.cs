namespace SlzrCrossGate.Core.Models
{
    /// <summary>
    /// 预约发布状态枚举
    /// </summary>
    public enum ScheduledPublishStatus
    {
        /// <summary>
        /// 待发布
        /// </summary>
        Pending = 1,

        /// <summary>
        /// 已发布
        /// </summary>
        Published = 2,

        /// <summary>
        /// 已取消
        /// </summary>
        Cancelled = 3,

        /// <summary>
        /// 发布失败
        /// </summary>
        Failed = 4
    }
}
