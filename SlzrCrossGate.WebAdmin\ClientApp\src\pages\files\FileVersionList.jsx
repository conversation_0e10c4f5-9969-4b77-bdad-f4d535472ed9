import React, { useState, useEffect, useMemo, useRef } from 'react';
import {
  Box,
  Paper,
  Typography,
  Grid,
  Button,
  TextField,
  TablePagination,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
  MenuItem,
  FormControl,
  FormHelperText,
  InputLabel,
  Select,
  Chip
} from '@mui/material';
import ResponsiveTable, {
  ResponsiveTableHead,
  ResponsiveTableBody,
  ResponsiveTableRow,
  ResponsiveTableCell
} from '../../components/ResponsiveTable';
import {
  ChevronRight as ChevronRight,
  Refresh as RefreshIcon,
  Search as SearchIcon,
  Clear as ClearIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
  CloudDownload as CloudDownloadIcon,
  Publish as PublishIcon,
  Schedule as ScheduleIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useSnackbar } from 'notistack';
import { fileAPI } from '../../services/api';
import { formatDateTime } from '../../utils/dateUtils';
import { parseErrorMessage } from '../../utils/errorHandler';
import MerchantAutocomplete from '../../components/MerchantAutocomplete';
import { useAuth } from '../../contexts/AuthContext';
import { FeatureGuard } from '../../components/FeatureGuard';
import { useFeaturePermission } from '../../hooks/useFeaturePermission';
import { PERMISSIONS } from '../../constants/permissions';

// 文件版本操作按钮组件
const FileVersionActionButtons = ({ fileVersion, onEdit, onDelete, onPublish, onScheduledPublish, onDownload }) => {
  const { checkMultiple } = useFeaturePermission();

  // 批量检查权限
  const permissions = useMemo(() =>
    checkMultiple([
      PERMISSIONS.FILE_VERSION.UPLOAD,
      PERMISSIONS.FILE_VERSION.DELETE,
      PERMISSIONS.FILE_VERSION.PUBLISH
    ]),
    [checkMultiple]
  );

  // 权限判断 - 后端已有完整权限控制，前端只需检查功能权限
  const canDelete = permissions[PERMISSIONS.FILE_VERSION.DELETE];
  const canPublish = permissions[PERMISSIONS.FILE_VERSION.PUBLISH];

  return (
    <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
      {/* 下载按钮 - 所有用户都可以下载，不需要功能权限控制 */}
      <Tooltip title="下载">
        <IconButton
          size="small"
          color="primary"
          onClick={() => onDownload(fileVersion)}
        >
          <CloudDownloadIcon fontSize="small" />
        </IconButton>
      </Tooltip>

      {/* 发布按钮 */}
      <FeatureGuard featureKey={PERMISSIONS.FILE_VERSION.PUBLISH}>
        {canPublish && (
          <Tooltip title="立即发布">
            <IconButton
              size="small"
              color="success"
              onClick={() => onPublish(fileVersion)}
            >
              <PublishIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        )}
      </FeatureGuard>

      {/* 预约发布按钮 */}
      <FeatureGuard featureKey={PERMISSIONS.FILE_VERSION.PUBLISH}>
        {canPublish && (
          <Tooltip title="预约发布">
            <IconButton
              size="small"
              color="warning"
              onClick={() => onScheduledPublish(fileVersion)}
            >
              <ScheduleIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        )}
      </FeatureGuard>

      {/* 删除按钮 */}
      <FeatureGuard featureKey={PERMISSIONS.FILE_VERSION.DELETE}>
        {canDelete && (
          <Tooltip title="删除">
            <IconButton
              size="small"
              color="error"
              onClick={() => onDelete(fileVersion)}
            >
              <DeleteIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        )}
      </FeatureGuard>
    </Box>
  );
};

const FileVersionList = () => {
  const navigate = useNavigate();
  const { enqueueSnackbar } = useSnackbar();
  const { user } = useAuth();

  // 页面访问权限通过菜单权限控制，不再进行页面级别权限检查
  const [fileVersions, setFileVersions] = useState([]);
  const [fileTypes, setFileTypes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const [isSystemAdmin, setIsSystemAdmin] = useState(false);

  // 筛选条件
  const [filters, setFilters] = useState({
    merchantId: '',
    fileTypeId: '',
    filePara: '',
    ver: ''
  });

  // 选中的商户对象（用于MerchantAutocomplete）
  const [selectedMerchant, setSelectedMerchant] = useState(null);

  // 上传对话框状态
  const [openUploadDialog, setOpenUploadDialog] = useState(false);
  const [uploadForm, setUploadForm] = useState({
    merchantId: '',
    fileTypeId: '',
    filePara: '',
    ver: '',
    remarks: '',
    file: null
  });
  const [autoParseInfo, setAutoParseInfo] = useState(null); // 自动解析信息
  const [uploadError, setUploadError] = useState('');
  const [uploadLoading, setUploadLoading] = useState(false);
  const [selectedUploadMerchant, setSelectedUploadMerchant] = useState(null);

  // 删除确认对话框
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [fileVersionToDelete, setFileVersionToDelete] = useState(null);
  const [deleteLoading, setDeleteLoading] = useState(false);

  // 防重复请求
  const loadFileVersionsRef = useRef();
  const hasLoadedRef = useRef(false);

  // 加载文件版本列表
  const loadFileVersions = async (isInitialLoad = false, forceLoad = false) => {
    try {
      setLoading(true);

      // 构建查询参数
      const params = {
        page: page + 1,
        pageSize: rowsPerPage,
        ...Object.fromEntries(
          Object.entries(filters).filter(([_, value]) => value !== '')
        )
      };

      // 移除undefined的参数
      Object.keys(params).forEach(key => {
        if (params[key] === undefined) {
          delete params[key];
        }
      });

      // 避免重复请求：检查参数是否真的发生了变化（除非强制加载）
      const paramsString = JSON.stringify(params);
      if (!isInitialLoad && !forceLoad && loadFileVersionsRef.current === paramsString) {
        console.log('FileVersionList: 参数未变化，跳过重复请求');
        setLoading(false);
        return;
      }
      loadFileVersionsRef.current = paramsString;

      console.log('FileVersionList: 执行数据请求', params);
      const response = await fileAPI.getFileVersions(params);
      setFileVersions(response.items);
      setTotalCount(response.totalCount);
    } catch (error) {
      console.error('Error loading file versions:', error);
    } finally {
      setLoading(false);
    }
  };

  // 加载文件类型列表
  const loadFileTypes = async () => {
    try {
      // 使用不分页API获取所有文件类型，确保下拉框中包含所有数据
      const response = await fileAPI.getAllFileTypes();
      setFileTypes(response.items || []);
    } catch (error) {
      console.error('Error loading file types:', error);
    }
  };



  // 统一的数据加载逻辑
  useEffect(() => {
    // 防止重复加载
    if (hasLoadedRef.current) {
      console.log('FileVersionList: 已加载过，跳过重复请求');
      return;
    }

    console.log('FileVersionList: 执行首次加载');
    hasLoadedRef.current = true;
    loadFileVersions(true, false); // 标记为初始加载，非强制加载
    loadFileTypes();
    setIsSystemAdmin(user?.roles?.includes('SystemAdmin'));
  }, []);

  // 当参数变化时重新加载
  useEffect(() => {
    if (hasLoadedRef.current) { // 只有在首次加载完成后才响应参数变化
      console.log('FileVersionList: 参数变化，重新加载');
      loadFileVersions(false, false); // 非初始加载，非强制加载
    }
  }, [page, rowsPerPage, filters]);

  // 处理筛选条件变更
  const handleFilterChange = (event) => {
    const { name, value } = event.target;
    setFilters(prev => ({ ...prev, [name]: value }));
  };

  // 应用筛选
  const applyFilters = () => {
    setPage(0);
    loadFileVersions(false, true); // 强制执行搜索
  };

  // 清除筛选
  const clearFilters = () => {
    // 重置所有筛选条件
    setFilters({
      merchantId: '',
      fileTypeId: '',
      filePara: '',
      ver: ''
    });

    // 重置选中的商户
    setSelectedMerchant(null);

    // 重置页码并重新加载数据
    setPage(0);
    loadFileVersions(false, true); // 强制执行搜索
  };

  // 处理分页变更
  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // 打开上传对话框
  const openUploadFileDialog = () => {
    setUploadForm({
      merchantId: '',
      fileTypeId: '',
      filePara: '',
      ver: '',
      remarks: '',
      file: null
    });
    setSelectedUploadMerchant(null); // 重置选中的商户
    setUploadError('');
    setAutoParseInfo(null); // 清空自动解析信息
    setOpenUploadDialog(true);
  };

  // 处理上传表单变更
  const handleUploadFormChange = (event) => {
    const { name, value } = event.target;

    // 根据字段类型进行特殊处理
    if (name === 'filePara') {
      // 文件参数：只允许字母和数字，不超过8位
      const sanitizedValue = value.replace(/[^a-zA-Z0-9]/g, '').slice(0, 8);
      setUploadForm(prev => ({ ...prev, [name]: sanitizedValue }));
    } else if (name === 'ver') {
      // 版本号：只允许16进制字符(0-9, A-F, a-f)，自动转为大写，限制4位
      const sanitizedValue = value.replace(/[^0-9a-fA-F]/g, '').slice(0, 4).toUpperCase();
      setUploadForm(prev => ({ ...prev, [name]: sanitizedValue }));
    } else {
      setUploadForm(prev => ({ ...prev, [name]: value }));
    }
  };

  // 解析文件名并自动填充表单
  const parseFileName = (fileName) => {
    if (!fileName) return null;

    // 移除文件扩展名
    const nameWithoutExt = fileName.replace(/\.[^/.]+$/, "");

    // 正则表达式匹配格式：{3位文件类型}{0-8位文件参数}_{4位版本号}{其他可选内容}
    // 总长度3-11位字母数字 + 下划线 + 4位16进制 + 任意后续内容
    // 例如：APKBUS_0012 解析为 APK + BUS + 0012
    // 例如：APK_12345 解析为 APK + (空) + 1234 (忽略5)
    const regex = /^([A-Za-z0-9]{3})([A-Za-z0-9]{0,8})_([0-9A-Fa-f]{4}).*$/;
    const match = nameWithoutExt.match(regex);

    if (match) {
      return {
        fileTypeId: match[1].toUpperCase(), // 文件类型转大写 (固定3位)
        filePara: match[2].toUpperCase(),   // 文件参数转大写 (0-8位)
        ver: match[3].toUpperCase()         // 版本号转大写 (4位16进制)
      };
    }

    return null;
  };

  // 处理文件选择
  const handleFileSelect = (event) => {
    const selectedFile = event.target.files[0];
    if (!selectedFile) return;

    // 解析文件名
    const parsedInfo = parseFileName(selectedFile.name);

    if (parsedInfo) {
      // 如果解析成功，自动填充表单
      setUploadForm(prev => ({
        ...prev,
        file: selectedFile,
        fileTypeId: parsedInfo.fileTypeId,
        filePara: parsedInfo.filePara,
        ver: parsedInfo.ver
      }));

      // 设置自动解析信息用于显示提示
      setAutoParseInfo({
        fileName: selectedFile.name,
        fileTypeId: parsedInfo.fileTypeId,
        filePara: parsedInfo.filePara,
        ver: parsedInfo.ver
      });

      console.log('已自动解析文件名:', parsedInfo);
    } else {
      // 如果解析失败，只设置文件
      setUploadForm(prev => ({ ...prev, file: selectedFile }));
      setAutoParseInfo(null);
      console.log('文件名格式不匹配自动解析规则:', selectedFile.name);
    }
  };

  // 上传文件
  const uploadFile = async () => {
    setUploadLoading(true);
    setUploadError('');

    try {
      const formData = new FormData();
      formData.append('MerchantID', uploadForm.merchantId);
      formData.append('FileTypeID', uploadForm.fileTypeId);
      formData.append('FilePara', uploadForm.filePara || ''); // 确保不为null或undefined
      formData.append('Ver', uploadForm.ver);
      formData.append('Remarks', uploadForm.remarks || ''); // 添加备注字段
      formData.append('File', uploadForm.file);

      await fileAPI.uploadFile(formData);

      setOpenUploadDialog(false);
      enqueueSnackbar('文件上传成功', { variant: 'success' });
      loadFileVersions(false, true); // 强制刷新列表
    } catch (error) {
      console.error('Error uploading file:', error);
      const errorMessage = parseErrorMessage(error, '文件上传失败');
      setUploadError(errorMessage);
      enqueueSnackbar(errorMessage, { variant: 'error' });
    } finally {
      setUploadLoading(false);
    }
  };

  // 打开删除确认对话框
  const openDeleteConfirmDialog = (fileVersion) => {
    setFileVersionToDelete(fileVersion);
    setOpenDeleteDialog(true);
  };

  // 删除文件版本
  const deleteFileVersion = async () => {
    if (!fileVersionToDelete) return;

    setDeleteLoading(true);
    try {
      await fileAPI.deleteFileVersion(fileVersionToDelete.id);
      setOpenDeleteDialog(false);
      enqueueSnackbar('文件版本删除成功', { variant: 'success' });
      loadFileVersions(false, true); // 强制刷新列表
    } catch (error) {
      console.error('Error deleting file version:', error);
      const errorMessage = parseErrorMessage(error, '删除失败');
      enqueueSnackbar(errorMessage, { variant: 'error' });
    } finally {
      setDeleteLoading(false);
    }
  };

  // 下载文件
  const downloadFile = async (fileVersion) => {
    try {
      console.log('开始下载文件，ID:', fileVersion.id);

      // 使用fileAPI服务发送带有认证令牌的请求
      const blob = await fileAPI.downloadFileVersion(fileVersion.id);

      console.log('下载响应blob:', blob);
      console.log('Blob类型:', blob.type);
      console.log('Blob大小:', blob.size);

      // 检查blob是否有效
      if (!blob || blob.size === 0) {
        throw new Error('下载的文件为空');
      }

      // 创建blob URL并触发下载
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');

      // 设置下载文件名，组合文件类型、文件参数和版本号
      const merchantId = fileVersion.merchantID;
      const fileTypeId = fileVersion.fileTypeID;
      const filePara = fileVersion.filePara;
      const ver = fileVersion.ver;
      const fileName = `${fileTypeId}${filePara}_${ver}.bin`;

      link.href = url;
      link.setAttribute('download', fileName);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // 释放blob URL
      window.URL.revokeObjectURL(url);

      // 记录下载日志
      console.log(`下载文件成功: ID=${fileVersion.id}, 文件名=${fileName}`);
    } catch (error) {
      console.error('下载文件失败:', error);
      const errorMessage = parseErrorMessage(error, '下载文件失败');
      enqueueSnackbar(errorMessage, { variant: 'error' });
    }
  };

  // 跳转到发布页面
  const goToPublish = (fileVersion) => {
    navigate('/app/files/publish', { state: { fileVersion } });
  };

  // 跳转到预约发布页面
  const goToScheduledPublish = (fileVersion) => {
    navigate('/app/files/publish', { state: { fileVersion, defaultMode: 'scheduled' } });
  };

  // 格式化文件大小
  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">
        文件版本管理
        </Typography>
        <Button
          variant="outlined"
          startIcon={<ChevronRight />}
          onClick={() => navigate('/app/files/publish-list')}
        >
          查看发布记录
        </Button>
      </Box>


      {/* 筛选条件 */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={6} md={2}>
            <MerchantAutocomplete
              value={selectedMerchant}
              onChange={(event, newValue) => {
                setSelectedMerchant(newValue);
                // 商户变更时，清空文件类型选择
                setFilters(prev => ({
                  ...prev,
                  merchantId: newValue ? newValue.merchantID : '',  // 确保使用merchantID而非id
                  fileTypeId: '' // 清空文件类型选择
                }));
                // 商户变更日志已移除
              }}
              size="small"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <TextField
              fullWidth
              label="文件类型"
              name="fileTypeId"
              value={filters.fileTypeId}
              onChange={handleFilterChange}
              size="small"
              select
              disabled={!selectedMerchant} // 未选择商户时禁用
              SelectProps={{
                MenuProps: {
                  PaperProps: {
                    style: {
                      maxHeight: 300, // 设置下拉菜单最大高度
                      overflowY: 'auto', // 启用垂直滚动条
                    },
                  },
                },
              }}
            >
              <MenuItem value="">全部</MenuItem>
              {fileTypes
                // 只显示选中商户的文件类型，尝试多种可能的字段名匹配
                .filter(type => {
                  if (!selectedMerchant) return false;

                  // 调试日志已移除，避免控制台输出过多信息

                  return type.merchantID && type.merchantID === selectedMerchant.merchantID;
                })
                .map((type) => (
                  <MenuItem key={`${type.code}-${type.merchantId || type.merchantID || type.MerchantId || type.MerchantID}`} value={type.code}>
                    {type.code} - {type.name || '未命名'}
                  </MenuItem>
                ))}
            </TextField>
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <TextField
              fullWidth
              label="文件参数"
              name="filePara"
              value={filters.filePara}
              onChange={handleFilterChange}
              size="small"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <TextField
              fullWidth
              label="版本号"
              name="ver"
              value={filters.ver}
              onChange={handleFilterChange}
              size="small"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={1}>
            <Button
              fullWidth
              variant="contained"
              color="primary"
              startIcon={<SearchIcon />}
              onClick={applyFilters}
            >
              搜索
            </Button>
          </Grid>
          <Grid item xs={12} sm={6} md={1}>
            <Button
              fullWidth
              variant="outlined"
              startIcon={<ClearIcon />}
              onClick={clearFilters}
            >
              清除
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {/* 操作按钮 */}
      <Box sx={{ mb: 2 }}>
        <FeatureGuard featureKey={PERMISSIONS.FILE_VERSION.UPLOAD}>
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            onClick={openUploadFileDialog}
          >
            上传文件
          </Button>
        </FeatureGuard>
        <Button
          variant="outlined"
          color="primary"
          startIcon={<RefreshIcon />}
          onClick={loadFileVersions}
          sx={{ ml: 1 }}
        >
          刷新
        </Button>
      </Box>

      {/* 文件版本列表 */}
      <Paper>
        <ResponsiveTable minWidth={1200} stickyActions={true}>
          <ResponsiveTableHead>
            <ResponsiveTableRow>
              <ResponsiveTableCell hideOn={['xs']}>商户</ResponsiveTableCell>
              <ResponsiveTableCell>文件类型</ResponsiveTableCell>
              <ResponsiveTableCell hideOn={['xs', 'sm']}>文件参数</ResponsiveTableCell>
              <ResponsiveTableCell>版本号</ResponsiveTableCell>
              <ResponsiveTableCell hideOn={['xs', 'sm']}>文件大小</ResponsiveTableCell>
              <ResponsiveTableCell hideOn={['xs', 'sm']}>CRC校验</ResponsiveTableCell>
              <ResponsiveTableCell hideOn={['xs', 'sm']}>备注信息</ResponsiveTableCell>
              <ResponsiveTableCell hideOn={['xs']}>上传时间</ResponsiveTableCell>
              <ResponsiveTableCell hideOn={['xs', 'sm']}>操作人</ResponsiveTableCell>
              <ResponsiveTableCell sticky={true} minWidth={120}>操作</ResponsiveTableCell>
            </ResponsiveTableRow>
          </ResponsiveTableHead>
          <ResponsiveTableBody>
            {loading ? (
              <ResponsiveTableRow>
                <ResponsiveTableCell colSpan={10} align="center">
                  <CircularProgress size={24} />
                </ResponsiveTableCell>
              </ResponsiveTableRow>
            ) : fileVersions.length === 0 ? (
              <ResponsiveTableRow>
                <ResponsiveTableCell colSpan={10} align="center">
                  没有找到文件版本
                </ResponsiveTableCell>
              </ResponsiveTableRow>
            ) : (
              fileVersions.map((fileVersion) => (
                <ResponsiveTableRow key={fileVersion.id}>

                  <ResponsiveTableCell hideOn={['xs']}>
                    <Tooltip title={fileVersion.merchantID || ''}>
                      <span>{fileVersion.merchantName}</span>
                    </Tooltip>
                  </ResponsiveTableCell>
                  <ResponsiveTableCell>
                    <Box>
                      <Typography variant="body2">
                        {fileVersion.fileTypeName}({fileVersion.fileTypeID})
                      </Typography>
                      {/* 在小屏幕上显示文件参数 */}
                      <Typography variant="caption" color="textSecondary" sx={{ display: { xs: 'block', md: 'none' } }}>
                        参数: {fileVersion.filePara || '-'}
                      </Typography>
                    </Box>
                  </ResponsiveTableCell>
                  <ResponsiveTableCell hideOn={['xs', 'sm']}>{fileVersion.filePara}</ResponsiveTableCell>
                  <ResponsiveTableCell>
                    <Box>
                      <Typography variant="body2" fontWeight="bold">
                        {fileVersion.ver}
                      </Typography>
                      {/* 在小屏幕上显示文件大小和时间 */}
                      <Typography variant="caption" color="textSecondary" sx={{ display: { xs: 'block', sm: 'none' } }}>
                        {formatFileSize(fileVersion.fileSize)} | {formatDateTime(fileVersion.createTime)}
                      </Typography>
                    </Box>
                  </ResponsiveTableCell>
                  <ResponsiveTableCell hideOn={['xs', 'sm']}>{formatFileSize(fileVersion.fileSize)}</ResponsiveTableCell>
                  <ResponsiveTableCell hideOn={['xs', 'sm']}>{fileVersion.crc}</ResponsiveTableCell>
                  <ResponsiveTableCell hideOn={['xs', 'sm']}>
                    <Tooltip title={fileVersion.remarks || ''} placement="top">
                      <Box
                        sx={{
                          maxWidth: 200,
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap'
                        }}
                      >
                        {fileVersion.remarks || '-'}
                      </Box>
                    </Tooltip>
                  </ResponsiveTableCell>
                  <ResponsiveTableCell hideOn={['xs']}>{formatDateTime(fileVersion.createTime)}</ResponsiveTableCell>
                  <ResponsiveTableCell hideOn={['xs', 'sm']}>{fileVersion.operator || '-'}</ResponsiveTableCell>
                  <ResponsiveTableCell sticky={true}>
                    <FileVersionActionButtons
                      fileVersion={fileVersion}
                      onDownload={() => downloadFile(fileVersion)}
                      onPublish={() => goToPublish(fileVersion)}
                      onScheduledPublish={() => goToScheduledPublish(fileVersion)}
                      onDelete={() => openDeleteConfirmDialog(fileVersion)}
                    />
                  </ResponsiveTableCell>
                </ResponsiveTableRow>
              ))
            )}
          </ResponsiveTableBody>
        </ResponsiveTable>
        <TablePagination
          rowsPerPageOptions={[5, 10, 25, 50]}
          component="div"
          count={totalCount}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
          labelRowsPerPage="每页行数:"
          labelDisplayedRows={({ from, to, count }) => `${from}-${to} 共 ${count}`}
        />
      </Paper>

      {/* 上传文件对话框 */}
      <Dialog open={openUploadDialog} onClose={() => setOpenUploadDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>上传文件</DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <MerchantAutocomplete
                  value={selectedUploadMerchant}
                  onChange={(event, newValue) => {
                    setSelectedUploadMerchant(newValue);
                    // 清空文件类型选择
                    setUploadForm(prev => ({
                      ...prev,
                      merchantId: newValue ? newValue.merchantID : '',  // 确保使用merchantID
                      fileTypeId: '' // 清空文件类型选择
                    }));
                  }}
                  required
                  error={!uploadForm.merchantId}
                  size="medium"
                  helperText={!uploadForm.merchantId ? '请选择商户' : ''}
                />
              </Grid>
              <Grid item xs={12}>
                <Button
                  variant="outlined"
                  component="label"
                  fullWidth
                  sx={{ height: '56px' }}
                  disabled={!selectedUploadMerchant} // 未选择商户时禁用
                >
                  {uploadForm.file ? uploadForm.file.name : '选择文件'}
                  <input
                    type="file"
                    hidden
                    onChange={handleFileSelect}
                    disabled={!selectedUploadMerchant} // 未选择商户时禁用
                  />
                </Button>
                {!selectedUploadMerchant ? (
                  <Typography color="text.secondary" variant="caption">
                    请先选择商户
                  </Typography>
                ) : !uploadForm.file ? (
                  <Typography color="error" variant="caption">
                    请选择文件
                  </Typography>
                ) : null}

                {/* 自动解析提示 */}
                {autoParseInfo && (
                  <Box sx={{
                    mt: 1,
                    p: 1.5,
                    backgroundColor: 'success.light',
                    borderRadius: 1,
                    border: '1px solid',
                    borderColor: 'success.main'
                  }}>
                    <Typography variant="body2" color="success.dark" sx={{ fontWeight: 'bold', mb: 0.5 }}>
                      ✓ 已自动解析文件名
                    </Typography>
                    <Typography variant="caption" display="block" color="success.dark">
                      文件类型: <strong>{autoParseInfo.fileTypeId}</strong> |
                      文件参数: <strong>{autoParseInfo.filePara}</strong> |
                      版本: <strong>{autoParseInfo.ver}</strong>
                    </Typography>
                    <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5, display: 'block' }}>
                      格式: {'{文件类型3位}{文件参数0-8位}_{版本4位16进制}{其他可选}'}
                    </Typography>
                    <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>
                      示例: APKBUS_0012_sy.apk → APK + BUS + 0012
                    </Typography>
                    <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>
                      示例: APK_12345.bin → APK + (空) + 1234
                    </Typography>
                  </Box>
                )}
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth required error={!uploadForm.fileTypeId}>
                  <InputLabel>文件类型</InputLabel>
                  <Select
                    name="fileTypeId"
                    value={uploadForm.fileTypeId}
                    onChange={handleUploadFormChange}
                    label="文件类型"
                    disabled={!selectedUploadMerchant} // 未选择商户时禁用
                    MenuProps={{
                      PaperProps: {
                        style: {
                          maxHeight: 300, // 设置下拉菜单最大高度
                          overflowY: 'auto', // 启用垂直滚动条
                        },
                      },
                    }}
                  >
                    {fileTypes
                      // 只显示选中商户的文件类型，使用正确的selectedUploadMerchant变量
                      .filter(type => {
                        if (!selectedUploadMerchant) return false;

                        // 调试日志已移除，避免控制台输出过多信息

                        // 尝试多种可能的字段名匹配
                        return (
                          // 尝试merchantId字段
                          (type.merchantId && type.merchantId === selectedUploadMerchant.merchantID) ||
                          // 尝试merchantID字段
                          (type.merchantID && type.merchantID === selectedUploadMerchant.merchantID) ||
                          // 尝试MerchantId字段
                          (type.MerchantId && type.MerchantId === selectedUploadMerchant.merchantID) ||
                          // 尝试MerchantID字段
                          (type.MerchantID && type.MerchantID === selectedUploadMerchant.merchantID)
                        );
                      })
                      .map((type) => (
                        <MenuItem key={`${type.code}-${type.merchantId || type.merchantID || type.MerchantId || type.MerchantID}`} value={type.code}>
                          {type.code} - {type.name || '未命名'}
                        </MenuItem>
                      ))}
                  </Select>
                  {!uploadForm.fileTypeId && <FormHelperText>请选择文件类型</FormHelperText>}
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="文件参数"
                  name="filePara"
                  value={uploadForm.filePara}
                  onChange={handleUploadFormChange}
                  disabled={!selectedUploadMerchant} // 未选择商户时禁用
                  // 文件参数不是必填项
                  helperText={!selectedUploadMerchant ? "请先选择商户" : "选填，仅允许字母和数字，最多8位"}
                  inputProps={{
                    maxLength: 8,
                    pattern: '[A-Za-z0-9]*'
                  }}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="版本号"
                  name="ver"
                  value={uploadForm.ver}
                  onChange={handleUploadFormChange}
                  required
                  disabled={!selectedUploadMerchant} // 未选择商户时禁用
                  error={!uploadForm.ver || uploadForm.ver.length !== 4}
                  helperText={
                    !selectedUploadMerchant ? "请先选择商户" :
                    !uploadForm.ver ? '请输入版本号' :
                    uploadForm.ver.length !== 4 ? '版本号必须为4位16进制字符' : ''
                  }
                  inputProps={{
                    maxLength: 4,
                    pattern: '[0-9A-Fa-f]{4}'
                  }}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="备注信息"
                  name="remarks"
                  value={uploadForm.remarks}
                  onChange={handleUploadFormChange}
                  disabled={!selectedUploadMerchant} // 未选择商户时禁用
                  multiline
                  rows={3}
                  helperText={!selectedUploadMerchant ? "请先选择商户" : "可选，记录本次文件上传的说明信息"}
                  inputProps={{
                    maxLength: 500
                  }}
                />
              </Grid>

            </Grid>
            {uploadError && (
              <Typography color="error" sx={{ mt: 2 }}>
                {uploadError}
              </Typography>
            )}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenUploadDialog(false)}>取消</Button>
          <Button
            onClick={uploadFile}
            variant="contained"
            color="primary"
            disabled={
              uploadLoading ||
              !uploadForm.merchantId ||
              !uploadForm.fileTypeId ||
              !uploadForm.ver ||
              uploadForm.ver.length !== 4 || // 版本号必须为4位
              !uploadForm.file
            }
          >
            {uploadLoading ? <CircularProgress size={24} /> : '上传'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* 删除确认对话框 */}
      <Dialog open={openDeleteDialog} onClose={() => setOpenDeleteDialog(false)}>
        <DialogTitle>确认删除</DialogTitle>
        <DialogContent>
          <Typography>
            确定要删除文件版本 "{fileVersionToDelete?.fileFullType} - {fileVersionToDelete?.ver}" 吗？此操作不可撤销。
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDeleteDialog(false)}>取消</Button>
          <Button
            onClick={deleteFileVersion}
            variant="contained"
            color="error"
            disabled={deleteLoading}
          >
            {deleteLoading ? <CircularProgress size={24} /> : '删除'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default FileVersionList;
