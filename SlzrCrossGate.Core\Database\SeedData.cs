using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SlzrCrossGate.Core.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SlzrCrossGate.Core.Database
{
    public static class SeedData
    {
        public static async Task InitializeUser(IServiceProvider serviceProvider)
        {
            
            var roleManager = serviceProvider.GetRequiredService<RoleManager<ApplicationRole>>();
            var loggerFactory = serviceProvider.GetRequiredService<ILoggerFactory>();
            var logger = loggerFactory.CreateLogger(typeof(SeedData));

            // 系统级角色
            var systemRoles = new List<ApplicationRole>
            {
                new() { Name = "SystemAdmin", IsSysAdmin = true},
                new() { Name = "MerchantAdmin", IsSysAdmin = false},
                new() { Name = "MerchantUser", IsSysAdmin = false}
            };

            foreach (var role in systemRoles)
            {
                if (role.Name != null && !await roleManager.RoleExistsAsync(role.Name))
                {
                    await roleManager.CreateAsync(role);
                }
            }

            // 系统管理员
            var userManager = serviceProvider.GetRequiredService<UserManager<ApplicationUser>>();
            var admin = new ApplicationUser
            {
                UserName = "admin",
                RealName = "系统管理员",
                Email="<EMAIL>"
            };

            var existingAdmin = await userManager.FindByNameAsync(admin.UserName);
            if (existingAdmin == null)
            {
                // 创建新的admin用户
                var result = await userManager.CreateAsync(admin, "Slzr!123456");
                if (result.Succeeded)
                {
                    await userManager.AddToRoleAsync(admin, "SystemAdmin");
                }
            }
            else
            {
                // 检查现有admin用户是否需要重置密码
                if (string.IsNullOrEmpty(existingAdmin.PasswordHash))
                {
                    // 密码哈希为空，执行重置
                    var resetToken = await userManager.GeneratePasswordResetTokenAsync(existingAdmin);
                    var resetResult = await userManager.ResetPasswordAsync(existingAdmin, resetToken, "Slzr!123456");
                    
                    if (resetResult.Succeeded)
                    {
                        // 标记用户需要强制更改密码
                        existingAdmin.RequirePasswordChange = true;
                        await userManager.UpdateAsync(existingAdmin);
                        logger.LogInformation("admin用户密码已重置为: Slzr!123456 ，用户登录后将强制要求更改密码");
                    }
                    else
                    {
                        logger.LogError("admin密码重置失败: {Errors}", 
                            string.Join(", ", resetResult.Errors.Select(e => e.Description)));
                    }
                }
                
                // 确保admin用户拥有SystemAdmin角色
                if (!await userManager.IsInRoleAsync(existingAdmin, "SystemAdmin"))
                {
                    await userManager.AddToRoleAsync(existingAdmin, "SystemAdmin");
                    logger.LogInformation("为admin用户添加了SystemAdmin角色");
                }
            }
        }
    }
}
