using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using NPOI.SS.Formula.Functions;
using SlzrCrossGate.Core.Database;
using SlzrCrossGate.Core.Models;
using SlzrCrossGate.Core.Service;
using SlzrCrossGate.Core.Service.BusinessServices;
using SlzrCrossGate.Core.Attributes;
using SlzrCrossGate.WebAdmin.DTOs;


using SlzrCrossGate.WebAdmin.Services;
using System.Security.Claims;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Reflection;
using System.Text.RegularExpressions;

namespace SlzrCrossGate.WebAdmin.Controllers
{
    // 字段配置类
    public class ExportFieldConfig
    {
        [JsonPropertyName("key")]
        public string Key { get; set; } = string.Empty;

        [JsonPropertyName("displayName")]
        public string DisplayName { get; set; } = string.Empty;

        [JsonPropertyName("dataPath")]
        public string DataPath { get; set; } = string.Empty;

        [JsonPropertyName("type")]
        public string Type { get; set; } = string.Empty;

        [JsonPropertyName("config")]
        public object? Config { get; set; }
    }

    // 导出请求类
    public class ExportTerminalsRequest
    {
        public string? MerchantId { get; set; }
        public string? LineNo { get; set; }
        public string? DeviceNo { get; set; }
        public string? MachineId { get; set; }
        public string? TerminalType { get; set; }
        public DeviceActiveStatus? ActiveStatus { get; set; }
        public string? LicensePlate { get; set; } // 新增车牌号筛选
        public List<ExportFieldConfig> Fields { get; set; } = new List<ExportFieldConfig>();
    }

    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class TerminalsController : ControllerBase
    {
        private readonly TcpDbContext _dbContext;
        private readonly TerminalEventPublishService _terminalEventPublishService;
        private readonly UserService _userService;
        private readonly FilePublishEventService _filePublishEventService;
        private readonly MsgboxEventService _msgboxEventService;
        private readonly ILogger<TerminalsController> _logger;

        public TerminalsController(
            TcpDbContext dbContext,
            TerminalEventPublishService terminalEventPublishService,
            UserService userService,
            FilePublishEventService filePublishEventService,
            MsgboxEventService msgboxEventService,
            ILogger<TerminalsController> logger)
        {
            _dbContext = dbContext;
            _terminalEventPublishService = terminalEventPublishService;
            _userService = userService;
            _logger = logger;
            _filePublishEventService = filePublishEventService;
            _msgboxEventService = msgboxEventService;
        }

        // GET: api/Terminals
        [HttpGet]
        public async Task<ActionResult<PaginatedResult<TerminalDto>>> GetTerminals(
            [FromQuery] string? merchantId = null,
            [FromQuery] string? lineNo = null,
            [FromQuery] string? deviceNo = null,
            [FromQuery] string? machineId = null,
            [FromQuery] string? terminalType = null,
            [FromQuery] string? fileType = null,
            [FromQuery] string? fileVersion = null,
            [FromQuery] DeviceActiveStatus? activeStatus = null,
            [FromQuery] string? licensePlate = null,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 10)
        {
            // 获取当前用户的商户ID
            var currentUserMerchantId = await _userService.GetUserMerchantIdAsync(User);
            var isSystemAdmin = User.IsInRole("SystemAdmin");

            // 如果不是系统管理员，只能查看自己商户的终端
            if (!isSystemAdmin && merchantId != null && merchantId != currentUserMerchantId)
            {
                return Forbid();
            }

            // 如果不是系统管理员且未指定商户ID，则使用当前用户的商户ID
            if (!isSystemAdmin && merchantId == null)
            {
                merchantId = currentUserMerchantId;
            }

            // 构建查询，包含商户信息
            var query = _dbContext.Terminals
                .Include(t => t.Status)
                .Where(t => !t.IsDeleted)
                .AsQueryable();



            // 应用筛选条件
            if (!string.IsNullOrEmpty(merchantId))
            {
                query = query.Where(t => t.MerchantID == merchantId);
            }

            if (!string.IsNullOrEmpty(lineNo))
            {
                query = query.Where(t => t.LineNO == lineNo);
            }

            if (!string.IsNullOrEmpty(deviceNo))
            {
                query = query.Where(t => t.DeviceNO == deviceNo);
            }

            if (!string.IsNullOrEmpty(machineId))
            {
                query = query.Where(t => t.MachineID == machineId);
            }

            if (!string.IsNullOrEmpty(terminalType))
            {
                query = query.Where(t => t.TerminalType == terminalType);
            }

            if (activeStatus.HasValue)
            {
                if (activeStatus.Value == DeviceActiveStatus.Active)
                {
                    query = query.Where(t => t.Status != null && t.Status.ActiveStatus == activeStatus.Value && t.Status.LastActiveTime > DateTime.Now.AddMinutes(-5));
                }
                else
                {
                    query = query.Where(t => t.Status == null || t.Status.ActiveStatus == DeviceActiveStatus.Inactive || t.Status.LastActiveTime < DateTime.Now.AddMinutes(-5));
                }
            }

            // 文件版本筛选需要特殊处理，因为是JSON字段
            if (!string.IsNullOrEmpty(fileType) && !string.IsNullOrEmpty(fileVersion))
            {
                // 这里需要根据数据库类型进行不同的处理
                // 对于MySQL，可以使用JSON_EXTRACT函数
                // 但由于EF Core的限制，这里我们先获取所有符合其他条件的终端，然后在内存中筛选
                var terminalsWithMerchantsForFilter = await (from terminal in query
                                                            join merchant in _dbContext.Merchants on terminal.MerchantID equals merchant.MerchantID
                                                            join vehicle in _dbContext.VehicleInfos on new { terminal.MerchantID, terminal.DeviceNO } equals new { vehicle.MerchantID, vehicle.DeviceNO } into vehicleGroup
                                                            from vehicle in vehicleGroup.DefaultIfEmpty()
                                                            join linePrice in _dbContext.LinePriceInfos on new { terminal.MerchantID, LineNumber = terminal.LineNO } equals new { linePrice.MerchantID, LineNumber = linePrice.LineNumber + linePrice.GroupNumber } into linePriceGroup
                                                            from linePrice in linePriceGroup.DefaultIfEmpty()
                                                            where !merchant.IsDelete && (vehicle == null || vehicle.IsActive)
                                                            select new { Terminal = terminal, Merchant = merchant, Vehicle = vehicle, LinePrice = linePrice })
                                                            .ToListAsync();

                var filteredTerminals = terminalsWithMerchantsForFilter.Where(tm =>
                    tm.Terminal.Status != null &&
                    tm.Terminal.Status.FileVersionMetadata.TryGetValue(fileType, out var version) &&
                    version.Current == fileVersion).ToList();

                var totalCount = filteredTerminals.Count;
                var pagedTerminals = filteredTerminals
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToList();

                var terminalDtos = pagedTerminals.Select(tm => new TerminalDto
                {
                    ID = tm.Terminal.ID,
                    MerchantID = tm.Terminal.MerchantID,
                    MerchantName = tm.Merchant.Name ?? "",
                    MachineID = tm.Terminal.MachineID,
                    DeviceNO = tm.Terminal.DeviceNO,
                    LineNO = tm.Terminal.LineNO,
                    TerminalType = tm.Terminal.TerminalType,
                    CreateTime = tm.Terminal.CreateTime,
                    Status = tm.Terminal.Status != null ? new TerminalStatusDto
                    {
                        ActiveStatus = tm.Terminal.Status.ActiveStatus,
                        LastActiveTime = tm.Terminal.Status.LastActiveTime,
                        ConnectionProtocol = tm.Terminal.Status.ConnectionProtocol ?? "",
                        EndPoint = tm.Terminal.Status.EndPoint ?? "",
                        FileVersionMetadata = tm.Terminal.Status.FileVersionMetadata,
                        PropertyMetadata = tm.Terminal.Status.PropertyMetadata
                    } : null,
                    // 车辆和线路信息
                    LicensePlate = tm.Vehicle?.LicensePlate, // 车牌号，允许为空
                    LineName = tm.LinePrice?.LineName, // 线路名称，允许为空

                    // 兼容属性
                    TerminalID = tm.Terminal.ID,
                    TerminalTypeID = tm.Terminal.TerminalType,
                    IsActive = tm.Terminal.Status?.ActiveStatus == DeviceActiveStatus.Active,
                    CreatedTime = tm.Terminal.CreateTime,
                    UpdatedTime = tm.Terminal.CreateTime
                }).ToList();

                return new PaginatedResult<TerminalDto>
                {
                    Items = terminalDtos,
                    TotalCount = totalCount,
                    Page = page,
                    PageSize = pageSize
                };
            }

            // 使用LEFT JOIN查询获取终端、商户、车辆和线路信息
            var joinQuery = from terminal in query
                           join merchant in _dbContext.Merchants on terminal.MerchantID equals merchant.MerchantID
                           join vehicle in _dbContext.VehicleInfos on new { terminal.MerchantID, terminal.DeviceNO } equals new { vehicle.MerchantID, vehicle.DeviceNO } into vehicleGroup
                           from vehicle in vehicleGroup.DefaultIfEmpty()
                           join linePrice in _dbContext.LinePriceInfos on new { terminal.MerchantID, LineNumber = terminal.LineNO } equals new { linePrice.MerchantID, LineNumber = linePrice.LineNumber + linePrice.GroupNumber } into linePriceGroup
                           from linePrice in linePriceGroup.DefaultIfEmpty()
                           where !merchant.IsDelete && (vehicle == null || vehicle.IsActive)
                           select new { Terminal = terminal, Merchant = merchant, Vehicle = vehicle, LinePrice = linePrice };

            // 添加车牌号筛选
            if (!string.IsNullOrEmpty(licensePlate))
            {
                joinQuery = joinQuery.Where(x => x.Vehicle != null && x.Vehicle.LicensePlate.Contains(licensePlate));
            }

            // 获取总记录数
            var count = await joinQuery.CountAsync();

            // 应用分页
            var terminalsWithMerchants = await joinQuery
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            // 转换为DTO
            var terminalDtos2 = terminalsWithMerchants.Select(tm => new TerminalDto
            {
                ID = tm.Terminal.ID,
                MerchantID = tm.Terminal.MerchantID,
                MerchantName = tm.Merchant.Name ?? "",
                MachineID = tm.Terminal.MachineID,
                DeviceNO = tm.Terminal.DeviceNO,
                LineNO = tm.Terminal.LineNO,
                TerminalType = tm.Terminal.TerminalType,
                CreateTime = tm.Terminal.CreateTime,
                Status = tm.Terminal.Status != null ? new TerminalStatusDto
                {
                    ActiveStatus = tm.Terminal.Status.ActiveStatus,
                    LastActiveTime = tm.Terminal.Status.LastActiveTime,
                    ConnectionProtocol = tm.Terminal.Status.ConnectionProtocol ?? "",
                    EndPoint = tm.Terminal.Status.EndPoint ?? "",
                    FileVersionMetadata = tm.Terminal.Status.FileVersionMetadata,
                    PropertyMetadata = tm.Terminal.Status.PropertyMetadata
                } : null,

                // 车辆和线路信息
                LicensePlate = tm.Vehicle?.LicensePlate, // 车牌号，允许为空
                LineName = tm.LinePrice?.LineName, // 线路名称，允许为空

                // 兼容属性
                TerminalID = tm.Terminal.ID,
                TerminalTypeID = tm.Terminal.TerminalType,
                IsActive = tm.Terminal.Status?.ActiveStatus == DeviceActiveStatus.Active,
                CreatedTime = tm.Terminal.CreateTime,
                UpdatedTime = tm.Terminal.CreateTime
            }).ToList();

            return new PaginatedResult<TerminalDto>
            {
                Items = terminalDtos2,
                TotalCount = count,
                Page = page,
                PageSize = pageSize
            };
        }

        // GET: api/Terminals/5
        [HttpGet("{id}")]
        public async Task<ActionResult<TerminalDto>> GetTerminal(string id)
        {
            // 获取当前用户的商户ID
            var currentUserMerchantId = await _userService.GetUserMerchantIdAsync(User);
            var isSystemAdmin = User.IsInRole("SystemAdmin");

            // 首先获取终端基本信息
            var terminal = await _dbContext.Terminals
                .Include(t => t.Status)
                .FirstOrDefaultAsync(t => t.ID == id && !t.IsDeleted);

            if (terminal == null)
            {
                return NotFound();
            }

            // 获取商户信息
            var merchant = await _dbContext.Merchants
                .FirstOrDefaultAsync(m => m.MerchantID == terminal.MerchantID && !m.IsDelete);

            if (merchant == null)
            {
                return NotFound();
            }

            // 获取车辆信息（可选）
            var vehicle = await _dbContext.VehicleInfos
                .FirstOrDefaultAsync(v => v.MerchantID == terminal.MerchantID && v.DeviceNO == terminal.DeviceNO && v.IsActive);

            // 获取线路信息（可选）
            var linePrice = await _dbContext.LinePriceInfos
                .FirstOrDefaultAsync(lp => lp.MerchantID == terminal.MerchantID &&
                                          lp.LineNumber + lp.GroupNumber == terminal.LineNO);

            var terminalWithRelations = new { Terminal = terminal, Merchant = merchant, Vehicle = vehicle, LinePrice = linePrice };

            // 如果不是系统管理员，只能查看自己商户的终端
            if (!isSystemAdmin && terminal.MerchantID != currentUserMerchantId)
            {
                return Forbid();
            }

            // 获取该商户的终端属性字典配置
            var propertyDictionaries = await _dbContext.MerchantDictionaries
                .Where(d => d.MerchantID == terminal.MerchantID
                           && d.DictionaryType == "TerminalProperty"
                           && d.IsActive)
                .ToDictionaryAsync(d => d.DictionaryCode, d => d.DictionaryLabel);

            // 构建增强的属性元数据
            var enhancedPropertyMetadata = new Dictionary<string, object>();
            if (terminal.Status?.PropertyMetadata != null)
            {
                foreach (var property in terminal.Status.PropertyMetadata)
                {
                    enhancedPropertyMetadata[property.Key] = new
                    {
                        Value = property.Value,
                        DisplayName = propertyDictionaries.TryGetValue(property.Key, out var displayName)
                            ? displayName
                            : property.Key
                    };
                }
            }

            return new TerminalDto
            {
                ID = terminal.ID,
                MerchantID = terminal.MerchantID,
                MerchantName = merchant.Name ?? "",
                MachineID = terminal.MachineID,
                DeviceNO = terminal.DeviceNO,
                LineNO = terminal.LineNO,
                TerminalType = terminal.TerminalType,
                CreateTime = terminal.CreateTime,
                Status = terminal.Status != null ? new TerminalStatusDto
                {
                    ActiveStatus = terminal.Status.ActiveStatus,
                    LastActiveTime = terminal.Status.LastActiveTime,
                    ConnectionProtocol = terminal.Status.ConnectionProtocol ?? "",
                    EndPoint = terminal.Status.EndPoint ?? "",
                    FileVersionMetadata = terminal.Status.FileVersionMetadata,
                    PropertyMetadata = terminal.Status.PropertyMetadata,
                    EnhancedPropertyMetadata = enhancedPropertyMetadata
                } : null,

                // 车辆和线路信息
                LicensePlate = vehicle?.LicensePlate,
                LineName = linePrice?.LineName,

                // 兼容属性
                TerminalID = terminal.ID,
                TerminalTypeID = terminal.TerminalType,
                IsActive = terminal.Status?.ActiveStatus == DeviceActiveStatus.Active,
                CreatedTime = terminal.CreateTime,
                UpdatedTime = terminal.CreateTime
            };
        }

        // GET: api/Terminals/Stats
        [HttpGet("stats")]
        public async Task<ActionResult<TerminalStatsDto>> GetTerminalStats([FromQuery] string? merchantId = null)
        {
            // 获取当前用户的商户ID
            var currentUserMerchantId = await _userService.GetUserMerchantIdAsync(User);
            var isSystemAdmin = User.IsInRole("SystemAdmin");

            // 如果不是系统管理员，只能查看自己商户的终端
            if (!isSystemAdmin && merchantId != null && merchantId != currentUserMerchantId)
            {
                return Forbid();
            }

            // 如果不是系统管理员且未指定商户ID，则使用当前用户的商户ID
            if (!isSystemAdmin && merchantId == null)
            {
                merchantId = currentUserMerchantId;
            }

            // 构建查询
            var query = _dbContext.Terminals
                .Include(t => t.Status)
                .Where(t => !t.IsDeleted)
                .AsQueryable();

            // 应用商户筛选
            if (!string.IsNullOrEmpty(merchantId))
            {
                query = query.Where(t => t.MerchantID == merchantId);
            }

            // 获取统计数据
            var totalCount = await query.CountAsync();
            var activeCount = await query.CountAsync(t => t.Status != null && t.Status.ActiveStatus == DeviceActiveStatus.Active && t.Status.LastActiveTime > DateTime.Now.AddMinutes(-5));
            var inactiveCount = await query.CountAsync(t => t.Status != null && (t.Status.ActiveStatus == DeviceActiveStatus.Inactive || t.Status.LastActiveTime < DateTime.Now.AddMinutes(-5)));

            // 获取线路统计数据
            int registeredLineCount = 0;
            int unregisteredLineCount = 0;

            if (!string.IsNullOrEmpty(merchantId))
            {
                // 终端表中该商户的所有线路总数
                var totalLineCount = await _dbContext.Terminals
                    .Where(t => !t.IsDeleted && t.MerchantID == merchantId)
                    .Select(t => t.LineNO)
                    .Distinct()
                    .CountAsync();

                // 未注册线路统计：查询Terminals表中存在但LinePriceInfos表中不存在的线路
                unregisteredLineCount = await (from t in _dbContext.Terminals
                                              where !t.IsDeleted && t.MerchantID == merchantId
                                              group t by t.LineNO into g
                                              where !_dbContext.LinePriceInfos.Any(lp => lp.MerchantID == merchantId && (lp.LineNumber == g.Key || lp.LineNumber + lp.GroupNumber == g.Key))
                                              select g.Key).CountAsync();

                // 已注册线路数 = 总线路数 - 未注册线路数
                registeredLineCount = totalLineCount - unregisteredLineCount;
            }
            else if (isSystemAdmin)
            {
                // 系统管理员查看全部数据
                // 终端表中所有商户的线路总数
                var totalLineCount = await _dbContext.Terminals
                    .Where(t => !t.IsDeleted)
                    .Select(t => new { t.MerchantID, t.LineNO })
                    .Distinct()
                    .CountAsync();

                // 未注册线路统计
                unregisteredLineCount = await (from t in _dbContext.Terminals
                                              where !t.IsDeleted
                                              group t by new { t.MerchantID, t.LineNO } into g
                                              where !_dbContext.LinePriceInfos.Any(lp => lp.MerchantID == g.Key.MerchantID && (lp.LineNumber == g.Key.LineNO || lp.LineNumber + lp.GroupNumber == g.Key.LineNO))
                                              select g.Key).CountAsync();

                // 已注册线路数 = 总线路数 - 未注册线路数
                registeredLineCount = totalLineCount - unregisteredLineCount;
            }

            return new TerminalStatsDto
            {
                TotalCount = totalCount,
                ActiveCount = activeCount,
                InactiveCount = inactiveCount,
                RegisteredLineCount = registeredLineCount,
                UnregisteredLineCount = unregisteredLineCount
            };
        }

        // GET: api/Terminals/LineStats
        [HttpGet("linestats")]
        public async Task<ActionResult<PaginatedResult<LineStatsDetailDto>>> GetLineStats(
            [FromQuery] string? merchantId = null,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 10)
        {
            // 获取当前用户的商户ID
            var currentUserMerchantId = await _userService.GetUserMerchantIdAsync(User);
            var isSystemAdmin = User.IsInRole("SystemAdmin");

            // 如果不是系统管理员，只能查看自己商户的终端
            if (!isSystemAdmin && merchantId != null && merchantId != currentUserMerchantId)
            {
                return Forbid();
            }

            // 如果不是系统管理员且未指定商户ID，则使用当前用户的商户ID
            if (!isSystemAdmin && merchantId == null)
            {
                merchantId = currentUserMerchantId;
            }

            if (string.IsNullOrEmpty(merchantId))
            {
                return BadRequest("MerchantId is required");
            }

            try
            {
                // 使用原生SQL查询获取线路统计信息，性能更好
                var sql = @"
                    SELECT
                        t.LineNO,
                        COUNT(*) as Count,
                        COUNT(*) as TotalCount,
                        SUM(CASE
                            WHEN ts.ActiveStatus = 1 AND ts.LastActiveTime > DATE_SUB(NOW(), INTERVAL 5 MINUTE)
                            THEN 1 ELSE 0
                        END) as OnlineCount,
                        SUM(CASE
                            WHEN ts.ActiveStatus != 1 OR ts.LastActiveTime <= DATE_SUB(NOW(), INTERVAL 5 MINUTE) OR ts.ActiveStatus IS NULL
                            THEN 1 ELSE 0
                        END) as OfflineCount,
                        SUM(CASE
                            WHEN ts.ActiveStatus = 1 AND ts.LastActiveTime > DATE_SUB(NOW(), INTERVAL 5 MINUTE)
                            THEN 1 ELSE 0
                        END) as ActiveCount
                    FROM Terminals t
                    LEFT JOIN TerminalStatuses ts ON t.ID = ts.ID
                    WHERE t.MerchantID = {0} AND t.IsDeleted = 0
                    GROUP BY t.LineNO
                    ORDER BY t.LineNO
                    LIMIT {1} OFFSET {2}";

                var offset = (page - 1) * pageSize;

                // 执行查询
                var lineStats = await _dbContext.Database
                    .SqlQueryRaw<LineStatsDetailDto>(sql, merchantId, pageSize, offset)
                    .ToListAsync();

                // 使用EF Core查询获取总数，避免原生SQL的复杂性
                var totalCount = await _dbContext.Terminals
                    .Where(t => t.MerchantID == merchantId && !t.IsDeleted)
                    .Select(t => t.LineNO)
                    .Distinct()
                    .CountAsync();

                return new PaginatedResult<LineStatsDetailDto>
                {
                    Items = lineStats,
                    TotalCount = totalCount,
                    Page = page,
                    PageSize = pageSize
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting line stats for merchant {MerchantId}", merchantId);
                return StatusCode(500, "Internal server error");
            }
        }

        // GET: api/Terminals/Events - 获取所有终端事件列表
        [HttpGet("events")]
        public async Task<ActionResult<PaginatedResult<TerminalEventDto>>> GetAllTerminalEvents(
            [FromQuery] string? merchantId = null,
            [FromQuery] string? serialNo = null,
            [FromQuery] string? deviceNo = null,
            [FromQuery] TerminalEventType? eventType = null,
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 10)
        {
            // 获取当前用户的商户ID
            var currentUserMerchantId = await _userService.GetUserMerchantIdAsync(User);
            var isSystemAdmin = User.IsInRole("SystemAdmin");

            // 如果不是系统管理员，只能查看自己商户的事件
            if (!isSystemAdmin && merchantId != null && merchantId != currentUserMerchantId)
            {
                return Forbid();
            }

            // 构建查询 - 联接终端表和商户表以支持按序列号、设备编号筛选和获取商户名称
            var query = from e in _dbContext.TerminalEvents
                        join t in _dbContext.Terminals on e.TerminalID equals t.ID
                        join m in _dbContext.Merchants on e.MerchantID equals m.MerchantID
                        where !t.IsDeleted
                        select new { Event = e, Terminal = t, Merchant = m };

            // 商户筛选
            if (!isSystemAdmin)
            {
                // 非系统管理员只能查看自己商户的事件
                query = query.Where(x => x.Event.MerchantID == currentUserMerchantId);
            }
            else if (!string.IsNullOrEmpty(merchantId))
            {
                // 系统管理员可以筛选指定商户
                query = query.Where(x => x.Event.MerchantID == merchantId);
            }

            // 出厂序列号筛选
            if (!string.IsNullOrEmpty(serialNo))
            {
                query = query.Where(x => x.Terminal.MachineID.Contains(serialNo));
            }

            // 设备编号筛选
            if (!string.IsNullOrEmpty(deviceNo))
            {
                query = query.Where(x => x.Terminal.DeviceNO.Contains(deviceNo));
            }

            // 事件类型筛选
            if (eventType.HasValue)
            {
                query = query.Where(x => x.Event.EventType == eventType.Value);
            }

            // 时间范围筛选
            if (startDate.HasValue)
            {
                query = query.Where(x => x.Event.EventTime >= startDate.Value);
            }

            if (endDate.HasValue)
            {
                query = query.Where(x => x.Event.EventTime <= endDate.Value);
            }

            // 获取总记录数
            var count = await query.CountAsync();

            // 应用分页和排序
            var events = await query
                .OrderByDescending(x => x.Event.EventTime)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .Select(x => new TerminalEventDto
                {
                    ID = x.Event.ID,
                    MerchantID = x.Event.MerchantID,
                    TerminalID = x.Event.TerminalID,
                    EventTime = x.Event.EventTime,
                    EventName = x.Event.EventName,
                    EventType = x.Event.EventType,
                    Severity = x.Event.Severity,
                    Remark = x.Event.Remark,
                    Operator = x.Event.Operator,
                    // 添加终端信息
                    MachineID = x.Terminal.MachineID,
                    DeviceNO = x.Terminal.DeviceNO,
                    // 添加商户信息
                    MerchantName = x.Merchant.Name
                })
                .ToListAsync();

            return new PaginatedResult<TerminalEventDto>
            {
                Items = events,
                TotalCount = count,
                Page = page,
                PageSize = pageSize
            };
        }

        // GET: api/Terminals/5/Events
        [HttpGet("{id}/events")]
        public async Task<ActionResult<PaginatedResult<TerminalEventDto>>> GetTerminalEvents(
            string id,
            [FromQuery] TerminalEventType? eventType = null,
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 10)
        {
            // 获取当前用户的商户ID
            var currentUserMerchantId = await _userService.GetUserMerchantIdAsync(User);
            var isSystemAdmin = User.IsInRole("SystemAdmin");

            // 检查终端是否存在
            var terminal = await _dbContext.Terminals.FirstOrDefaultAsync(t => t.ID == id && !t.IsDeleted);
            if (terminal == null)
            {
                return NotFound();
            }

            // 如果不是系统管理员，只能查看自己商户的终端
            if (!isSystemAdmin && terminal.MerchantID != currentUserMerchantId)
            {
                return Forbid();
            }

            // 构建查询
            var query = _dbContext.TerminalEvents
                .Where(e => e.TerminalID == id)
                .AsQueryable();

            // 应用筛选条件
            if (eventType.HasValue)
            {
                query = query.Where(e => e.EventType == eventType.Value);
            }

            if (startDate.HasValue)
            {
                query = query.Where(e => e.EventTime >= startDate.Value);
            }

            if (endDate.HasValue)
            {
                query = query.Where(e => e.EventTime <= endDate.Value);
            }

            // 获取总记录数
            var count = await query.CountAsync();

            // 应用分页和排序
            var events = await query
                .OrderByDescending(e => e.EventTime)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            // 转换为DTO
            var eventDtos = events.Select(e => new TerminalEventDto
            {
                ID = e.ID,
                MerchantID = e.MerchantID,
                TerminalID = e.TerminalID,
                EventTime = e.EventTime,
                EventName = e.EventName,
                EventType = e.EventType,
                Severity = e.Severity,
                Remark = e.Remark,
                Operator = e.Operator
            }).ToList();

            return new PaginatedResult<TerminalEventDto>
            {
                Items = eventDtos,
                TotalCount = count,
                Page = page,
                PageSize = pageSize
            };
        }

        // POST: api/Terminals/SendMessage
        [HttpPost("SendMessage")]
        [RequireFeaturePermission("terminal.send_message")]
        public async Task<IActionResult> SendMessage([FromBody] SendMessageDto model)
        {
            // 获取当前用户的商户ID和用户名
            var currentUserMerchantId = await _userService.GetUserMerchantIdAsync(User);
            var isSystemAdmin = User.IsInRole("SystemAdmin");
            var username = UserService.GetUserNameForOperator(User);

            // 验证终端列表
            var terminals = await _dbContext.Terminals
                .Where(t => model.TerminalIds.Contains(t.ID) && !t.IsDeleted)
                .ToListAsync();

            if (terminals.Count == 0)
            {
                return BadRequest("No valid terminals found");
            }

            // 如果不是系统管理员，只能向自己商户的终端发送消息
            if (!isSystemAdmin && terminals.Any(t => t.MerchantID != currentUserMerchantId))
            {
                return Forbid();
            }

            // 验证消息类型 - 需要同时匹配消息类型代码和商户ID
            var msgType = await _dbContext.MsgTypes.FirstOrDefaultAsync(m => m.ID == model.MsgTypeCode && m.MerchantID == model.MerchantId);
            if (msgType == null)
            {
                return BadRequest("Invalid message type");
            }

            if (msgType.CodeType == MessageCodeType.HEX && model.Content != "")
            {
                //检查是否为16进制字符串
                if (Regex.IsMatch(model.Content, @"^[0-9A-Fa-f]+$") == false)
                {
                    return BadRequest("当前消息内容须为16进制字符串");
                }
            }
            // 创建消息内容
            var msgContent = new MsgContent
            {
                MerchantID = terminals.First().MerchantID,
                MsgTypeID = model.MsgTypeCode,
                Content = model.Content,
                CreateTime = DateTime.Now,
                Operator = username
            };

            await _dbContext.MsgContents.AddAsync(msgContent);
            await _dbContext.SaveChangesAsync();

            // 为每个终端创建消息
            var msgBoxes = terminals.Select(t => new MsgBox
            {
                MerchantID = t.MerchantID,
                TerminalID = t.ID,
                MsgContentID = msgContent.ID,
                SendTime = DateTime.Now,
                Status = MessageStatus.Unread,
                ReadTime = null
            }).ToList();

            await _dbContext.MsgBoxes.AddRangeAsync(msgBoxes);
            await _dbContext.SaveChangesAsync();

            // 记录事件
            foreach (var terminal in terminals)
            {
                await _msgboxEventService.Publish(new MsgboxEventMessage
                {
                    ID = msgContent.ID,
                    ActionType = MsgboxEventActionType.Send,
                    MerchantID = msgContent.MerchantID,
                    TerminalID = terminal.ID,
                    ActionTime = DateTime.Now
                });

                await _terminalEventPublishService.PublishTerminalEventAsync(new TerminalEventMessage
                {
                    MerchantID = terminal.MerchantID,
                    TerminalID = terminal.ID,
                    EventType = TerminalEventType.MessageSent,
                    Severity = EventSeverity.Info,
                    Remark = $"Message sent: Type={model.MsgTypeCode}, Content={model.Content}",
                    Operator = username
                });
            }

            return Ok(new { MessageId = msgContent.ID, TerminalCount = terminals.Count });
        }

        // POST: api/Terminals/PublishFile
        [HttpPost("PublishFile")]
        public async Task<IActionResult> PublishFile([FromBody] PublishFileDto model)
        {
            // 获取当前用户的商户ID和用户名
            var currentUserMerchantId = await _userService.GetUserMerchantIdAsync(User);
            var isSystemAdmin = User.IsInRole("SystemAdmin");
            var username = UserService.GetUserNameForOperator(User);

            // 验证文件版本
            var fileVer = await _dbContext.FileVers.FirstOrDefaultAsync(f => f.ID == model.FileVerId && !f.IsDelete);
            if (fileVer == null)
            {
                return BadRequest("Invalid file version");
            }

            // 验证终端列表(文件版本的商户需要和终端的商户ID一致)
            var terminals = await _dbContext.Terminals
                .Where(t => model.TerminalIds.Contains(t.ID) && t.MerchantID == fileVer.MerchantID && !t.IsDeleted)
                .ToListAsync();

            if (terminals.Count == 0)
            {
                return BadRequest("No valid terminals found");
            }

            // 如果不是系统管理员，只能向自己商户的终端发布文件
            if (!isSystemAdmin && terminals.Any(t => t.MerchantID != currentUserMerchantId))
            {
                return Forbid();
            }

            foreach (var terminal in model.TerminalIds)
            {

                var now=DateTime.Now;
                // 检查该文件类型和发布目标与发布类型是否已存在
                var filePublish = await _dbContext.FilePublishs
                    .FirstOrDefaultAsync(p =>p.FileFullType == fileVer.FileFullType
                                        && p.PublishType == PublishTypeOption.Terminal
                                        && p.PublishTarget == terminal
                                        && p.MerchantID == fileVer.MerchantID);
                if (filePublish != null)
                {
                    if(filePublish.FileVerID == fileVer.ID)
                    {
                        return Conflict("该发布已存在");
                    }
                    //存在其它版本的发布，更新版本信息
                    filePublish.FileVerID = fileVer.ID;
                    filePublish.Ver = fileVer.Ver;
                    filePublish.FileSize = fileVer.FileSize;
                    filePublish.Crc = fileVer.Crc;
                    filePublish.UploadFileID = fileVer.UploadFileID;
                    filePublish.PublishTime = now;
                    filePublish.Operator = username;
                }
                else
                {
                    // 创建文件发布记录
                    filePublish = new FilePublish
                    {
                        MerchantID = fileVer.MerchantID,
                        FileTypeID = fileVer.FileTypeID,
                        FilePara = fileVer.FilePara,
                        FileFullType = fileVer.FileFullType,
                        Ver = fileVer.Ver,
                        FileSize = fileVer.FileSize,
                        Crc = fileVer.Crc,
                        FileVerID = fileVer.ID,
                        UploadFileID = fileVer.UploadFileID,
                        PublishType = PublishTypeOption.Terminal,
                        PublishTarget = terminal,
                        PublishTime = DateTime.Now,
                        Operator = username
                    };

                    await _dbContext.FilePublishs.AddAsync(filePublish);
                }

                // 创建文件发布历史记录
                var filePublishHistory = new FilePublishHistory
                {
                    MerchantID = terminals.First().MerchantID,
                    FileTypeID = fileVer.FileTypeID,
                    FilePara = fileVer.FilePara,
                    FileFullType = fileVer.FileFullType,
                    Ver = fileVer.Ver,
                    FileSize = fileVer.FileSize,
                    Crc = fileVer.Crc,
                    FileVerID = fileVer.ID,
                    UploadFileID = fileVer.UploadFileID,
                    PublishType = PublishTypeOption.Terminal,
                    PublishTarget = terminal,
                    PublishTime = filePublish.PublishTime,
                    Operator = username
                };
                await _dbContext.FilePublishHistories.AddAsync(filePublishHistory);

                await _filePublishEventService.Publish(new FilePublishEventMessage
                {
                    ActionType = FilePublishEventActionType.Publish,
                    FilePublishID = filePublish.ID,
                    MerchantID = filePublish.MerchantID,
                    FileTypeID = filePublish.FileTypeID,
                    FilePara = filePublish.FilePara,
                    FileFullType = filePublish.FileFullType,
                    Ver = filePublish.Ver,
                    FileCrc = filePublish.Crc,
                    FileSize = filePublish.FileSize,
                    Operator = filePublish.Operator,
                    PublishTarget = filePublish.PublishTarget,
                    PublishType = filePublish.PublishType,
                    ActionTime = filePublish.PublishTime
                });

            }
            await _dbContext.SaveChangesAsync();


            // 记录事件
            foreach (var terminal in terminals)
            {
                await _terminalEventPublishService.PublishTerminalEventAsync(new TerminalEventMessage
                {
                    MerchantID = terminal.MerchantID,
                    TerminalID = terminal.ID,
                    EventType = TerminalEventType.FilePublished,
                    Severity = EventSeverity.Info,
                    //Remark = $"File published: Type={fileVer.FileFullType}, Version={fileVer.Ver}",
                    Remark = $"指定终端发布文件: 类型={fileVer.FileFullType}, 版本={fileVer.Ver}",
                    Operator = username
                });

            }

            return Ok(new {TerminalCount = terminals.Count });
        }

        // POST: api/Terminals/Export
        [HttpPost("export")]
        public async Task<IActionResult> ExportTerminals([FromBody] ExportTerminalsRequest request)
        {
            // 获取当前用户的商户ID
            var currentUserMerchantId = await _userService.GetUserMerchantIdAsync(User);
            var isSystemAdmin = User.IsInRole("SystemAdmin");

            // 如果不是系统管理员，只能查看自己商户的终端
            if (!isSystemAdmin && request.MerchantId != null && request.MerchantId != currentUserMerchantId)
            {
                return Forbid();
            }

            // 如果不是系统管理员且未指定商户ID，则使用当前用户的商户ID
            if (!isSystemAdmin && request.MerchantId == null)
            {
                request.MerchantId = currentUserMerchantId;
            }

            // 构建查询，包含商户、车辆和线路信息
            var query = (from terminal in _dbContext.Terminals.Include(t => t.Status)
                        join merchant in _dbContext.Merchants on terminal.MerchantID equals merchant.MerchantID
                        join vehicle in _dbContext.VehicleInfos on new { terminal.MerchantID, terminal.DeviceNO } equals new { vehicle.MerchantID, vehicle.DeviceNO } into vehicleGroup
                        from vehicle in vehicleGroup.DefaultIfEmpty()
                        join linePrice in _dbContext.LinePriceInfos on new { terminal.MerchantID, LineNumber = terminal.LineNO } equals new { linePrice.MerchantID, LineNumber = linePrice.LineNumber + linePrice.GroupNumber } into linePriceGroup
                        from linePrice in linePriceGroup.DefaultIfEmpty()
                        where !terminal.IsDeleted && !merchant.IsDelete && (vehicle == null || vehicle.IsActive)
                        select new { Terminal = terminal, Merchant = merchant, Vehicle = vehicle, LinePrice = linePrice })
                        .AsQueryable();

            // 应用筛选条件
            if (!string.IsNullOrEmpty(request.MerchantId))
            {
                query = query.Where(tm => tm.Terminal.MerchantID == request.MerchantId);
            }

            if (!string.IsNullOrEmpty(request.LineNo))
            {
                query = query.Where(tm => tm.Terminal.LineNO == request.LineNo);
            }

            if (!string.IsNullOrEmpty(request.DeviceNo))
            {
                query = query.Where(tm => tm.Terminal.DeviceNO == request.DeviceNo);
            }

            if (!string.IsNullOrEmpty(request.MachineId))
            {
                query = query.Where(tm => tm.Terminal.MachineID == request.MachineId);
            }

            if (!string.IsNullOrEmpty(request.TerminalType))
            {
                query = query.Where(tm => tm.Terminal.TerminalType == request.TerminalType);
            }

            if (request.ActiveStatus.HasValue)
            {
                query = query.Where(tm => tm.Terminal.Status != null && tm.Terminal.Status.ActiveStatus == request.ActiveStatus.Value);
            }

            // 添加车牌号筛选
            if (!string.IsNullOrEmpty(request.LicensePlate))
            {
                query = query.Where(tm => tm.Vehicle != null && tm.Vehicle.LicensePlate.Contains(request.LicensePlate));
            }

            // 获取终端列表
            var terminalsWithMerchants = await query.ToListAsync();

            // 获取字段配置
            List<ExportFieldConfig> fieldConfigs;
            if (request.Fields != null && request.Fields.Count > 0)
            {
                fieldConfigs = request.Fields;
                _logger.LogInformation($"使用前端传递的字段配置: {fieldConfigs.Count} 个字段");
                foreach (var config in fieldConfigs)
                {
                    _logger.LogInformation($"字段: {config.Key}, 显示名: {config.DisplayName}, 数据路径: {config.DataPath}, 类型: {config.Type}");
                }
            }
            else
            {
                _logger.LogInformation("未提供字段配置，使用默认字段");
                // 使用默认字段
                fieldConfigs = GetDefaultFieldConfigs();
            }

            // 转换为CSV格式
            var csv = new System.Text.StringBuilder();

            // 构建CSV头部
            var headers = fieldConfigs.Select(f => f.DisplayName);
            csv.AppendLine(string.Join(",", headers));

            // 构建数据行
            foreach (var terminalWithMerchant in terminalsWithMerchants)
            {
                var values = new List<string>();
                foreach (var fieldConfig in fieldConfigs)
                {
                    var value = GetFieldValueFromTerminalWithMerchant(terminalWithMerchant, fieldConfig);
                    // 处理CSV中的特殊字符
                    var csvValue = value?.ToString()?.Replace(",", "，").Replace("\n", " ").Replace("\r", " ") ?? "";
                    values.Add($"\"{csvValue}\"");
                }
                csv.AppendLine(string.Join(",", values));
            }

            // 返回CSV文件，使用UTF-8 BOM编码以确保Excel正确显示中文
            var csvBytes = System.Text.Encoding.UTF8.GetPreamble()
                .Concat(System.Text.Encoding.UTF8.GetBytes(csv.ToString()))
                .ToArray();

            return File(csvBytes, "text/csv; charset=utf-8", "terminals.csv");
        }

        /// <summary>
        /// 获取默认字段配置
        /// </summary>
        private static List<ExportFieldConfig> GetDefaultFieldConfigs()
        {
            return new List<ExportFieldConfig>
            {
                new() { Key = "id", DisplayName = "ID", DataPath = "id", Type = "text" },
                new() { Key = "merchantID", DisplayName = "商户ID", DataPath = "merchantID", Type = "text" },
                new() { Key = "merchantName", DisplayName = "商户名称", DataPath = "merchantName", Type = "text" },
                new() { Key = "machineID", DisplayName = "出厂序列号", DataPath = "machineID", Type = "text" },
                new() { Key = "deviceNO", DisplayName = "设备编号", DataPath = "deviceNO", Type = "text" },
                new() { Key = "lineNO", DisplayName = "线路编号", DataPath = "lineNO", Type = "text" },
                new() { Key = "lineName", DisplayName = "线路名称", DataPath = "lineName", Type = "text" },
                new() { Key = "licensePlate", DisplayName = "车牌号", DataPath = "licensePlate", Type = "text" },
                new() { Key = "terminalType", DisplayName = "终端类型", DataPath = "terminalType", Type = "text" },
                new() { Key = "createTime", DisplayName = "注册时间", DataPath = "createTime", Type = "datetime" },
                new() { Key = "status", DisplayName = "状态", DataPath = "status", Type = "status" },
                new() { Key = "lastActiveTime", DisplayName = "最后活跃时间", DataPath = "status.lastActiveTime", Type = "datetime" }
            };
        }

        /// <summary>
        /// 根据字段配置获取终端字段值
        /// </summary>
        private static object? GetFieldValue(Terminal terminal, ExportFieldConfig fieldConfig)
        {
            try
            {
                var value = GetNestedValue(terminal, fieldConfig.DataPath);
                Console.WriteLine($"字段 {fieldConfig.Key} (路径: {fieldConfig.DataPath}) 的值: {value}");

                // 根据字段类型进行特殊处理
                return fieldConfig.Type switch
                {
                    "datetime" => value is DateTime dt ? dt.ToString("yyyy-MM-dd HH:mm:ss") : value?.ToString(),
                    "date" => value is DateTime d ? d.ToString("yyyy-MM-dd") : value?.ToString(),
                    "status" => FormatStatusValue(terminal.Status),
                    "fileVersion" => FormatFileVersionValue(terminal, fieldConfig),
                    _ => value?.ToString() ?? ""
                };
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取字段 {fieldConfig.Key} 值时出错: {ex.Message}");
                return "";
            }
        }

        /// <summary>
        /// 获取嵌套属性值
        /// </summary>
        private static object? GetNestedValue(object obj, string path)
        {
            if (obj == null || string.IsNullOrEmpty(path)) return null;

            var current = obj;
            var parts = path.Split('.');

            foreach (var part in parts)
            {
                if (current == null) return null;

                var property = current.GetType().GetProperty(part, BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance);
                if (property == null) return null;

                current = property.GetValue(current);
            }

            return current;
        }

        /// <summary>
        /// 格式化状态值
        /// </summary>
        private static string FormatStatusValue(TerminalStatus? status)
        {
            if (status == null) return "未知";

            // 判断是否在线（5分钟内活跃且状态为激活）
            var isOnline = status.ActiveStatus == DeviceActiveStatus.Active &&
                          status.LastActiveTime != DateTime.MinValue &&
                          (DateTime.Now - status.LastActiveTime).TotalMinutes < 5;

            if (isOnline)
            {
                return "在线";
            }
            else
            {
                // 计算离线时长
                if (status.LastActiveTime != DateTime.MinValue)
                {
                    var offlineTime = DateTime.Now - status.LastActiveTime;
                    if (offlineTime.TotalDays >= 1)
                        return $"离线{(int)offlineTime.TotalDays}天";
                    else if (offlineTime.TotalHours >= 1)
                        return $"离线{(int)offlineTime.TotalHours}小时";
                    else
                        return $"离线{(int)offlineTime.TotalMinutes}分钟";
                }
                return "离线";
            }
        }

        /// <summary>
        /// 格式化文件版本值
        /// </summary>
        private static string FormatFileVersionValue(Terminal terminal, ExportFieldConfig fieldConfig)
        {
            try
            {
                if (terminal.Status?.FileVersionMetadata == null) return "";

                // 如果有配置信息，使用配置的版本键
                if (fieldConfig.Config != null)
                {
                    var configJson = JsonSerializer.Serialize(fieldConfig.Config);
                    var config = JsonSerializer.Deserialize<Dictionary<string, object>>(configJson);

                    if (config != null && config.TryGetValue("versionKeys", out var versionKeysObj))
                    {
                        var versionKeysJson = JsonSerializer.Serialize(versionKeysObj);
                        var versionKeys = JsonSerializer.Deserialize<string[]>(versionKeysJson);

                        if (versionKeys != null && versionKeys.Length > 0)
                        {
                            var versions = new List<string>();
                            foreach (var key in versionKeys)
                            {
                                // 替换模板变量
                                var actualKey = key.Replace("{terminalType}", terminal.TerminalType);

                                if (terminal.Status.FileVersionMetadata.TryGetValue(actualKey, out var versionInfo))
                                {
                                    var versionStr = versionInfo.Current;
                                    if (config.TryGetValue("showVersionKey", out var showKeyObj) &&
                                        showKeyObj.ToString().Equals("true", StringComparison.OrdinalIgnoreCase))
                                    {
                                        versionStr = $"{actualKey}: {versionStr}";
                                    }
                                    versions.Add(versionStr);
                                }
                            }
                            return string.Join(", ", versions);
                        }
                    }
                }

                // 默认显示所有版本信息
                var allVersions = terminal.Status.FileVersionMetadata
                    .Select(kv => $"{kv.Key}: {kv.Value.Current}")
                    .ToList();

                return string.Join(", ", allVersions);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"格式化文件版本时出错: {ex.Message}");
                return "";
            }
        }

        /// <summary>
        /// 从包含商户信息的终端数据中获取字段值
        /// </summary>
        private static object? GetFieldValueFromTerminalWithMerchant(dynamic terminalWithMerchant, ExportFieldConfig fieldConfig)
        {
            try
            {
                var terminal = terminalWithMerchant.Terminal;
                var merchant = terminalWithMerchant.Merchant;
                var vehicle = terminalWithMerchant.Vehicle;
                var linePrice = terminalWithMerchant.LinePrice;

                // 直接根据字段路径获取值，避免创建复杂的匿名对象
                var value = GetFieldValueByPath(terminal, merchant, vehicle, linePrice, fieldConfig.DataPath);
                Console.WriteLine($"字段 {fieldConfig.Key} (路径: {fieldConfig.DataPath}) 的值: {value}");

                // 根据字段类型进行特殊处理
                return fieldConfig.Type switch
                {
                    "datetime" => value is DateTime dt ? dt.ToString("yyyy-MM-dd HH:mm:ss") : value?.ToString(),
                    "date" => value is DateTime d ? d.ToString("yyyy-MM-dd") : value?.ToString(),
                    "status" => FormatStatusValue(terminal.Status),
                    "fileVersion" => FormatFileVersionValue(terminal, fieldConfig),
                    _ => value?.ToString() ?? ""
                };
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取字段 {fieldConfig.Key} 值时出错: {ex.Message}");
                return "";
            }
        }

        /// <summary>
        /// 根据路径获取字段值
        /// </summary>
        private static object? GetFieldValueByPath(Terminal terminal, Merchant merchant, dynamic vehicle, dynamic linePrice, string dataPath)
        {
            if (string.IsNullOrEmpty(dataPath)) return null;

            // 处理商户相关字段
            if (dataPath.Equals("merchantName", StringComparison.OrdinalIgnoreCase))
            {
                return merchant.Name;
            }

            // 处理终端基本字段
            switch (dataPath.ToLower())
            {
                case "id":
                    return terminal.ID;
                case "merchantid":
                    return terminal.MerchantID;
                case "machineid":
                    return terminal.MachineID;
                case "deviceno":
                    return terminal.DeviceNO;
                case "lineno":
                    return terminal.LineNO;
                case "terminaltype":
                    return terminal.TerminalType;
                case "createtime":
                    return terminal.CreateTime;
                case "status":
                    return terminal.Status;
                case "licenseplate":
                    return vehicle?.LicensePlate;
                case "linename":
                    return linePrice?.LineName;
                default:
                    // 处理嵌套路径（如 status.lastActiveTime）
                    if (dataPath.StartsWith("status.", StringComparison.OrdinalIgnoreCase))
                    {
                        if (terminal.Status == null) return null;

                        var statusPath = dataPath.Substring(7); // 移除 "status." 前缀

                        // 特殊处理 propertyMetadata 路径
                        if (statusPath.StartsWith("propertyMetadata.", StringComparison.OrdinalIgnoreCase))
                        {
                            var propertyKey = statusPath.Substring(17); // 移除 "propertyMetadata." 前缀
                            if (terminal.Status.PropertyMetadata != null &&
                                terminal.Status.PropertyMetadata.TryGetValue(propertyKey, out var propertyValue))
                            {
                                return propertyValue;
                            }
                            return null;
                        }

                        // 特殊处理 fileVersionMetadata 路径
                        if (statusPath.StartsWith("fileVersionMetadata.", StringComparison.OrdinalIgnoreCase))
                        {
                            var versionPath = statusPath.Substring(20); // 移除 "fileVersionMetadata." 前缀
                            var parts = versionPath.Split('.');

                            if (parts.Length >= 2 && terminal.Status.FileVersionMetadata != null)
                            {
                                var versionKey = parts[0];
                                var property = parts[1];

                                if (terminal.Status.FileVersionMetadata.TryGetValue(versionKey, out var versionInfo))
                                {
                                    return property.ToLower() switch
                                    {
                                        "current" => versionInfo.Current,
                                        "expected" => versionInfo.Expected,
                                        _ => null
                                    };
                                }
                            }
                            return null;
                        }

                        return GetNestedValue(terminal.Status, statusPath);
                    }

                    // 其他情况，尝试直接从terminal获取
                    return GetNestedValue(terminal, dataPath);
            }
        }
    }
}
