using System.ComponentModel.DataAnnotations;

namespace SlzrCrossGate.WebAdmin.DTOs
{
    /// <summary>
    /// 审计日志查询基础DTO
    /// </summary>
    public class AuditLogQueryDto
    {
        /// <summary>
        /// 用户名
        /// </summary>
        public string? UserName { get; set; }

        /// <summary>
        /// 真实姓名
        /// </summary>
        public string? RealName { get; set; }

        /// <summary>
        /// 商户ID
        /// </summary>
        public string? MerchantId { get; set; }

        /// <summary>
        /// IP地址
        /// </summary>
        public string? IpAddress { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime? StartTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime? EndTime { get; set; }

        /// <summary>
        /// 操作结果
        /// </summary>
        public bool? IsSuccess { get; set; }

        /// <summary>
        /// 页码
        /// </summary>
        public int Page { get; set; } = 1;

        /// <summary>
        /// 每页大小
        /// </summary>
        public int PageSize { get; set; } = 10;
    }

    /// <summary>
    /// 登录日志查询DTO
    /// </summary>
    public class LoginLogQueryDto : AuditLogQueryDto
    {
        /// <summary>
        /// 登录类型
        /// </summary>
        public string? LoginType { get; set; }

        /// <summary>
        /// 登录方式
        /// </summary>
        public string? LoginMethod { get; set; }
    }

    /// <summary>
    /// 密码修改日志查询DTO
    /// </summary>
    public class PasswordChangeLogQueryDto : AuditLogQueryDto
    {
        /// <summary>
        /// 修改类型
        /// </summary>
        public string? ChangeType { get; set; }

        /// <summary>
        /// 目标用户名
        /// </summary>
        public string? TargetUserName { get; set; }
    }

    /// <summary>
    /// 操作日志查询DTO
    /// </summary>
    public class OperationLogQueryDto : AuditLogQueryDto
    {
        /// <summary>
        /// 操作模块
        /// </summary>
        public string? Module { get; set; }

        /// <summary>
        /// 操作类型
        /// </summary>
        public string? OperationType { get; set; }

        /// <summary>
        /// 操作对象
        /// </summary>
        public string? OperationTarget { get; set; }

        /// <summary>
        /// 请求路径
        /// </summary>
        public string? RequestPath { get; set; }

        /// <summary>
        /// HTTP方法
        /// </summary>
        public string? HttpMethod { get; set; }
    }

    /// <summary>
    /// 登录日志响应DTO
    /// </summary>
    public class LoginLogDto
    {
        public int Id { get; set; }
        public string? UserId { get; set; }
        public string? UserName { get; set; }
        public string? RealName { get; set; }
        public string? MerchantId { get; set; }
        public string? MerchantName { get; set; }
        public string? IpAddress { get; set; }
        public string? UserAgent { get; set; }
        public DateTime OperationTime { get; set; }
        public bool IsSuccess { get; set; }
        public string? FailureReason { get; set; }
        public string? Remarks { get; set; }
        public string? LoginType { get; set; }
        public string? LoginMethod { get; set; }
        public string? SessionId { get; set; }
        public DateTime? LogoutTime { get; set; }
    }

    /// <summary>
    /// 密码修改日志响应DTO
    /// </summary>
    public class PasswordChangeLogDto
    {
        public int Id { get; set; }
        public string? UserId { get; set; }
        public string? UserName { get; set; }
        public string? RealName { get; set; }
        public string? MerchantId { get; set; }
        public string? MerchantName { get; set; }
        public string? IpAddress { get; set; }
        public string? UserAgent { get; set; }
        public DateTime OperationTime { get; set; }
        public bool IsSuccess { get; set; }
        public string? FailureReason { get; set; }
        public string? Remarks { get; set; }
        public string? ChangeType { get; set; }
        public string? TargetUserId { get; set; }
        public string? TargetUserName { get; set; }
        public string? TargetRealName { get; set; }
    }

    /// <summary>
    /// 操作日志响应DTO
    /// </summary>
    public class OperationLogDto
    {
        public int Id { get; set; }
        public string? UserId { get; set; }
        public string? UserName { get; set; }
        public string? RealName { get; set; }
        public string? MerchantId { get; set; }
        public string? MerchantName { get; set; }
        public string? IpAddress { get; set; }
        public string? UserAgent { get; set; }
        public DateTime OperationTime { get; set; }
        public bool IsSuccess { get; set; }
        public string? FailureReason { get; set; }
        public string? Remarks { get; set; }
        public string? Module { get; set; }
        public string? OperationType { get; set; }
        public string? OperationTarget { get; set; }
        public string? OperationDetails { get; set; }
        public string? RequestPath { get; set; }
        public string? HttpMethod { get; set; }
        public int? ResponseStatusCode { get; set; }
        public long? ExecutionTime { get; set; }
    }

    /// <summary>
    /// 审计日志统计DTO
    /// </summary>
    public class AuditLogStatsDto
    {
        /// <summary>
        /// 今日登录次数
        /// </summary>
        public int TodayLogins { get; set; }

        /// <summary>
        /// 今日登录失败次数
        /// </summary>
        public int TodayLoginFailures { get; set; }

        /// <summary>
        /// 今日密码修改次数
        /// </summary>
        public int TodayPasswordChanges { get; set; }

        /// <summary>
        /// 今日操作次数
        /// </summary>
        public int TodayOperations { get; set; }

        /// <summary>
        /// 本周登录次数
        /// </summary>
        public int WeekLogins { get; set; }

        /// <summary>
        /// 本月登录次数
        /// </summary>
        public int MonthLogins { get; set; }

        /// <summary>
        /// 活跃用户数 (本周)
        /// </summary>
        public int ActiveUsers { get; set; }
    }
}
