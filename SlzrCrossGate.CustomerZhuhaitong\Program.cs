using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using Serilog;
using SlzrCrossGate.CustomerZhuhaitong.Data;
using SlzrCrossGate.CustomerZhuhaitong.Models;
using SlzrCrossGate.CustomerZhuhaitong.Services;
using System.Text;

var builder = WebApplication.CreateBuilder(args);

// 配置Serilog
Log.Logger = new LoggerConfiguration()
    .ReadFrom.Configuration(builder.Configuration)
    .Enrich.WithProperty("Service", "SlzrCrossGate.CustomerZhuhaitong")
    .Enrich.WithProperty("Customer", "zhuhaitong")
    .CreateLogger();

builder.Host.UseSerilog();

// 添加服务
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// 配置数据库连接
// 只读认证上下文 - 不参与迁移
builder.Services.AddDbContext<AuthReadOnlyContext>(options =>
{
    options.UseMySql(
        builder.Configuration.GetConnectionString("AuthReadOnlyConnection"),
        ServerVersion.Create(8, 0, 0, Pomelo.EntityFrameworkCore.MySql.Infrastructure.ServerType.MySql)
    );
    // 禁用变更跟踪，提高性能
    options.UseQueryTrackingBehavior(QueryTrackingBehavior.NoTracking);
});

// 客户数据上下文 - 不使用迁移，只映射现有表
builder.Services.AddDbContext<CustomerDataContext>(options =>
    options.UseMySql(
        builder.Configuration.GetConnectionString("DefaultConnection"),
        ServerVersion.Create(8, 0, 0, Pomelo.EntityFrameworkCore.MySql.Infrastructure.ServerType.MySql)
    ));

// 注册服务
builder.Services.AddScoped<ReadOnlyAuthService>();
builder.Services.AddScoped<FtpFileStatsService>();

// 设置客户表前缀环境变量
Environment.SetEnvironmentVariable("CUSTOMER_TABLE_PREFIX", "Customer_Zhuhaitong");

// 设置客户路径配置
Environment.SetEnvironmentVariable("CUSTOMER_PATH_TYPE", "customer");
Environment.SetEnvironmentVariable("CUSTOMER_BASE_PATH", "/zhuhaitong");

// 配置JWT认证（与主项目保持一致）
builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddJwtBearer(options =>
    {
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidateAudience = true,
            ValidateLifetime = true,
            ValidateIssuerSigningKey = true,
            ValidIssuer = builder.Configuration["Jwt:Issuer"],
            ValidAudience = builder.Configuration["Jwt:Audience"],
            IssuerSigningKey = new SymmetricSecurityKey(
                Encoding.UTF8.GetBytes(builder.Configuration["Jwt:Key"] ?? "")),
            // 关键设置：确保JWT的Claims被正确映射
            NameClaimType = System.Security.Claims.ClaimTypes.Name,
            RoleClaimType = System.Security.Claims.ClaimTypes.Role
        };
    });

// 配置CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowMainApp", policy =>
    {
        policy.WithOrigins(builder.Configuration["MainApp:BaseUrl"] ?? "http://localhost:5270")
              .AllowAnyHeader()
              .AllowAnyMethod()
              .AllowCredentials();
    });
});

var app = builder.Build();

// 配置请求管道
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseCors("AllowMainApp");

// 配置静态文件服务 - 支持基础路径
app.UseStaticFiles();

app.UseRouting();
app.UseAuthentication();
app.UseAuthorization();

// 配置基础路径支持
var basePath = Environment.GetEnvironmentVariable("ASPNETCORE_BASEPATH") ?? "/customa";
if (!string.IsNullOrEmpty(basePath) && basePath != "/")
{
    app.UsePathBase(basePath);
}

app.MapControllers();

// SPA回退路由 - 支持基础路径
app.MapFallbackToFile("index.html");

app.Run();
