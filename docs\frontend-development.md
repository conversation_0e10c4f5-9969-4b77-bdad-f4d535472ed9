# 前端开发规范

## 📋 概述

基于实际项目代码分析的 WebAdmin 前端开发规范和技术实现。

## 🛠️ 技术栈

### 核心框架 (基于 package.json)
```json
{
  "react": "^18.2.0",
  "react-dom": "^18.2.0",
  "react-router-dom": "^6.22.1",
  "@mui/material": "^5.15.10",
  "@mui/icons-material": "^5.15.10",
  "@mui/lab": "^5.0.0-alpha.165",
  "@mui/x-data-grid": "^6.19.4",
  "@mui/x-date-pickers": "^6.20.2"
}
```

### 工具库
```json
{
  "axios": "^1.6.7",
  "formik": "^2.4.5",
  "yup": "^1.3.3",
  "notistack": "^3.0.1",
  "jwt-decode": "^4.0.0",
  "chart.js": "^4.4.1",
  "recharts": "^2.15.3",
  "qrcode.react": "^4.2.0",
  "date-fns": "^2.29.3"
}
```

### 构建工具
```json
{
  "vite": "^5.1.0",
  "@vitejs/plugin-react": "^4.2.1"
}
```

## 🏗️ 项目结构

### 实际目录结构
```
ClientApp/src/
├── components/           # 公共组件
│   ├── MerchantAutocomplete.jsx  # 商户下拉选择组件
│   ├── VersionManager.jsx        # 版本管理组件
│   └── ...
├── contexts/            # React Context
│   ├── AuthContext.jsx  # 认证上下文
│   ├── ThemeContext.jsx # 主题上下文
│   └── ...
├── pages/              # 页面组件
│   ├── auth/           # 认证相关页面
│   ├── dashboard/      # 仪表盘
│   ├── terminals/      # 终端管理
│   ├── files/          # 文件管理
│   ├── messages/       # 消息管理
│   ├── users/          # 用户管理
│   ├── merchants/      # 商户管理
│   ├── settings/       # 系统设置
│   └── ...
├── utils/              # 工具函数
├── App.jsx            # 应用入口
├── main.jsx           # 主入口
└── routes.jsx         # 路由配置
```

## 🎨 UI 设计规范

### Material-UI 主题
- **基础主题**: 使用 Material-UI 5.15.10
- **主题切换**: 支持亮色/暗色主题切换
- **响应式设计**: 支持桌面端和移动端适配

### 组件使用规范
- **表格组件**: 使用 `@mui/x-data-grid` 进行数据展示
- **日期选择**: 使用 `@mui/x-date-pickers` 组件
- **图标**: 使用 `@mui/icons-material` 和 `react-feather`
- **通知**: 使用 `notistack` 进行消息提示

## 📝 表单处理

### Formik + Yup 验证
```jsx
// 标准表单实现模式
import { Formik, Form, Field } from 'formik';
import * as Yup from 'yup';

const validationSchema = Yup.object({
  username: Yup.string().required('用户名必填'),
  password: Yup.string().min(6, '密码至少6位').required('密码必填')
});

// 表单组件实现
<Formik
  initialValues={{ username: '', password: '' }}
  validationSchema={validationSchema}
  onSubmit={handleSubmit}
>
  {/* 表单内容 */}
</Formik>
```

## 🌐 网络请求

### Axios 配置
```javascript
// 基于实际项目的 API 配置
const api = axios.create({
  baseURL: '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
});

// 请求拦截器 - 添加认证令牌
api.interceptors.request.use(config => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});
```

## 🔐 认证管理

### JWT 令牌处理
```jsx
// 基于 jwt-decode 的令牌解析
import jwtDecode from 'jwt-decode';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within AuthProvider');
  }
  return context;
};
```

### 路由保护
```jsx
// 基于认证状态的路由保护
import { Navigate } from 'react-router-dom';

const ProtectedRoute = ({ children }) => {
  const { isAuthenticated } = useAuth();
  return isAuthenticated ? children : <Navigate to="/login" />;
};
```

## 📊 数据可视化

### Chart.js + Recharts
```jsx
// 图表组件实现
import { Chart as ChartJS } from 'chart.js';
import { Line, Bar } from 'react-chartjs-2';
import { LineChart, BarChart } from 'recharts';

// 根据数据类型选择合适的图表库
// Chart.js - 复杂交互图表
// Recharts - 简单数据展示
```

## 🔧 开发配置

### Vite 配置 (vite.config.js)
```javascript
export default defineConfig({
  plugins: [react()],
  optimizeDeps: {
    esbuildOptions: {
      loader: {
        '.js': 'jsx',  // 支持 .js 文件中的 JSX
      },
    },
  },
  server: {
    port: 3000,
    proxy: {
      '/api': {
        target: 'https://localhost:7296',
        changeOrigin: true,
        secure: false,
      }
    }
  }
});
```

## 📱 响应式设计

### Material-UI 断点
```jsx
// 使用 Material-UI 的响应式系统
import { useTheme, useMediaQuery } from '@mui/material';

const theme = useTheme();
const isMobile = useMediaQuery(theme.breakpoints.down('md'));

// 根据屏幕尺寸调整布局
<Grid container spacing={isMobile ? 1 : 2}>
  <Grid item xs={12} md={6}>
    {/* 内容 */}
  </Grid>
</Grid>
```

## 🎯 组件开发规范

### 统一组件模式
```jsx
// 商户选择组件示例 (MerchantAutocomplete)
import { Autocomplete, TextField } from '@mui/material';

const MerchantAutocomplete = ({ 
  value, 
  onChange, 
  label = "商户", 
  size = "small",
  ...props 
}) => {
  return (
    <Autocomplete
      value={value}
      onChange={onChange}
      options={merchants}
      getOptionLabel={(option) => `${option.merchantID} - ${option.name}`}
      renderInput={(params) => (
        <TextField {...params} label={label} size={size} />
      )}
      {...props}
    />
  );
};
```

## 🔄 状态管理

### React Context + Hooks
```jsx
// 主题上下文实现
const ThemeContext = createContext();

export const ThemeProvider = ({ children }) => {
  const [darkMode, setDarkMode] = useState(false);
  
  const toggleTheme = () => setDarkMode(!darkMode);
  
  return (
    <ThemeContext.Provider value={{ darkMode, toggleTheme }}>
      {children}
    </ThemeContext.Provider>
  );
};
```

## 📦 构建与部署

### 构建命令
```bash
# 开发环境
npm run dev

# 生产构建
npm run build

# 预览构建结果
npm run preview
```

### 构建优化
- **代码分割**: 按路由自动分割
- **文件hash**: 确保版本更新时浏览器获取新文件
- **资源优化**: 图片、字体等资源优化

## 🐛 调试与测试

### 开发工具
- **React DevTools**: React 组件调试
- **Redux DevTools**: 状态管理调试（如使用）
- **Network Tab**: API 请求调试

### 错误处理
```jsx
// 全局错误边界
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    console.error('Error caught by boundary:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return <h1>Something went wrong.</h1>;
    }
    return this.props.children;
  }
}
```

## 📋 代码规范

### ESLint 配置
```javascript
// .eslintrc.js
module.exports = {
  extends: [
    'eslint:recommended',
    '@vitejs/eslint-config-react'
  ],
  rules: {
    'react/prop-types': 'off',
    'no-unused-vars': 'warn'
  }
};
```

### 命名规范
- **组件**: PascalCase (如 `UserListView`)
- **文件**: PascalCase (如 `UserListView.jsx`)
- **变量**: camelCase (如 `userData`)
- **常量**: UPPER_SNAKE_CASE (如 `API_BASE_URL`)

## 🎨 UI样式规范

### 核心原则
- **一致性**: 所有页面使用相同的布局模式和组件样式
- **响应式**: 支持手机、平板、桌面等多种屏幕尺寸
- **可访问性**: 使用标准字体大小，确保可读性

### 布局规范
```javascript
// ✅ 正确：适应屏幕宽度
<Container maxWidth={false}>
  <Box sx={{ pt: 3, pb: 3 }}>
    {/* 页面内容 */}
  </Box>
</Container>

// ❌ 错误：固定最大宽度
<Container maxWidth="xl">
```

### 字体规范
```javascript
// 使用标准variant，禁止手动设置fontSize
<Typography variant="h4">页面标题</Typography>
<Typography variant="subtitle2">卡片标题</Typography>
<Typography variant="body2">正文内容</Typography>
<Typography variant="caption">说明文字</Typography>
```

### 响应式网格
```javascript
// 标准的响应式断点设置
<Grid item xs={12} sm={6} md={4} lg={3}>
  <Paper sx={{ p: 2, height: '100%' }}>
    {/* 卡片内容 */}
  </Paper>
</Grid>
```

### 详细规范文档
- [UI样式规范详细文档](./frontend/ui-style-guide.md)
- [样式规范快速参考](./frontend/ui-style-quick-reference.md)
- [样式检查工具](./frontend/style-check-tools.md)

## 📚 相关文档

- [系统架构文档](./system-architecture.md)
- [后端开发规范](./backend-development.md)
- [数据库设计文档](./database/)
- [部署指南](./deployment/)
- [UI样式规范](./frontend/ui-style-guide.md)

---

**版本**: v2.1
**最后更新**: 2025-07-30
**维护者**: 前端开发团队
