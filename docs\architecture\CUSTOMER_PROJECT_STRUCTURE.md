# 客户项目文件结构说明

## 清理后的项目结构

经过清理，客户项目现在使用简化的手动数据库管理方案，以下是当前的文件结构：

### 📁 核心文件

#### 数据访问层
```
SlzrCrossGate.CustomerTemplate/
├── Data/
│   └── CustomerDataContext.cs          # 客户数据上下文（不使用迁移）
├── Models/
│   └── ReadOnlyModels.cs               # 只读认证模型和服务
└── Services/
    └── CustomerDataService.cs          # 客户数据服务
```

#### 脚本工具
```
scripts/
├── create-customer-project.sh          # 创建客户项目脚本
├── manage-customer-tables.sh           # 手动管理客户表脚本
└── setup-customer-database-security.sql # 数据库安全配置脚本
```

#### 文档
```
├── CUSTOMER_MANUAL_DATABASE_GUIDE.md   # 手动数据库管理指南
├── EXTERNAL_LINK_MENU_GUIDE.md         # 外部链接菜单配置指南
└── MICROFRONTEND_DEPLOYMENT_GUIDE.md   # 微前端部署指南
```

### 🗑️ 已删除的文件

以下文件已被删除，因为我们采用了手动数据库管理方案：

#### 不再需要的数据访问文件
- ❌ `SlzrCrossGate.CustomerTemplate/Data/AuthDbContext.cs` - 被 ReadOnlyModels.cs 替代
- ❌ `SlzrCrossGate.CustomerTemplate/Data/CustomerDbContext.cs` - 被 CustomerDataContext.cs 替代
- ❌ `SlzrCrossGate.CustomerTemplate/Data/CustomerMigrationContext.cs` - 不使用EF迁移

#### 不再需要的迁移文件
- ❌ `SlzrCrossGate.WebAdmin/Migrations/AddExternalLinkSupportToMenuItems.cs` - 迁移文件
- ❌ `scripts/manage-customer-migrations.sh` - EF迁移管理脚本
- ❌ `scripts/create-customer-tables.sql` - 被 manage-customer-tables.sh 替代

#### 过时的文档
- ❌ `CUSTOMER_DATABASE_GUIDE.md` - 基于EF迁移的旧指南

### 🎯 简化后的架构

#### 数据库访问模式
```
客户项目
├── AuthReadOnlyContext (只读认证)
│   ├── 验证JWT token
│   ├── 获取用户信息
│   └── 检查用户权限
└── CustomerDataContext (业务数据)
    ├── 映射手动创建的表
    ├── 不使用EF迁移
    └── 业务逻辑处理
```

#### 表管理方式
```
手动管理
├── SQL脚本创建表
├── 脚本工具管理结构
├── 备份和恢复功能
└── 灵活的结构调整
```

### 🚀 使用流程

#### 1. 创建客户项目
```bash
./scripts/create-customer-project.sh customer-a
```

#### 2. 创建数据表
```bash
./scripts/manage-customer-tables.sh customer-a create
```

#### 3. 开发业务功能
```csharp
// 使用简化的数据服务
var data = await _customerDataService.GetDataListAsync(userId);
await _customerDataService.SaveDataAsync(userId, data);
```

#### 4. 管理表结构
```bash
# 添加列
./scripts/manage-customer-tables.sh customer-a add-column Data "NewColumn" "VARCHAR(100)"

# 备份数据
./scripts/manage-customer-tables.sh customer-a backup
```

### ✅ 方案优势

1. **简化架构**：
   - 去除了复杂的EF迁移管理
   - 只保留必要的数据访问功能
   - 减少了文件数量和复杂性

2. **手动管理**：
   - 直接使用SQL脚本管理表结构
   - 灵活调整数据库结构
   - 避免迁移冲突问题

3. **清晰职责**：
   - 认证验证：只读访问主项目表
   - 业务数据：完全控制客户专用表
   - 数据隔离：强制按商户分离

4. **易于维护**：
   - 文件结构简单清晰
   - 工具脚本功能完整
   - 文档指导详细

### 📋 下一步操作

如果你需要创建客户项目，可以直接使用：

```bash
# 1. 创建客户项目
./scripts/create-customer-project.sh your-customer-name

# 2. 创建数据表
./scripts/manage-customer-tables.sh your-customer-name create

# 3. 开始开发客户特定功能
```

这个简化的架构完美适合客户定制项目的需求：表数量不多、结构相对稳定、维护简单。
