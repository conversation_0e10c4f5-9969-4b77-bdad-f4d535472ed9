import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Grid,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Card,
  CardContent,
  Divider,
  Alert,
  FormLabel,
  RadioGroup,
  FormControlLabel,
  Radio,
  Tabs,
  Tab,
  Chip,
  List,
  ListItem,
  ListItemText,
  Checkbox,
  ListItemIcon,
  FormHelperText,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell
} from '@mui/material';
import {
  Send as SendIcon,
  ArrowBack as ArrowBackIcon,
  Info as InfoIcon,
  Code as CodeIcon,
  Memory as MemoryIcon,
  ViewList as ListIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { messageAPI, terminalAPI } from '../../services/api';
import { formatDateTime } from '../../utils/dateUtils';
import { parseErrorMessage } from '../../utils/errorHandler';
import MerchantAutocomplete from '../../components/MerchantAutocomplete';

const MessageSend = () => {
  const navigate = useNavigate();
  const [sendType, setSendType] = useState('terminal'); // 'terminal', 'line', 'merchant'
  const [messageTypes, setMessageTypes] = useState([]);
  const [terminals, setTerminals] = useState([]);

  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState('');
  const [result, setResult] = useState(null);

  // 线路和终端选择对话框状态
  const [lineDialog, setLineDialog] = useState(false);
  const [terminalDialog, setTerminalDialog] = useState(false);
  const [selectedLine, setSelectedLine] = useState(null);
  const [selectedTerminals, setSelectedTerminals] = useState([]);
  const [lineStats, setLineStats] = useState([]);
  const [lineLoading, setLineLoading] = useState(false);

  // 统一的消息表单
  const [messageForm, setMessageForm] = useState({
    merchantId: '',
    msgTypeCode: '',
    content: '',
    // 终端相关
    terminalIds: [],
    // 线路相关
    lineNo: '',
  });

  // 选中的商户对象（用于MerchantAutocomplete）
  const [selectedMerchant, setSelectedMerchant] = useState(null);

  // 加载消息类型列表
  const loadMessageTypes = async (merchantId = '') => {
    try {
      // 只有在提供了商户ID时才加载消息类型
      if (merchantId) {
        const response = await messageAPI.getAllMessageTypes(merchantId);
        // 直接使用response数组，因为api.js中的响应拦截器已经将response.data解包
        setMessageTypes(response);
        console.log('加载消息类型成功:', response);
      } else {
        // 当没有提供商户ID时，清空消息类型列表
        setMessageTypes([]);
      }
    } catch (error) {
      console.error('Error loading message types:', error);
    }
  };

  // 获取所选消息类型的详细信息
  const getSelectedMessageType = () => {
    if (!messageForm.msgTypeCode || messageTypes.length === 0) return null;
    return messageTypes.find(type => type.code === messageForm.msgTypeCode);
  };

  // 加载线路统计信息
  const loadLineStats = async () => {
    if (!messageForm.merchantId) return;

    setLineLoading(true);
    try {
      const response = await terminalAPI.getLineStats({ merchantId: messageForm.merchantId });
      setLineStats(response.items || []);
    } catch (error) {
      console.error('Error loading line stats:', error);
      setLineStats([]);
    } finally {
      setLineLoading(false);
    }
  };

  // 处理线路选择
  const openLineDialog = () => {
    setLineDialog(true);
    loadLineStats();
  };

  const handleSelectLine = (line) => {
    setSelectedLine(line);
    setMessageForm(prev => ({ ...prev, lineNo: line.lineNO }));
    setLineDialog(false);
  };

  // 处理终端选择
  const openTerminalDialog = () => {
    setTerminalDialog(true);
    loadTerminals();
  };

  const handleTerminalSelectInDialog = (terminalId) => {
    const newSelectedTerminals = selectedTerminals.includes(terminalId)
      ? selectedTerminals.filter(id => id !== terminalId)
      : [...selectedTerminals, terminalId];

    setSelectedTerminals(newSelectedTerminals);
    setMessageForm(prev => ({ ...prev, terminalIds: newSelectedTerminals }));
  };

  const handleTerminalSelectAll = () => {
    const allTerminalIds = terminals.map(t => t.id);
    const newSelected = selectedTerminals.length === allTerminalIds.length ? [] : allTerminalIds;
    setSelectedTerminals(newSelected);
    setMessageForm(prev => ({ ...prev, terminalIds: newSelected }));
  };

  // 获取编码类型的显示文本
  const getCodeTypeName = (codeType) => {
    switch (codeType) {
      case 1:
        return '文本 (UTF8)';
      case 2:
        return '十六进制 (HEX)';
      default:
        return '未知编码类型';
    }
  };

  // 加载终端列表
  const loadTerminals = async () => {
    try {
      const response = await terminalAPI.getTerminals({ pageSize: 100 });
      setTerminals(response.items);
    } catch (error) {
      console.error('Error loading terminals:', error);
    }
  };

  useEffect(() => {
    // 初始化时只加载终端列表，不加载消息类型
    loadTerminals();
  }, []);

  // 处理发送类型变更
  const handleSendTypeChange = (event) => {
    setSendType(event.target.value);
    // 重置表单中与发送类型相关的字段
    setMessageForm(prev => ({
      ...prev,
      terminalIds: [],
      lineNo: ''
    }));
    setSelectedLine(null);
    setSelectedTerminals([]);
  };

  // 处理表单变更
  const handleFormChange = (event) => {
    const { name, value } = event.target;
    setMessageForm(prev => ({ ...prev, [name]: value }));
  };



  // 发送消息
  const sendMessage = async () => {
    setLoading(true);
    setError('');
    setSuccess(false);
    setResult(null);

    try {
      let response;

      if (sendType === 'terminal') {
        // 发送到终端
        response = await messageAPI.sendMessageToTerminals({
          merchantId: messageForm.merchantId,
          terminalIds: messageForm.terminalIds,
          msgTypeCode: messageForm.msgTypeCode,
          content: messageForm.content
        });
      } else if (sendType === 'line') {
        // 发送到线路
        response = await messageAPI.sendMessageToLine({
          merchantId: messageForm.merchantId,
          lineNo: messageForm.lineNo,
          msgTypeCode: messageForm.msgTypeCode,
          content: messageForm.content
        });
      } else {
        // 发送到商户
        response = await messageAPI.sendMessageToMerchant({
          merchantId: messageForm.merchantId,
          msgTypeCode: messageForm.msgTypeCode,
          content: messageForm.content
        });
      }

      setSuccess(true);
      setResult(response);
    } catch (error) {
      console.error('Error sending message:', error);
      const errorMessage = parseErrorMessage(error, '发送失败');
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };



  // 验证表单是否可提交
  const isFormValid = () => {
    if (!messageForm.merchantId || !messageForm.msgTypeCode || !messageForm.content) {
      return false;
    }

    if (sendType === 'terminal' && messageForm.terminalIds.length === 0) {
      return false;
    }

    if (sendType === 'line' && !messageForm.lineNo) {
      return false;
    }

    return true;
  };

  return (
    <Box sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
          <Button
            startIcon={<ArrowBackIcon />}
            onClick={() => navigate('/app/messages')}
          >
            返回消息列表
          </Button>
        </Box>

      <Typography variant="h4" gutterBottom>
        消息发布
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Paper sx={{ p: 3 }}>
        <Grid container spacing={3}>
          {/* 发送方式选择 */}
          <Grid item xs={12}>
            <FormControl component="fieldset">
              <FormLabel component="legend">发布范围</FormLabel>
              <RadioGroup
                row
                name="sendType"
                value={sendType}
                onChange={handleSendTypeChange}
              >
                <FormControlLabel value="terminal" control={<Radio />} label="指定终端设备" />
                <FormControlLabel value="line" control={<Radio />} label="指定线路的所有设备" />
                <FormControlLabel value="merchant" control={<Radio />} label="商户的所有设备" />
              </RadioGroup>
            </FormControl>
          </Grid>

          {/* 商户选择 - 所有发送方式共用 */}
          <Grid item xs={12}>
            <MerchantAutocomplete
              value={selectedMerchant}
              onChange={(_, newValue) => {
                setSelectedMerchant(newValue);
                const merchantId = newValue ? newValue.merchantID : '';
                setMessageForm(prev => ({
                  ...prev,
                  merchantId: merchantId,
                  msgTypeCode: '' // 重置消息类型选择
                }));
                // 重新加载所选商户的消息类型
                loadMessageTypes(merchantId);
              }}
              required
              error={!messageForm.merchantId}
              size="medium"
              helperText={!messageForm.merchantId ? "请先选择商户" : ""}
            />
          </Grid>

          {/* 消息类型选择 - 所有发送方式共用 */}
          <Grid item xs={12}>
            <FormControl fullWidth required error={!messageForm.msgTypeCode}>
              <InputLabel>消息类型</InputLabel>
              <Select
                name="msgTypeCode"
                value={messageForm.msgTypeCode}
                onChange={handleFormChange}
                label="消息类型"
                disabled={!messageForm.merchantId}
              >
                {messageTypes.map((type) => (
                  <MenuItem key={`${type.code}-${type.merchantId}`} value={type.code}>
                    {type.code} - {type.name || '未命名'}
                  </MenuItem>
                ))}
              </Select>
              {!messageForm.msgTypeCode && (
                <FormHelperText error>请选择消息类型</FormHelperText>
              )}
            </FormControl>
          </Grid>

          {/* 消息类型详情 - 所有发送方式共用 */}
          {messageForm.msgTypeCode && (
            <Grid item xs={12}>
              <Card
                variant="outlined"
                sx={{
                  borderRadius: 2,
                  boxShadow: (theme) => `0 6px 16px 0 ${theme.palette.mode === 'dark' ? 'rgba(0,0,0,0.3)' : 'rgba(0,0,0,0.08)'}`,
                  backdropFilter: 'blur(8px)',
                  background: (theme) => theme.palette.mode === 'dark'
                    ? 'rgba(66, 66, 66, 0.8)'
                    : 'rgba(255, 255, 255, 0.9)',
                  transition: 'all 0.3s ease-in-out',
                  '&:hover': {
                    boxShadow: (theme) => `0 8px 24px 0 ${theme.palette.mode === 'dark' ? 'rgba(0,0,0,0.4)' : 'rgba(0,0,0,0.12)'}`,
                    transform: 'translateY(-2px)'
                  }
                }}
              >
                <CardContent sx={{ p: 3 }}>
                  <Typography variant="h6" gutterBottom color="primary" sx={{ fontWeight: 600, mb: 2, display: 'flex', alignItems: 'center' }}>
                    <InfoIcon sx={{ mr: 1, fontSize: '1.2rem' }} /> 消息类型详情
                  </Typography>
                  <Divider sx={{ mb: 2 }} />

                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={4}>
                      <Box sx={{
                        p: 2,
                        borderRadius: 1,
                        bgcolor: (theme) => theme.palette.mode === 'dark' ? 'rgba(99, 99, 99, 0.15)' : 'rgba(25, 118, 210, 0.08)',
                        display: 'flex',
                        flexDirection: 'column',
                        height: '100%'
                      }}>
                        <Typography variant="subtitle2" color="textSecondary" gutterBottom>编码类型</Typography>
                        <Typography variant="body1" sx={{ fontWeight: 500, mt: 1, display: 'flex', alignItems: 'center' }}>
                          {getSelectedMessageType()?.codeType === 1 ? (
                            <><CodeIcon sx={{ mr: 1, color: 'text.secondary' }} /> {getCodeTypeName(getSelectedMessageType()?.codeType)}</>
                          ) : (
                            <><MemoryIcon sx={{ mr: 1, color: 'text.secondary' }} /> {getCodeTypeName(getSelectedMessageType()?.codeType)}</>
                          )}
                        </Typography>
                      </Box>
                    </Grid>

                    <Grid item xs={12} sm={8}>
                      <Box sx={{
                        p: 2,
                        borderRadius: 1,
                        bgcolor: (theme) => theme.palette.mode === 'dark' ? 'rgba(99, 99, 99, 0.15)' : 'rgba(25, 118, 210, 0.08)',
                        height: '100%'
                      }}>
                        <Typography variant="subtitle2" color="textSecondary" gutterBottom>备注</Typography>
                        <Typography variant="body1">
                          {getSelectedMessageType()?.remark || '无备注信息'}
                        </Typography>
                      </Box>
                    </Grid>

                    <Grid item xs={12}>
                      <Box sx={{
                        p: 2,
                        borderRadius: 1,
                        bgcolor: (theme) => theme.palette.mode === 'dark' ? 'rgba(99, 99, 99, 0.15)' : 'rgba(25, 118, 210, 0.08)'
                      }}>
                        <Typography variant="subtitle2" color="textSecondary" gutterBottom>
                          <CodeIcon sx={{ mr: 1, fontSize: '1rem', verticalAlign: 'middle' }} />
                          消息示例
                        </Typography>

                        {getSelectedMessageType()?.exampleMessage ? (
                          <Box
                            component="pre"
                            sx={{
                              p: 2,
                              borderRadius: 1,
                              mt: 1,
                              overflow: 'auto',
                              backgroundColor: (theme) =>
                                theme.palette.mode === 'dark' ? 'rgba(30, 30, 30, 0.8)' : 'rgba(240, 240, 240, 0.8)',
                              fontFamily: 'monospace',
                              fontSize: '0.875rem',
                              border: '1px solid',
                              borderColor: (theme) =>
                                theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'
                            }}
                          >
                            {getSelectedMessageType()?.exampleMessage}
                          </Box>
                        ) : (
                          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                            未提供消息示例
                          </Typography>
                        )}
                      </Box>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>
          )}

          {/* 消息内容 - 所有发送方式共用 */}
          <Grid item xs={12}>
            <TextField
              fullWidth
              multiline
              rows={4}
              label="消息内容"
              name="content"
              value={messageForm.content}
              onChange={handleFormChange}
              required
              error={!messageForm.content}
              helperText={!messageForm.content ? '请输入消息内容' : ''}
            />
          </Grid>

          {/* 终端选择部分 - 仅发送到终端时显示 */}
          {sendType === 'terminal' && (
            <Grid item xs={12}>
              <Typography variant="subtitle2" color="textSecondary" gutterBottom>
                选择终端
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Button
                  variant="outlined"
                  startIcon={<ListIcon />}
                  onClick={openTerminalDialog}
                  sx={{ minWidth: 200 }}
                  disabled={!messageForm.merchantId}
                >
                  {selectedTerminals.length > 0 ? `已选择 ${selectedTerminals.length} 个终端` : '选择终端'}
                </Button>
                {selectedTerminals.length > 0 && (
                  <Typography variant="body2" color="textSecondary">
                    已选择终端数量: {selectedTerminals.length}
                  </Typography>
                )}
              </Box>
              {sendType === 'terminal' && messageForm.terminalIds.length === 0 && (
                <Typography variant="caption" color="error">
                  请至少选择一个终端
                </Typography>
              )}
            </Grid>
          )}

          {/* 线路选择部分 - 仅发送到线路时显示 */}
          {sendType === 'line' && (
            <Grid item xs={12}>
              <Typography variant="subtitle2" color="textSecondary" gutterBottom>
                选择线路
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Button
                  variant="outlined"
                  startIcon={<ListIcon />}
                  onClick={openLineDialog}
                  sx={{ minWidth: 200 }}
                  disabled={!messageForm.merchantId}
                >
                  {selectedLine ? `线路: ${selectedLine.lineNO}` : '选择线路'}
                </Button>
                {selectedLine && (
                  <Typography variant="body2" color="textSecondary">
                    在线: {selectedLine.onlineCount} | 离线: {selectedLine.offlineCount} | 总计: {selectedLine.totalCount}
                  </Typography>
                )}
              </Box>
              {sendType === 'line' && !messageForm.lineNo && (
                <Typography variant="caption" color="error">
                  请选择线路
                </Typography>
              )}
            </Grid>
          )}

          {/* 发送结果 */}
          {success && result && (
            <Grid item xs={12}>
              <Alert severity="success" sx={{ mb: 2 }}>
                <Typography variant="subtitle1">
                  消息发布成功！
                </Typography>
                <Typography variant="body2">
                  消息ID: {result.messageId}
                </Typography>
                <Typography variant="body2">
                  接收终端数: {result.terminalCount}
                </Typography>
                <Typography variant="body2">
                  消息类型: {result.messageType}
                </Typography>
                <Typography variant="body2">
                  发布时间: {formatDateTime(result.sendTime)}
                </Typography>
              </Alert>
            </Grid>
          )}

          {/* 按钮组 - 所有发送方式共用 */}
          <Grid item xs={12}>
            <Box sx={{ display: 'flex', gap: 2 }}>
              <Button
                variant="contained"
                color="primary"
                startIcon={<SendIcon />}
                onClick={sendMessage}
                disabled={loading || !isFormValid()}
              >
                {loading ? <CircularProgress size={24} /> : '发布消息'}
              </Button>
              <Button
                variant="outlined"
                startIcon={<ArrowBackIcon />}
                onClick={() => navigate('/app/messages')}
              >
                返回消息管理
              </Button>
            </Box>
          </Grid>
        </Grid>
      </Paper>

      {/* 线路选择对话框 */}
      <Dialog
        open={lineDialog}
        onClose={() => setLineDialog(false)}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: {
            height: 'auto',
            minHeight: '400px',
            maxHeight: 'min(85vh, 700px)',
            display: 'flex',
            flexDirection: 'column',
            margin: '16px'
          }
        }}
      >
        <DialogTitle sx={{ flexShrink: 0 }}>选择线路</DialogTitle>
        <DialogContent sx={{
          flex: 1,
          overflow: 'hidden',
          display: 'flex',
          flexDirection: 'column',
          p: 0
        }}>
          <Box sx={{
            p: 2,
            flex: 1,
            display: 'flex',
            flexDirection: 'column',
            minHeight: 0
          }}>
            <Typography variant="subtitle2" gutterBottom sx={{ flexShrink: 0 }}>
              商户: {selectedMerchant?.name}({selectedMerchant?.merchantID})
            </Typography>
            <Box sx={{
              flex: 1,
              overflow: 'auto',
              border: '1px solid',
              borderColor: 'divider',
              borderRadius: 1,
              minHeight: 0
            }}>
              <Table stickyHeader size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>线路编号</TableCell>
                    <TableCell>总终端数</TableCell>
                    <TableCell>在线终端数</TableCell>
                    <TableCell>离线终端数</TableCell>
                    <TableCell>操作</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {lineLoading ? (
                    <TableRow>
                      <TableCell colSpan={5} align="center">
                        <CircularProgress size={24} />
                      </TableCell>
                    </TableRow>
                  ) : lineStats.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={5} align="center">
                        没有找到线路信息
                      </TableCell>
                    </TableRow>
                  ) : (
                    lineStats.map((line) => (
                      <TableRow
                        key={line.lineNO}
                        hover
                        sx={{ cursor: 'pointer' }}
                        onClick={() => handleSelectLine(line)}
                      >
                        <TableCell>{line.lineNO}</TableCell>
                        <TableCell>{line.totalCount}</TableCell>
                        <TableCell>
                          <Chip
                            label={line.onlineCount}
                            color="success"
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={line.offlineCount}
                            color="error"
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          <Button
                            size="small"
                            variant="contained"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleSelectLine(line);
                            }}
                          >
                            选择
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </Box>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setLineDialog(false)}>取消</Button>
        </DialogActions>
      </Dialog>

      {/* 终端选择对话框 */}
      <Dialog
        open={terminalDialog}
        onClose={() => setTerminalDialog(false)}
        maxWidth="lg"
        fullWidth
        PaperProps={{
          sx: {
            height: 'auto',
            minHeight: '500px',
            maxHeight: 'min(90vh, 800px)',
            display: 'flex',
            flexDirection: 'column',
            margin: '16px'
          }
        }}
      >
        <DialogTitle sx={{ flexShrink: 0 }}>选择终端</DialogTitle>
        <DialogContent sx={{
          flex: 1,
          overflow: 'hidden',
          display: 'flex',
          flexDirection: 'column',
          p: 0
        }}>
          <Box sx={{
            p: 2,
            flex: 1,
            display: 'flex',
            flexDirection: 'column',
            minHeight: 0
          }}>
            <Typography variant="subtitle2" gutterBottom sx={{ flexShrink: 0 }}>
              商户: {selectedMerchant?.name}({selectedMerchant?.merchantID})
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2, flexShrink: 0 }}>
              <Button
                size="small"
                variant="outlined"
                onClick={handleTerminalSelectAll}
              >
                {selectedTerminals.length === terminals.filter(t => t.merchantID === messageForm.merchantId).length ? '取消全选' : '全选'}
              </Button>
              <Typography variant="body2" color="textSecondary">
                已选择: {selectedTerminals.length} / {terminals.filter(t => t.merchantID === messageForm.merchantId).length}
              </Typography>
            </Box>
            <Box sx={{
              flex: 1,
              overflow: 'auto',
              border: '1px solid',
              borderColor: 'divider',
              borderRadius: 1,
              minHeight: 0
            }}>
              <Table stickyHeader size="small">
                <TableHead>
                  <TableRow>
                    <TableCell padding="checkbox">
                      <Checkbox
                        indeterminate={selectedTerminals.length > 0 && selectedTerminals.length < terminals.filter(t => t.merchantID === messageForm.merchantId).length}
                        checked={terminals.filter(t => t.merchantID === messageForm.merchantId).length > 0 && selectedTerminals.length === terminals.filter(t => t.merchantID === messageForm.merchantId).length}
                        onChange={handleTerminalSelectAll}
                      />
                    </TableCell>
                    <TableCell>终端ID</TableCell>
                    <TableCell>设备编号</TableCell>
                    <TableCell>线路编号</TableCell>
                    <TableCell>出厂序列号</TableCell>
                    <TableCell>状态</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {terminals
                    .filter(t => t.merchantID === messageForm.merchantId)
                    .map((terminal) => (
                      <TableRow
                        key={terminal.id}
                        hover
                        sx={{ cursor: 'pointer' }}
                        onClick={() => handleTerminalSelectInDialog(terminal.id)}
                      >
                        <TableCell padding="checkbox">
                          <Checkbox
                            checked={selectedTerminals.includes(terminal.id)}
                            onChange={() => handleTerminalSelectInDialog(terminal.id)}
                          />
                        </TableCell>
                        <TableCell>{terminal.id}</TableCell>
                        <TableCell>{terminal.deviceNO}</TableCell>
                        <TableCell>{terminal.lineNO}</TableCell>
                        <TableCell>{terminal.machineID}</TableCell>
                        <TableCell>
                          <Chip
                            label={terminal.status?.activeStatus === 1 ? "在线" : "离线"}
                            color={terminal.status?.activeStatus === 1 ? "success" : "error"}
                            size="small"
                          />
                        </TableCell>
                      </TableRow>
                    ))}
                </TableBody>
              </Table>
            </Box>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setTerminalDialog(false)}>取消</Button>
          <Button
            variant="contained"
            onClick={() => setTerminalDialog(false)}
            disabled={selectedTerminals.length === 0}
          >
            确定 ({selectedTerminals.length})
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default MessageSend;
