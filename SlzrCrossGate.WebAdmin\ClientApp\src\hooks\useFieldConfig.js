import { useState, useEffect, useCallback, useMemo, useRef } from 'react';

/**
 * 字段配置管理Hook
 * 负责加载、缓存和管理字段配置
 */
export const useFieldConfig = () => {
  const [fieldConfig, setFieldConfig] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [lastUpdated, setLastUpdated] = useState(null);

  // 防重复请求
  const hasLoadedRef = useRef(false);

  // 默认配置（降级方案）
  const defaultConfig = useMemo(() => ({
    version: "1.0.0",
    fieldCategories: {
      basic: {
        name: "基础信息",
        fields: {
          merchantName: {
            key: "merchantName",
            displayName: "商户",
            dataPath: "merchantName",
            sortable: true,
            width: 150,
            hideOn: ["xs"],
            type: "text",
            enabled: true,
            order: 1
          },
          deviceNO: {
            key: "deviceNO",
            displayName: "设备编号",
            dataPath: "deviceNO",
            sortable: true,
            width: 120,
            hideOn: [],
            type: "text",
            enabled: true,
            order: 2
          },
          status: {
            key: "status",
            displayName: "状态",
            dataPath: "status",
            sortable: true,
            width: 120,
            hideOn: [],
            type: "status",
            enabled: true,
            order: 3
          }
        }
      }
    },
    defaultSettings: {
      enabledFields: ["merchantName", "deviceNO", "status"]
    }
  }), []);

  /**
   * 加载字段配置
   */
  const loadFieldConfig = async (forceReload = false) => {
    try {
      setLoading(true);
      setError(null);

      console.log('useFieldConfig: 执行字段配置加载', { forceReload });

      forceReload=true;
      // 添加时间戳防止缓存
      const timestamp = forceReload ? `?v=${Date.now()}` : '';
      const response = await fetch(`/config/terminalFields.json${timestamp}`);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const config = await response.json();
      
      // 验证配置格式
      if (!config.fieldCategories) {
        throw new Error('配置文件格式错误：缺少 fieldCategories');
      }
      
      setFieldConfig(config);
      setLastUpdated(config.lastUpdated || new Date().toISOString());
      
      // 缓存到localStorage（可选）
      try {
        localStorage.setItem('terminalFieldConfig', JSON.stringify({
          config,
          timestamp: Date.now()
        }));
      } catch (e) {
        console.warn('无法缓存配置到localStorage:', e);
      }
      
    } catch (err) {
      console.error('加载字段配置失败:', err);
      setError(err.message);
      
      // 尝试从localStorage加载缓存
      try {
        const cached = localStorage.getItem('terminalFieldConfig');
        if (cached) {
          const { config } = JSON.parse(cached);
          setFieldConfig(config);
          console.warn('使用缓存的字段配置');
        } else {
          setFieldConfig(defaultConfig);
          console.warn('使用默认字段配置');
        }
      } catch (e) {
        setFieldConfig(defaultConfig);
        console.warn('使用默认字段配置');
      }
    } finally {
      setLoading(false);
    }
  };

  /**
   * 获取所有可用字段（扁平化）
   */
  const getAllFields = useMemo(() => {
    if (!fieldConfig?.fieldCategories) return {};
    
    const allFields = {};
    Object.values(fieldConfig.fieldCategories).forEach(category => {
      Object.assign(allFields, category.fields);
    });
    
    return allFields;
  }, [fieldConfig]);

  /**
   * 获取启用的字段
   */
  const getEnabledFields = useMemo(() => {
    const allFields = getAllFields;
    return Object.values(allFields)
      .filter(field => field.enabled)
      .sort((a, b) => (a.order || 999) - (b.order || 999));
  }, [getAllFields]);

  /**
   * 获取字段分类列表
   */
  const getFieldCategories = useMemo(() => {
    if (!fieldConfig?.fieldCategories) return [];
    
    return Object.entries(fieldConfig.fieldCategories).map(([key, category]) => ({
      key,
      name: category.name,
      description: category.description,
      fields: Object.values(category.fields)
    }));
  }, [fieldConfig]);

  /**
   * 根据键名获取字段配置
   */
  const getFieldByKey = useCallback((key) => {
    return getAllFields[key] || null;
  }, [getAllFields]);

  /**
   * 检查字段是否启用
   */
  const isFieldEnabled = useCallback((key) => {
    const field = getFieldByKey(key);
    return field?.enabled || false;
  }, [getFieldByKey]);

  /**
   * 获取渲染类型列表
   */
  const getRenderTypes = useMemo(() => {
    return fieldConfig?.renderTypes || {};
  }, [fieldConfig]);

  /**
   * 重新加载配置
   */
  const reloadConfig = useCallback(() => {
    console.log('useFieldConfig: 强制重新加载配置');
    hasLoadedRef.current = false; // 重置加载标志
    loadFieldConfig(true);
  }, []);

  /**
   * 获取配置统计信息
   */
  const getConfigStats = useMemo(() => {
    const allFields = getAllFields;
    const enabledCount = Object.values(allFields).filter(f => f.enabled).length;
    const totalCount = Object.keys(allFields).length;

    return {
      total: totalCount,
      enabled: enabledCount,
      disabled: totalCount - enabledCount,
      categories: Object.keys(fieldConfig?.fieldCategories || {}).length
    };
  }, [getAllFields, fieldConfig]);

  // 初始化加载
  useEffect(() => {
    // 防止重复加载
    if (hasLoadedRef.current) {
      console.log('useFieldConfig: 已加载过，跳过重复请求');
      return;
    }

    console.log('useFieldConfig: 执行首次加载');
    hasLoadedRef.current = true;
    loadFieldConfig();
  }, []);

  return {
    // 状态
    fieldConfig,
    loading,
    error,
    lastUpdated,
    
    // 数据获取方法
    getAllFields,
    getEnabledFields,
    getFieldCategories,
    getFieldByKey,
    getRenderTypes,
    getConfigStats,
    
    // 工具方法
    isFieldEnabled,
    reloadConfig,
    
    // 原始加载方法
    loadFieldConfig
  };
};

/**
 * 字段配置上下文Hook（用于全局状态管理）
 */
export const useFieldConfigContext = () => {
  // 这里可以扩展为使用React Context
  // 目前直接返回useFieldConfig的结果
  return useFieldConfig();
};
