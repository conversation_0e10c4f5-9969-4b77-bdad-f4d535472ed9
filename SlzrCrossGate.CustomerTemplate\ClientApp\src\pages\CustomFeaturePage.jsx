import React, { useState } from 'react';
import {
  <PERSON>,
  Typography,
  Card,
  CardContent,
  Button,
  Grid,
  TextField,
  Alert,
  Paper,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Switch,
  FormControlLabel
} from '@mui/material';
import {
  ArrowLeft as ArrowLeftIcon,
  Settings as SettingsIcon,
  Save as SaveIcon,
  Check as CheckIcon
} from 'react-feather';
import { useNavigate } from 'react-router-dom';
import { useTheme } from '../contexts/ThemeContext';

const CustomFeaturePage = () => {
  const navigate = useNavigate();
  const { mode } = useTheme();
  const [settings, setSettings] = useState({
    feature1: true,
    feature2: false,
    customValue: '',
    description: ''
  });
  const [saved, setSaved] = useState(false);

  const handleSave = () => {
    // 模拟保存操作
    console.log('保存设置:', settings);
    setSaved(true);
    setTimeout(() => setSaved(false), 3000);
  };

  const handleSettingChange = (key, value) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  return (
    <Box sx={{ p: 3 }}>
      {/* 顶部导航 */}
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <Button
          startIcon={<ArrowLeftIcon size={20} />}
          onClick={() => navigate('/')}
          sx={{ mr: 2 }}
        >
          返回首页
        </Button>
        <Typography variant="h4" component="h1">
          定制功能配置
        </Typography>
      </Box>

      {/* 保存成功提示 */}
      {saved && (
        <Alert 
          severity="success" 
          sx={{ mb: 3 }}
          icon={<CheckIcon size={20} />}
        >
          设置已保存成功！
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* 功能配置 */}
        <Grid item xs={12} md={8}>
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                <SettingsIcon size={24} style={{ marginRight: 8 }} />
                <Typography variant="h6">
                  功能开关
                </Typography>
              </Box>
              
              <List>
                <ListItem>
                  <ListItemIcon>
                    <Box
                      sx={{
                        width: 8,
                        height: 8,
                        borderRadius: '50%',
                        bgcolor: settings.feature1 ? 'success.main' : 'grey.400'
                      }}
                    />
                  </ListItemIcon>
                  <ListItemText
                    primary="高级功能 A"
                    secondary="启用后将提供额外的数据处理能力"
                  />
                  <FormControlLabel
                    control={
                      <Switch
                        checked={settings.feature1}
                        onChange={(e) => handleSettingChange('feature1', e.target.checked)}
                        color="primary"
                      />
                    }
                    label=""
                  />
                </ListItem>
                
                <Divider />
                
                <ListItem>
                  <ListItemIcon>
                    <Box
                      sx={{
                        width: 8,
                        height: 8,
                        borderRadius: '50%',
                        bgcolor: settings.feature2 ? 'success.main' : 'grey.400'
                      }}
                    />
                  </ListItemIcon>
                  <ListItemText
                    primary="实验性功能 B"
                    secondary="测试阶段的新功能，可能不稳定"
                  />
                  <FormControlLabel
                    control={
                      <Switch
                        checked={settings.feature2}
                        onChange={(e) => handleSettingChange('feature2', e.target.checked)}
                        color="primary"
                      />
                    }
                    label=""
                  />
                </ListItem>
              </List>
            </CardContent>
          </Card>

          {/* 自定义配置 */}
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                自定义配置
              </Typography>
              
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="自定义值"
                    value={settings.customValue}
                    onChange={(e) => handleSettingChange('customValue', e.target.value)}
                    placeholder="输入自定义值"
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    multiline
                    rows={4}
                    label="描述"
                    value={settings.description}
                    onChange={(e) => handleSettingChange('description', e.target.value)}
                    placeholder="输入功能描述..."
                  />
                </Grid>
              </Grid>
              
              <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}>
                <Button
                  variant="contained"
                  startIcon={<SaveIcon size={20} />}
                  onClick={handleSave}
                >
                  保存设置
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* 侧边信息 */}
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              主题信息
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              当前主题模式：<strong>{mode === 'dark' ? '暗色模式' : '亮色模式'}</strong>
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              这个页面展示了模板项目的基本功能结构，包括：
            </Typography>
            <List dense>
              <ListItem>
                <ListItemText primary="• 主题系统集成" />
              </ListItem>
              <ListItem>
                <ListItemText primary="• 玻璃拟态设计" />
              </ListItem>
              <ListItem>
                <ListItemText primary="• 响应式布局" />
              </ListItem>
              <ListItem>
                <ListItemText primary="• 组件样式统一" />
              </ListItem>
            </List>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default CustomFeaturePage;
