#!/bin/bash

# 创建客户项目脚本
# 用法: ./create-customer-project.sh <customer-name>

set -e

CUSTOMER_NAME=$1
BASE_PATH_TYPE=${2:-"customer"}  # customer, extension, module, custom
CUSTOM_PATH=$3

if [ -z "$CUSTOMER_NAME" ]; then
    echo "用法: $0 <customer-name> [path-type] [custom-path]"
    echo "示例: $0 customer-a"
    echo "示例: $0 customer-a extension"
    echo "示例: $0 customer-a custom /apps/customer-a"
    echo ""
    echo "路径类型:"
    echo "  customer   - /customer-a/ (默认)"
    echo "  extension  - /extensions/customer-a/"
    echo "  module     - /modules/customer-a/"
    echo "  custom     - 自定义路径"
    exit 1
fi

# 转换为Pascal命名
CUSTOMER_PASCAL=$(echo "$CUSTOMER_NAME" | sed 's/-\([a-z]\)/\U\1/g' | sed 's/^./\U&/')
PROJECT_NAME="SlzrCrossGate.Customer$CUSTOMER_PASCAL"
PROJECT_DIR="$PROJECT_NAME"

# 确定访问路径
case "$BASE_PATH_TYPE" in
    "customer")
        ACCESS_PATH="/$CUSTOMER_NAME"
        ;;
    "extension")
        ACCESS_PATH="/extensions/$CUSTOMER_NAME"
        ;;
    "module")
        ACCESS_PATH="/modules/$CUSTOMER_NAME"
        ;;
    "custom")
        if [ -z "$CUSTOM_PATH" ]; then
            echo "错误: 使用custom路径类型时必须提供自定义路径"
            exit 1
        fi
        ACCESS_PATH="$CUSTOM_PATH"
        ;;
    *)
        echo "错误: 不支持的路径类型: $BASE_PATH_TYPE"
        exit 1
        ;;
esac

echo "创建客户项目: $PROJECT_NAME"
echo "访问路径: $ACCESS_PATH"
echo "路径类型: $BASE_PATH_TYPE"

# 检查项目是否已存在
if [ -d "$PROJECT_DIR" ]; then
    echo "错误: 项目目录 $PROJECT_DIR 已存在"
    exit 1
fi

# 创建项目目录结构
mkdir -p "$PROJECT_DIR"
cd "$PROJECT_DIR"

# 创建后端项目结构
mkdir -p {Controllers,DTOs,Services,Models,ClientApp/src/{pages,components,services,utils}}

echo "创建后端项目文件..."

# 创建.csproj文件
cat > "$PROJECT_NAME.csproj" << EOF
<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <SpaRoot>ClientApp\</SpaRoot>
    <SpaProxyServerUrl>http://localhost:3001</SpaProxyServerUrl>
    <SpaProxyLaunchCommand>npm start</SpaProxyLaunchCommand>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.SpaServices.Extensions" Version="8.0.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0" />
    <PackageReference Include="Serilog.AspNetCore" Version="8.0.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="5.0.0" />
    <PackageReference Include="Serilog.Formatting.Compact" Version="2.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="../SlzrCrossGate.Core/SlzrCrossGate.Core.csproj" />
    <ProjectReference Include="../SlzrCrossGate.Common/SlzrCrossGate.Common.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Content Remove="ClientApp\**" />
    <None Remove="ClientApp\**" />
  </ItemGroup>

</Project>
EOF

# 创建Program.cs
cat > "Program.cs" << EOF
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using System.Text;
using Serilog;

var builder = WebApplication.CreateBuilder(args);

// 配置Serilog
Log.Logger = new LoggerConfiguration()
    .ReadFrom.Configuration(builder.Configuration)
    .Enrich.WithProperty("Service", "$PROJECT_NAME")
    .Enrich.WithProperty("Customer", "$CUSTOMER_NAME")
    .CreateLogger();

builder.Host.UseSerilog();

// 添加服务
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// 配置数据库连接
// 只读认证上下文 - 不参与迁移
builder.Services.AddDbContext<AuthReadOnlyContext>(options =>
{
    options.UseMySql(
        builder.Configuration.GetConnectionString("AuthConnection"),
        ServerVersion.AutoDetect(builder.Configuration.GetConnectionString("AuthConnection"))
    );
    // 禁用变更跟踪，提高性能
    options.UseQueryTrackingBehavior(QueryTrackingBehavior.NoTracking);
});

// 客户数据上下文 - 不使用迁移，只映射现有表
builder.Services.AddDbContext<CustomerDataContext>(options =>
    options.UseMySql(
        builder.Configuration.GetConnectionString("CustomerConnection"),
        ServerVersion.AutoDetect(builder.Configuration.GetConnectionString("CustomerConnection"))
    ));

// 注册服务
builder.Services.AddScoped<ReadOnlyAuthService>();
builder.Services.AddScoped<CustomerDataService>();

// 设置客户表前缀环境变量
Environment.SetEnvironmentVariable("CUSTOMER_TABLE_PREFIX", "Customer_$CUSTOMER_PASCAL");

// 设置客户路径配置
Environment.SetEnvironmentVariable("CUSTOMER_PATH_TYPE", "$BASE_PATH_TYPE");
Environment.SetEnvironmentVariable("CUSTOMER_CUSTOM_PATH", "$CUSTOM_PATH");

// 配置JWT认证（与主项目保持一致）
builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddJwtBearer(options =>
    {
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidateAudience = true,
            ValidateLifetime = true,
            ValidateIssuerSigningKey = true,
            ValidIssuer = builder.Configuration["Jwt:Issuer"],
            ValidAudience = builder.Configuration["Jwt:Audience"],
            IssuerSigningKey = new SymmetricSecurityKey(
                Encoding.UTF8.GetBytes(builder.Configuration["Jwt:Key"] ?? ""))
        };
    });

// 配置CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowMainApp", policy =>
    {
        policy.WithOrigins(builder.Configuration["MainApp:BaseUrl"] ?? "http://localhost:5270")
              .AllowAnyHeader()
              .AllowAnyMethod()
              .AllowCredentials();
    });
});

var app = builder.Build();

// 配置请求管道
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseCors("AllowMainApp");
app.UseAuthentication();
app.UseAuthorization();

app.MapControllers();

// 配置静态文件服务
app.UseStaticFiles();
app.UseRouting();

// SPA回退路由
app.MapFallbackToFile("index.html");

app.Run();
EOF

echo "创建前端项目文件..."

# 创建前端package.json
cat > "ClientApp/package.json" << EOF
{
  "name": "slzr-$CUSTOMER_NAME-frontend",
  "version": "1.0.0",
  "private": true,
  "dependencies": {
    "@mui/material": "^5.15.0",
    "@mui/icons-material": "^5.15.0",
    "@emotion/react": "^11.11.0",
    "@emotion/styled": "^11.11.0",
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "react-router-dom": "^6.20.0",
    "axios": "^1.6.0",
    "notistack": "^3.0.0"
  },
  "devDependencies": {
    "@vitejs/plugin-react": "^4.2.0",
    "vite": "^5.0.0",
    "eslint": "^8.55.0",
    "eslint-plugin-react": "^7.33.0",
    "eslint-plugin-react-hooks": "^4.6.0"
  },
  "scripts": {
    "dev": "vite",
    "build": "vite build",
    "preview": "vite preview",
    "start": "vite --port 3001"
  }
}
EOF

echo "项目 $PROJECT_NAME 创建完成!"
echo ""
echo "配置信息:"
echo "  项目名称: $PROJECT_NAME"
echo "  访问路径: $ACCESS_PATH"
echo "  路径类型: $BASE_PATH_TYPE"
echo "  表前缀: Customer_$CUSTOMER_PASCAL"
echo ""
echo "下一步:"
echo "1. cd $PROJECT_DIR"
echo "2. cd ClientApp && npm install"
echo "3. 创建数据表: ../scripts/manage-customer-tables.sh $CUSTOMER_NAME create"
echo "4. 配置菜单项（重要）:"
echo "   在数据库中添加菜单项，确保包含以下关键字段："
echo "   - ItemKey: '$CUSTOMER_NAME-dashboard' (用于新路由系统)"
echo "   - Href: 'https://yourdomain.com$ACCESS_PATH/'"
echo "   - IsExternal: 1"
echo "   - Target: '_iframe'"
echo "5. 开始开发客户特定功能"
echo ""
echo "菜单配置SQL示例:"
echo "-- 1. 添加菜单分组（如果不存在）"
echo "INSERT IGNORE INTO MenuGroups (GroupKey, Title, IconName, SortOrder, IsEnabled, VisibleToSystemAdmin, VisibleToMerchantAdmin, VisibleToUser)"
echo "VALUES ('customer-features', '客户功能', 'ExtensionIcon', 10, 1, 1, 1, 0);"
echo ""
echo "-- 2. 添加菜单项"
echo "INSERT INTO MenuItems ("
echo "    MenuGroupId, ItemKey, Title, Href, IconName, SortOrder,"
echo "    IsEnabled, VisibleToSystemAdmin, VisibleToMerchantAdmin, VisibleToUser,"
echo "    IsExternal, Target"
echo ") VALUES ("
echo "    (SELECT Id FROM MenuGroups WHERE GroupKey = 'customer-features'),"
echo "    '$CUSTOMER_NAME-dashboard',  -- 重要：用于新路由系统"
echo "    '$CUSTOMER_PASCAL 功能',"
echo "    'https://yourdomain.com$ACCESS_PATH/',"
echo "    'DashboardIcon',"
echo "    1,"
echo "    1, 1, 1, 0,"
echo "    1,        -- IsExternal = true"
echo "    '_iframe' -- 在iframe中打开"
echo ");"
echo ""
echo "Nginx配置示例:"
echo "location $ACCESS_PATH/ {"
echo "    rewrite ^$ACCESS_PATH/(.*)$ /\$1 break;"
echo "    proxy_pass http://$CUSTOMER_NAME:80;"
echo "    # ... 其他代理配置"
echo "}"
