using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using NPOI.SS.UserModel;
using NPOI.SS.Util;
using NPOI.XSSF.UserModel;
using NPOI.HSSF.UserModel;
using SlzrCrossGate.Core.Database;
using SlzrCrossGate.Core.Models;
using SlzrCrossGate.Core.Repositories;
using SlzrCrossGate.WebAdmin.DTOs;
using System.IO;
using System.Text;

namespace SlzrCrossGate.WebAdmin.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class UnionPayTerminalKeysController : ControllerBase
    {
        private readonly TcpDbContext _dbContext;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly ILogger<UnionPayTerminalKeysController> _logger;

        private readonly Repository<UnionPayTerminalKey> _repository;

        // 辅助方法：获取单元格的字符串值
        private string? GetStringCellValue(ICell? cell)
        {
            if (cell == null) return null;

            switch (cell.CellType)
            {
                case CellType.String:
                    return cell.StringCellValue?.Trim();
                case CellType.Numeric:
                    // 如果是数字，转为字符串返回
                    return cell.NumericCellValue.ToString().Trim();
                case CellType.Boolean:
                    return cell.BooleanCellValue.ToString().Trim();
                case CellType.Formula:
                    switch (cell.CachedFormulaResultType)
                    {
                        case CellType.String:
                            return cell.StringCellValue?.Trim();
                        case CellType.Numeric:
                            return cell.NumericCellValue.ToString().Trim();
                        default:
                            return null;
                    }
                default:
                    return null;
            }
        }

        public UnionPayTerminalKeysController(
            TcpDbContext dbContext,
            UserManager<ApplicationUser> userManager,
            Repository<UnionPayTerminalKey> repository,
            ILogger<UnionPayTerminalKeysController> logger)
        {
            _dbContext = dbContext;
            _userManager = userManager;
            _logger = logger;
            _repository = repository;
        }

        // GET: api/UnionPayTerminalKeys
        [HttpGet]
        public async Task<ActionResult<PaginatedResult<UnionPayTerminalKeyDto>>> GetUnionPayTerminalKeys(
            [FromQuery] string? search,
            [FromQuery] bool? isInUse,
            [FromQuery] string? merchantId,
            [FromQuery] string? sortBy = "CreatedAt",
            [FromQuery] string? sortDirection = "desc",
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 10)
        {
            try
            {
                // 获取当前用户
                var currentUser = await _userManager.GetUserAsync(User);
                if (currentUser == null)
                {
                    return Unauthorized();
                }

                // 检查是否是系统管理员
                var isSystemAdmin = await _userManager.IsInRoleAsync(currentUser, "SystemAdmin");

                // 构建查询
                IQueryable<UnionPayTerminalKey> query = _dbContext.UnionPayTerminalKeys;

                // 如果不是系统管理员，只能查看自己商户的数据
                if (!isSystemAdmin && !string.IsNullOrEmpty(currentUser.MerchantID))
                {
                    query = query.Where(k => k.MerchantID == currentUser.MerchantID);
                }
                // 如果传入了商户ID参数，则按商户ID筛选
                else if (!string.IsNullOrEmpty(merchantId))
                {
                    query = query.Where(k => k.MerchantID == merchantId);
                }

                // 搜索条件
                if (!string.IsNullOrEmpty(search))
                {
                    search = search.Trim();
                    query = query.Where(k =>
                        k.MerchantID.Contains(search) ||
                        k.UP_MerchantID.Contains(search) ||
                        k.UP_TerminalID.Contains(search) ||
                        k.UP_Key.Contains(search) ||
                        (k.UP_MerchantName != null && k.UP_MerchantName.Contains(search)) ||
                        (k.MachineID != null && k.MachineID.Contains(search)) ||
                        (k.LineID != null && k.LineID.Contains(search)) ||
                        (k.MachineNO != null && k.MachineNO.Contains(search))
                    );
                }

                // 是否使用状态过滤
                if (isInUse.HasValue)
                {
                    query = query.Where(k => k.IsInUse == isInUse.Value);
                }

                // 排序
                if (!string.IsNullOrEmpty(sortBy))
                {
                    // 默认按创建时间降序排序
                    query = sortBy.ToLower() switch
                    {
                        "createdat" => sortDirection?.ToLower() == "asc" ? query.OrderBy(k => k.CreatedAt) : query.OrderByDescending(k => k.CreatedAt),
                        "updatedat" => sortDirection?.ToLower() == "asc" ? query.OrderBy(k => k.UpdatedAt) : query.OrderByDescending(k => k.UpdatedAt),
                        "merchantid" => sortDirection?.ToLower() == "asc" ? query.OrderBy(k => k.MerchantID) : query.OrderByDescending(k => k.MerchantID),
                        "isinuse" => sortDirection?.ToLower() == "asc" ? query.OrderBy(k => k.IsInUse) : query.OrderByDescending(k => k.IsInUse),
                        _ => query.OrderByDescending(k => k.CreatedAt)
                    };
                }
                else
                {
                    query = query.OrderByDescending(k => k.CreatedAt);
                }

                // 计算总记录数
                var totalCount = await query.CountAsync();

                // 分页
                var keys = await query
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .Join(_dbContext.Merchants,
                          key => key.MerchantID,
                          merchant => merchant.MerchantID,
                          (key, merchant) => new { Key = key, Merchant = merchant })
                    .Select(x => new UnionPayTerminalKeyDto
                    {
                        ID = x.Key.ID,
                        MerchantID = x.Key.MerchantID,
                        MerchantName = x.Merchant.Name ?? string.Empty,
                        UP_MerchantID = x.Key.UP_MerchantID,
                        UP_TerminalID = x.Key.UP_TerminalID,
                        UP_Key = x.Key.UP_Key,
                        UP_MerchantName = x.Key.UP_MerchantName,
                        IsInUse = x.Key.IsInUse,
                        MachineID = x.Key.MachineID,
                        LineID = x.Key.LineID,
                        MachineNO = x.Key.MachineNO,
                        CreatedAt = x.Key.CreatedAt,
                        UpdatedAt = x.Key.UpdatedAt
                    })
                    .ToListAsync();

                // 返回分页结果
                return Ok(new PaginatedResult<UnionPayTerminalKeyDto>
                {
                    Items = keys,
                    TotalCount = totalCount,
                    Page = page,
                    PageSize = pageSize
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取银联终端密钥列表时发生错误");
                return StatusCode(500, new { message = "服务器内部错误" });
            }
        }

        // GET: api/UnionPayTerminalKeys/5
        [HttpGet("{id}")]
        public async Task<ActionResult<UnionPayTerminalKeyDto>> GetUnionPayTerminalKey(int id)
        {
            try
            {
                // 获取当前用户
                var currentUser = await _userManager.GetUserAsync(User);
                if (currentUser == null)
                {
                    return Unauthorized();
                }

                // 检查是否是系统管理员
                var isSystemAdmin = await _userManager.IsInRoleAsync(currentUser, "SystemAdmin");

                // 获取银联终端密钥
                var key = await _dbContext.UnionPayTerminalKeys
                    .Join(_dbContext.Merchants,
                          k => k.MerchantID,
                          m => m.MerchantID,
                          (k, m) => new { Key = k, Merchant = m })
                    .Where(x => x.Key.ID == id)
                    .Select(x => new UnionPayTerminalKeyDto
                    {
                        ID = x.Key.ID,
                        MerchantID = x.Key.MerchantID,
                        MerchantName = x.Merchant.Name ?? string.Empty,
                        UP_MerchantID = x.Key.UP_MerchantID,
                        UP_TerminalID = x.Key.UP_TerminalID,
                        UP_Key = x.Key.UP_Key,
                        UP_MerchantName = x.Key.UP_MerchantName,
                        IsInUse = x.Key.IsInUse,
                        MachineID = x.Key.MachineID,
                        LineID = x.Key.LineID,
                        MachineNO = x.Key.MachineNO,
                        CreatedAt = x.Key.CreatedAt,
                        UpdatedAt = x.Key.UpdatedAt
                    })
                    .FirstOrDefaultAsync();

                if (key == null)
                {
                    return NotFound(new { message = "找不到指定的银联终端密钥" });
                }

                // 如果不是系统管理员，只能查看自己商户的数据
                if (!isSystemAdmin && !string.IsNullOrEmpty(currentUser.MerchantID) && key.MerchantID != currentUser.MerchantID)
                {
                    return Forbid();
                }

                return Ok(key);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取银联终端密钥详情时发生错误");
                return StatusCode(500, new { message = "服务器内部错误" });
            }
        }

        // POST: api/UnionPayTerminalKeys
        [HttpPost]
        [Authorize(Roles = "SystemAdmin,MerchantAdmin")]
        public async Task<ActionResult<UnionPayTerminalKeyDto>> CreateUnionPayTerminalKey([FromBody] CreateUnionPayTerminalKeyDto createDto)
        {
            try
            {
                // 获取当前用户
                var currentUser = await _userManager.GetUserAsync(User);
                if (currentUser == null)
                {
                    return Unauthorized();
                }

                // 检查是否是系统管理员
                var isSystemAdmin = await _userManager.IsInRoleAsync(currentUser, "SystemAdmin");
                var isMerchantAdmin = await _userManager.IsInRoleAsync(currentUser, "MerchantAdmin");

                // 如果不是系统管理员，只能创建自己商户的数据
                if (!isSystemAdmin && isMerchantAdmin && !string.IsNullOrEmpty(currentUser.MerchantID) && createDto.MerchantID != currentUser.MerchantID)
                {
                    return Forbid();
                }

                // 检查商户是否存在
                var merchant = await _dbContext.Merchants.FindAsync(createDto.MerchantID);
                if (merchant == null)
                {
                    return BadRequest(new { message = "商户不存在" });
                }

                // 检查是否已存在相同银联商户号和终端号的记录
                var exists = await _dbContext.UnionPayTerminalKeys
                    .AnyAsync(k => k.UP_MerchantID == createDto.UP_MerchantID && k.UP_TerminalID == createDto.UP_TerminalID);

                if (exists)
                {
                    return BadRequest(new { message = "已存在相同银联商户号和终端号的记录" });
                }

                // 创建新的银联终端密钥
                var now = DateTime.Now;
                var newKey = new UnionPayTerminalKey
                {
                    MerchantID = createDto.MerchantID,
                    UP_MerchantID = createDto.UP_MerchantID,
                    UP_TerminalID = createDto.UP_TerminalID,
                    UP_Key = createDto.UP_Key,
                    UP_MerchantName = createDto.UP_MerchantName,
                    IsInUse = false,
                    CreatedAt = now,
                    UpdatedAt = now
                };

                _dbContext.UnionPayTerminalKeys.Add(newKey);
                await _dbContext.SaveChangesAsync();

                // 查询包含商户名称的完整数据
                var result = await _dbContext.UnionPayTerminalKeys
                    .Join(_dbContext.Merchants,
                          k => k.MerchantID,
                          m => m.MerchantID,
                          (k, m) => new { Key = k, Merchant = m })
                    .Where(x => x.Key.ID == newKey.ID)
                    .Select(x => new UnionPayTerminalKeyDto
                    {
                        ID = x.Key.ID,
                        MerchantID = x.Key.MerchantID,
                        MerchantName = x.Merchant.Name ?? string.Empty,
                        UP_MerchantID = x.Key.UP_MerchantID,
                        UP_TerminalID = x.Key.UP_TerminalID,
                        UP_Key = x.Key.UP_Key,
                        UP_MerchantName = x.Key.UP_MerchantName,
                        IsInUse = x.Key.IsInUse,
                        MachineID = x.Key.MachineID,
                        LineID = x.Key.LineID,
                        MachineNO = x.Key.MachineNO,
                        CreatedAt = x.Key.CreatedAt,
                        UpdatedAt = x.Key.UpdatedAt
                    })
                    .FirstOrDefaultAsync();

                return CreatedAtAction(nameof(GetUnionPayTerminalKey), new { id = newKey.ID }, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建银联终端密钥时发生错误");
                return StatusCode(500, new { message = "服务器内部错误" });
            }
        }

        // PUT: api/UnionPayTerminalKeys/5
        [HttpPut("{id}")]
        [Authorize(Roles = "SystemAdmin,MerchantAdmin")]
        public async Task<IActionResult> UpdateUnionPayTerminalKey(int id, [FromBody] UpdateUnionPayTerminalKeyDto updateDto)
        {
            try
            {
                // 获取当前用户
                var currentUser = await _userManager.GetUserAsync(User);
                if (currentUser == null)
                {
                    return Unauthorized();
                }

                // 检查是否是系统管理员
                var isSystemAdmin = await _userManager.IsInRoleAsync(currentUser, "SystemAdmin");
                var isMerchantAdmin = await _userManager.IsInRoleAsync(currentUser, "MerchantAdmin");

                // 获取要更新的银联终端密钥
                var key = await _dbContext.UnionPayTerminalKeys.FindAsync(id);
                if (key == null)
                {
                    return NotFound(new { message = "找不到指定的银联终端密钥" });
                }

                // 如果不是系统管理员，只能更新自己商户的数据
                if (!isSystemAdmin && isMerchantAdmin && !string.IsNullOrEmpty(currentUser.MerchantID) && key.MerchantID != currentUser.MerchantID)
                {
                    return Forbid();
                }

                // 如果已被使用，不允许修改
                if (key.IsInUse)
                {
                    return BadRequest(new { message = "已被使用的银联终端密钥不允许修改" });
                }

                // 检查是否已存在相同银联商户号和终端号的记录（排除自身）
                var exists = await _dbContext.UnionPayTerminalKeys
                    .AnyAsync(k => k.ID != id && k.UP_MerchantID == updateDto.UP_MerchantID && k.UP_TerminalID == updateDto.UP_TerminalID);

                if (exists)
                {
                    return BadRequest(new { message = "已存在相同银联商户号和终端号的记录" });
                }

                // 更新银联终端密钥
                key.UP_MerchantID = updateDto.UP_MerchantID;
                key.UP_TerminalID = updateDto.UP_TerminalID;
                key.UP_Key = updateDto.UP_Key;
                key.UP_MerchantName = updateDto.UP_MerchantName;
                key.UpdatedAt = DateTime.Now;

                await _dbContext.SaveChangesAsync();

                return Ok(new { message = "银联终端密钥更新成功" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新银联终端密钥时发生错误");
                return StatusCode(500, new { message = "服务器内部错误" });
            }
        }

        // DELETE: api/UnionPayTerminalKeys/5
        [HttpDelete("{id}")]
        [Authorize(Roles = "SystemAdmin,MerchantAdmin")]
        public async Task<IActionResult> DeleteUnionPayTerminalKey(int id)
        {
            try
            {
                // 获取当前用户
                var currentUser = await _userManager.GetUserAsync(User);
                if (currentUser == null)
                {
                    return Unauthorized();
                }

                // 检查是否是系统管理员
                var isSystemAdmin = await _userManager.IsInRoleAsync(currentUser, "SystemAdmin");
                var isMerchantAdmin = await _userManager.IsInRoleAsync(currentUser, "MerchantAdmin");

                // 获取要删除的银联终端密钥
                var key = await _dbContext.UnionPayTerminalKeys.FindAsync(id);
                if (key == null)
                {
                    return NotFound(new { message = "找不到指定的银联终端密钥" });
                }

                // 如果不是系统管理员，只能删除自己商户的数据
                if (!isSystemAdmin && isMerchantAdmin && !string.IsNullOrEmpty(currentUser.MerchantID) && key.MerchantID != currentUser.MerchantID)
                {
                    return Forbid();
                }

                // 如果已被使用，不允许删除
                if (key.IsInUse)
                {
                    return BadRequest(new { message = "已被使用的银联终端密钥不允许删除" });
                }

                _dbContext.UnionPayTerminalKeys.Remove(key);
                await _dbContext.SaveChangesAsync();

                return Ok(new { message = "银联终端密钥删除成功" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除银联终端密钥时发生错误");
                return StatusCode(500, new { message = "服务器内部错误" });
            }
        }

        // POST: api/UnionPayTerminalKeys/import
        [HttpPost("import")]
        [Authorize(Roles = "SystemAdmin,MerchantAdmin")]
        public async Task<ActionResult<ImportUnionPayTerminalKeyResultDto>> ImportUnionPayTerminalKeys(IFormFile file)
        {
            try
            {
                // 获取当前用户
                var currentUser = await _userManager.GetUserAsync(User);
                if (currentUser == null)
                {
                    return Unauthorized();
                }

                // 检查是否是系统管理员
                var isSystemAdmin = await _userManager.IsInRoleAsync(currentUser, "SystemAdmin");
                var isMerchantAdmin = await _userManager.IsInRoleAsync(currentUser, "MerchantAdmin");

                if (file == null || file.Length == 0)
                {
                    return BadRequest(new { message = "未接收到文件或文件为空" });
                }

                string fileExtension = Path.GetExtension(file.FileName).ToLower();
                if (fileExtension != ".xlsx" && fileExtension != ".xls" && fileExtension != ".csv")
                {
                    return BadRequest(new { message = "仅支持Excel或CSV文件格式" });
                }

                var result = new ImportUnionPayTerminalKeyResultDto
                {
                    TotalCount = 0,
                    SuccessCount = 0,
                    FailCount = 0,
                    Errors = new List<string>()
                };

                List<UnionPayTerminalKey> keys = new List<UnionPayTerminalKey>();

                // 一次性获取所有商户ID，避免重复查询
                var merchantIdList = await _dbContext.Merchants
                    .Select(m => m.MerchantID)
                    .ToListAsync();
                var allMerchantIds = new HashSet<string>(merchantIdList);

                // 读取文件内容
                using (var stream = new MemoryStream())
                {
                    await file.CopyToAsync(stream);
                    stream.Position = 0;

                    if (fileExtension == ".csv")
                    {
                        // 处理CSV文件
                        using var reader = new StreamReader(stream);
                        string? line = reader.ReadLine(); // 跳过表头

                        // 跳过说明行、示例行和提示行，从第5行开始读取数据
                        for (int i = 0; i < 3; i++)
                        {
                            reader.ReadLine(); // 跳过第2、3、4行
                        }

                        // 处理数据行
                        var linesToProcess = new List<string>();
                        while ((line = reader.ReadLine()) != null)
                        {
                            linesToProcess.Add(line);
                        }

                        // 处理所有数据行
                        foreach (var dataLine in linesToProcess)
                        {
                            if (string.IsNullOrWhiteSpace(dataLine)) continue;

                            // 检查是否为空数据行（所有关键字段都为空）
                            if (IsEmptyDataLine(dataLine)) continue;

                            result.TotalCount++;
                            var columns = dataLine.Split(',');

                            try
                            {
                                if (columns.Length < 4)
                                {
                                    result.FailCount++;
                                    result.Errors.Add($"第{result.TotalCount}行: 数据列数不足");
                                    continue;
                                }

                                string merchantId = columns[0].Trim().Trim('"');
                                string upMerchantId = columns[1].Trim().Trim('"');
                                string upTerminalId = columns[2].Trim().Trim('"');
                                string upKey = columns[3].Trim().Trim('"');
                                string? upMerchantName = columns.Length > 4 ? columns[4].Trim().Trim('"') : null;



                                // 验证必填字段
                                if (string.IsNullOrEmpty(merchantId) || string.IsNullOrEmpty(upMerchantId) ||
                                    string.IsNullOrEmpty(upTerminalId) || string.IsNullOrEmpty(upKey))
                                {
                                    result.FailCount++;
                                    result.Errors.Add($"第{result.TotalCount}行: 必填字段不能为空");
                                    continue;
                                }

                                // 检查商户是否存在（内存检查）
                                if (!allMerchantIds.Contains(merchantId))
                                {
                                    result.FailCount++;
                                    result.Errors.Add($"第{result.TotalCount}行: 商户ID {merchantId} 不存在");
                                    continue;
                                }

                                // 如果当前用户是商户管理员，只能导入自己商户的数据
                                if (!isSystemAdmin && isMerchantAdmin && !string.IsNullOrEmpty(currentUser.MerchantID) && merchantId != currentUser.MerchantID)
                                {
                                    result.FailCount++;
                                    result.Errors.Add($"第{result.TotalCount}行: 无权导入商户ID {merchantId} 的数据");
                                    continue;
                                }

                                // 检查是否已存在相同的商户ID+银联商户号+银联终端号组合
                                var exists = await _dbContext.UnionPayTerminalKeys
                                    .AnyAsync(k => k.MerchantID == merchantId && k.UP_MerchantID == upMerchantId && k.UP_TerminalID == upTerminalId);

                                if (exists)
                                {
                                    result.FailCount++;
                                    result.Errors.Add($"第{result.TotalCount}行: 已存在相同的商户ID {merchantId}、银联商户号 {upMerchantId} 和终端号 {upTerminalId} 的记录");
                                    continue;
                                }

                                // 创建新的银联终端密钥
                                var now = DateTime.Now;
                                var newKey = new UnionPayTerminalKey
                                {
                                    MerchantID = merchantId,
                                    UP_MerchantID = upMerchantId,
                                    UP_TerminalID = upTerminalId,
                                    UP_Key = upKey,
                                    UP_MerchantName = upMerchantName,
                                    IsInUse = false,
                                    CreatedAt = now,
                                    UpdatedAt = now
                                };
                                keys.Add(newKey);
                            }
                            catch (Exception ex)
                            {
                                result.FailCount++;
                                result.Errors.Add($"第{result.TotalCount}行: {ex.Message}");
                            }
                        }
                    }
                    else
                    {
                        // 处理Excel文件
                        IWorkbook workbook;

                        // 根据文件扩展名创建不同的工作簿对象
                        if (fileExtension == ".xlsx")
                        {
                            workbook = new XSSFWorkbook(stream);
                        }
                        else // .xls
                        {
                            workbook = new HSSFWorkbook(stream);
                        }

                        ISheet sheet = workbook.GetSheetAt(0); // 获取第一个工作表

                        // 跳过表头、说明行、示例行和提示行，从第5行开始读取数据
                        int startRowIndex = 4; // 从第5行开始（索引4）

                        // 处理数据行
                        for (int rowIndex = startRowIndex; rowIndex <= sheet.LastRowNum; rowIndex++)
                        {
                            IRow? row = sheet.GetRow(rowIndex);
                            if (row == null) continue;

                            // 检查行是否为空（所有关键单元格都为空）
                            if (IsEmptyDataRow(row)) continue;

                            result.TotalCount++;

                            try
                            {
                                // 获取单元格值并转换为字符串
                                string? merchantId = GetStringCellValue(row.GetCell(0));
                                string? upMerchantId = GetStringCellValue(row.GetCell(1));
                                string? upTerminalId = GetStringCellValue(row.GetCell(2));
                                string? upKey = GetStringCellValue(row.GetCell(3));
                                string? upMerchantName = GetStringCellValue(row.GetCell(4));



                                if (string.IsNullOrEmpty(merchantId) || string.IsNullOrEmpty(upMerchantId) ||
                                    string.IsNullOrEmpty(upTerminalId) || string.IsNullOrEmpty(upKey))
                                {
                                    result.FailCount++;
                                    result.Errors.Add($"第{rowIndex + 1}行: 必填字段不能为空");
                                    continue;
                                }

                                // 检查商户是否存在（内存检查）
                                if (!allMerchantIds.Contains(merchantId))
                                {
                                    result.FailCount++;
                                    result.Errors.Add($"第{rowIndex + 1}行: 商户ID {merchantId} 不存在");
                                    continue;
                                }

                                // 如果当前用户是商户管理员，只能导入自己商户的数据
                                if (!isSystemAdmin && isMerchantAdmin && !string.IsNullOrEmpty(currentUser.MerchantID) && merchantId != currentUser.MerchantID)
                                {
                                    result.FailCount++;
                                    result.Errors.Add($"第{rowIndex + 1}行: 无权导入商户ID {merchantId} 的数据");
                                    continue;
                                }

                                // 检查是否已存在相同的商户ID+银联商户号+银联终端号组合
                                var exists = await _dbContext.UnionPayTerminalKeys
                                    .AnyAsync(k => k.MerchantID == merchantId && k.UP_MerchantID == upMerchantId && k.UP_TerminalID == upTerminalId);

                                if (exists)
                                {
                                    result.FailCount++;
                                    result.Errors.Add($"第{rowIndex + 1}行: 已存在相同的商户ID {merchantId}、银联商户号 {upMerchantId} 和终端号 {upTerminalId} 的记录");
                                    continue;
                                }

                                // 创建新的银联终端密钥
                                var now = DateTime.Now;
                                var newKey = new UnionPayTerminalKey
                                {
                                    MerchantID = merchantId,
                                    UP_MerchantID = upMerchantId,
                                    UP_TerminalID = upTerminalId,
                                    UP_Key = upKey,
                                    UP_MerchantName = upMerchantName,
                                    IsInUse = false,
                                    CreatedAt = now,
                                    UpdatedAt = now
                                };

                                keys.Add(newKey);
                            }
                            catch (Exception ex)
                            {
                                result.FailCount++;
                                result.Errors.Add($"第{row}行: {ex.Message}");
                            }
                        }
                    }

                    // 批量保存通过验证的记录
                    result.SuccessCount = keys.Count;
                    if (keys.Count > 0)
                    {
                        try
                        {
                            // 尝试使用批量插入
                            await _repository.BulkInsertAsync(keys);
                        }
                        catch (Exception bulkEx) when (bulkEx.Message.Contains("Loading local data is disabled"))
                        {
                            // 如果批量插入失败（MySQL local_infile被禁用），则使用普通插入
                            _logger.LogWarning("批量插入失败，使用普通插入方式: {Error}", bulkEx.Message);

                            foreach (var key in keys)
                            {
                                _dbContext.UnionPayTerminalKeys.Add(key);
                            }
                            await _dbContext.SaveChangesAsync();
                        }
                    }
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导入银联终端密钥时发生错误");
                return StatusCode(500, new { message = "服务器内部错误" });
            }
        }        // GET: api/UnionPayTerminalKeys/template
        [HttpGet("template")]
        public IActionResult DownloadTemplate()
        {
            try
            {
                // 创建Excel文件
                XSSFWorkbook workbook = new XSSFWorkbook();
                ISheet sheet = workbook.CreateSheet("银联终端密钥");

                // 创建表头样式
                ICellStyle headerStyle = workbook.CreateCellStyle();
                IFont headerFont = workbook.CreateFont();
                headerFont.IsBold = true;
                headerStyle.SetFont(headerFont);
                headerStyle.FillForegroundColor = IndexedColors.Grey25Percent.Index;
                headerStyle.FillPattern = FillPattern.SolidForeground;

                // 创建说明行样式
                ICellStyle noteStyle = workbook.CreateCellStyle();
                IFont noteFont = workbook.CreateFont();
                noteFont.IsItalic = true;
                noteFont.Color = IndexedColors.Grey50Percent.Index;
                noteStyle.SetFont(noteFont);

                // 创建文本格式样式（用于保持前导零）
                ICellStyle textStyle = workbook.CreateCellStyle();
                IDataFormat dataFormat = workbook.CreateDataFormat();
                textStyle.DataFormat = dataFormat.GetFormat("@"); // @ 表示文本格式

                // 创建表头
                IRow headerRow = sheet.CreateRow(0);
                string[] headers = { "商户ID", "银联商户号", "银联终端号", "银联终端密钥", "银联商户名称" };
                for (int i = 0; i < headers.Length; i++)
                {
                    ICell cell = headerRow.CreateCell(i);
                    cell.SetCellValue(headers[i]);
                    cell.CellStyle = headerStyle;
                }

                // 添加说明行
                IRow noteRow = sheet.CreateRow(1);
                string[] notes = {
                    "必填，8位字符，保持前导零",
                    "必填，15位数字",
                    "必填，8位数字",
                    "必填，32位十六进制",
                    "可选"
                };
                for (int i = 0; i < notes.Length; i++)
                {
                    ICell cell = noteRow.CreateCell(i);
                    cell.SetCellValue(notes[i]);
                    cell.CellStyle = noteStyle;
                }

                // 创建示例数据行
                IRow exampleRow = sheet.CreateRow(2);
                ICellStyle exampleStyle = workbook.CreateCellStyle();
                IFont exampleFont = workbook.CreateFont();
                exampleFont.Color = IndexedColors.Grey50Percent.Index;
                exampleStyle.SetFont(exampleFont);
                exampleStyle.FillForegroundColor = IndexedColors.LightGreen.Index;
                exampleStyle.FillPattern = FillPattern.SolidForeground;

                string[] examples = {
                    "00009998", "123456789012345", "12345678", "0123456789ABCDEF0123456789ABCDEF", "测试商户"
                };

                for (int i = 0; i < examples.Length; i++)
                {
                    ICell cell = exampleRow.CreateCell(i);
                    cell.SetCellValue(examples[i]);
                    cell.CellStyle = exampleStyle;
                }

                // 创建导入说明行
                IRow instructionRow = sheet.CreateRow(3);
                ICellStyle instructionStyle = workbook.CreateCellStyle();
                IFont instructionFont = workbook.CreateFont();
                instructionFont.IsBold = true;
                instructionFont.Color = IndexedColors.Red.Index;
                instructionStyle.SetFont(instructionFont);
                instructionStyle.FillForegroundColor = IndexedColors.LightYellow.Index;
                instructionStyle.FillPattern = FillPattern.SolidForeground;

                ICell instructionCell = instructionRow.CreateCell(0);
                instructionCell.SetCellValue("【重要提示】请从第5行开始填写数据，前4行为模板说明，不会被导入！");
                instructionCell.CellStyle = instructionStyle;

                // 合并说明单元格
                sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(3, 3, 0, headers.Length - 1));

                // 为数据区域设置文本格式（防止自动格式化数字）
                // 设置前1000行的格式，足够大部分导入需求
                for (int rowIndex = 4; rowIndex < 1004; rowIndex++)
                {
                    IRow dataRow = sheet.CreateRow(rowIndex);
                    for (int colIndex = 0; colIndex < headers.Length; colIndex++)
                    {
                        ICell cell = dataRow.CreateCell(colIndex);
                        // 对于商户ID、银联商户号、银联终端号这些可能有前导零的字段，设置为文本格式
                        if (colIndex == 0 || colIndex == 1 || colIndex == 2 || colIndex == 3)
                        {
                            cell.CellStyle = textStyle;
                        }
                    }
                }

                // 添加数据验证
                var helper = sheet.GetDataValidationHelper();

                // 为商户ID列添加长度验证（假设商户ID为8位）
                var merchantIdConstraint = helper.CreateTextLengthConstraint(OperatorType.EQUAL, "8", null);
                var merchantIdValidation = helper.CreateValidation(merchantIdConstraint, new CellRangeAddressList(4, 1003, 0, 0));
                merchantIdValidation.CreateErrorBox("输入错误", "商户ID必须为8位字符");
                merchantIdValidation.ShowErrorBox = true;
                sheet.AddValidationData(merchantIdValidation);

                // 为银联商户号列添加长度验证（通常为15位）
                var upMerchantIdConstraint = helper.CreateTextLengthConstraint(OperatorType.EQUAL, "15", null);
                var upMerchantIdValidation = helper.CreateValidation(upMerchantIdConstraint, new CellRangeAddressList(4, 1003, 1, 1));
                upMerchantIdValidation.CreateErrorBox("输入错误", "银联商户号必须为15位数字");
                upMerchantIdValidation.ShowErrorBox = true;
                sheet.AddValidationData(upMerchantIdValidation);

                // 为银联终端号列添加长度验证（通常为8位）
                var upTerminalIdConstraint = helper.CreateTextLengthConstraint(OperatorType.EQUAL, "8", null);
                var upTerminalIdValidation = helper.CreateValidation(upTerminalIdConstraint, new CellRangeAddressList(4, 1003, 2, 2));
                upTerminalIdValidation.CreateErrorBox("输入错误", "银联终端号必须为8位数字");
                upTerminalIdValidation.ShowErrorBox = true;
                sheet.AddValidationData(upTerminalIdValidation);

                // 为银联终端密钥列添加长度验证（通常为32位十六进制）
                var upKeyConstraint = helper.CreateTextLengthConstraint(OperatorType.EQUAL, "32", null);
                var upKeyValidation = helper.CreateValidation(upKeyConstraint, new CellRangeAddressList(4, 1003, 3, 3));
                upKeyValidation.CreateErrorBox("输入错误", "银联终端密钥必须为32位十六进制字符");
                upKeyValidation.ShowErrorBox = true;
                sheet.AddValidationData(upKeyValidation);

                // 设置列宽
                sheet.SetColumnWidth(0, 18 * 256); // 商户ID
                sheet.SetColumnWidth(1, 22 * 256); // 银联商户号
                sheet.SetColumnWidth(2, 18 * 256); // 银联终端号
                sheet.SetColumnWidth(3, 42 * 256); // 银联终端密钥
                sheet.SetColumnWidth(4, 32 * 256); // 银联商户名称

                // 写入内存流并获取字节数组
                byte[] excelBytes;
                using (MemoryStream ms = new MemoryStream())
                {
                    workbook.Write(ms);
                    ms.Flush();
                    excelBytes = ms.ToArray();
                }
                workbook.Close();

                // 返回文件
                string fileName = $"银联终端密钥导入模板_{DateTime.Now:yyyyMMddHHmmss}.xlsx";
                return File(excelBytes, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "下载银联终端密钥导入模板时发生错误");
                return StatusCode(500, new { message = "服务器内部错误" });
            }
        }

        // GET: api/UnionPayTerminalKeys/export
        [HttpGet("export")]
        public async Task<IActionResult> ExportUnionPayTerminalKeys(
            [FromQuery] string? search,
            [FromQuery] bool? isInUse,
            [FromQuery] string? merchantId,
            [FromQuery] string? sortBy = "CreatedAt",
            [FromQuery] string? sortDirection = "desc")
        {
            try
            {
                // 获取当前用户
                var currentUser = await _userManager.GetUserAsync(User);
                if (currentUser == null)
                {
                    return Unauthorized();
                }

                // 检查是否是系统管理员
                var isSystemAdmin = await _userManager.IsInRoleAsync(currentUser, "SystemAdmin");

                // 构建查询
                IQueryable<UnionPayTerminalKey> query = _dbContext.UnionPayTerminalKeys;

                // 如果不是系统管理员，只能导出自己商户的数据
                if (!isSystemAdmin && !string.IsNullOrEmpty(currentUser.MerchantID))
                {
                    query = query.Where(k => k.MerchantID == currentUser.MerchantID);
                }
                // 如果传入了商户ID参数，则按商户ID筛选
                else if (!string.IsNullOrEmpty(merchantId))
                {
                    query = query.Where(k => k.MerchantID == merchantId);
                }

                // 搜索条件
                if (!string.IsNullOrEmpty(search))
                {
                    search = search.Trim();
                    query = query.Where(k =>
                        k.MerchantID.Contains(search) ||
                        k.UP_MerchantID.Contains(search) ||
                        k.UP_TerminalID.Contains(search) ||
                        k.UP_Key.Contains(search) ||
                        (k.UP_MerchantName != null && k.UP_MerchantName.Contains(search)) ||
                        (k.MachineID != null && k.MachineID.Contains(search)) ||
                        (k.LineID != null && k.LineID.Contains(search)) ||
                        (k.MachineNO != null && k.MachineNO.Contains(search))
                    );
                }

                // 是否使用状态过滤
                if (isInUse.HasValue)
                {
                    query = query.Where(k => k.IsInUse == isInUse.Value);
                }

                // 排序
                if (!string.IsNullOrEmpty(sortBy))
                {
                    query = sortBy.ToLower() switch
                    {
                        "createdat" => sortDirection?.ToLower() == "asc" ? query.OrderBy(k => k.CreatedAt) : query.OrderByDescending(k => k.CreatedAt),
                        "updatedat" => sortDirection?.ToLower() == "asc" ? query.OrderBy(k => k.UpdatedAt) : query.OrderByDescending(k => k.UpdatedAt),
                        "merchantid" => sortDirection?.ToLower() == "asc" ? query.OrderBy(k => k.MerchantID) : query.OrderByDescending(k => k.MerchantID),
                        "isinuse" => sortDirection?.ToLower() == "asc" ? query.OrderBy(k => k.IsInUse) : query.OrderByDescending(k => k.IsInUse),
                        _ => query.OrderByDescending(k => k.CreatedAt)
                    };
                }
                else
                {
                    query = query.OrderByDescending(k => k.CreatedAt);
                }

                // 获取数据（限制最大10000条记录）
                var keys = await query
                    .Take(10000)
                    .Join(_dbContext.Merchants,
                          key => key.MerchantID,
                          merchant => merchant.MerchantID,
                          (key, merchant) => new { Key = key, Merchant = merchant })
                    .Select(x => new UnionPayTerminalKeyDto
                    {
                        ID = x.Key.ID,
                        MerchantID = x.Key.MerchantID,
                        MerchantName = x.Merchant.Name ?? string.Empty,
                        UP_MerchantID = x.Key.UP_MerchantID,
                        UP_TerminalID = x.Key.UP_TerminalID,
                        UP_Key = x.Key.UP_Key,
                        UP_MerchantName = x.Key.UP_MerchantName,
                        IsInUse = x.Key.IsInUse,
                        MachineID = x.Key.MachineID,
                        LineID = x.Key.LineID,
                        MachineNO = x.Key.MachineNO,
                        CreatedAt = x.Key.CreatedAt,
                        UpdatedAt = x.Key.UpdatedAt
                    })
                    .ToListAsync();

                // 创建CSV内容
                var csv = new StringBuilder();
                csv.AppendLine("商户ID,商户名称,银联商户号,银联终端号,银联终端密钥,银联商户名称,使用状态,出厂序列号,线路编号,设备编号,创建时间,更新时间");

                foreach (var key in keys)
                {
                    csv.AppendLine($"{key.MerchantID},{key.MerchantName},{key.UP_MerchantID},{key.UP_TerminalID},{key.UP_Key}," +
                                  $"{key.UP_MerchantName ?? ""},{(key.IsInUse ? "已使用" : "未使用")},{key.MachineID ?? ""}," +
                                  $"{key.LineID ?? ""},{key.MachineNO ?? ""},{key.CreatedAt:yyyy-MM-dd HH:mm:ss},{key.UpdatedAt:yyyy-MM-dd HH:mm:ss}");
                }

                // 返回CSV文件
                var csvBytes = Encoding.UTF8.GetBytes(csv.ToString());
                var fileName = $"银联终端密钥_{DateTime.Now:yyyyMMddHHmmss}.csv";
                return File(csvBytes, "text/csv", fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导出银联终端密钥时发生错误");
                return StatusCode(500, new { message = "服务器内部错误" });
            }
        }

        // POST: api/UnionPayTerminalKeys/{id}/unbind
        [HttpPost("{id}/unbind")]
        [Authorize(Roles = "SystemAdmin,MerchantAdmin")]
        public async Task<IActionResult> UnbindUnionPayTerminalKey(int id)
        {
            try
            {
                // 获取当前用户
                var currentUser = await _userManager.GetUserAsync(User);
                if (currentUser == null)
                {
                    return Unauthorized();
                }

                // 检查是否是系统管理员
                var isSystemAdmin = await _userManager.IsInRoleAsync(currentUser, "SystemAdmin");
                var isMerchantAdmin = await _userManager.IsInRoleAsync(currentUser, "MerchantAdmin");

                // 获取要解绑的银联终端密钥
                var key = await _dbContext.UnionPayTerminalKeys.FindAsync(id);
                if (key == null)
                {
                    return NotFound(new { message = "找不到指定的银联终端密钥" });
                }

                // 如果不是系统管理员，只能解绑自己商户的数据
                if (!isSystemAdmin && isMerchantAdmin && !string.IsNullOrEmpty(currentUser.MerchantID) && key.MerchantID != currentUser.MerchantID)
                {
                    return Forbid();
                }

                // 如果未被使用，无需解绑
                if (!key.IsInUse)
                {
                    return BadRequest(new { message = "该银联终端密钥未被绑定，无需解绑" });
                }

                // 解绑银联终端密钥
                key.IsInUse = false;
                key.MachineID = null;
                key.LineID = null;
                key.MachineNO = null;
                key.UpdatedAt = DateTime.Now;

                await _dbContext.SaveChangesAsync();

                return Ok(new { message = "银联终端密钥解绑成功" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "解绑银联终端密钥时发生错误");
                return StatusCode(500, new { message = "服务器内部错误" });
            }
        }



        // 辅助方法：检测CSV行是否为空数据行
        private static bool IsEmptyDataLine(string line)
        {
            if (string.IsNullOrWhiteSpace(line)) return true;

            var columns = line.Split(',');
            if (columns.Length < 4) return true;

            // 检查前4个关键字段是否都为空
            for (int i = 0; i < 4; i++)
            {
                var value = columns[i].Trim().Trim('"');
                if (!string.IsNullOrEmpty(value))
                {
                    return false; // 有非空字段，不是空行
                }
            }
            return true; // 所有关键字段都为空
        }

        // 辅助方法：检测Excel行是否为空数据行
        private bool IsEmptyDataRow(IRow row)
        {
            // 检查前4个关键单元格是否都为空
            for (int i = 0; i < 4; i++)
            {
                var value = GetStringCellValue(row.GetCell(i));
                if (!string.IsNullOrEmpty(value))
                {
                    return false; // 有非空字段，不是空行
                }
            }
            return true; // 所有关键字段都为空
        }
    }
}
