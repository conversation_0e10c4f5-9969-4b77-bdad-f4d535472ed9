import React, { memo } from 'react';
import { Skeleton } from '@mui/material';
import { useFeaturePermission } from '../hooks/useFeaturePermission';

/**
 * 功能权限保护组件 (优化版)
 * 
 * 特性:
 * 1. 支持权限常量
 * 2. 性能优化
 * 3. 错误边界处理
 * 4. 开发模式调试
 */
export const FeatureGuard = memo(({ 
  featureKey,           // 必需: 功能标识符 (支持PERMISSIONS常量)
  children,             // 必需: 有权限时显示的内容
  fallback = null,      // 可选: 无权限时显示的内容
  additionalCheck = true, // 可选: 额外的检查条件
  showDebugInfo = false // 可选: 开发模式显示调试信息
}) => {
  const { canPerform, loading, error } = useFeaturePermission();
  
  // 开发模式调试信息
  if (process.env.NODE_ENV === 'development' && showDebugInfo) {
    console.log(`FeatureGuard [${featureKey}]:`, {
      hasPermission: canPerform(featureKey, true),
      additionalCheck,
      finalResult: canPerform(featureKey, additionalCheck)
    });
  }

  // 加载中状态
  if (loading) {
    return fallback || <Skeleton variant="rectangular" width={100} height={36} />;
  }

  // 错误状态
  if (error) {
    console.error(`FeatureGuard error for ${featureKey}:`, error);
    return fallback;
  }

  // 权限检查
  if (!canPerform(featureKey, additionalCheck)) {
    return fallback;
  }
  
  return children;
});

FeatureGuard.displayName = 'FeatureGuard';

/**
 * 无权限页面组件
 */
export const NoPermissionPage = ({ message = "您没有访问此页面的权限" }) => {
  return (
    <div style={{ 
      display: 'flex', 
      flexDirection: 'column', 
      alignItems: 'center', 
      justifyContent: 'center', 
      height: '400px',
      textAlign: 'center'
    }}>
      <div style={{ fontSize: '64px', marginBottom: '16px' }}>🔒</div>
      <h2 style={{ color: '#666', marginBottom: '8px' }}>访问受限</h2>
      <p style={{ color: '#999' }}>{message}</p>
    </div>
  );
};

/**
 * 权限拒绝原因消息映射
 */
export const getPermissionDeniedMessage = (reason) => {
  const messages = {
    'insufficient_permission': '您没有执行此操作的权限',
    'not_self': '不能对自己执行此操作',
    'is_system_admin': '需要系统管理员权限',
    'target_not_system_admin': '不能对系统管理员执行此操作',
    'same_merchant_or_admin': '只能操作同商户的用户',
    'user_active': '用户必须处于活跃状态',
    'long_time_no_login': '用户最近有登录记录',
    'item_active': '项目必须处于非活跃状态',
    'has_dependencies': '存在关联数据，无法删除',
    'status_not_allow': '当前状态不允许此操作',
    'merchant_mismatch': '只能操作本商户的数据'
  };
  return messages[reason] || '操作被拒绝';
};
