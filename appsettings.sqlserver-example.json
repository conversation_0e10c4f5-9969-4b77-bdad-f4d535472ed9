{
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost,1433;Database=slzrcrossgate;User Id=sa;Password=YourPassword123!;TrustServerCertificate=true;MultipleActiveResultSets=true"
  },
  "DatabaseProvider": "SqlServer",
  
  // 迁移配置
  "EnableMigration": true,  // WebAdmin: true, ApiService: false (推荐)
  "CheckIndexesOnStartup": false,  // ApiService 是否在启动时检查索引完整性
  
  // 迁移选项
  "Migration": {
    "CommandTimeout": 600,  // 迁移命令超时时间（秒）
    "CreateBackup": true,   // 是否在迁移前创建备份
    "BackupPath": "./backups",  // 备份文件路径
    "ValidateIndexes": true,    // 是否验证索引完整性
    "AutoRecovery": true,       // 是否启用自动索引恢复
    "LogDetailedErrors": true   // 是否记录详细的错误信息
  },
  
  // 索引恢复配置
  "IndexRecovery": {
    "EnableAutoRecovery": true,     // 是否启用自动恢复
    "MaxRetryAttempts": 3,          // 最大重试次数
    "RetryDelaySeconds": 5,         // 重试间隔（秒）
    "GenerateRecoveryScript": true, // 是否生成恢复脚本
    "ScriptOutputPath": "./recovery-scripts"  // 脚本输出路径
  },
  
  // 健康检查配置
  "HealthChecks": {
    "Database": {
      "Enabled": true,
      "CheckIndexes": true,  // 是否在健康检查中包含索引检查
      "Timeout": 30          // 健康检查超时时间（秒）
    }
  },
  
  // 日志配置
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "SlzrCrossGate.Core.Services.DatabaseMigrationService": "Debug",
      "SlzrCrossGate.Core.Services.IndexRecoveryService": "Debug",
      "Microsoft.EntityFrameworkCore.Database.Command": "Warning"
    }
  },
  
  // RabbitMQ配置
  "RabbitMQ": {
    "HostName": "localhost",
    "UserName": "guest",
    "Password": "guest",
    "Port": 5672
  },
  
  // 文件服务配置
  "FileService": {
    "DefaultStorageType": "Local",
    "LocalFilePath": "C:\\Files",
    "MinIO": {
      "Endpoint": "minio.example.com",
      "AccessKey": "your-access-key",
      "SecretKey": "your-secret-key",
      "BucketName": "your-bucket-name"
    }
  },
  
  // JWT配置
  "Jwt": {
    "Key": "YourSecretKeyHere12345678901234567890",
    "Issuer": "WebAdmin",
    "Audience": "WebAdmin",
    "ExpiresInHours": 24
  },
  
  // 会话超时配置
  "SessionTimeout": {
    "Minutes": 120,
    "WarningMinutes": 10
  },
  
  // 微信配置
  "Wechat": {
    "AppId": "wx123456789abcdef",
    "AppSecret": "your_app_secret_here",
    "RedirectUrl": "https://localhost:7296/api/auth/wechat-callback",
    "QrCodeExpiryMinutes": 5
  },
  
  // 终端日志处理配置
  "TerminalLogProcessing": {
    "Enabled": true,
    "BatchSize": 100,
    "BatchIntervalSeconds": 10,
    "EnableDebugLogging": false,
    "Exchange": "SlzrCrossGate.Data",
    "Queue": "SlzrCrossGate.Data.Queue.TerminalLog",
    "RoutingKey": "Tcp.city.#"
  }
}
