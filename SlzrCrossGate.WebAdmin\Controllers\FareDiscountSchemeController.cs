using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using SlzrCrossGate.Core.Database;
using SlzrCrossGate.Core.Models;
using SlzrCrossGate.WebAdmin.DTOs;
using SlzrCrossGate.WebAdmin.Services;
using System.Text.Json;
using System.Text.Encodings.Web;
using System.Text.Unicode;
using System.Text;
using SlzrCrossGate.Core.Service.FileStorage;

namespace SlzrCrossGate.WebAdmin.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class FareDiscountSchemeController : ControllerBase
    {
        private readonly TcpDbContext _dbContext;
        private readonly UserService _userService;
        private readonly FileService _fileService;
        private readonly ILogger<FareDiscountSchemeController> _logger;

        // 常量定义
        private const string FARE_DISCOUNT_FILE_TYPE = "PZB"; // 票价折扣文件类型固定为PZB

        public FareDiscountSchemeController(
            TcpDbContext dbContext,
            UserService userService,
            FileService fileService,
            ILogger<FareDiscountSchemeController> logger)
        {
            _dbContext = dbContext;
            _userService = userService;
            _fileService = fileService;
            _logger = logger;
        }

        // GET: api/FareDiscountScheme
        [HttpGet]
        public async Task<ActionResult<PaginatedResult<FareDiscountSchemeDto>>> GetFareDiscountSchemes([FromQuery] FareDiscountSchemeQueryDto query)
        {
            try
            {
                // 获取当前用户的商户ID
                var currentUserMerchantId = await _userService.GetUserMerchantIdAsync(User);
                var isSystemAdmin = User.IsInRole("SystemAdmin");

                var dbQuery = _dbContext.FareDiscountSchemes
                    .Include(s => s.Merchant)
                    .AsQueryable();

                // 如果不是系统管理员，只能查看自己商户的方案
                if (!isSystemAdmin)
                {
                    if (string.IsNullOrEmpty(currentUserMerchantId))
                    {
                        return BadRequest("无法获取用户商户信息");
                    }
                    dbQuery = dbQuery.Where(s => s.MerchantID == currentUserMerchantId);
                }
                else if (!string.IsNullOrEmpty(query.MerchantId))
                {
                    // 系统管理员可以按商户筛选
                    dbQuery = dbQuery.Where(s => s.MerchantID == query.MerchantId);
                }

                // 应用搜索条件
                if (!string.IsNullOrEmpty(query.Search))
                {
                    dbQuery = dbQuery.Where(s => s.SchemeName.Contains(query.Search) || 
                                                s.SchemeCode.Contains(query.Search) ||
                                                (s.Description != null && s.Description.Contains(query.Search)));
                }

                // 应用状态筛选
                if (query.IsActive.HasValue)
                {
                    dbQuery = dbQuery.Where(s => s.IsActive == query.IsActive.Value);
                }

                // 获取总数
                var totalCount = await dbQuery.CountAsync();

                // 应用分页和排序
                var items = await dbQuery
                    .OrderByDescending(s => s.UpdateTime)
                    .Skip((query.Page - 1) * query.PageSize)
                    .Take(query.PageSize)
                    .Select(s => new FareDiscountSchemeDto
                    {
                        ID = s.ID,
                        MerchantID = s.MerchantID,
                        MerchantName = s.Merchant != null ? s.Merchant.Name : "",
                        SchemeName = s.SchemeName,
                        SchemeCode = s.SchemeCode,
                        Description = s.Description,
                        ExtraParams = !string.IsNullOrEmpty(s.ExtraParamsJson) ?
                            JsonSerializer.Deserialize<object>(s.ExtraParamsJson, (JsonSerializerOptions?)null) : null,
                        CardDiscountInfo = !string.IsNullOrEmpty(s.CardDiscountInfoJson) ?
                            JsonSerializer.Deserialize<object>(s.CardDiscountInfoJson, (JsonSerializerOptions?)null) : null,
                        IsActive = s.IsActive,
                        UsageCount = s.UsageCount,
                        LastUsedTime = s.LastUsedTime,
                        CurrentVersion = s.CurrentVersion,
                        CurrentFilePara = s.CurrentFilePara,
                        CurrentFileVerID = s.CurrentFileVerID,
                        CreateTime = s.CreateTime,
                        UpdateTime = s.UpdateTime,
                        Creator = s.Creator,
                        Updater = s.Updater
                    })
                    .ToListAsync();

                return Ok(new PaginatedResult<FareDiscountSchemeDto>
                {
                    Items = items,
                    TotalCount = totalCount,
                    Page = query.Page,
                    PageSize = query.PageSize
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取票价折扣方案列表失败");
                return StatusCode(500, new { message = "服务器内部错误" });
            }
        }

        // GET: api/FareDiscountScheme/{id}
        [HttpGet("{id}")]
        public async Task<ActionResult<FareDiscountSchemeDto>> GetFareDiscountScheme(int id)
        {
            try
            {
                // 获取当前用户的商户ID
                var currentUserMerchantId = await _userService.GetUserMerchantIdAsync(User);
                var isSystemAdmin = User.IsInRole("SystemAdmin");

                var scheme = await _dbContext.FareDiscountSchemes
                    .Include(s => s.Merchant)
                    .FirstOrDefaultAsync(s => s.ID == id);

                if (scheme == null)
                {
                    return NotFound("票价折扣方案不存在");
                }

                // 如果不是系统管理员，只能查看自己商户的方案
                if (!isSystemAdmin && scheme.MerchantID != currentUserMerchantId)
                {
                    return Forbid();
                }

                var dto = new FareDiscountSchemeDto
                {
                    ID = scheme.ID,
                    MerchantID = scheme.MerchantID,
                    MerchantName = scheme.Merchant?.Name ?? "",
                    SchemeName = scheme.SchemeName,
                    SchemeCode = scheme.SchemeCode,
                    Description = scheme.Description,
                    ExtraParams = !string.IsNullOrEmpty(scheme.ExtraParamsJson) ? 
                        JsonSerializer.Deserialize<object>(scheme.ExtraParamsJson) : null,
                    CardDiscountInfo = !string.IsNullOrEmpty(scheme.CardDiscountInfoJson) ? 
                        JsonSerializer.Deserialize<object>(scheme.CardDiscountInfoJson) : null,
                    IsActive = scheme.IsActive,
                    UsageCount = scheme.UsageCount,
                    LastUsedTime = scheme.LastUsedTime,
                    CurrentVersion = scheme.CurrentVersion,
                    CurrentFilePara = scheme.CurrentFilePara,
                    CurrentFileVerID = scheme.CurrentFileVerID,
                    CreateTime = scheme.CreateTime,
                    UpdateTime = scheme.UpdateTime,
                    Creator = scheme.Creator,
                    Updater = scheme.Updater
                };

                return Ok(dto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取票价折扣方案详情失败，ID: {Id}", id);
                return StatusCode(500, new { message = "服务器内部错误" });
            }
        }

        // POST: api/FareDiscountScheme
        [HttpPost]
        public async Task<ActionResult<FareDiscountSchemeDto>> CreateFareDiscountScheme(CreateFareDiscountSchemeDto model)
        {
            try
            {
                // 获取当前用户的商户ID和用户名
                var currentUserMerchantId = await _userService.GetUserMerchantIdAsync(User);
                var isSystemAdmin = User.IsInRole("SystemAdmin");
                var username = UserService.GetUserNameForOperator(User);

                if (string.IsNullOrEmpty(model.MerchantID))
                {
                    return BadRequest("必须指定商户ID");
                }

                // 如果不是系统管理员，只能创建自己商户的方案
                if (!isSystemAdmin && model.MerchantID != currentUserMerchantId)
                {
                    return Forbid();
                }

                // 确定目标商户ID
                var targetMerchantId = model.MerchantID;

                // 检查方案编码是否已存在
                var existingScheme = await _dbContext.FareDiscountSchemes
                    .FirstOrDefaultAsync(s => s.MerchantID == targetMerchantId && s.SchemeCode == model.SchemeCode);

                if (existingScheme != null)
                {
                    return BadRequest("方案编码已存在");
                }

                // 创建新方案
                var scheme = new FareDiscountScheme
                {
                    MerchantID = targetMerchantId,
                    SchemeName = model.SchemeName,
                    SchemeCode = model.SchemeCode,
                    Description = model.Description,
                    ExtraParamsJson = model.ExtraParams != null ? JsonSerializer.Serialize(model.ExtraParams) : null,
                    CardDiscountInfoJson = model.CardDiscountInfo != null ? JsonSerializer.Serialize(model.CardDiscountInfo) : null,
                    IsActive = model.IsActive,
                    CurrentFilePara = model.SchemeCode, // 默认使用方案编号作为文件参数
                    CreateTime = DateTime.Now,
                    UpdateTime = DateTime.Now,
                    Creator = username,
                    Updater = username,
                    UsageCount = 0
                };

                _dbContext.FareDiscountSchemes.Add(scheme);
                await _dbContext.SaveChangesAsync();

                // 返回创建的方案
                var merchant = await _dbContext.Merchants.FindAsync(targetMerchantId);
                var dto = new FareDiscountSchemeDto
                {
                    ID = scheme.ID,
                    MerchantID = scheme.MerchantID,
                    MerchantName = merchant?.Name ?? "",
                    SchemeName = scheme.SchemeName,
                    SchemeCode = scheme.SchemeCode,
                    Description = scheme.Description,
                    ExtraParams = model.ExtraParams,
                    CardDiscountInfo = model.CardDiscountInfo,
                    IsActive = scheme.IsActive,
                    UsageCount = scheme.UsageCount,
                    LastUsedTime = scheme.LastUsedTime,
                    CurrentVersion = scheme.CurrentVersion,
                    CurrentFilePara = scheme.CurrentFilePara,
                    CurrentFileVerID = scheme.CurrentFileVerID,
                    CreateTime = scheme.CreateTime,
                    UpdateTime = scheme.UpdateTime,
                    Creator = scheme.Creator,
                    Updater = scheme.Updater
                };

                return CreatedAtAction(nameof(GetFareDiscountScheme), new { id = scheme.ID }, dto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建票价折扣方案失败");
                return StatusCode(500, new { message = "服务器内部错误" });
            }
        }

        // PUT: api/FareDiscountScheme/{id}
        [HttpPut("{id}")]
        public async Task<ActionResult<FareDiscountSchemeDto>> UpdateFareDiscountScheme(int id, UpdateFareDiscountSchemeDto model)
        {
            try
            {
                // 获取当前用户的商户ID和用户名
                var currentUserMerchantId = await _userService.GetUserMerchantIdAsync(User);
                var isSystemAdmin = User.IsInRole("SystemAdmin");
                var username = UserService.GetUserNameForOperator(User);

                var scheme = await _dbContext.FareDiscountSchemes
                    .Include(s => s.Merchant)
                    .FirstOrDefaultAsync(s => s.ID == id);

                if (scheme == null)
                {
                    return NotFound("票价折扣方案不存在");
                }

                // 如果不是系统管理员，只能修改自己商户的方案
                if (!isSystemAdmin && scheme.MerchantID != currentUserMerchantId)
                {
                    return Forbid();
                }

                // 更新方案信息
                scheme.SchemeName = model.SchemeName;
                scheme.Description = model.Description;
                scheme.ExtraParamsJson = model.ExtraParams != null ? JsonSerializer.Serialize(model.ExtraParams) : null;
                scheme.CardDiscountInfoJson = model.CardDiscountInfo != null ? JsonSerializer.Serialize(model.CardDiscountInfo) : null;
                scheme.IsActive = model.IsActive;
                scheme.UpdateTime = DateTime.Now;
                scheme.Updater = username;

                await _dbContext.SaveChangesAsync();

                // 返回更新后的方案
                var dto = new FareDiscountSchemeDto
                {
                    ID = scheme.ID,
                    MerchantID = scheme.MerchantID,
                    MerchantName = scheme.Merchant?.Name ?? "",
                    SchemeName = scheme.SchemeName,
                    SchemeCode = scheme.SchemeCode,
                    Description = scheme.Description,
                    ExtraParams = model.ExtraParams,
                    CardDiscountInfo = model.CardDiscountInfo,
                    IsActive = scheme.IsActive,
                    UsageCount = scheme.UsageCount,
                    LastUsedTime = scheme.LastUsedTime,
                    CurrentVersion = scheme.CurrentVersion,
                    CurrentFilePara = scheme.CurrentFilePara,
                    CurrentFileVerID = scheme.CurrentFileVerID,
                    CreateTime = scheme.CreateTime,
                    UpdateTime = scheme.UpdateTime,
                    Creator = scheme.Creator,
                    Updater = scheme.Updater
                };

                return Ok(dto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新票价折扣方案失败，ID: {Id}", id);
                return StatusCode(500, new { message = "服务器内部错误" });
            }
        }

        // DELETE: api/FareDiscountScheme/{id}
        [HttpDelete("{id}")]
        public async Task<ActionResult> DeleteFareDiscountScheme(int id)
        {
            try
            {
                // 获取当前用户的商户ID
                var currentUserMerchantId = await _userService.GetUserMerchantIdAsync(User);
                var isSystemAdmin = User.IsInRole("SystemAdmin");

                var scheme = await _dbContext.FareDiscountSchemes
                    .FirstOrDefaultAsync(s => s.ID == id);

                if (scheme == null)
                {
                    return NotFound("票价折扣方案不存在");
                }

                // 如果不是系统管理员，只能删除自己商户的方案
                if (!isSystemAdmin && scheme.MerchantID != currentUserMerchantId)
                {
                    return Forbid();
                }

                // 检查是否有线路正在使用此方案
                var usageCount = await _dbContext.LinePriceInfos
                    .CountAsync(l => l.CurrentFareDiscountSchemeID == id);

                if (usageCount > 0)
                {
                    return BadRequest($"无法删除，当前有 {usageCount} 条线路正在使用此方案");
                }

                // 检查是否有版本历史使用此方案
                var versionUsageCount = await _dbContext.LinePriceInfoVersions
                    .CountAsync(v => v.FareDiscountSchemeID == id);

                if (versionUsageCount > 0)
                {
                    return BadRequest($"无法删除，历史版本中有 {versionUsageCount} 个版本使用了此方案");
                }

                _dbContext.FareDiscountSchemes.Remove(scheme);
                await _dbContext.SaveChangesAsync();

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除票价折扣方案失败，ID: {Id}", id);
                return StatusCode(500, new { message = "服务器内部错误" });
            }
        }

        // POST: api/FareDiscountScheme/{id}/copy
        [HttpPost("{id}/copy")]
        public async Task<ActionResult<FareDiscountSchemeDto>> CopyFareDiscountScheme(int id, [FromBody] string newSchemeName)
        {
            try
            {
                // 获取当前用户的商户ID和用户名
                var currentUserMerchantId = await _userService.GetUserMerchantIdAsync(User);
                var isSystemAdmin = User.IsInRole("SystemAdmin");
                var username = UserService.GetUserNameForOperator(User);

                var originalScheme = await _dbContext.FareDiscountSchemes
                    .Include(s => s.Merchant)
                    .FirstOrDefaultAsync(s => s.ID == id);

                if (originalScheme == null)
                {
                    return NotFound("原票价折扣方案不存在");
                }

                // 如果不是系统管理员，只能复制自己商户的方案
                if (!isSystemAdmin && originalScheme.MerchantID != currentUserMerchantId)
                {
                    return Forbid();
                }

                if (string.IsNullOrEmpty(newSchemeName))
                {
                    return BadRequest("新方案名称不能为空");
                }

                // 生成新的方案编码
                var newSchemeCode = $"{originalScheme.SchemeCode}_COPY_{DateTime.Now:yyyyMMddHHmmss}";

                // 检查新方案编码是否已存在
                var existingScheme = await _dbContext.FareDiscountSchemes
                    .FirstOrDefaultAsync(s => s.MerchantID == originalScheme.MerchantID && s.SchemeCode == newSchemeCode);

                if (existingScheme != null)
                {
                    return BadRequest("生成的方案编码已存在，请稍后重试");
                }

                // 创建复制的方案
                var newScheme = new FareDiscountScheme
                {
                    MerchantID = originalScheme.MerchantID,
                    SchemeName = newSchemeName,
                    SchemeCode = newSchemeCode,
                    Description = $"复制自：{originalScheme.SchemeName}",
                    ExtraParamsJson = originalScheme.ExtraParamsJson,
                    CardDiscountInfoJson = originalScheme.CardDiscountInfoJson,
                    IsActive = true,
                    CreateTime = DateTime.Now,
                    UpdateTime = DateTime.Now,
                    Creator = username,
                    Updater = username,
                    UsageCount = 0
                };

                _dbContext.FareDiscountSchemes.Add(newScheme);
                await _dbContext.SaveChangesAsync();

                // 返回创建的方案
                var dto = new FareDiscountSchemeDto
                {
                    ID = newScheme.ID,
                    MerchantID = newScheme.MerchantID,
                    MerchantName = originalScheme.Merchant?.Name ?? "",
                    SchemeName = newScheme.SchemeName,
                    SchemeCode = newScheme.SchemeCode,
                    Description = newScheme.Description,
                    ExtraParams = !string.IsNullOrEmpty(newScheme.ExtraParamsJson) ?
                        JsonSerializer.Deserialize<object>(newScheme.ExtraParamsJson) : null,
                    CardDiscountInfo = !string.IsNullOrEmpty(newScheme.CardDiscountInfoJson) ?
                        JsonSerializer.Deserialize<object>(newScheme.CardDiscountInfoJson) : null,
                    IsActive = newScheme.IsActive,
                    UsageCount = newScheme.UsageCount,
                    LastUsedTime = newScheme.LastUsedTime,
                    CurrentVersion = newScheme.CurrentVersion,
                    CurrentFilePara = newScheme.CurrentFilePara,
                    CurrentFileVerID = newScheme.CurrentFileVerID,
                    CreateTime = newScheme.CreateTime,
                    UpdateTime = newScheme.UpdateTime,
                    Creator = newScheme.Creator,
                    Updater = newScheme.Updater
                };

                return CreatedAtAction(nameof(GetFareDiscountScheme), new { id = newScheme.ID }, dto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "复制票价折扣方案失败，ID: {Id}", id);
                return StatusCode(500, new { message = "服务器内部错误" });
            }
        }

        // GET: api/FareDiscountScheme/active
        [HttpGet("active")]
        public async Task<ActionResult<List<FareDiscountSchemeDto>>> GetActiveFareDiscountSchemes()
        {
            try
            {
                // 获取当前用户的商户ID
                var currentUserMerchantId = await _userService.GetUserMerchantIdAsync(User);
                var isSystemAdmin = User.IsInRole("SystemAdmin");

                if (!isSystemAdmin && string.IsNullOrEmpty(currentUserMerchantId))
                {
                    return BadRequest("无法获取用户商户信息");
                }

                var query = _dbContext.FareDiscountSchemes
                    .Include(s => s.Merchant)
                    .Where(s => s.IsActive);

                // 如果不是系统管理员，只能查看自己商户的方案
                if (!isSystemAdmin)
                {
                    query = query.Where(s => s.MerchantID == currentUserMerchantId);
                }

                var schemes = await query
                    .OrderBy(s => s.SchemeName)
                    .Select(s => new FareDiscountSchemeDto
                    {
                        ID = s.ID,
                        MerchantID = s.MerchantID,
                        MerchantName = s.Merchant != null ? s.Merchant.Name : "",
                        SchemeName = s.SchemeName,
                        SchemeCode = s.SchemeCode,
                        Description = s.Description,
                        IsActive = s.IsActive,
                        UsageCount = s.UsageCount,
                        LastUsedTime = s.LastUsedTime
                    })
                    .ToListAsync();

                return Ok(schemes);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取活跃票价折扣方案失败");
                return StatusCode(500, new { message = "服务器内部错误" });
            }
        }

        #region 票价折扣方案版本管理 APIs

        // GET: api/FareDiscountScheme/{id}/versions
        [HttpGet("{id}/versions")]
        public async Task<ActionResult<PaginatedResult<FareDiscountSchemeVersionDto>>> GetFareDiscountSchemeVersions(int id, [FromQuery] FareDiscountSchemeVersionQueryDto query)
        {
            try
            {
                // 获取当前用户的商户ID
                var currentUserMerchantId = await _userService.GetUserMerchantIdAsync(User);
                var isSystemAdmin = User.IsInRole("SystemAdmin");

                // 检查方案是否存在
                var scheme = await _dbContext.FareDiscountSchemes.FindAsync(id);
                if (scheme == null)
                {
                    return NotFound("票价折扣方案不存在");
                }

                // 权限检查
                if (!isSystemAdmin && scheme.MerchantID != currentUserMerchantId)
                {
                    return Forbid();
                }

                var dbQuery = _dbContext.FareDiscountSchemeVersions
                    .Include(v => v.Merchant)
                    .Where(v => v.FareDiscountSchemeID == id);

                // 状态筛选
                if (query.Status.HasValue)
                {
                    dbQuery = dbQuery.Where(v => v.Status == query.Status.Value);
                }

                var totalCount = await dbQuery.CountAsync();

                var items = await dbQuery
                    .OrderByDescending(v => v.CreateTime)
                    .Skip((query.Page - 1) * query.PageSize)
                    .Take(query.PageSize)
                    .Select(v => new FareDiscountSchemeVersionDto
                    {
                        ID = v.ID,
                        MerchantID = v.MerchantID,
                        MerchantName = v.Merchant != null ? v.Merchant.Name : "",
                        FareDiscountSchemeID = v.FareDiscountSchemeID,
                        SchemeName = v.SchemeName,
                        Version = v.Version,
                        FilePara = v.FilePara,
                        ExtraParams = !string.IsNullOrEmpty(v.ExtraParamsJson) ?
                            JsonSerializer.Deserialize<object>(v.ExtraParamsJson, (JsonSerializerOptions?)null) : null,
                        CardDiscountInfo = !string.IsNullOrEmpty(v.CardDiscountInfoJson) ?
                            JsonSerializer.Deserialize<object>(v.CardDiscountInfoJson, (JsonSerializerOptions?)null) : null,
                        Status = v.Status,
                        IsPublished = v.IsPublished,
                        FileVerID = v.FileVerID,
                        CreateTime = v.CreateTime,
                        UpdateTime = v.UpdateTime,
                        SubmitTime = v.SubmitTime,
                        Creator = v.Creator,
                        Updater = v.Updater,
                        Submitter = v.Submitter,
                        Remarks = v.Remarks
                    })
                    .ToListAsync();

                return Ok(new PaginatedResult<FareDiscountSchemeVersionDto>
                {
                    Items = items,
                    TotalCount = totalCount,
                    Page = query.Page,
                    PageSize = query.PageSize
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取票价折扣方案版本列表失败，方案ID: {SchemeId}", id);
                return StatusCode(500, new { message = "服务器内部错误" });
            }
        }

        // POST: api/FareDiscountScheme/{id}/submit
        [HttpPost("{id}/submit")]
        public async Task<ActionResult> SubmitFareDiscountScheme(int id, SubmitFareDiscountSchemeVersionDto model)
        {
            try
            {
                // 验证模型
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values
                        .SelectMany(v => v.Errors)
                        .Select(e => e.ErrorMessage)
                        .ToList();
                    return BadRequest(new { message = string.Join("; ", errors) });
                }

                // 额外的手动验证
                if (!string.IsNullOrEmpty(model.Version))
                {
                    if (!System.Text.RegularExpressions.Regex.IsMatch(model.Version, @"^[0-9A-Fa-f]{4}$"))
                    {
                        return BadRequest(new { message = "版本号必须是4位16进制字符（如：0001、A1B2）" });
                    }
                }

                if (!string.IsNullOrEmpty(model.FilePara))
                {
                    if (!System.Text.RegularExpressions.Regex.IsMatch(model.FilePara, @"^[A-Za-z0-9]{1,8}$"))
                    {
                        return BadRequest(new { message = "文件参数只能包含英文和数字，最多8个字符" });
                    }
                }

                if (!string.IsNullOrEmpty(model.Remarks) && model.Remarks.Length > 500)
                {
                    return BadRequest(new { message = "备注信息不能超过500个字符" });
                }

                // 获取当前用户的商户ID和用户名
                var currentUserMerchantId = await _userService.GetUserMerchantIdAsync(User);
                var isSystemAdmin = User.IsInRole("SystemAdmin");
                var username = UserService.GetUserNameForOperator(User);

                // 检查方案是否存在
                var scheme = await _dbContext.FareDiscountSchemes.FindAsync(id);
                if (scheme == null)
                {
                    return NotFound("票价折扣方案不存在");
                }

                // 权限检查
                if (!isSystemAdmin && scheme.MerchantID != currentUserMerchantId)
                {
                    return Forbid();
                }

                // 确定版本号
                string versionNumber = model.Version ?? "";
                if (string.IsNullOrEmpty(versionNumber))
                {
                    // 基于当前版本号生成新版本号
                    if (string.IsNullOrEmpty(scheme.CurrentVersion))
                    {
                        versionNumber = "0001"; // 首次提交从0001开始
                    }
                    else
                    {
                        // 将当前版本号转换为16进制整数，加1，再转回16进制字符串
                        int currentVersionInt = Convert.ToInt32(scheme.CurrentVersion, 16) % 0xFFFF;
                        versionNumber = (currentVersionInt + 1).ToString("X4");
                    }
                }

                // 确定文件参数
                string filePara = model.FilePara ?? scheme.CurrentFilePara;

                // 检查版本是否已存在（参考文件版本管理的逻辑）
                var existingFileVer = await _dbContext.FileVers
                    .FirstOrDefaultAsync(f => f.MerchantID == scheme.MerchantID &&
                                            f.FileTypeID == FARE_DISCOUNT_FILE_TYPE &&
                                            f.FilePara == filePara &&
                                            f.Ver == versionNumber);

                if (existingFileVer != null && !existingFileVer.IsDelete)
                {
                    // 检查是否有关联的文件发布
                    var hasFilePublish = await _dbContext.FilePublishs
                        .AnyAsync(p => p.FileVerID == existingFileVer.ID);

                    if (hasFilePublish)
                    {
                        return BadRequest("当前版本已经发布，不能重复创建，请使用其它版本号或删除已发布版本");
                    }
                    else
                    {
                        // 标记旧版本为删除
                        existingFileVer.IsDelete = true;
                    }
                }

                // 生成完整的票价折扣文件内容
                var fileContent = GenerateFareDiscountFileContent(scheme, versionNumber, filePara);
                if (fileContent == null)
                {
                    return BadRequest("生成票价折扣文件内容失败");
                }

                // 配置JSON序列化选项，不转义中文字符
                var jsonOptions = new JsonSerializerOptions
                {
                    Encoder = JavaScriptEncoder.Create(UnicodeRanges.All),
                    WriteIndented = false // 保持紧凑格式
                };

                // 将文件内容转换为JSON字符串
                string fileContentJson = JsonSerializer.Serialize(fileContent, jsonOptions);

                // 使用GB2312编码
                byte[] fileContentBytes = Encoding.GetEncoding("gb2312").GetBytes(fileContentJson);

                // 生成文件名
                string fileName = $"PZB_{filePara}_{versionNumber}.json";

                // 保存到文件存储
                var filePath = await _fileService.SaveTemporaryFile(fileContentBytes, fileName, username);
                if (string.IsNullOrEmpty(filePath))
                {
                    return BadRequest("保存文件失败");
                }

                // 计算CRC
                var crc = CalculateCrc32(fileContentBytes);

                // 生成文件ID
                var fileId = Guid.NewGuid().ToString("N");

                // 创建上传文件记录
                var uploadFile = new UploadFile
                {
                    ID = fileId,
                    FileName = fileName,
                    FileSize = fileContentBytes.Length,
                    FilePath = filePath,
                    UploadTime = DateTime.Now,
                    Crc = crc,
                    UploadedBy = username,
                    ContentType = "application/json"
                };

                _dbContext.UploadFiles.Add(uploadFile);

                // 创建文件版本记录
                var fileFullType = $"{FARE_DISCOUNT_FILE_TYPE}{filePara}";
                var fileVersion = new FileVer
                {
                    MerchantID = scheme.MerchantID,
                    FileTypeID = FARE_DISCOUNT_FILE_TYPE,
                    FilePara = filePara,
                    FileFullType = fileFullType,
                    Ver = versionNumber,
                    CreateTime = DateTime.Now,
                    UpdateTime = DateTime.Now,
                    UploadFileID = fileId,
                    FileSize = fileContentBytes.Length,
                    Crc = crc,
                    Operator = username,
                    IsDelete = false
                };

                _dbContext.FileVers.Add(fileVersion);

                // 创建版本历史记录
                var versionHistory = new FareDiscountSchemeVersion
                {
                    MerchantID = scheme.MerchantID,
                    FareDiscountSchemeID = id,
                    SchemeName = scheme.SchemeName,
                    Version = versionNumber,
                    FilePara = filePara,
                    ExtraParamsJson = scheme.ExtraParamsJson,
                    CardDiscountInfoJson = scheme.CardDiscountInfoJson,
                    FileContentJson = fileContentJson,
                    Status = FareDiscountVersionStatus.Submitted,
                    IsPublished = true,
                    Remarks = model.Remarks, // 添加备注信息
                    CreateTime = DateTime.Now,
                    UpdateTime = DateTime.Now,
                    SubmitTime = DateTime.Now,
                    Creator = username,
                    Updater = username,
                    Submitter = username
                };

                _dbContext.FareDiscountSchemeVersions.Add(versionHistory);

                // 先保存以获取文件版本ID
                await _dbContext.SaveChangesAsync();

                // 关联文件版本ID
                versionHistory.FileVerID = fileVersion.ID;

                // 更新方案的当前版本信息
                scheme.CurrentVersion = versionNumber;
                scheme.CurrentFilePara = filePara;
                scheme.CurrentFileVerID = fileVersion.ID;
                scheme.UpdateTime = DateTime.Now;
                scheme.Updater = username;

                await _dbContext.SaveChangesAsync();

                return Ok(new { message = "版本提交成功", version = versionNumber, fileVerID = fileVersion.ID });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "提交票价折扣方案失败，方案ID: {SchemeId}", id);
                return BadRequest($"提交失败：{ex.Message}");
            }
        }

        // GET: api/FareDiscountScheme/versions/{versionId}
        [HttpGet("versions/{versionId}")]
        public async Task<ActionResult<FareDiscountSchemeVersionDto>> GetFareDiscountSchemeVersion(int versionId)
        {
            try
            {
                // 获取当前用户的商户ID
                var currentUserMerchantId = await _userService.GetUserMerchantIdAsync(User);
                var isSystemAdmin = User.IsInRole("SystemAdmin");

                var version = await _dbContext.FareDiscountSchemeVersions
                    .Include(v => v.Merchant)
                    .FirstOrDefaultAsync(v => v.ID == versionId);

                if (version == null)
                {
                    return NotFound("票价折扣方案版本不存在");
                }

                // 权限检查
                if (!isSystemAdmin && version.MerchantID != currentUserMerchantId)
                {
                    return Forbid();
                }

                var dto = new FareDiscountSchemeVersionDto
                {
                    ID = version.ID,
                    MerchantID = version.MerchantID,
                    MerchantName = version.Merchant?.Name ?? "",
                    FareDiscountSchemeID = version.FareDiscountSchemeID,
                    SchemeName = version.SchemeName,
                    Version = version.Version,
                    FilePara = version.FilePara,
                    ExtraParams = !string.IsNullOrEmpty(version.ExtraParamsJson) ?
                        JsonSerializer.Deserialize<object>(version.ExtraParamsJson, (JsonSerializerOptions?)null) : null,
                    CardDiscountInfo = !string.IsNullOrEmpty(version.CardDiscountInfoJson) ?
                        JsonSerializer.Deserialize<object>(version.CardDiscountInfoJson, (JsonSerializerOptions?)null) : null,
                    Status = version.Status,
                    IsPublished = version.IsPublished,
                    FileVerID = version.FileVerID,
                    CreateTime = version.CreateTime,
                    UpdateTime = version.UpdateTime,
                    SubmitTime = version.SubmitTime,
                    Creator = version.Creator,
                    Updater = version.Updater,
                    Submitter = version.Submitter,
                    Remarks = version.Remarks
                };

                return Ok(dto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取票价折扣方案版本详情失败，版本ID: {VersionId}", versionId);
                return StatusCode(500, new { message = "服务器内部错误" });
            }
        }



        // POST: api/FareDiscountScheme/versions/{versionId}/preview
        [HttpPost("versions/{versionId}/preview")]
        public async Task<ActionResult<PreviewFareDiscountSchemeFileResponseDto>> PreviewFareDiscountSchemeFile(int versionId)
        {
            try
            {
                // 获取当前用户的商户ID
                var currentUserMerchantId = await _userService.GetUserMerchantIdAsync(User);
                var isSystemAdmin = User.IsInRole("SystemAdmin");

                var version = await _dbContext.FareDiscountSchemeVersions.FindAsync(versionId);
                if (version == null)
                {
                    return NotFound("票价折扣方案版本不存在");
                }

                // 权限检查
                if (!isSystemAdmin && version.MerchantID != currentUserMerchantId)
                {
                    return Forbid();
                }

                // 如果有保存的文件内容，直接返回
                if (!string.IsNullOrEmpty(version.FileContentJson))
                {
                    var savedContent = JsonSerializer.Deserialize<object>(version.FileContentJson);
                    return new PreviewFareDiscountSchemeFileResponseDto { FileContent = savedContent };
                }

                // 检查版本信息是否完整
                if (string.IsNullOrEmpty(version.ExtraParamsJson) || string.IsNullOrEmpty(version.CardDiscountInfoJson))
                {
                    return BadRequest("版本信息不完整，无法预览");
                }

                // 生成票价折扣文件内容
                var fileContent = GenerateFareDiscountFileContent(version);
                if (fileContent == null)
                {
                    return BadRequest("生成票价折扣文件内容失败");
                }

                return new PreviewFareDiscountSchemeFileResponseDto { FileContent = fileContent };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "预览票价折扣方案文件失败，版本ID: {VersionId}", versionId);
                return StatusCode(500, new { message = "服务器内部错误" });
            }
        }

        // GET: api/FareDiscountScheme/versions/{versionId}/download
        [HttpGet("versions/{versionId}/download")]
        public async Task<ActionResult> DownloadFareDiscountSchemeFile(int versionId)
        {
            try
            {
                // 获取当前用户的商户ID
                var currentUserMerchantId = await _userService.GetUserMerchantIdAsync(User);
                var isSystemAdmin = User.IsInRole("SystemAdmin");

                var version = await _dbContext.FareDiscountSchemeVersions.FindAsync(versionId);
                if (version == null)
                {
                    return NotFound("票价折扣方案版本不存在");
                }

                // 权限检查
                if (!isSystemAdmin && version.MerchantID != currentUserMerchantId)
                {
                    return Forbid();
                }

                string fileContentJson;

                // 如果有保存的文件内容，直接使用
                if (!string.IsNullOrEmpty(version.FileContentJson))
                {
                    fileContentJson = version.FileContentJson;
                }
                else
                {
                    // 检查版本信息是否完整
                    if (string.IsNullOrEmpty(version.ExtraParamsJson) || string.IsNullOrEmpty(version.CardDiscountInfoJson))
                    {
                        return BadRequest("版本信息不完整，无法下载");
                    }

                    // 生成票价折扣文件内容
                    var fileContent = GenerateFareDiscountFileContent(version);
                    if (fileContent == null)
                    {
                        return BadRequest("生成票价折扣文件内容失败");
                    }

                    // 配置JSON序列化选项，不转义中文字符
                    var jsonOptions = new JsonSerializerOptions
                    {
                        Encoder = JavaScriptEncoder.UnsafeRelaxedJsonEscaping,
                        WriteIndented = true // 格式化输出，便于阅读
                    };

                    fileContentJson = JsonSerializer.Serialize(fileContent, jsonOptions);
                }

                // 使用GB2312编码
                byte[] fileContentBytes = Encoding.GetEncoding("gb2312").GetBytes(fileContentJson);

                // 生成文件名
                string fileName = $"PZB_{version.FilePara}_{version.Version}.json";

                // 返回文件下载，设置正确的Content-Type和编码
                return File(fileContentBytes, "application/json; charset=gb2312", fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "下载票价折扣方案文件失败，版本ID: {VersionId}", versionId);
                return StatusCode(500, new { message = "服务器内部错误" });
            }
        }



        #endregion

        #region Helper Methods

        // 生成票价折扣文件内容（从版本记录）
        private object? GenerateFareDiscountFileContent(FareDiscountSchemeVersion version)
        {
            try
            {
                // 获取额外参数和卡类信息
                var extraParams = !string.IsNullOrEmpty(version.ExtraParamsJson)
                    ? JsonSerializer.Deserialize<Dictionary<string, object>>(version.ExtraParamsJson)
                    : new Dictionary<string, object>();

                var cardDiscountInfo = !string.IsNullOrEmpty(version.CardDiscountInfoJson)
                    ? JsonSerializer.Deserialize<List<Dictionary<string, object>>>(version.CardDiscountInfoJson)
                    : new List<Dictionary<string, object>>();

                // 构建content内容（不包含线路基本信息）
                var content = new Dictionary<string, object>();

                // 添加额外参数
                if (extraParams != null)
                {
                    foreach (var param in extraParams)
                    {
                        content.Add(param.Key, param.Value);
                    }
                }

                // 添加卡类折扣信息
                if (cardDiscountInfo != null && cardDiscountInfo.Count > 0)
                {
                    content.Add("cardDiscountInformation", cardDiscountInfo);
                }

                // 创建完整文件结构
                var fileContent = new Dictionary<string, object>
                {
                    { "type", FARE_DISCOUNT_FILE_TYPE },
                    { "para", version.FilePara },
                    { "name", "票价折扣方案文件" },
                    { "version", version.Version },
                    { "datetime", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") },
                    { "content", content }
                };

                return fileContent;
            }
            catch (Exception)
            {
                return null;
            }
        }

        // 生成票价折扣文件内容（从方案）
        private object? GenerateFareDiscountFileContent(FareDiscountScheme scheme, string version, string filePara)
        {
            try
            {
                // 获取额外参数和卡类信息
                var extraParams = !string.IsNullOrEmpty(scheme.ExtraParamsJson)
                    ? JsonSerializer.Deserialize<Dictionary<string, object>>(scheme.ExtraParamsJson)
                    : new Dictionary<string, object>();

                var cardDiscountInfo = !string.IsNullOrEmpty(scheme.CardDiscountInfoJson)
                    ? JsonSerializer.Deserialize<List<Dictionary<string, object>>>(scheme.CardDiscountInfoJson)
                    : new List<Dictionary<string, object>>();

                // 构建content内容（不包含线路基本信息）
                var content = new Dictionary<string, object>();

                // 添加额外参数
                if (extraParams != null)
                {
                    foreach (var param in extraParams)
                    {
                        content.Add(param.Key, param.Value);
                    }
                }

                // 添加卡类折扣信息
                if (cardDiscountInfo != null && cardDiscountInfo.Count > 0)
                {
                    content.Add("cardDiscountInformation", cardDiscountInfo);
                }

                // 创建完整文件结构
                var fileContent = new Dictionary<string, object>
                {
                    { "type", FARE_DISCOUNT_FILE_TYPE },
                    { "para", filePara },
                    { "name", "票价折扣方案文件" },
                    { "version", version },
                    { "datetime", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") },
                    { "content", content }
                };

                return fileContent;
            }
            catch (Exception)
            {
                return null;
            }
        }

        // 计算CRC32
        private string CalculateCrc32(byte[] data)
        {
            return SlzrCrossGate.Common.CRC.calFileCRC(data).ToString("X2").PadLeft(8, '0');
        }

        #endregion
    }
}
