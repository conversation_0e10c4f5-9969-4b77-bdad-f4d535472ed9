# 客户项目集成指南

## 🎯 概述

本指南详细说明如何将客户项目正确集成到WebAdmin主应用的菜单系统中，确保享受到新路由系统的所有优势。

## 🆕 新路由系统优势

### URL结构对比
```
旧版本: /app/dashboard?iframe=客户功能
新版本: /app/external/customer-a-dashboard
```

### 主要改进
- ✅ **专业URL**: 语义化的路由路径
- ✅ **菜单选中**: 客户项目菜单项正确高亮
- ✅ **SEO友好**: 英文标识符，避免中文编码
- ✅ **架构清晰**: 每个外部系统独立页面组件

## 📋 集成步骤

### 步骤1: 创建客户项目

使用项目提供的脚本创建客户项目：

```bash
# 基本用法
./scripts/create-customer-project.sh customer-a

# 指定路径类型
./scripts/create-customer-project.sh customer-a extension
./scripts/create-customer-project.sh customer-a custom /apps/customer-a
```

### 步骤2: 配置数据库菜单

#### 2.1 添加菜单分组（如果不存在）

```sql
INSERT IGNORE INTO MenuGroups (
    GroupKey, Title, IconName, SortOrder, 
    IsEnabled, VisibleToSystemAdmin, VisibleToMerchantAdmin, VisibleToUser
)
VALUES (
    'customer-features', 
    '客户功能', 
    'ExtensionIcon', 
    10, 
    1, 1, 1, 0
);
```

#### 2.2 添加菜单项（关键配置）

```sql
INSERT INTO MenuItems (
    MenuGroupId, 
    ItemKey,                    -- ⚠️ 关键字段：用于路由匹配
    Title, 
    Href, 
    IconName, 
    SortOrder,
    IsEnabled, 
    VisibleToSystemAdmin, 
    VisibleToMerchantAdmin, 
    VisibleToUser,
    IsExternal,                 -- ⚠️ 必须设置为1
    Target                      -- ⚠️ 推荐使用'_iframe'
)
VALUES (
    (SELECT Id FROM MenuGroups WHERE GroupKey = 'customer-features'),
    'customer-a-dashboard',     -- ItemKey命名规范
    '客户A仪表盘',
    'https://yourdomain.com/customer-a/',
    'DashboardIcon',
    1,
    1, 1, 1, 0,
    1,                          -- IsExternal = true
    '_iframe'                   -- 在iframe中打开
);
```

### 步骤3: ItemKey命名规范

#### 命名格式
```
{customer-name}-{feature-name}
```

#### 示例
- `customer-a-dashboard` - 客户A仪表盘
- `customer-a-reports` - 客户A报表
- `zhuhaitong-query` - 珠海通查询
- `monitoring-system` - 监控系统

#### 规则
- ✅ 使用小写字母和连字符
- ✅ 保持简洁和语义化
- ❌ 避免中文和特殊字符
- ❌ 不要使用空格或下划线

### 步骤4: 验证集成效果

#### 4.1 URL验证
访问客户项目后，URL应该显示为：
```
https://yourdomain.com/app/external/customer-a-dashboard
```

#### 4.2 菜单选中验证
- 客户项目菜单项应该高亮显示
- 其他菜单项应该处于非选中状态

#### 4.3 功能验证
- iframe正确加载客户项目
- 认证信息正确传递
- 页面交互正常工作

## 🔧 常见问题排查

### 问题1: 菜单不高亮

**原因**: ItemKey字段缺失或配置错误

**解决方案**:
```sql
-- 检查ItemKey字段
SELECT ItemKey, Title, IsExternal FROM MenuItems WHERE IsExternal = 1;

-- 更新ItemKey字段
UPDATE MenuItems 
SET ItemKey = 'customer-a-dashboard' 
WHERE Title = '客户A功能' AND IsExternal = 1;
```

### 问题2: 404错误

**原因**: 路由配置不匹配

**检查清单**:
- ItemKey是否符合命名规范
- IsExternal是否设置为1
- Target是否设置为'_iframe'

### 问题3: iframe不显示

**原因**: URL配置错误或客户项目未启动

**解决方案**:
- 检查Href字段的URL是否正确
- 确认客户项目服务正在运行
- 检查CORS配置

## 📚 配置模板

### 完整的菜单配置模板

```sql
-- 客户项目菜单配置模板
-- 替换以下变量：
-- {CUSTOMER_NAME} - 客户名称（如：customer-a）
-- {CUSTOMER_TITLE} - 客户显示名称（如：客户A）
-- {CUSTOMER_URL} - 客户项目URL
-- {ICON_NAME} - 图标名称

-- 1. 添加菜单分组
INSERT IGNORE INTO MenuGroups (
    GroupKey, Title, IconName, SortOrder, 
    IsEnabled, VisibleToSystemAdmin, VisibleToMerchantAdmin, VisibleToUser
)
VALUES (
    'customer-features', 
    '客户功能', 
    'ExtensionIcon', 
    10, 
    1, 1, 1, 0
);

-- 2. 添加菜单项
INSERT INTO MenuItems (
    MenuGroupId, ItemKey, Title, Href, IconName, SortOrder,
    IsEnabled, VisibleToSystemAdmin, VisibleToMerchantAdmin, VisibleToUser,
    IsExternal, Target
)
VALUES (
    (SELECT Id FROM MenuGroups WHERE GroupKey = 'customer-features'),
    '{CUSTOMER_NAME}-dashboard',
    '{CUSTOMER_TITLE}功能',
    '{CUSTOMER_URL}',
    '{ICON_NAME}',
    1,
    1, 1, 1, 0,
    1,
    '_iframe'
);
```

## 🎯 最佳实践

### 1. 开发阶段
- 使用localhost URL进行开发测试
- 确保客户项目支持iframe嵌入
- 实现与主应用的认证集成

### 2. 部署阶段
- 更新Href为生产环境URL
- 配置Nginx反向代理
- 设置正确的CORS策略

### 3. 维护阶段
- 定期检查菜单配置的正确性
- 监控客户项目的可用性
- 及时更新文档和配置

## 🔗 相关文档

- `EXTERNAL_SYSTEM_ROUTING_IMPLEMENTATION.md` - 路由重构详情
- `EXTERNAL_LINK_MENU_GUIDE.md` - 外部链接配置指南
- `scripts/create-customer-project.sh` - 客户项目创建脚本
- `CUSTOMER_TEMPLATE_SYNC_GUIDE.md` - 模板同步指南

---

**重要提醒**: 确保ItemKey字段正确配置，这是新路由系统正常工作的关键！
